#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Enhanced Modern Settings View
Complete settings interface using all modular sections with advanced navigation and search
"""

import flet as ft
from typing import Dict, Any, List, Optional
from .settings_controller import SettingsController
from .sections import EmailSection, NotificationsSection, GoogleServicesSection, WindowsSection, ReportsSection, LLMSection
from .components import QuickActionsCard, InfoBox
from core import get_logger

logger = get_logger(__name__)

class ModernSettingsView:
    """
    Enhanced modern settings view with all sections, advanced navigation, and search functionality
    Provides a complete replacement for the monolithic settings.py
    """
    
    def __init__(self, app_instance):
        self.app = app_instance
        self.controller = SettingsController(app_instance)
        
        # Initialize all sections
        self.sections = {
            'email': EmailSection(self.controller),
            'notifications': NotificationsSection(self.controller),
            'google_services': GoogleServicesSection(self.controller),
            'windows': WindowsSection(self.controller),
            'reports': <PERSON><PERSON>ection(self.controller),
            'llm': LLMSection(self.controller)
        }
        
        # Navigation state
        self.current_tab = 'essentials'
        self.search_query = ''
        self.filtered_sections = list(self.sections.keys())
        
        # Tab configuration
        self.tabs_config = {
            'essentials': {
                'title': 'Essenziali',
                'icon': ft.Icons.SETTINGS,
                'sections': ['email', 'notifications'],
                'description': 'Configurazioni di base per email e notifiche'
            },
            'integrations': {
                'title': 'Integrazioni',
                'icon': ft.Icons.EXTENSION,
                'sections': ['google_services', 'windows', 'llm'],
                'description': 'Servizi Google, LLM e integrazione Windows'
            },
            'reports': {
                'title': 'Report',
                'icon': ft.Icons.ANALYTICS,
                'sections': ['reports'],
                'description': 'Report automatici e analisi personalizzate'
            },
            'advanced': {
                'title': 'Avanzate',
                'icon': ft.Icons.TUNE,
                'sections': [],  # Advanced settings would go here
                'description': 'Configurazioni avanzate e strumenti di sistema'
            }
        }
        
        logger.info("Enhanced ModernSettingsView initialized with all sections")
    
    def _create_header(self) -> ft.Container:
        """Create enhanced header with search and global actions"""
        
        def on_search_change(e):
            self.search_query = e.control.value.lower()
            self._apply_search_filter()
            self._refresh_content()
        
        def clear_search(e):
            self.search_query = ''
            self.search_field.value = ''
            self._apply_search_filter()
            self._refresh_content()
        
        def save_all_settings(e):
            """Save all settings across all sections"""
            if self.controller.has_validation_errors():
                self._show_error_notification("Correggi tutti gli errori di validazione prima di salvare")
                return
            
            success = self.controller.save_settings()
            if success:
                self._show_success_notification("🎉 Tutte le impostazioni salvate con successo!")
            else:
                self._show_error_notification("❌ Errore durante il salvataggio delle impostazioni")
        
        def export_settings(e):
            """Export all settings to file"""
            success = self.controller.export_settings()
            if success:
                self._show_success_notification("⬇️ Impostazioni esportate con successo!")
            else:
                self._show_error_notification("❌ Errore durante l'esportazione")
        
        # Search field
        self.search_field = ft.TextField(
            hint_text="🔍 Cerca nelle impostazioni...",
            width=300,
            on_change=on_search_change,
            suffix=ft.IconButton(
                icon=ft.Icons.CLEAR,
                on_click=clear_search,
                tooltip="Cancella ricerca"
            ) if self.search_query else None,
            border_radius=ft.border_radius.all(25)
        )
        
        # Global action buttons
        action_buttons = ft.Row([
            ft.IconButton(
                icon=ft.Icons.DOWNLOAD,
                tooltip="Esporta Impostazioni",
                on_click=export_settings,
                bgcolor=ft.Colors.BLUE_50,
                style=ft.ButtonStyle(
                    shape=ft.RoundedRectangleBorder(radius=8)
                )
            ),
            ft.ElevatedButton(
                text="Salva Tutto",
                icon=ft.Icons.SAVE,
                on_click=save_all_settings,
                bgcolor=ft.Colors.GREEN_600,
                color=ft.Colors.WHITE,
                style=ft.ButtonStyle(
                    shape=ft.RoundedRectangleBorder(radius=8)
                )
            )
        ], spacing=8)
        
        # Settings summary
        summary = self.controller.get_settings_summary()
        summary_text = f"📊 {summary['categories']} categorie configurate"
        if summary['validation_errors'] > 0:
            summary_text += f" • ⚠️ {summary['validation_errors']} errori"
        if summary['configured_services']:
            summary_text += f" • 🔗 {len(summary['configured_services'])} servizi connessi"
        
        return ft.Container(
            content=ft.Column([
                # Title and subtitle
                ft.Row([
                    ft.Column([
                        ft.Text(
                            "⚙️ Impostazioni Agevolami PM",
                            size=28,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.GREY_800
                        ),
                        ft.Text(
                            "Configura l'applicazione secondo le tue esigenze",
                            size=14,
                            color=ft.Colors.GREY_600
                        )
                    ], spacing=4, expand=True),
                    action_buttons
                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                
                # Search and summary
                ft.Row([
                    self.search_field,
                    ft.Container(expand=True),
                    ft.Text(summary_text, size=12, color=ft.Colors.GREY_600)
                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN)
            ], spacing=16),
            padding=ft.padding.all(20),
            bgcolor=ft.Colors.WHITE,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.GREY_200),
            margin=ft.margin.only(bottom=16)
        )
    
    def _create_tabs_navigation(self) -> ft.Container:
        """Create enhanced tabs navigation"""
        
        def on_tab_change(tab_key: str):
            self.current_tab = tab_key
            self._refresh_content()
        
        tabs = []
        for tab_key, tab_config in self.tabs_config.items():
            is_active = tab_key == self.current_tab
            
            # Count sections with errors in this tab
            error_count = 0
            for section_key in tab_config['sections']:
                if self.controller.has_validation_errors(section_key):
                    error_count += 1
            
            tab_button = ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Icon(
                            tab_config['icon'],
                            size=20,
                            color=ft.Colors.WHITE if is_active else ft.Colors.GREY_600
                        ),
                        ft.Text(
                            tab_config['title'],
                            size=14,
                            weight=ft.FontWeight.BOLD if is_active else ft.FontWeight.NORMAL,
                            color=ft.Colors.WHITE if is_active else ft.Colors.GREY_700
                        ),
                        ft.Container(
                            content=ft.Text(
                                str(error_count),
                                size=10,
                                color=ft.Colors.WHITE,
                                weight=ft.FontWeight.BOLD
                            ),
                            bgcolor=ft.Colors.RED_600,
                            border_radius=10,
                            padding=ft.padding.symmetric(horizontal=6, vertical=2),
                            visible=error_count > 0
                        )
                    ], spacing=8, alignment=ft.MainAxisAlignment.CENTER),
                    ft.Text(
                        tab_config['description'],
                        size=10,
                        color=ft.Colors.WHITE if is_active else ft.Colors.GREY_500,
                        text_align=ft.TextAlign.CENTER
                    )
                ], spacing=4, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                bgcolor=ft.Colors.BLUE_600 if is_active else ft.Colors.TRANSPARENT,
                padding=ft.padding.all(12),
                border_radius=8,
                on_click=lambda e, key=tab_key: on_tab_change(key),
                ink=True,
                expand=True
            )
            tabs.append(tab_button)
        
        return ft.Container(
            content=ft.Row(tabs, spacing=8, expand=True),
            padding=ft.padding.all(16),
            bgcolor=ft.Colors.GREY_100,
            border_radius=8,
            margin=ft.margin.only(bottom=16)
        )
    
    def _create_quick_actions(self) -> QuickActionsCard:
        """Create quick actions section"""
        
        def test_email(e):
            """Test email configuration"""
            self._show_info_notification("🧪 Test email avviato...")
        
        def backup_now(e):
            """Trigger immediate backup"""
            self._show_info_notification("💾 Backup immediato avviato...")
        
        def sync_services(e):
            """Sync all connected services"""
            self._show_info_notification("🔄 Sincronizzazione servizi avviata...")
        
        def generate_report(e):
            """Generate immediate report"""
            self._show_info_notification("📊 Generazione report avviata...")
        
        actions = [
            {
                "title": "Test Email",
                "description": "Verifica configurazione SMTP",
                "icon": ft.Icons.EMAIL,
                "on_click": test_email,
                "color": ft.Colors.BLUE_600
            },
            {
                "title": "Backup Immediato",
                "description": "Salva dati su Google Drive",
                "icon": ft.Icons.BACKUP,
                "on_click": backup_now,
                "color": ft.Colors.GREEN_600
            },
            {
                "title": "Sync Servizi",
                "description": "Aggiorna tutti i servizi",
                "icon": ft.Icons.SYNC,
                "on_click": sync_services,
                "color": ft.Colors.PURPLE_600
            },
            {
                "title": "Report Istantaneo",
                "description": "Genera report completo",
                "icon": ft.Icons.ASSESSMENT,
                "on_click": generate_report,
                "color": ft.Colors.ORANGE_600
            }
        ]
        
        return QuickActionsCard(
            title="🚀 Azioni Rapide",
            description="Strumenti per testare e gestire le configurazioni",
            actions=actions
        )
    
    def _create_status_overview(self) -> ft.Container:
        """Create system status overview"""
        summary = self.controller.get_settings_summary()
        
        # Status indicators
        status_items = []
        
        # Email status
        email_color = ft.Colors.GREEN_600 if summary['email_configured'] else ft.Colors.GREY_500
        email_icon = ft.Icons.CHECK_CIRCLE if summary['email_configured'] else ft.Icons.RADIO_BUTTON_UNCHECKED
        status_items.append(
            ft.Row([
                ft.Icon(email_icon, size=16, color=email_color),
                ft.Text("Email SMTP", size=12, color=email_color)
            ], spacing=8)
        )
        
        # Notifications status
        notif_color = ft.Colors.GREEN_600 if summary['notifications_enabled'] else ft.Colors.GREY_500
        notif_icon = ft.Icons.NOTIFICATIONS_ACTIVE if summary['notifications_enabled'] else ft.Icons.NOTIFICATIONS_OFF
        status_items.append(
            ft.Row([
                ft.Icon(notif_icon, size=16, color=notif_color),
                ft.Text("Notifiche", size=12, color=notif_color)
            ], spacing=8)
        )
        
        # Google Services status
        google_color = ft.Colors.GREEN_600 if summary['google_services_connected'] > 0 else ft.Colors.GREY_500
        google_icon = ft.Icons.CLOUD_DONE if summary['google_services_connected'] > 0 else ft.Icons.CLOUD_OFF
        status_items.append(
            ft.Row([
                ft.Icon(google_icon, size=16, color=google_color),
                ft.Text(f"Google ({summary['google_services_connected']}/3)", size=12, color=google_color)
            ], spacing=8)
        )
        
        # Reports status
        reports_color = ft.Colors.GREEN_600 if summary['reports_enabled'] else ft.Colors.GREY_500
        reports_icon = ft.Icons.SCHEDULE_SEND if summary['reports_enabled'] else ft.Icons.REPORT_OFF
        status_items.append(
            ft.Row([
                ft.Icon(reports_icon, size=16, color=reports_color),
                ft.Text("Report", size=12, color=reports_color)
            ], spacing=8)
        )
        
        return ft.Container(
            content=ft.Column([
                ft.Text("📋 Stato Sistema", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_800),
                ft.Column(status_items, spacing=8)
            ], spacing=12),
            padding=ft.padding.all(16),
            bgcolor=ft.Colors.BLUE_50,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.BLUE_200)
        )
    
    def _apply_search_filter(self):
        """Apply search filter to sections"""
        if not self.search_query:
            self.filtered_sections = list(self.sections.keys())
            return
        
        # Search in section titles, descriptions, and settings
        filtered = []
        for section_key, section in self.sections.items():
            # Check section title/description
            if self.search_query in section_key.lower():
                filtered.append(section_key)
                continue
            
            # Check section content (would need to implement search in sections)
            # For now, simple keyword matching
            keywords = {
                'email': ['smtp', 'mail', 'email', 'server', 'port'],
                'notifications': ['alert', 'notification', 'remind', 'schedule'],
                'google_services': ['google', 'drive', 'calendar', 'tasks', 'cloud'],
                'windows': ['startup', 'notification', 'system', 'integration'],
                'reports': ['report', 'analysis', 'schedule', 'export'],
                'llm': ['llm', 'ai', 'openrouter', 'gpt', 'claude', 'model', 'api', 'incentivi', 'analisi']
            }
            
            if section_key in keywords:
                if any(keyword in self.search_query for keyword in keywords[section_key]):
                    filtered.append(section_key)
        
        self.filtered_sections = filtered
    
    def _create_sections_content(self) -> List[ft.Control]:
        """Create content based on current tab and filters"""
        content = []
        
        # Get sections for current tab
        current_tab_sections = self.tabs_config[self.current_tab]['sections']
        
        # Filter sections based on search and tab
        visible_sections = [
            key for key in current_tab_sections 
            if key in self.filtered_sections
        ]
        
        if not visible_sections and self.search_query:
            # Show "no results" message
            content.append(
                InfoBox.create(
                    title="🔍 Nessun Risultato",
                    message=f"Nessuna impostazione trovata per '{self.search_query}'",
                    type="info",
                    items=[
                        "Prova con termini di ricerca diversi",
                        "Controlla le altre schede",
                        "Cancella la ricerca per vedere tutte le opzioni"
                    ]
                )
            )
            return content
        
        # Add sections
        for section_key in visible_sections:
            if section_key in self.sections:
                section_card = self.sections[section_key].build()
                content.append(section_card)
        
        # Add quick actions for essentials tab
        if self.current_tab == 'essentials' and not self.search_query:
            content.insert(0, self._create_quick_actions().build())
        
        # Add status overview for advanced tab
        if self.current_tab == 'advanced':
            content.extend([
                self._create_status_overview(),
                InfoBox.create(
                    title="🛠️ Strumenti Avanzati",
                    message="Funzioni avanzate in sviluppo",
                    type="info",
                    items=[
                        "Diagnostica sistema completa",
                        "Backup e ripristino avanzato",
                        "Configurazioni performance",
                        "Log e debugging tools"
                    ]
                )
            ])
        
        return content
    
    def _refresh_content(self):
        """Refresh the content area"""
        # In a real implementation, this would update the UI
        logger.debug(f"Refreshing content for tab: {self.current_tab}, query: '{self.search_query}'")
    
    def _show_success_notification(self, message: str):
        """Show success notification"""
        logger.info(f"SUCCESS: {message}")
    
    def _show_error_notification(self, message: str):
        """Show error notification"""
        logger.error(f"ERROR: {message}")
    
    def _show_info_notification(self, message: str):
        """Show info notification"""
        logger.info(f"INFO: {message}")
    
    def build(self) -> ft.Container:
        """Build the complete modern settings view"""
        
        # Create main content
        sections_content = self._create_sections_content()
        
        # Main scrollable content
        main_content = ft.Column([
            self._create_header(),
            self._create_tabs_navigation(),
            *sections_content
        ], spacing=0, scroll=ft.ScrollMode.AUTO)
        
        return ft.Container(
            content=main_content,
            padding=ft.padding.all(20),
            expand=True,
            bgcolor=ft.Colors.GREY_50
        )
    
    def refresh_data(self):
        """Refresh all data and reload settings"""
        self.controller._load_settings()
        # Refresh all sections
        for section in self.sections.values():
            if hasattr(section, 'refresh_data'):
                section.refresh_data()
        logger.info("Settings data refreshed")

    def handle_setting_change(self, category: str, key: str, value: Any):
        """Handle external setting changes"""
        success = self.controller.set_setting(category, key, value)
        if success:
            self._refresh_content()
        return success
    
    def get_validation_summary(self) -> Dict[str, Any]:
        """Get validation summary across all sections"""
        return {
            'has_errors': self.controller.has_validation_errors(),
            'errors_by_category': self.controller.get_validation_errors(),
            'total_errors': sum(
                len(errors) for errors in self.controller.get_validation_errors().values()
            )
        } 