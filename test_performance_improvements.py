#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to verify performance improvements
"""

import time
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_cache_manager():
    """Test the cache manager functionality"""
    print("Testing Cache Manager...")
    
    from core.services.cache_manager import get_cache, get_google_api_cache
    
    # Test basic cache operations
    cache = get_cache()
    
    # Test set/get
    start_time = time.time()
    cache.set("test_key", {"data": "test_value"}, ttl=60)
    value = cache.get("test_key")
    end_time = time.time()
    
    assert value == {"data": "test_value"}, "Cache set/get failed"
    print(f"✅ Basic cache operations: {(end_time - start_time) * 1000:.2f}ms")
    
    # Test Google API cache
    api_cache = get_google_api_cache()
    
    # Test calendar events cache
    start_time = time.time()
    api_cache.set_calendar_events("test_calendar", "2024-01-01", "2024-01-31", [{"event": "test"}])
    events = api_cache.get_calendar_events("test_calendar", "2024-01-01", "2024-01-31")
    end_time = time.time()
    
    assert events == [{"event": "test"}], "Google API cache failed"
    print(f"✅ Google API cache operations: {(end_time - start_time) * 1000:.2f}ms")
    
    # Test cache stats
    stats = cache.get_cache_stats()
    print(f"✅ Cache stats: {stats['total_entries']} entries")

def test_background_service_manager():
    """Test the background service manager"""
    print("\nTesting Background Service Manager...")
    
    from core.services.background_service_manager import get_service_manager, ServiceStatus
    
    manager = get_service_manager()
    
    # Test service registration
    def dummy_initializer():
        time.sleep(0.1)  # Simulate initialization time
        return {"service": "dummy", "initialized": True}
    
    start_time = time.time()
    manager.register_service("test_service", dummy_initializer)
    
    # Check that registration is non-blocking
    registration_time = time.time() - start_time
    assert registration_time < 0.05, f"Service registration took too long: {registration_time:.3f}s"
    print(f"✅ Non-blocking service registration: {registration_time * 1000:.2f}ms")
    
    # Wait for service to be ready
    service = manager.wait_for_service("test_service", timeout=5.0)
    assert service is not None, "Service failed to initialize"
    assert service["service"] == "dummy", "Service data incorrect"
    print("✅ Background service initialization completed")
    
    # Test service status
    status = manager.get_service_status("test_service")
    assert status == ServiceStatus.READY, f"Service status incorrect: {status}"
    print("✅ Service status tracking works")

def test_data_cache_performance():
    """Test data cache performance improvements"""
    print("\nTesting Data Cache Performance...")
    
    # Mock database manager for testing
    class MockDBManager:
        def __init__(self):
            self.call_count = 0
        
        def get_all_clients(self):
            self.call_count += 1
            time.sleep(0.01)  # Simulate database query time
            return [{"id": i, "name": f"Client {i}"} for i in range(100)]
        
        def get_all_projects(self):
            self.call_count += 1
            time.sleep(0.01)
            return [{"id": i, "name": f"Project {i}"} for i in range(50)]
    
    from core.services.data_cache_manager import DataCacheManager
    
    mock_db = MockDBManager()
    data_cache = DataCacheManager(mock_db)
    
    # Test first call (should hit database)
    start_time = time.time()
    clients1 = data_cache.get_all_clients()
    first_call_time = time.time() - start_time
    
    # Test second call (should hit cache)
    start_time = time.time()
    clients2 = data_cache.get_all_clients()
    second_call_time = time.time() - start_time
    
    assert len(clients1) == 100, "First call data incorrect"
    assert clients1 == clients2, "Cache data doesn't match"
    assert mock_db.call_count == 1, f"Database called {mock_db.call_count} times, expected 1"
    assert second_call_time < first_call_time / 2, "Cache not significantly faster"
    
    print(f"✅ First call (DB): {first_call_time * 1000:.2f}ms")
    print(f"✅ Second call (Cache): {second_call_time * 1000:.2f}ms")
    print(f"✅ Cache speedup: {first_call_time / second_call_time:.1f}x faster")

def test_google_services_lazy_loading():
    """Test that Google services don't block UI initialization"""
    print("\nTesting Google Services Lazy Loading...")
    
    # Test that importing views doesn't trigger Google service initialization
    start_time = time.time()
    
    try:
        from ui.views.enhanced_calendar import EnhancedCalendarView
        from ui.views.tasks import TasksView
        from ui.views.deadlines import DeadlinesView
        
        # Mock app instance
        class MockApp:
            class MockDBManager:
                pass
            db_manager = MockDBManager()
        
        mock_app = MockApp()
        
        # Create views - this should be fast since Google services are lazy-loaded
        calendar_view = EnhancedCalendarView(mock_app)
        tasks_view = TasksView(mock_app)
        deadlines_view = DeadlinesView(mock_app)
        
        initialization_time = time.time() - start_time
        
        # Should be very fast since no Google API calls are made
        assert initialization_time < 0.1, f"View initialization too slow: {initialization_time:.3f}s"
        print(f"✅ View initialization: {initialization_time * 1000:.2f}ms")
        
        # Test that Google services return dummy services when not ready
        assert not calendar_view.google_service.is_enabled(), "Google service should be disabled initially"
        assert not tasks_view.google_tasks_service.is_enabled(), "Google Tasks should be disabled initially"
        assert not deadlines_view.google_service.is_enabled(), "Google Calendar should be disabled initially"
        
        print("✅ Google services properly lazy-loaded")
        
    except ImportError as e:
        print(f"⚠️  Could not test view imports: {e}")

def main():
    """Run all performance tests"""
    print("🚀 Testing Performance Improvements for Agevolami PM")
    print("=" * 60)
    
    try:
        test_cache_manager()
        test_background_service_manager()
        test_data_cache_performance()
        test_google_services_lazy_loading()
        
        print("\n" + "=" * 60)
        print("✅ All performance tests passed!")
        print("\n📊 Performance Improvements Summary:")
        print("• Cache Manager: Fast in-memory caching with TTL")
        print("• Background Services: Non-blocking Google service initialization")
        print("• Data Cache: Cached database queries for faster UI loading")
        print("• Lazy Loading: Google services don't block UI startup")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
