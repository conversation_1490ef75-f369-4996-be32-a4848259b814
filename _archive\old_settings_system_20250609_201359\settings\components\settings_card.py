#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Modern Settings Card Component
Provides a clean, expandable card interface for settings sections
"""

import flet as ft
from typing import List, Optional, Callable
from core import get_logger

logger = get_logger(__name__)

class SettingsCard:
    """
    Modern settings card with expandable content and beautiful styling
    """
    
    def __init__(
        self,
        title: str,
        description: str = None,
        icon: str = None,
        icon_color: str = None,
        expanded: bool = True,
        expandable: bool = True,
        content: List[ft.Control] = None,
        actions: List[ft.Control] = None,
        on_expand: Callable = None,
        accent_color: str = None
    ):
        self.title = title
        self.description = description
        self.icon = icon
        self.icon_color = icon_color or ft.Colors.BLUE_600
        self.expanded = expanded
        self.expandable = expandable
        self.content = content or []
        self.actions = actions or []
        self.on_expand = on_expand
        self.accent_color = accent_color or ft.Colors.BLUE_600
        
        # Internal state
        self._card_container = None
    
    def build(self) -> ft.Container:
        """Build the settings card"""
        
        # Header with title and expand button
        header_content = []
        
        # Icon and title row
        title_row = []
        
        if self.icon:
            title_row.append(
                ft.Icon(
                    self.icon,
                    size=20,
                    color=self.icon_color
                )
            )
        
        title_row.extend([
            ft.Text(
                self.title,
                size=16,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.GREY_800,
                expand=True
            )
        ])
        
        if self.expandable:
            title_row.append(
                ft.IconButton(
                    icon=ft.Icons.EXPAND_LESS if self.expanded else ft.Icons.EXPAND_MORE,
                    icon_size=20,
                    icon_color=ft.Colors.GREY_600,
                    tooltip="Espandi/Richiudi",
                    on_click=self._toggle_expand,
                    style=ft.ButtonStyle(
                        shape=ft.CircleBorder(),
                        padding=4
                    )
                )
            )
        
        header_content.append(
            ft.Row(
                title_row,
                spacing=12,
                alignment=ft.MainAxisAlignment.SPACE_BETWEEN
            )
        )
        
        # Description
        if self.description:
            header_content.append(
                ft.Text(
                    self.description,
                    size=12,
                    color=ft.Colors.GREY_600,
                    weight=ft.FontWeight.W_400
                )
            )
        
        # Build complete card content
        card_content = [
            ft.Container(
                content=ft.Column(header_content, spacing=4),
                padding=ft.padding.all(20),
                bgcolor=ft.Colors.WHITE,
                border_radius=ft.border_radius.only(
                    top_left=12, 
                    top_right=12,
                    bottom_left=12 if not self.expanded else 0,
                    bottom_right=12 if not self.expanded else 0
                ),
                border=ft.border.all(1, ft.Colors.GREY_200),
                ink=True if self.expandable else False
            )
        ]
        
        # Expanded content
        if self.expanded and (self.content or self.actions):
            expanded_content = []
            
            # Divider
            expanded_content.append(
                ft.Divider(height=1, color=ft.Colors.GREY_200)
            )
            
            # Main content
            if self.content:
                expanded_content.extend(self.content)
            
            # Actions section
            if self.actions:
                expanded_content.append(
                    ft.Container(
                        content=ft.Row(
                            self.actions,
                            spacing=8,
                            alignment=ft.MainAxisAlignment.END,
                            wrap=True
                        ),
                        padding=ft.padding.only(top=16)
                    )
                )
            
            card_content.append(
                ft.Container(
                    content=ft.Column(expanded_content, spacing=16),
                    padding=ft.padding.only(left=20, right=20, bottom=20),
                    bgcolor=ft.Colors.WHITE,
                    border_radius=ft.border_radius.only(
                        bottom_left=12,
                        bottom_right=12
                    ),
                    border=ft.border.only(
                        left=ft.BorderSide(1, ft.Colors.GREY_200),
                        right=ft.BorderSide(1, ft.Colors.GREY_200),
                        bottom=ft.BorderSide(1, ft.Colors.GREY_200)
                    )
                )
            )
        
        self._card_container = ft.Container(
            content=ft.Column(card_content, spacing=0),
            margin=ft.margin.only(bottom=16),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=4,
                color=ft.Colors.BLACK12,
                offset=ft.Offset(0, 2)
            ),
            animate=ft.Animation(300, ft.AnimationCurve.EASE_OUT)
        )
        
        return self._card_container
    
    def _toggle_expand(self, e):
        """Toggle expansion state"""
        if not self.expandable:
            return
            
        self.expanded = not self.expanded
        
        # Call external callback
        if self.on_expand:
            self.on_expand(self.expanded)
        
        # Rebuild the card
        if self._card_container:
            new_card = self.build()
            self._card_container.content = new_card.content
            self._card_container.update()
    
    def set_expanded(self, expanded: bool):
        """Set expansion state programmatically"""
        if self.expandable and self.expanded != expanded:
            self.expanded = expanded
            if self._card_container:
                new_card = self.build()
                self._card_container.content = new_card.content
                self._card_container.update()
    
    def add_content(self, control: ft.Control):
        """Add content to the card"""
        self.content.append(control)
    
    def add_action(self, control: ft.Control):
        """Add action button to the card"""
        self.actions.append(control)
    
    def update_status(self, status: str, message: str = None):
        """Update card status with visual indicator"""
        # Add status indicator to title
        status_colors = {
            "success": ft.Colors.GREEN_600,
            "error": ft.Colors.RED_600,
            "warning": ft.Colors.ORANGE_600,
            "info": ft.Colors.BLUE_600
        }
        
        status_icons = {
            "success": ft.Icons.CHECK_CIRCLE,
            "error": ft.Icons.ERROR,
            "warning": ft.Icons.WARNING,
            "info": ft.Icons.INFO
        }
        
        if status in status_colors:
            self.icon_color = status_colors[status]
            if status in status_icons:
                self.icon = status_icons[status]
        
        # Rebuild if container exists
        if self._card_container:
            new_card = self.build()
            self._card_container.content = new_card.content
            self._card_container.update()


class SettingsCardSection:
    """
    A section within a settings card for organizing related controls
    """
    
    def __init__(
        self,
        title: str = None,
        description: str = None,
        controls: List[ft.Control] = None,
        layout: str = "column"  # column, row, grid
    ):
        self.title = title
        self.description = description
        self.controls = controls or []
        self.layout = layout
    
    def build(self) -> ft.Container:
        """Build the section"""
        content = []
        
        # Section header
        if self.title:
            content.append(
                ft.Text(
                    self.title,
                    size=14,
                    weight=ft.FontWeight.W_600,
                    color=ft.Colors.GREY_800
                )
            )
        
        if self.description:
            content.append(
                ft.Text(
                    self.description,
                    size=11,
                    color=ft.Colors.GREY_600
                )
            )
        
        # Controls layout
        if self.controls:
            if self.layout == "row":
                controls_container = ft.Row(
                    self.controls,
                    spacing=16,
                    wrap=True
                )
            elif self.layout == "grid":
                # Simple 2-column grid
                rows = []
                for i in range(0, len(self.controls), 2):
                    row_controls = self.controls[i:i+2]
                    if len(row_controls) == 1:
                        row_controls.append(ft.Container())  # Fill empty space
                    rows.append(ft.Row(row_controls, spacing=16))
                controls_container = ft.Column(rows, spacing=8)
            else:  # column
                controls_container = ft.Column(
                    self.controls,
                    spacing=8
                )
            
            content.append(controls_container)
        
        return ft.Container(
            content=ft.Column(content, spacing=8),
            padding=ft.padding.symmetric(vertical=8)
        )
    
    def add_control(self, control: ft.Control):
        """Add a control to this section"""
        self.controls.append(control)


class QuickActionsCard:
    """
    Special card for quick actions with prominent buttons
    """
    
    def __init__(
        self,
        title: str,
        actions: List[dict],  # [{"text": str, "icon": str, "on_click": callable, "style": str}]
        description: str = None,
        accent_color: str = None
    ):
        self.title = title
        self.actions = actions
        self.description = description
        self.accent_color = accent_color or ft.Colors.BLUE_600
    
    def build(self) -> ft.Container:
        """Build the quick actions card"""
        
        # Header
        header = ft.Row([
            ft.Icon(
                ft.Icons.FLASH_ON,
                size=20,
                color=self.accent_color
            ),
            ft.Text(
                self.title,
                size=16,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.GREY_800
            )
        ], spacing=8)
        
        # Description
        description = None
        if self.description:
            description = ft.Text(
                self.description,
                size=12,
                color=ft.Colors.GREY_600
            )
        
        # Action buttons
        action_buttons = []
        for action in self.actions:
            style = action.get("style", "primary")
            
            # Support both "text" and "title" keys
            button_text = action.get("text", action.get("title", ""))

            if style == "primary":
                button = ft.ElevatedButton(
                    text=button_text,
                    icon=action.get("icon"),
                    on_click=action.get("on_click"),
                    bgcolor=self.accent_color,
                    color=ft.Colors.WHITE,
                    expand=True,
                    style=ft.ButtonStyle(
                        shape=ft.RoundedRectangleBorder(radius=8),
                        elevation=2,
                        padding=ft.padding.symmetric(horizontal=16, vertical=12)
                    )
                )
            else:
                button = ft.OutlinedButton(
                    text=button_text,
                    icon=action.get("icon"),
                    on_click=action.get("on_click"),
                    expand=True,
                    style=ft.ButtonStyle(
                        shape=ft.RoundedRectangleBorder(radius=8),
                        padding=ft.padding.symmetric(horizontal=16, vertical=12)
                    )
                )
            
            action_buttons.append(button)
        
        # Arrange buttons in rows of 2
        button_rows = []
        for i in range(0, len(action_buttons), 2):
            row_buttons = action_buttons[i:i+2]
            button_rows.append(ft.Row(row_buttons, spacing=12))
        
        # Build content
        content = [header]
        if description:
            content.append(description)
        
        content.append(
            ft.Container(height=8)  # Spacer
        )
        
        content.extend(button_rows)
        
        return ft.Container(
            content=ft.Column(content, spacing=8),
            padding=ft.padding.all(20),
            margin=ft.margin.only(bottom=16),
            bgcolor=ft.Colors.WHITE,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_200),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=4,
                color=ft.Colors.BLACK12,
                offset=ft.Offset(0, 2)
            )
        ) 