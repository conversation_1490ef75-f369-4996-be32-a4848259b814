#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Advanced Search Component
Interactive search interface with autocomplete, suggestions, and real-time results
"""

import flet as ft
from typing import Dict, List, Any, Optional, Callable
from .search_engine import SettingsSearchEngine, SearchResult
from core import get_logger

logger = get_logger(__name__)

class AdvancedSearchComponent:
    """
    Advanced search component with modern UX features
    """
    
    def __init__(self, on_search_callback: Optional[Callable[[str, List[SearchResult]], None]] = None):
        self.search_engine = SettingsSearchEngine()
        self.on_search_callback = on_search_callback
        
        # Component state
        self.current_query = ""
        self.search_results: List[SearchResult] = []
        self.show_suggestions = False
        self.suggestions: List[str] = []
        
        # UI components (will be created in build())
        self.search_field: Optional[ft.TextField] = None
        self.suggestions_dropdown: Optional[ft.Container] = None
        self.results_container: Optional[ft.Container] = None
        
        logger.debug("Advanced Search Component initialized")
    
    def _on_search_change(self, e):
        """Handle search field changes with real-time suggestions"""
        query = e.control.value.strip()
        self.current_query = query
        
        if len(query) >= 2:
            # Get suggestions for autocomplete
            self.suggestions = self.search_engine.get_suggestions(query, max_suggestions=8)
            self.show_suggestions = len(self.suggestions) > 0
            
            # Perform search
            self.search_results = self.search_engine.search(query, max_results=15)
            
            # Update UI
            self._update_suggestions_dropdown()
            self._update_search_results()
            
            # Notify callback
            if self.on_search_callback:
                self.on_search_callback(query, self.search_results)
        else:
            # Clear suggestions and results
            self.suggestions = []
            self.search_results = []
            self.show_suggestions = False
            self._update_suggestions_dropdown()
            self._update_search_results()
            
            if self.on_search_callback:
                self.on_search_callback("", [])
    
    def _on_suggestion_click(self, suggestion: str):
        """Handle suggestion click"""
        self.search_field.value = suggestion
        self.current_query = suggestion
        self.show_suggestions = False
        
        # Perform search with selected suggestion
        self.search_results = self.search_engine.search(suggestion, max_results=15)
        self._update_suggestions_dropdown()
        self._update_search_results()
        
        if self.on_search_callback:
            self.on_search_callback(suggestion, self.search_results)
        
        # Update the search field
        self.search_field.update()
    
    def _on_clear_search(self, e):
        """Clear search and reset"""
        self.search_field.value = ""
        self.current_query = ""
        self.suggestions = []
        self.search_results = []
        self.show_suggestions = False
        
        self._update_suggestions_dropdown()
        self._update_search_results()
        
        if self.on_search_callback:
            self.on_search_callback("", [])
        
        self.search_field.update()
    
    def _update_suggestions_dropdown(self):
        """Update the suggestions dropdown"""
        if not self.suggestions_dropdown:
            return
        
        if self.show_suggestions and self.suggestions:
            suggestion_items = []
            
            for suggestion in self.suggestions:
                suggestion_items.append(
                    ft.Container(
                        content=ft.Row([
                            ft.Icon(ft.Icons.SEARCH, size=16, color=ft.Colors.GREY_500),
                            ft.Text(
                                suggestion,
                                size=12,
                                color=ft.Colors.GREY_700
                            )
                        ], spacing=8),
                        padding=ft.padding.symmetric(horizontal=12, vertical=8),
                        on_click=lambda e, s=suggestion: self._on_suggestion_click(s),
                        bgcolor=ft.Colors.TRANSPARENT,
                        border_radius=4,
                        ink=True
                    )
                )
            
            self.suggestions_dropdown.content = ft.Column(
                suggestion_items,
                spacing=0,
                tight=True
            )
            self.suggestions_dropdown.visible = True
        else:
            self.suggestions_dropdown.visible = False
        
        if hasattr(self.suggestions_dropdown, 'update'):
            self.suggestions_dropdown.update()
    
    def _update_search_results(self):
        """Update the search results display"""
        if not self.results_container:
            return
        
        if self.search_results:
            result_items = []
            
            # Group results by section
            sections_with_results = {}
            for result in self.search_results:
                if result.section_key not in sections_with_results:
                    sections_with_results[result.section_key] = []
                sections_with_results[result.section_key].append(result)
            
            for section_key, section_results in sections_with_results.items():
                # Section header
                section_title = section_results[0].section_title
                result_items.append(
                    ft.Container(
                        content=ft.Text(
                            f"📂 {section_title}",
                            size=14,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.BLUE_600
                        ),
                        padding=ft.padding.only(left=8, top=12, bottom=4)
                    )
                )
                
                # Results for this section
                for result in section_results:
                    self._create_result_item(result, result_items)
            
            # Show "no results" if empty
            if not result_items:
                result_items.append(
                    ft.Container(
                        content=ft.Column([
                            ft.Icon(ft.Icons.SEARCH_OFF, size=32, color=ft.Colors.GREY_400),
                            ft.Text(
                                f"Nessun risultato per '{self.current_query}'",
                                size=14,
                                color=ft.Colors.GREY_600,
                                text_align=ft.TextAlign.CENTER
                            ),
                            ft.Text(
                                "Prova con termini diversi o più generici",
                                size=12,
                                color=ft.Colors.GREY_500,
                                text_align=ft.TextAlign.CENTER
                            )
                        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=8),
                        padding=ft.padding.all(20)
                    )
                )
            
            self.results_container.content = ft.Column(
                result_items,
                spacing=0,
                scroll=ft.ScrollMode.AUTO
            )
            self.results_container.visible = True
        else:
            self.results_container.visible = False
        
        if hasattr(self.results_container, 'update'):
            self.results_container.update()
    
    def _create_result_item(self, result: SearchResult, result_items: List[ft.Control]):
        """Create a single result item"""
        
        def on_result_click(e):
            """Handle result click - navigate to the setting"""
            logger.info(f"Navigate to {result.section_key}.{result.setting_key or 'main'}")
            # Here you would implement navigation to the specific setting
        
        # Determine icon based on match type and content
        if result.setting_key:
            icon = ft.Icons.TUNE
            title = result.setting_title or result.setting_key
            subtitle = f"in {result.section_title}"
        else:
            icon = ft.Icons.FOLDER
            title = result.section_title
            subtitle = result.context
        
        # Match type indicator
        match_color = {
            "exact": ft.Colors.GREEN_600,
            "partial": ft.Colors.BLUE_600,
            "description": ft.Colors.ORANGE_600,
            "general": ft.Colors.GREY_600
        }.get(result.match_type, ft.Colors.GREY_600)
        
        # Relevance indicator (stars based on score)
        stars = "⭐" * min(3, max(1, int(result.relevance_score * 3)))
        
        result_item = ft.Container(
            content=ft.Row([
                ft.Icon(icon, size=18, color=match_color),
                ft.Column([
                    ft.Text(
                        title,
                        size=13,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.GREY_800
                    ),
                    ft.Text(
                        subtitle,
                        size=11,
                        color=ft.Colors.GREY_600
                    )
                ], spacing=2, expand=True),
                ft.Text(
                    stars,
                    size=10,
                    tooltip=f"Rilevanza: {result.relevance_score:.1f}"
                )
            ], spacing=12, alignment=ft.MainAxisAlignment.START),
            padding=ft.padding.symmetric(horizontal=12, vertical=8),
            border_radius=6,
            on_click=on_result_click,
            ink=True,
            bgcolor=ft.Colors.TRANSPARENT
        )
        
        result_items.append(result_item)
    
    def _create_popular_searches(self) -> ft.Container:
        """Create popular searches section"""
        popular_searches = self.search_engine.get_popular_searches()
        
        chips = []
        for search_term in popular_searches[:6]:  # Show top 6
            chip = ft.Container(
                content=ft.Text(
                    search_term,
                    size=11,
                    color=ft.Colors.BLUE_700
                ),
                padding=ft.padding.symmetric(horizontal=8, vertical=4),
                bgcolor=ft.Colors.BLUE_50,
                border_radius=12,
                border=ft.border.all(1, ft.Colors.BLUE_200),
                on_click=lambda e, term=search_term: self._on_suggestion_click(term),
                ink=True
            )
            chips.append(chip)
        
        return ft.Container(
            content=ft.Column([
                ft.Text(
                    "🔥 Ricerche Popolari",
                    size=12,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.GREY_700
                ),
                ft.Row(
                    chips,
                    spacing=8,
                    wrap=True
                )
            ], spacing=8),
            padding=ft.padding.all(12),
            bgcolor=ft.Colors.GREY_50,
            border_radius=8,
            visible=not self.current_query  # Only show when not searching
        )
    
    def build(self) -> ft.Container:
        """Build the advanced search component"""
        
        # Search input field
        self.search_field = ft.TextField(
            hint_text="🔍 Cerca impostazioni, sezioni, configurazioni...",
            width=400,
            on_change=self._on_search_change,
            suffix=ft.IconButton(
                icon=ft.Icons.CLEAR,
                on_click=self._on_clear_search,
                tooltip="Cancella ricerca",
                visible=bool(self.current_query)
            ),
            border_radius=ft.border_radius.all(25),
            content_padding=ft.padding.symmetric(horizontal=16, vertical=12)
        )
        
        # Suggestions dropdown
        self.suggestions_dropdown = ft.Container(
            content=ft.Column([], spacing=0, tight=True),
            bgcolor=ft.Colors.WHITE,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.GREY_300),
            padding=ft.padding.symmetric(vertical=4),
            visible=False,
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=8,
                color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
                offset=ft.Offset(0, 2)
            )
        )
        
        # Search results container
        self.results_container = ft.Container(
            content=ft.Column([], spacing=0),
            bgcolor=ft.Colors.WHITE,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.GREY_300),
            max_height=300,
            visible=False,
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=8,
                color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
                offset=ft.Offset(0, 2)
            )
        )
        
        # Popular searches
        popular_searches = self._create_popular_searches()
        
        # Main search container
        return ft.Container(
            content=ft.Column([
                # Search field
                self.search_field,
                
                # Suggestions dropdown (positioned below search field)
                self.suggestions_dropdown,
                
                # Results or popular searches
                ft.Container(
                    content=ft.Column([
                        self.results_container,
                        popular_searches
                    ], spacing=8),
                    margin=ft.margin.only(top=8)
                )
            ], spacing=0, tight=True),
            width=450
        )
    
    def clear_search(self):
        """Programmatically clear the search"""
        if self.search_field:
            self._on_clear_search(None)
    
    def set_query(self, query: str):
        """Programmatically set search query"""
        if self.search_field:
            self.search_field.value = query
            self.current_query = query
            self._on_search_change(type('Event', (), {'control': type('Control', (), {'value': query})})())
    
    def get_current_results(self) -> List[SearchResult]:
        """Get current search results"""
        return self.search_results.copy()
    
    def get_sections_with_matches(self) -> List[str]:
        """Get list of section keys that have search matches"""
        return list(set(result.section_key for result in self.search_results)) 