# -*- coding: utf-8 -*-
"""
Vista dettagli cliente per Agevolami PM
"""

import flet as ft
from datetime import datetime, date
from typing import Optional, List, Dict, Any
from uuid import UUID

from core.models import Client, Project, Deadline, ProjectStatus, DeadlineStatus
from core.utils import get_logger

logger = get_logger(__name__)

class ClientDetailView:
    """Vista per visualizzare i dettagli di un cliente"""
    
    def __init__(self, app, client_id: UUID):
        self.app = app
        self.page = app.page
        self.client_id = client_id
        self.client: Optional[Client] = None
        self.projects: List[Project] = []
        self.deadlines: List[Deadline] = []
        
        # UI Components
        self.content = ft.Column()
        self.header_section = ft.Container()
        self.company_details_section = ft.Container()
        self.stats_section = ft.Container()
        self.projects_section = ft.Container()
        self.deadlines_section = ft.Container()
        
    def build(self) -> ft.Control:
        """Costruisce la vista dettagli cliente"""
        try:
            self._load_data()
            self._build_header()
            self._build_company_details()
            self._build_stats()
            self._build_projects_section()
            self._build_deadlines_section()
            
            self.content = ft.Column(
                controls=[
                    self.header_section,
                    ft.Divider(height=20),
                    self.company_details_section,
                    ft.Divider(height=20),
                    self.stats_section,
                    ft.Divider(height=20),
                    self.projects_section,
                    ft.Divider(height=20),
                    self.deadlines_section
                ],
                spacing=0,
                scroll=ft.ScrollMode.AUTO
            )
            
            return ft.Container(
                content=self.content,
                padding=20,
                expand=True
            )
            
        except Exception as e:
            logger.error(f"Errore costruzione vista cliente: {e}")
            return ft.Container(
                content=ft.Text(f"Errore caricamento dettagli cliente: {e}", color=ft.Colors.RED),
                padding=20
            )
    
    def _load_data(self):
        """Carica i dati del cliente"""
        try:
            self.client = self.app.db.get_client(self.client_id)
            if not self.client:
                raise Exception("Cliente non trovato")
                
            self.projects = self.app.db.get_projects_by_client(self.client_id)
            self.deadlines = self.app.db.get_deadlines_by_client(self.client_id)
            
            logger.info(f"Caricati dati per cliente: {self.client.name}")
            
        except Exception as e:
            logger.error(f"Errore caricamento dati cliente: {e}")
            raise
    
    def _build_header(self):
        """Costruisce la sezione header con info cliente"""
        if not self.client:
            return
            
        # Avatar/Icon
        avatar = ft.CircleAvatar(
            content=ft.Text(
                self.client.name[0].upper() if self.client.name else "C",
                size=24,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.WHITE
            ),
            bgcolor=ft.Colors.BLUE,
            radius=30
        )
        
        # Info principale
        client_info = ft.Column(
            controls=[
                ft.Text(
                    self.client.name,
                    size=28,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.BLUE_900
                ),
                ft.Text(
                    self.client.business_name or "Azienda non specificata",
                    size=18,
                    color=ft.Colors.GREY_700
                ),
                ft.Row(
                    controls=[
                        ft.Icon(ft.Icons.EMAIL, size=16, color=ft.Colors.GREY_600),
                        ft.Text(self.client.email or "Email non disponibile", size=14)
                    ],
                    spacing=5
                ),
                ft.Row(
                    controls=[
                        ft.Icon(ft.Icons.PHONE, size=16, color=ft.Colors.GREY_600),
                        ft.Text(self.client.phone or "Telefono non disponibile", size=14)
                    ],
                    spacing=5
                ) if self.client.phone else ft.Container()
            ],
            spacing=5
        )
        
        # Pulsanti azione
        action_buttons = ft.Row(
            controls=[
                ft.ElevatedButton(
                    "Modifica Cliente",
                    icon=ft.Icons.EDIT,
                    on_click=lambda _: self._edit_client()
                ),
                ft.OutlinedButton(
                    "Nuovo Progetto",
                    icon=ft.Icons.ADD,
                    on_click=lambda _: self._create_project()
                )
            ],
            spacing=10
        )
        
        self.header_section = ft.Container(
            content=ft.Row(
                controls=[
                    avatar,
                    ft.Container(client_info, expand=True),
                    action_buttons
                ],
                alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                vertical_alignment=ft.CrossAxisAlignment.START
            ),
            bgcolor=ft.Colors.BLUE_50,
            padding=20,
            border_radius=10
        )
    
    def _build_company_details(self):
        """Costruisce la sezione dettagli azienda"""
        def copy_to_clipboard(text: str):
            """Copia il testo negli appunti"""
            if text:
                self.page.set_clipboard(text)
                snack_bar = ft.SnackBar(
                    content=ft.Text("Copiato negli appunti!"),
                    duration=2000
                )
                self.page.overlay.append(snack_bar)
                snack_bar.open = True
                self.page.update()
        
        def open_email_client(email: str):
            """Apre il client email predefinito"""
            if email:
                import webbrowser
                webbrowser.open(f"mailto:{email}")
        
        def create_detail_row(label: str, value: str, icon=None, is_email=False) -> ft.Container:
            """Crea una riga di dettaglio con pulsante copia e opzionalmente email"""
            if not value:
                return ft.Container()
            
            controls = [
                ft.Icon(icon, size=16, color=ft.Colors.GREY_600) if icon else ft.Container(width=16),
                ft.Text(label, size=12, color=ft.Colors.GREY_600, weight=ft.FontWeight.W_500),
                ft.Text(value, size=14, expand=True),
                ft.IconButton(
                    icon=ft.Icons.COPY,
                    icon_size=16,
                    tooltip="Copia",
                    on_click=lambda _: copy_to_clipboard(value)
                )
            ]
            
            if is_email:
                controls.append(
                    ft.IconButton(
                        icon=ft.Icons.EMAIL,
                        icon_size=16,
                        tooltip="Invia Email",
                        icon_color=ft.Colors.BLUE,
                        on_click=lambda _: open_email_client(value)
                    )
                )
            
            return ft.Container(
                content=ft.Row(
                    controls=controls,
                    spacing=10,
                    alignment=ft.CrossAxisAlignment.CENTER
                ),
                padding=ft.padding.symmetric(vertical=5)
            )
        
        def create_expandable_section(title: str, content: ft.Column, icon: ft.Icons, is_expanded: bool = False) -> ft.Container:
            """Crea una sezione espandibile"""
            expanded_ref = ft.Ref[bool]()
            expanded_ref.current = is_expanded
            
            def toggle_expansion(_):
                expanded_ref.current = not expanded_ref.current
                expansion_tile.controls[1].visible = expanded_ref.current
                # Access the IconButton through Container's content -> Row -> controls[0]
                expansion_tile.controls[0].content.controls[0].icon = ft.Icons.EXPAND_LESS if expanded_ref.current else ft.Icons.EXPAND_MORE
                self.page.update()
            
            expansion_tile = ft.Column(
                controls=[
                    ft.Container(
                        content=ft.Row(
                            controls=[
                                ft.IconButton(
                                    icon=ft.Icons.EXPAND_LESS if is_expanded else ft.Icons.EXPAND_MORE,
                                    icon_size=20,
                                    on_click=toggle_expansion
                                ),
                                ft.Icon(icon, size=20, color=ft.Colors.BLUE_700),
                                ft.Text(title, size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_900, expand=True),
                            ],
                            alignment=ft.MainAxisAlignment.START,
                            vertical_alignment=ft.CrossAxisAlignment.CENTER
                        ),
                        bgcolor=ft.Colors.BLUE_50,
                        padding=ft.padding.symmetric(horizontal=15, vertical=10),
                        border_radius=ft.border_radius.only(top_left=8, top_right=8),
                        on_click=toggle_expansion
                    ),
                    ft.Container(
                        content=content,
                        padding=20,
                        visible=is_expanded
                    )
                ],
                spacing=0
            )
            
            return ft.Card(
                content=expansion_tile,
                elevation=3,
                margin=ft.margin.symmetric(vertical=5)
            )
        
        # Sezione informazioni aziendali
        company_info = ft.Column(
            controls=[
                create_detail_row("Ragione Sociale:", self.client.business_name, ft.Icons.BUSINESS),
                create_detail_row("Partita IVA:", self.client.vat_number, ft.Icons.NUMBERS),
                create_detail_row("Codice Fiscale:", self.client.tax_code, ft.Icons.BADGE),
                create_detail_row("Email:", self.client.email, ft.Icons.EMAIL, is_email=True),
                create_detail_row("Telefono:", self.client.phone, ft.Icons.PHONE),
                create_detail_row("Codice Attività:", self.client.activity_code, ft.Icons.WORK),
                create_detail_row("Descrizione Attività:", self.client.activity_description, ft.Icons.DESCRIPTION),
            ],
            spacing=5
        )
        
        # Sezione indirizzo
        address_parts = []
        if self.client.street:
            street_full = f"{self.client.street} {self.client.civic_number or ''}" if self.client.civic_number else self.client.street
            address_parts.append(street_full)
        if self.client.city:
            address_parts.append(self.client.city)
        if self.client.commune and self.client.commune != self.client.city:
            address_parts.append(f"Comune: {self.client.commune}")
        if self.client.province:
            province_full = f"{self.client.province} ({self.client.province_code})" if self.client.province_code else self.client.province
            address_parts.append(province_full)
        if self.client.postal_code:
            address_parts.append(self.client.postal_code)
        
        full_address = ", ".join(address_parts) if address_parts else self.client.address
        
        address_info = ft.Column(
            controls=[
                create_detail_row("Via/Strada:", self.client.street, ft.Icons.LOCATION_ON),
                create_detail_row("Numero Civico:", self.client.civic_number, ft.Icons.HOME),
                create_detail_row("Città:", self.client.city, ft.Icons.LOCATION_CITY),
                create_detail_row("Comune:", self.client.commune, ft.Icons.LOCATION_CITY),
                create_detail_row("Provincia:", self.client.province, ft.Icons.MAP),
                create_detail_row("Codice Provincia:", self.client.province_code, ft.Icons.CODE),
                create_detail_row("CAP:", self.client.postal_code, ft.Icons.MARKUNREAD_MAILBOX),
                create_detail_row("Indirizzo Completo:", full_address, ft.Icons.PLACE) if full_address else ft.Container(),
            ],
            spacing=5
        )
        
        # Sezione rappresentante legale
        legal_rep_name = f"{self.client.legal_rep_name or ''} {self.client.legal_rep_surname or ''}".strip()
        legal_rep_residence = []
        if self.client.legal_rep_residence_street:
            street_full = f"{self.client.legal_rep_residence_street} {self.client.legal_rep_residence_civic or ''}" if self.client.legal_rep_residence_civic else self.client.legal_rep_residence_street
            legal_rep_residence.append(street_full)
        if self.client.legal_rep_residence_city:
            legal_rep_residence.append(self.client.legal_rep_residence_city)
        if self.client.legal_rep_residence_province:
            legal_rep_residence.append(self.client.legal_rep_residence_province)
        if self.client.legal_rep_residence_postal_code:
                legal_rep_residence.append(self.client.legal_rep_residence_postal_code)
        
        legal_rep_full_address = ", ".join(legal_rep_residence) if legal_rep_residence else None
        
        legal_rep_info = ft.Column(
            controls=[
                create_detail_row("Nome Completo:", legal_rep_name, ft.Icons.PERSON),
                create_detail_row("Codice Fiscale:", self.client.legal_rep_tax_code, ft.Icons.BADGE),
                create_detail_row("Data di Nascita:", self.client.legal_rep_birth_date, ft.Icons.CAKE),
                create_detail_row("Luogo di Nascita:", self.client.legal_rep_birth_place, ft.Icons.PLACE),
                create_detail_row("Qualità/Ruolo:", self.client.legal_rep_role, ft.Icons.WORK_OUTLINE),
                create_detail_row("Email:", self.client.legal_rep_email, ft.Icons.EMAIL, is_email=True),
                create_detail_row("Telefono:", self.client.legal_rep_phone, ft.Icons.PHONE),
                create_detail_row("Indirizzo Residenza:", legal_rep_full_address, ft.Icons.HOME),
            ],
            spacing=5
        )
        
        # Layout con sezioni espandibili
        self.company_details_section = ft.Container(
            content=ft.Column(
                controls=[
                    ft.Text("Dettagli Azienda", size=20, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_900),
                    ft.Divider(height=10),
                    create_expandable_section(
                        "Informazioni Aziendali", 
                        company_info, 
                        ft.Icons.BUSINESS,
                        is_expanded=True
                    ),
                    create_expandable_section(
                        "Indirizzo", 
                        address_info, 
                        ft.Icons.LOCATION_ON,
                        is_expanded=False
                    ),
                    create_expandable_section(
                        "Rappresentante Legale", 
                        legal_rep_info, 
                        ft.Icons.PERSON,
                        is_expanded=False
                    )
                ],
                spacing=10
            ),
            padding=ft.padding.all(10)
        )
    
    def _build_stats(self):
        """Costruisce la sezione statistiche"""
        active_projects = len([p for p in self.projects if p.status == ProjectStatus.IN_PROGRESS])
        completed_projects = len([p for p in self.projects if p.status == ProjectStatus.COMPLETED])
        pending_deadlines = len([d for d in self.deadlines if d.status == DeadlineStatus.PENDING])
        overdue_deadlines = len([d for d in self.deadlines if d.status == DeadlineStatus.OVERDUE])
        
        total_budget = sum(p.budget for p in self.projects if p.budget)
        
        stats_cards = [
            self._create_stat_card("Progetti Totali", str(len(self.projects)), ft.Icons.FOLDER, ft.Colors.BLUE),
            self._create_stat_card("Progetti Attivi", str(active_projects), ft.Icons.PLAY_CIRCLE, ft.Colors.GREEN),
            self._create_stat_card("Progetti Completati", str(completed_projects), ft.Icons.CHECK_CIRCLE, ft.Colors.ORANGE),
            self._create_stat_card("Scadenze Pendenti", str(pending_deadlines), ft.Icons.SCHEDULE, ft.Colors.AMBER),
            self._create_stat_card("Scadenze Scadute", str(overdue_deadlines), ft.Icons.WARNING, ft.Colors.RED),
            self._create_stat_card("Budget Totale", f"€{total_budget:,.0f}" if total_budget else "N/A", ft.Icons.EURO, ft.Colors.PURPLE)
        ]
        
        self.stats_section = ft.Container(
            content=ft.Column(
                controls=[
                    ft.Text("Statistiche Cliente", size=20, weight=ft.FontWeight.BOLD),
                    ft.ResponsiveRow(
                        controls=[
                            ft.Container(
                                card,
                                col={"sm": 6, "md": 4, "lg": 2}
                            ) for card in stats_cards
                        ]
                    )
                ],
                spacing=15
            )
        )
    
    def _create_stat_card(self, title: str, value: str, icon: str, color: str) -> ft.Card:
        """Crea una card statistica"""
        return ft.Card(
            content=ft.Container(
                content=ft.Column(
                    controls=[
                        ft.Row(
                            controls=[
                                ft.Icon(icon, color=color, size=24),
                                ft.Text(value, size=24, weight=ft.FontWeight.BOLD, color=color)
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_BETWEEN
                        ),
                        ft.Text(title, size=12, color=ft.Colors.GREY_600)
                    ],
                    spacing=5
                ),
                padding=15
            ),
            elevation=2
        )
    
    def _build_projects_section(self):
        """Costruisce la sezione progetti"""
        if not self.projects:
            projects_content = ft.Container(
                content=ft.Text("Nessun progetto trovato per questo cliente", color=ft.Colors.GREY_600),
                padding=20,
                alignment=ft.alignment.center
            )
        else:
            project_cards = []
            for project in self.projects[:10]:  # Mostra solo i primi 10
                project_cards.append(self._create_project_card(project))
            
            projects_content = ft.Column(
                controls=project_cards,
                spacing=10
            )
        
        self.projects_section = ft.Container(
            content=ft.Column(
                controls=[
                    ft.Row(
                        controls=[
                            ft.Text("Progetti", size=20, weight=ft.FontWeight.BOLD),
                            ft.TextButton(
                                f"Vedi tutti ({len(self.projects)})",
                                on_click=lambda _: self._view_all_projects()
                            ) if len(self.projects) > 10 else ft.Container()
                        ],
                        alignment=ft.MainAxisAlignment.SPACE_BETWEEN
                    ),
                    projects_content
                ],
                spacing=15
            )
        )
    
    def _create_project_card(self, project: Project) -> ft.Card:
        """Crea una card progetto"""
        status_color = {
            ProjectStatus.IN_PROGRESS: ft.Colors.GREEN,
            ProjectStatus.COMPLETED: ft.Colors.BLUE,
            ProjectStatus.SUSPENDED: ft.Colors.ORANGE,
            ProjectStatus.CANCELLED: ft.Colors.RED
        }.get(project.status, ft.Colors.GREY)
        
        return ft.Card(
            content=ft.Container(
                content=ft.Row(
                    controls=[
                        ft.Column(
                            controls=[
                                ft.Text(project.name, size=16, weight=ft.FontWeight.BOLD),
                                ft.Text(project.reference_code or "Codice non disponibile", size=12, color=ft.Colors.GREY_600),
                                ft.Row(
                                    controls=[
                                        ft.Container(
                                            content=ft.Text(
                                                project.status,
                                                size=10,
                                                color=ft.Colors.WHITE,
                                                weight=ft.FontWeight.BOLD
                                            ),
                                            bgcolor=status_color,
                                            padding=ft.padding.symmetric(horizontal=8, vertical=4),
                                            border_radius=12
                                        ),
                                        ft.Text(
                                            f"€{project.budget:,.0f}" if project.budget else "Budget N/A",
                                            size=12,
                                            color=ft.Colors.GREY_700
                                        )
                                    ],
                                    spacing=10
                                )
                            ],
                            spacing=5,
                            expand=True
                        ),
                        ft.IconButton(
                            icon=ft.Icons.ARROW_FORWARD,
                            on_click=lambda _, p=project: self._view_project_detail(p)
                        )
                    ],
                    alignment=ft.MainAxisAlignment.SPACE_BETWEEN
                ),
                padding=15
            ),
            elevation=1
        )
    
    def _build_deadlines_section(self):
        """Costruisce la sezione scadenze"""
        if not self.deadlines:
            deadlines_content = ft.Container(
                content=ft.Text("Nessuna scadenza trovata per questo cliente", color=ft.Colors.GREY_600),
                padding=20,
                alignment=ft.alignment.center
            )
        else:
            # Ordina per data di scadenza
            sorted_deadlines = sorted(self.deadlines, key=lambda d: d.due_date)
            deadline_cards = []
            
            for deadline in sorted_deadlines[:5]:  # Mostra solo le prime 5
                deadline_cards.append(self._create_deadline_card(deadline))
            
            deadlines_content = ft.Column(
                controls=deadline_cards,
                spacing=10
            )
        
        self.deadlines_section = ft.Container(
            content=ft.Column(
                controls=[
                    ft.Row(
                        controls=[
                            ft.Text("Prossime Scadenze", size=20, weight=ft.FontWeight.BOLD),
                            ft.TextButton(
                                f"Vedi tutte ({len(self.deadlines)})",
                                on_click=lambda _: self._view_all_deadlines()
                            ) if len(self.deadlines) > 5 else ft.Container()
                        ],
                        alignment=ft.MainAxisAlignment.SPACE_BETWEEN
                    ),
                    deadlines_content
                ],
                spacing=15
            )
        )
    
    def _create_deadline_card(self, deadline: Deadline) -> ft.Card:
        """Crea una card scadenza"""
        status_color = {
            DeadlineStatus.PENDING: ft.Colors.ORANGE,
            DeadlineStatus.COMPLETED: ft.Colors.GREEN,
            DeadlineStatus.OVERDUE: ft.Colors.RED
        }.get(deadline.status, ft.Colors.GREY)
        
        days_until = (deadline.due_date - date.today()).days
        urgency_text = ""
        if days_until < 0:
            urgency_text = f"Scaduta da {abs(days_until)} giorni"
        elif days_until == 0:
            urgency_text = "Scade oggi"
        elif days_until <= 7:
            urgency_text = f"Scade tra {days_until} giorni"
        else:
            urgency_text = f"Scade il {deadline.due_date.strftime('%d/%m/%Y')}"
        
        return ft.Card(
            content=ft.Container(
                content=ft.Row(
                    controls=[
                        ft.Column(
                            controls=[
                                ft.Text(deadline.title, size=14, weight=ft.FontWeight.BOLD),
                                ft.Text(deadline.description or "Nessuna descrizione", size=12, color=ft.Colors.GREY_600),
                                ft.Row(
                                    controls=[
                                        ft.Container(
                                            content=ft.Text(
                                                deadline.status.value,
                                                size=10,
                                                color=ft.Colors.WHITE,
                                                weight=ft.FontWeight.BOLD
                                            ),
                                            bgcolor=status_color,
                                            padding=ft.padding.symmetric(horizontal=8, vertical=4),
                                            border_radius=12
                                        ),
                                        ft.Text(urgency_text, size=12, color=ft.Colors.GREY_700)
                                    ],
                                    spacing=10
                                )
                            ],
                            spacing=5,
                            expand=True
                        )
                    ]
                ),
                padding=15
            ),
            elevation=1
        )
    
    def _edit_client(self):
        """Apre il form di modifica cliente"""
        # Naviga alla vista clienti con il form di modifica aperto
        self.app.main_layout._navigate_to("clients")
    
    def _create_project(self):
        """Apre il form di creazione progetto per questo cliente"""
        # Naviga alla vista progetti con il form di creazione aperto
        self.app.main_layout._navigate_to("projects")
    
    def _view_project_detail(self, project: Project):
        """Visualizza i dettagli di un progetto"""
        try:
            logger.info(f"Visualizza progetto: {project.name}")
            
            # Use the new navigation system for detail views
            self.app.main_layout.navigate_to_detail("project_detail", str(project.id))
            
            # Crea la vista dettagli progetto
            from ui.views.project_detail import ProjectDetailView
            detail_view = ProjectDetailView(self.app, project.id)
            
            # Aggiorna il content area del layout principale
            self.app.main_layout.content_area.content = detail_view.build()
            self.app.main_layout.page.update()
            
        except Exception as e:
            logger.error(f"Errore visualizzazione progetto: {e}")
    
    def _view_all_projects(self):
        """Visualizza tutti i progetti del cliente"""
        self.app.main_layout._navigate_to("projects")
    
    def _view_all_deadlines(self):
        """Visualizza tutte le scadenze del cliente"""
        self.app.main_layout._navigate_to("deadlines")