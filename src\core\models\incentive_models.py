#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Data models for Italian Financial Incentives Monitoring
"""

from dataclasses import dataclass, field
from datetime import datetime, date
from typing import Optional, List, Dict, Any
from uuid import UUID, uuid4
from enum import Enum

class IncentiveStatus(Enum):
    """Status of an incentive"""
    NEW = "new"
    REVIEWED = "reviewed"
    RELEVANT = "relevant"
    NOT_RELEVANT = "not_relevant"
    ARCHIVED = "archived"

class IncentiveSource(Enum):
    """Source website for incentives"""
    MIMIT = "mimit.gov.it"
    INVITALIA = "invitalia.it"
    SIMEST = "simest.it"
    CAMPANIA = "regione.campania.it"
    OTHER = "other"

class IncentivePriority(Enum):
    """Priority level of an incentive"""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

@dataclass
class IncentiveItem:
    """Individual incentive item found during monitoring"""
    id: UUID = field(default_factory=uuid4)
    title: str = ""
    description: str = ""
    source: IncentiveSource = IncentiveSource.OTHER
    source_url: str = ""
    content_hash: str = ""  # For duplicate detection
    
    # Dates
    found_date: datetime = field(default_factory=datetime.now)
    publication_date: Optional[datetime] = None
    deadline_date: Optional[date] = None
    
    # Classification
    status: IncentiveStatus = IncentiveStatus.NEW
    priority: IncentivePriority = IncentivePriority.MEDIUM
    tags: List[str] = field(default_factory=list)
    keywords_matched: List[str] = field(default_factory=list)
    
    # LLM Analysis
    llm_summary: str = ""
    llm_relevance_score: float = 0.0
    llm_analysis_date: Optional[datetime] = None
    
    # Additional data
    raw_content: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # User interaction
    user_notes: str = ""
    is_favorite: bool = False
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: Optional[datetime] = None

@dataclass
class MonitoringSession:
    """A monitoring session that checks websites for new incentives"""
    id: UUID = field(default_factory=uuid4)
    start_time: datetime = field(default_factory=datetime.now)
    end_time: Optional[datetime] = None
    
    # Configuration
    sources_checked: List[IncentiveSource] = field(default_factory=list)
    keywords_used: List[str] = field(default_factory=list)
    
    # Results
    items_found: int = 0
    new_items: int = 0
    errors_encountered: List[str] = field(default_factory=list)
    
    # Status
    status: str = "running"  # running, completed, failed
    success: bool = False

@dataclass
class IncentiveReport:
    """Report containing incentives for a specific period"""
    id: UUID = field(default_factory=uuid4)
    title: str = ""
    description: str = ""
    
    # Period
    start_date: date = field(default_factory=date.today)
    end_date: date = field(default_factory=date.today)
    
    # Content
    incentives: List[IncentiveItem] = field(default_factory=list)
    summary: str = ""
    
    # Generation info
    generated_at: datetime = field(default_factory=datetime.now)
    generated_by: str = "system"
    
    # Export info
    pdf_path: Optional[str] = None
    email_sent: bool = False
    email_sent_at: Optional[datetime] = None

@dataclass
class MonitoringConfig:
    """Configuration for incentives monitoring"""
    # Monitoring settings
    enabled: bool = False
    frequency: str = "weekly"  # daily, weekly, monthly
    keywords: List[str] = field(default_factory=list)
    sources: List[IncentiveSource] = field(default_factory=lambda: [
        IncentiveSource.MIMIT,
        IncentiveSource.INVITALIA,
        IncentiveSource.SIMEST
    ])

    # LLM settings
    openrouter_api_key: str = ""
    llm_model: str = "gpt-3.5-turbo"
    llm_api_url: str = "https://openrouter.ai/api/v1"
    llm_enabled: bool = True

    # Notification settings
    email_notifications: bool = True
    desktop_notifications: bool = False
    notification_email: str = ""

    # Filtering settings
    only_new_content: bool = True
    min_relevance_score: float = 0.5
    auto_archive_old: bool = True
    archive_after_days: int = 90

    # Advanced settings
    max_items_per_session: int = 50
    request_delay_seconds: float = 2.0
    user_agent: str = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"

    # Website configuration
    websites: List[Dict[str, Any]] = field(default_factory=list)

    # Custom models configuration
    custom_models: Dict[str, Any] = field(default_factory=dict)

    # Enhanced web scraping settings
    scraping_method: str = "requests"  # requests, selenium, hybrid
    timeout_seconds: int = 30

    # Content validation settings
    validation: Dict[str, Any] = field(default_factory=lambda: {
        'min_title_length': 10,
        'max_title_length': 200,
        'min_description_length': 20,
        'max_description_length': 1000,
        'required_keywords': [],
        'forbidden_keywords': ['cookie', 'privacy', 'login'],
        'min_relevance_score': 0.3,
        'max_duplicate_similarity': 0.85
    })

    # Retry configuration
    retry: Dict[str, Any] = field(default_factory=lambda: {
        'max_retries': 3,
        'base_delay': 1.0,
        'max_delay': 60.0,
        'exponential_base': 2.0,
        'jitter': True
    })

    # Monitoring configuration
    monitoring: Dict[str, Any] = field(default_factory=lambda: {
        'health_check_interval_hours': 6,
        'selector_validation_interval_hours': 24,
        'max_consecutive_failures': 5,
        'alert_on_health_issues': True
    })

# Helper functions for model conversion
def incentive_item_to_dict(item: IncentiveItem) -> Dict[str, Any]:
    """Convert IncentiveItem to dictionary for database storage"""
    return {
        'id': str(item.id),
        'title': item.title,
        'description': item.description,
        'source': item.source.value,
        'source_url': item.source_url,
        'content_hash': item.content_hash,
        'found_date': item.found_date.isoformat(),
        'publication_date': item.publication_date.isoformat() if item.publication_date else None,
        'deadline_date': item.deadline_date.isoformat() if item.deadline_date else None,
        'status': item.status.value,
        'priority': item.priority.value,
        'tags': ','.join(item.tags),
        'keywords_matched': ','.join(item.keywords_matched),
        'llm_summary': item.llm_summary,
        'llm_relevance_score': item.llm_relevance_score,
        'llm_analysis_date': item.llm_analysis_date.isoformat() if item.llm_analysis_date else None,
        'raw_content': item.raw_content,
        'metadata': str(item.metadata),
        'user_notes': item.user_notes,
        'is_favorite': item.is_favorite,
        'created_at': item.created_at.isoformat(),
        'updated_at': item.updated_at.isoformat() if item.updated_at else None
    }

def dict_to_incentive_item(data: Dict[str, Any]) -> IncentiveItem:
    """Convert dictionary from database to IncentiveItem"""
    return IncentiveItem(
        id=UUID(data['id']),
        title=data.get('title', ''),
        description=data.get('description', ''),
        source=IncentiveSource(data.get('source', 'other')),
        source_url=data.get('source_url', ''),
        content_hash=data.get('content_hash', ''),
        found_date=datetime.fromisoformat(data['found_date']) if data.get('found_date') else datetime.now(),
        publication_date=datetime.fromisoformat(data['publication_date']) if data.get('publication_date') else None,
        deadline_date=date.fromisoformat(data['deadline_date']) if data.get('deadline_date') else None,
        status=IncentiveStatus(data.get('status', 'new')),
        priority=IncentivePriority(data.get('priority', 'medium')),
        tags=data.get('tags', '').split(',') if data.get('tags') else [],
        keywords_matched=data.get('keywords_matched', '').split(',') if data.get('keywords_matched') else [],
        llm_summary=data.get('llm_summary', ''),
        llm_relevance_score=float(data.get('llm_relevance_score', 0.0)),
        llm_analysis_date=datetime.fromisoformat(data['llm_analysis_date']) if data.get('llm_analysis_date') else None,
        raw_content=data.get('raw_content', ''),
        metadata=eval(data.get('metadata', '{}')) if data.get('metadata') else {},
        user_notes=data.get('user_notes', ''),
        is_favorite=bool(data.get('is_favorite', False)),
        created_at=datetime.fromisoformat(data['created_at']) if data.get('created_at') else datetime.now(),
        updated_at=datetime.fromisoformat(data['updated_at']) if data.get('updated_at') else None
    )
