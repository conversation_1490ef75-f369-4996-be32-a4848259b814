#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Modelli dati base per Agevolami PM
"""

from datetime import datetime, date
from typing import Optional, List, Dict, Any
from enum import Enum
from pydantic import BaseModel, Field, validator
from uuid import uuid4, UUID

class ProjectStatus(str, Enum):
    """Stati del progetto"""
    DRAFT = "bozza"
    SUBMITTED = "presentato"
    APPROVED = "approvato"
    IN_PROGRESS = "in_corso"
    COMPLETED = "completato"
    SUSPENDED = "sospeso"
    CANCELLED = "annullato"

class DeadlineStatus(str, Enum):
    """Stati delle scadenze"""
    PENDING = "in_attesa"
    COMPLETED = "completato"
    OVERDUE = "scaduto"
    CANCELLED = "annullato"

class TaskStatus(str, Enum):
    """Stati delle task"""
    PENDING = "in_attesa"
    IN_PROGRESS = "in_corso"
    WAITING = "in_pausa"
    COMPLETED = "completato"
    CANCELLED = "annullato"

class AlertStatus(str, Enum):
    """Stati degli alert"""
    ACTIVE = "attivo"
    DISMISSED = "ignorato"
    COMPLETED = "completato"

class ProjectCategory(str, Enum):
    """Categorie principali di progetto"""
    INCENTIVI_PUBBLICI = "incentivi_pubblici"
    FISCALE_CONTABILE = "fiscale_contabile"
    CREDITI_FINANZA = "crediti_finanza"

class ProjectSubcategory(str, Enum):
    """Sottocategorie di progetto"""
    # Incentivi Pubblici
    RICERCA_INNOVAZIONE = "ricerca_innovazione"
    INVESTIMENTI_PRODUTTIVI = "investimenti_produttivi"
    # Fiscale & Contabile
    ADEMPIMENTI_FISCALI = "adempimenti_fiscali"
    CONTABILITA = "contabilita"
    # Crediti & Finanza
    CREDITI_IMPOSTA = "crediti_imposta"
    FINANZIAMENTI = "finanziamenti"

class ProjectType(str, Enum):
    """Tipi di progetto"""
    # Ricerca & Innovazione
    ACCORDI_INNOVAZIONE = "accordi_innovazione"
    TRANSIZIONE_50 = "transizione_50"
    CREDITI_RS = "crediti_rs"
    # Investimenti Produttivi
    INVESTIMENTI_SOSTENIBILI_40 = "investimenti_sostenibili_40"
    CONTRATTI_SVILUPPO = "contratti_sviluppo"
    MINI_CONTRATTI_SVILUPPO = "mini_contratti_sviluppo"
    CREDITI_BENI_STRUMENTALI = "crediti_beni_strumentali"
    # Adempimenti Fiscali
    SCADENZE_FISCALI = "scadenze_fiscali"
    DICHIARAZIONI = "dichiarazioni"
    VERSAMENTI = "versamenti"
    # Contabilità
    BILANCI = "bilanci"
    REVISIONI = "revisioni"
    # Crediti d'Imposta
    CREDITO_TRANSIZIONE_40 = "credito_transizione_40"
    CREDITO_FORMAZIONE_40 = "credito_formazione_40"
    ALTRI_CREDITI = "altri_crediti"
    # Finanziamenti
    PRESTITI_AGEVOLATI = "prestiti_agevolati"
    GARANZIE = "garanzie"

class Priority(str, Enum):
    """Livelli di priorità"""
    LOW = "bassa"
    MEDIUM = "media"
    HIGH = "alta"
    CRITICAL = "critica"

class RecurrenceType(str, Enum):
    """Tipi di ricorrenza per le scadenze"""
    NONE = "nessuna"
    DAILY = "giornaliera"
    WEEKLY = "settimanale"
    MONTHLY = "mensile"
    QUARTERLY = "trimestrale"
    YEARLY = "annuale"
    CUSTOM = "personalizzata"

class BaseEntity(BaseModel):
    """Modello base per tutte le entità"""
    id: UUID = Field(default_factory=uuid4)
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: Optional[datetime] = None
    
    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            date: lambda v: v.isoformat(),
            UUID: lambda v: str(v)
        }

class Client(BaseEntity):
    """Modello per i clienti"""
    name: str = Field(..., min_length=1, max_length=200)
    business_name: Optional[str] = Field(None, max_length=200)
    vat_number: Optional[str] = Field(None, max_length=20)
    tax_code: Optional[str] = Field(None, max_length=20)
    email: Optional[str] = Field(None, max_length=100)
    phone: Optional[str] = Field(None, max_length=20)
    
    # Address fields (separated)
    street: Optional[str] = Field(None, max_length=200)  # Via/Strada
    civic_number: Optional[str] = Field(None, max_length=20)  # Numero civico
    city: Optional[str] = Field(None, max_length=100)  # Città/Comune
    commune: Optional[str] = Field(None, max_length=100)  # Comune (if different from city)
    province: Optional[str] = Field(None, max_length=10)  # Provincia
    province_code: Optional[str] = Field(None, max_length=5)  # Codice provincia
    postal_code: Optional[str] = Field(None, max_length=10)  # CAP
    address: Optional[str] = Field(None, max_length=500)  # Full address (legacy field)
    
    # Activity information
    activity_code: Optional[str] = Field(None, max_length=20)  # Codice attività
    activity_description: Optional[str] = Field(None, max_length=200)  # Descrizione attività
    
    # Legal representative information
    legal_rep_name: Optional[str] = Field(None, max_length=200)  # Nome rappresentante legale
    legal_rep_surname: Optional[str] = Field(None, max_length=200)  # Cognome rappresentante legale
    legal_rep_tax_code: Optional[str] = Field(None, max_length=30)  # Codice fiscale rappresentante
    legal_rep_birth_date: Optional[str] = Field(None, max_length=20)  # Data di nascita
    legal_rep_birth_place: Optional[str] = Field(None, max_length=100)  # Luogo di nascita
    legal_rep_residence_street: Optional[str] = Field(None, max_length=200)  # Via residenza
    legal_rep_residence_civic: Optional[str] = Field(None, max_length=20)  # Numero civico residenza
    legal_rep_residence_city: Optional[str] = Field(None, max_length=100)  # Città residenza
    legal_rep_residence_province: Optional[str] = Field(None, max_length=10)  # Provincia residenza
    legal_rep_residence_postal_code: Optional[str] = Field(None, max_length=10)  # CAP residenza
    legal_rep_email: Optional[str] = Field(None, max_length=100)  # Email rappresentante
    legal_rep_phone: Optional[str] = Field(None, max_length=20)  # Telefono rappresentante
    legal_rep_role: Optional[str] = Field(None, max_length=100)  # Qualità (ruolo)
    
    notes: Optional[str] = None
    is_active: bool = True
    
    @validator('email')
    def validate_email(cls, v):
        if v and '@' not in v:
            raise ValueError('Email non valida')
        return v
    
    @validator('legal_rep_email')
    def validate_legal_rep_email(cls, v):
        if v and '@' not in v:
            raise ValueError('Email rappresentante legale non valida')
        return v

class Project(BaseEntity):
    """Modello per i progetti"""
    name: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = None
    reference_code: Optional[str] = Field(None, max_length=50)
    category: ProjectCategory
    subcategory: ProjectSubcategory
    project_type: ProjectType
    client_id: UUID
    status: ProjectStatus = ProjectStatus.DRAFT
    priority: Priority = Priority.MEDIUM
    
    # Date importanti
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    submission_date: Optional[date] = None
    approval_date: Optional[date] = None
    
    # Informazioni finanziarie
    budget: Optional[float] = Field(None, ge=0)
    approved_amount: Optional[float] = Field(None, ge=0)
    
    # Gestione SAL
    requires_sal: bool = False
    sal_count: int = Field(0, ge=0)
    
    # Metadati
    notes: Optional[str] = None
    tags: List[str] = Field(default_factory=list)
    
    @validator('end_date')
    def validate_end_date(cls, v, values):
        if v and 'start_date' in values and values['start_date']:
            if v <= values['start_date']:
                raise ValueError('La data di fine deve essere successiva alla data di inizio')
        return v

class SALType(str, Enum):
    """Tipi di SAL"""
    MILESTONE = "milestone"
    PERCENTAGE = "percentage"
    PHASE = "phase"
    DISBURSEMENT = "disbursement"

class SALMilestoneType(str, Enum):
    """Tipi di milestone SAL"""
    PROJECT_START = "project_start"
    MID_PROGRESS = "mid_progress"
    COMPLETION = "completion"
    FIRST_DISBURSEMENT = "first_disbursement"
    FINAL_DISBURSEMENT = "final_disbursement"
    REVIEW = "review"
    APPROVAL = "approval"
    CUSTOM = "custom"

class SAL(BaseEntity):
    """Modello per gli Stati Avanzamento Lavori"""
    project_id: UUID
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    sequence_number: int = Field(..., ge=1)

    # Tipologia e milestone
    sal_type: SALType = SALType.MILESTONE
    milestone_type: Optional[SALMilestoneType] = None

    # Avanzamento
    percentage: float = Field(..., ge=0, le=100)
    status: ProjectStatus = ProjectStatus.DRAFT

    # Date
    planned_start_date: Optional[date] = None
    planned_end_date: Optional[date] = None
    actual_start_date: Optional[date] = None
    actual_end_date: Optional[date] = None

    # Informazioni finanziarie
    budget: Optional[float] = Field(None, ge=0)
    actual_cost: Optional[float] = Field(None, ge=0)

    # Metadati aggiuntivi
    template_id: Optional[str] = None  # ID del template utilizzato
    auto_generated: bool = False  # Se generato automaticamente
    requires_approval: bool = False  # Se richiede approvazione
    approved_by: Optional[str] = None
    approved_at: Optional[datetime] = None

    notes: Optional[str] = None

class SALConfiguration(BaseEntity):
    """Configurazione SAL per tipo di progetto"""
    project_type: ProjectType
    project_category: ProjectCategory
    requires_sal: bool = False
    default_sal_count: int = Field(0, ge=0, le=10)
    sal_type: SALType = SALType.MILESTONE
    template_name: Optional[str] = None
    auto_create_deadlines: bool = False
    milestone_types: List[str] = Field(default_factory=list)
    description: Optional[str] = None
    is_active: bool = True

class SALTemplate(BaseEntity):
    """Template per la creazione automatica di SAL"""
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    project_type: ProjectType
    project_category: ProjectCategory
    sal_definitions: List[Dict[str, Any]] = Field(default_factory=list)
    is_active: bool = True

    # Esempio sal_definitions:
    # [
    #   {"name": "SAL 1 - Avvio", "percentage": 30, "milestone": "project_start"},
    #   {"name": "SAL 2 - Intermedio", "percentage": 70, "milestone": "mid_progress"},
    #   {"name": "SAL 3 - Finale", "percentage": 100, "milestone": "completion"}
    # ]

class Deadline(BaseEntity):
    """Modello per le scadenze"""
    title: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = None
    due_date: date
    status: DeadlineStatus = DeadlineStatus.PENDING
    priority: Priority = Priority.MEDIUM
    
    # Associazioni
    client_id: Optional[UUID] = None
    project_id: Optional[UUID] = None
    sal_id: Optional[UUID] = None
    
    # Configurazione ricorrenza
    recurrence_type: RecurrenceType = RecurrenceType.NONE
    recurrence_interval: int = Field(1, ge=1, le=365)  # Ogni X giorni/settimane/mesi
    recurrence_end_date: Optional[date] = None
    parent_deadline_id: Optional[UUID] = None  # Per scadenze generate da ricorrenza
    is_recurring: bool = False
    
    # Configurazione alert
    alert_days_before: int = Field(15, ge=0, le=365)
    email_notifications: bool = True
    
    # Google Calendar integration
    google_event_id: Optional[str] = None  # ID dell'evento in Google Calendar
    sync_to_google: bool = True  # Se sincronizzare con Google Calendar
    
    # Completamento
    completed_date: Optional[date] = None
    completed_by: Optional[str] = None
    
    notes: Optional[str] = None
    
    @validator('completed_date')
    def validate_completed_date(cls, v, values):
        if v and 'due_date' in values:
            if v < values['due_date']:
                # Completato in anticipo - OK
                pass
        return v

class Alert(BaseEntity):
    """Modello per gli alert"""
    deadline_id: UUID
    title: str = Field(..., min_length=1, max_length=200)
    message: str
    status: AlertStatus = AlertStatus.ACTIVE
    priority: Priority = Priority.MEDIUM
    
    # Configurazione
    days_before_deadline: int = Field(..., ge=0)
    email_sent: bool = False
    email_sent_at: Optional[datetime] = None
    
    # Gestione
    dismissed_at: Optional[datetime] = None
    dismissed_by: Optional[str] = None
    
    # Metadati
    metadata: Dict[str, Any] = Field(default_factory=dict)

class Document(BaseEntity):
    """Modello per i documenti allegati"""
    name: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = None
    file_path: str
    file_size: int = Field(..., ge=0)
    mime_type: Optional[str] = None
    
    # Associazioni
    client_id: Optional[UUID] = None
    project_id: Optional[UUID] = None
    sal_id: Optional[UUID] = None
    deadline_id: Optional[UUID] = None
    
    # Metadati
    tags: List[str] = Field(default_factory=list)
    is_public: bool = False
    
class EmailTemplate(BaseEntity):
    """Modello per i template email"""
    name: str = Field(..., min_length=1, max_length=100)
    subject: str = Field(..., min_length=1, max_length=200)
    body: str
    template_type: str = Field(..., max_length=50)
    
    # Variabili disponibili
    available_variables: List[str] = Field(default_factory=list)
    
    is_active: bool = True

class Task(BaseEntity):
    """Modello per le task - può essere associata a una scadenza o direttamente a un progetto"""
    title: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = None
    
    # Relazioni
    deadline_id: Optional[UUID] = None  # Scadenza padre (OPZIONALE)
    project_id: Optional[UUID] = None   # Progetto associato (OPZIONALE - derivato dalla scadenza, diretto, o standalone)
    parent_task_id: Optional[UUID] = None  # Task padre per sottotask (OPZIONALE)
    assigned_to: Optional[UUID] = None  # Per supporto multi-utente futuro
    
    # Stati e priorità
    status: TaskStatus = TaskStatus.PENDING
    priority: Priority = Priority.MEDIUM
    
    # Tempo stimato/effettivo
    estimated_hours: Optional[float] = Field(None, ge=0)
    actual_hours: Optional[float] = Field(None, ge=0)
    
    # Date
    due_date: Optional[datetime] = None  # Deve essere <= deadline.due_date (supporta data e ora)
    start_date: Optional[date] = None
    completed_date: Optional[date] = None
    
    # Avanzamento
    progress_percentage: int = Field(0, ge=0, le=100)
    
    # Dipendenze tra task
    depends_on_tasks: List[UUID] = Field(default_factory=list)
    
    # Integrazione Google Tasks
    google_task_id: Optional[str] = None
    google_task_list_id: Optional[str] = None
    sync_to_google_tasks: bool = True
    
    # Metadati
    notes: Optional[str] = None
    tags: List[str] = Field(default_factory=list)
    
    @validator('due_date')
    def validate_due_date(cls, v, values):
        if v and 'start_date' in values and values['start_date']:
            # Convert datetime to date for comparison if needed
            due_date_only = v.date() if isinstance(v, datetime) else v
            start_date = values['start_date']
            if due_date_only <= start_date:
                raise ValueError('La data di scadenza deve essere successiva alla data di inizio')
        return v
    
    @validator('completed_date')
    def validate_completed_date(cls, v, values):
        if v and values.get('status') != TaskStatus.COMPLETED:
            raise ValueError('Data completamento può essere impostata solo per task completate')
        return v