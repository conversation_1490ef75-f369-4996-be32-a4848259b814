#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script di migrazione per aggiungere categorie e sottocategorie ai progetti esistenti
"""

import sqlite3
import sys
from pathlib import Path

# Aggiungi il percorso src al PYTHONPATH
sys.path.insert(0, str(Path(__file__).parent / "src"))

from core.models import ProjectType
from core.utils.project_categorization import get_category_and_subcategory

def migrate_project_categories(db_path: str = "data/agevolami_pm.db"):
    """
    Migra i progetti esistenti aggiungendo categoria e sottocategoria
    """
    print("Inizio migrazione categorie progetti...")
    
    # Connessione al database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Recupera tutti i progetti esistenti
        cursor.execute("SELECT id, project_type FROM projects")
        projects = cursor.fetchall()
        
        print(f"Trovati {len(projects)} progetti da migrare")
        
        updated_count = 0
        
        for project_id, project_type_str in projects:
            try:
                # Converte la stringa in enum
                project_type = ProjectType(project_type_str)
                
                # Ottiene categoria e sottocategoria
                category, subcategory = get_category_and_subcategory(project_type)
                
                # Aggiorna il progetto
                cursor.execute(
                    "UPDATE projects SET category = ?, subcategory = ? WHERE id = ?",
                    (category.value, subcategory.value, project_id)
                )
                
                updated_count += 1
                print(f"Progetto {project_id}: {project_type_str} -> {category.value}/{subcategory.value}")
                
            except ValueError as e:
                print(f"Errore con progetto {project_id}, tipo {project_type_str}: {e}")
                # Assegna valori di default
                cursor.execute(
                    "UPDATE projects SET category = ?, subcategory = ? WHERE id = ?",
                    ("incentivi_pubblici", "ricerca_innovazione", project_id)
                )
                updated_count += 1
        
        # Commit delle modifiche
        conn.commit()
        print(f"\nMigrazione completata! {updated_count} progetti aggiornati.")
        
        # Verifica i risultati
        cursor.execute("""
            SELECT category, subcategory, COUNT(*) as count 
            FROM projects 
            GROUP BY category, subcategory 
            ORDER BY category, subcategory
        """)
        
        results = cursor.fetchall()
        print("\nRiepilogo per categoria/sottocategoria:")
        for category, subcategory, count in results:
            print(f"  {category}/{subcategory}: {count} progetti")
            
    except Exception as e:
        print(f"Errore durante la migrazione: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()

if __name__ == "__main__":
    migrate_project_categories()