#!/usr/bin/env python3
"""
<PERSON>ript to create a new release for Agevolami PM
Automates version bumping and tagging for GitHub Actions
"""

import os
import sys
import subprocess
import re
from pathlib import Path

def get_current_version():
    """Get current version from pyproject.toml"""
    pyproject_path = Path("pyproject.toml")
    if not pyproject_path.exists():
        print("Error: pyproject.toml not found")
        return None
    
    with open(pyproject_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    match = re.search(r'version = "([^"]+)"', content)
    if match:
        return match.group(1)
    
    print("Error: Could not find version in pyproject.toml")
    return None

def bump_version(current_version, bump_type):
    """Bump version based on type (major, minor, patch)"""
    parts = current_version.split('.')
    if len(parts) != 3:
        print(f"Error: Invalid version format: {current_version}")
        return None
    
    major, minor, patch = map(int, parts)
    
    if bump_type == 'major':
        major += 1
        minor = 0
        patch = 0
    elif bump_type == 'minor':
        minor += 1
        patch = 0
    elif bump_type == 'patch':
        patch += 1
    else:
        print(f"Error: Invalid bump type: {bump_type}")
        return None
    
    return f"{major}.{minor}.{patch}"

def update_version_in_files(new_version):
    """Update version in all relevant files"""
    files_to_update = [
        ("pyproject.toml", r'version = "[^"]+"', f'version = "{new_version}"'),
        ("src/core/updater.py", r'current_version: str = "[^"]+"', f'current_version: str = "{new_version}"'),
        ("src/core/update_manager.py", r'current_version: str = "[^"]+"', f'current_version: str = "{new_version}"'),
    ]
    
    for file_path, pattern, replacement in files_to_update:
        path = Path(file_path)
        if not path.exists():
            print(f"Warning: {file_path} not found, skipping...")
            continue
        
        with open(path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        new_content = re.sub(pattern, replacement, content)
        
        if new_content != content:
            with open(path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            print(f"Updated version in {file_path}")
        else:
            print(f"No version found to update in {file_path}")

def run_command(command, check=True):
    """Run a shell command"""
    print(f"Running: {command}")
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    
    if check and result.returncode != 0:
        print(f"Error running command: {command}")
        print(f"Error output: {result.stderr}")
        return False
    
    if result.stdout:
        print(result.stdout.strip())
    
    return True

def create_release(version, release_notes=""):
    """Create a new release"""
    print(f"\n🚀 Creating release v{version}")
    
    # Update version in files
    print("\n📝 Updating version in files...")
    update_version_in_files(version)
    
    # Stage changes
    print("\n📦 Staging changes...")
    if not run_command("git add ."):
        return False
    
    # Commit changes
    print(f"\n💾 Committing version bump...")
    commit_message = f"Bump version to {version}"
    if not run_command(f'git commit -m "{commit_message}"'):
        return False
    
    # Create and push tag
    print(f"\n🏷️  Creating tag v{version}...")
    tag_message = f"Release v{version}"
    if release_notes:
        tag_message += f"\n\n{release_notes}"
    
    if not run_command(f'git tag -a v{version} -m "{tag_message}"'):
        return False
    
    # Push changes and tag
    print("\n⬆️  Pushing to repository...")
    if not run_command("git push"):
        return False
    
    if not run_command(f"git push origin v{version}"):
        return False
    
    print(f"\n✅ Release v{version} created successfully!")
    print(f"🔗 GitHub Actions will automatically build and create the release.")
    print(f"📦 Check: https://github.com/your-username/agevolami_pm_v2/actions")
    
    return True

def main():
    """Main function"""
    if len(sys.argv) < 2:
        print("Usage: python create_release.py <bump_type> [release_notes]")
        print("Bump types: major, minor, patch")
        print("Example: python create_release.py patch 'Bug fixes and improvements'")
        sys.exit(1)
    
    bump_type = sys.argv[1].lower()
    release_notes = sys.argv[2] if len(sys.argv) > 2 else ""
    
    if bump_type not in ['major', 'minor', 'patch']:
        print("Error: Bump type must be 'major', 'minor', or 'patch'")
        sys.exit(1)
    
    # Get current version
    current_version = get_current_version()
    if not current_version:
        sys.exit(1)
    
    # Calculate new version
    new_version = bump_version(current_version, bump_type)
    if not new_version:
        sys.exit(1)
    
    print(f"Current version: {current_version}")
    print(f"New version: {new_version}")
    print(f"Bump type: {bump_type}")
    
    # Confirm with user
    response = input(f"\nProceed with release v{new_version}? (y/N): ")
    if response.lower() != 'y':
        print("Release cancelled.")
        sys.exit(0)
    
    # Create release
    if create_release(new_version, release_notes):
        print(f"\n🎉 Release v{new_version} is being processed!")
    else:
        print("\n❌ Release creation failed.")
        sys.exit(1)

if __name__ == "__main__":
    main()
