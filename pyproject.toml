[project]
name = "agevolami-pm"
version = "2.3.4"
description = "Sistema di gestione progetti e scadenze per studio di consulenza Agevolami.it"
readme = "README.md"
requires-python = ">=3.9"
authors = [
    { name = "Agevolami Development Team", email = "<EMAIL>" }
]
dependencies = [
    "flet>=0.27.0",
    "pydantic>=2.0.0",
    "python-dotenv>=1.0.0",
    "loguru>=0.7.0",
    "reportlab>=4.0.0",
    "plyer>=2.1.0",
    "google-api-python-client>=2.0.0",
    "google-auth>=2.0.0",
    "google-auth-oauthlib>=1.0.0",
    "google-auth-httplib2>=0.2.0",
    "packaging>=21.0"
]

[tool.flet]
org = "it.agevolami"
product = "Agevolami PM"
company = "Agevolami S.r.l."
copyright = "Copyright (C) 2025 by Agevolami S.r.l."
description = "Sistema di gestione progetti e scadenze fiscali"

[tool.flet.app]
path = "src"
name = "Agevolami PM"
icon = "assets/icon.ico"

[tool.flet.build.windows]
name = "AgevolamiPM"
description = "Sistema gestione progetti Agevolami"
version = "2.3.4"
company = "Agevolami S.r.l."
copyright = "© 2025 Agevolami S.r.l."

[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"