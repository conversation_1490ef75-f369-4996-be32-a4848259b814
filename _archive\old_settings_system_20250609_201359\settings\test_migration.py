#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Settings Migration Testing Script
Comprehensive testing and demonstration of the migration process
"""

import sys
import os
import time
import json
from pathlib import Path
from typing import Dict, Any, List
from datetime import datetime

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent))

from core import get_logger
from . import (
    create_modern_settings_view,
    run_migration,
    check_migration_status,
    get_system_info,
    CompleteSettingsSystem,
    run_settings_system_tests
)

logger = get_logger(__name__)

class MockAppInstance:
    """Mock application instance for testing"""
    
    def __init__(self):
        self.config = MockConfig()
        self.page = None
        self.version = "2.0.0"
        
        # Mock services that settings might use
        self.email_service = MockEmailService()
        self.db_manager = MockDatabaseManager()
        self.scheduler_service = MockSchedulerService()
        
        logger.info("Mock application instance created for testing")
    
    def __repr__(self):
        return f"MockAppInstance(version={self.version})"

class MockConfig:
    """Mock configuration object"""
    
    def __init__(self):
        self.data_dir = Path("test_data")
        self.database_path = self.data_dir / "test_agevolami.db"
        
        # Mock email configuration
        self.email_config = {
            'smtp_server': 'smtp.gmail.com',
            'smtp_port': 587,
            'smtp_username': '<EMAIL>',
            'smtp_password': 'test_password',
            'smtp_use_tls': True,
            'from_name': 'Test Agevolami PM',
            'from_email': '<EMAIL>'
        }
        
        # Mock alert configuration
        self.alert_config = {
            'email_notifications_enabled': True,
            'check_interval_hours': 24,
            'days_before_deadline': 7,
            'reminder_recipients': ['<EMAIL>']
        }
        
        # Create test data directory
        self.data_dir.mkdir(exist_ok=True)

class MockEmailService:
    """Mock email service"""
    
    def __init__(self):
        self.smtp_config = {}
    
    def test_connection(self):
        return True
    
    def send_test_email(self, to_email):
        return True

class MockDatabaseManager:
    """Mock database manager"""
    
    def get_all_deadlines(self):
        return []
    
    def get_all_tasks(self):
        return []

class MockSchedulerService:
    """Mock scheduler service"""
    
    def send_manual_report(self):
        return True

class MigrationTester:
    """
    Comprehensive migration testing utility
    """
    
    def __init__(self):
        self.app_instance = MockAppInstance()
        self.test_results = {
            'timestamp': datetime.now().isoformat(),
            'tests': {},
            'summary': {'passed': 0, 'failed': 0, 'total': 0}
        }
        
        logger.info("Migration Tester initialized")
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run comprehensive migration tests"""
        
        logger.info("🧪 Starting comprehensive migration tests...")
        
        # Test 1: System Info Check
        self._test_system_info()
        
        # Test 2: Migration Status Check
        self._test_migration_status_check()
        
        # Test 3: Modern System Creation
        self._test_modern_system_creation()
        
        # Test 4: Settings Operations
        self._test_settings_operations()
        
        # Test 5: Search Functionality
        self._test_search_functionality()
        
        # Test 6: Validation System
        self._test_validation_system()
        
        # Test 7: Theme Management
        self._test_theme_management()
        
        # Test 8: Performance Features
        self._test_performance_features()
        
        # Test 9: Responsive Layout
        self._test_responsive_layout()
        
        # Test 10: Complete System Integration
        self._test_complete_system_integration()
        
        # Calculate summary
        self._calculate_summary()
        
        logger.info(f"🧪 Tests completed: {self.test_results['summary']['passed']}/{self.test_results['summary']['total']} passed")
        
        return self.test_results
    
    def _test_system_info(self):
        """Test system information retrieval"""
        test_name = "system_info"
        
        try:
            system_info = get_system_info()
            
            required_keys = ['version', 'modern_system_available', 'migration_status']
            missing_keys = [key for key in required_keys if key not in system_info]
            
            if not missing_keys and system_info['modern_system_available']:
                self._record_test_success(test_name, f"System info complete: v{system_info['version']}")
            else:
                self._record_test_failure(test_name, f"Missing keys: {missing_keys}")
                
        except Exception as e:
            self._record_test_failure(test_name, f"Exception: {str(e)}")
    
    def _test_migration_status_check(self):
        """Test migration status checking"""
        test_name = "migration_status_check"
        
        try:
            migration_status = check_migration_status()
            
            required_keys = ['migrated', 'message']
            missing_keys = [key for key in required_keys if key not in migration_status]
            
            if not missing_keys:
                self._record_test_success(test_name, f"Migration status: {migration_status['message']}")
            else:
                self._record_test_failure(test_name, f"Missing keys: {missing_keys}")
                
        except Exception as e:
            self._record_test_failure(test_name, f"Exception: {str(e)}")
    
    def _test_modern_system_creation(self):
        """Test modern system creation"""
        test_name = "modern_system_creation"
        
        try:
            settings_system = create_modern_settings_view(self.app_instance, migrate_if_needed=False)
            
            if isinstance(settings_system, CompleteSettingsSystem):
                if settings_system.initialized:
                    self._record_test_success(test_name, "Modern system created and initialized")
                else:
                    self._record_test_failure(test_name, "System created but not initialized")
            else:
                self._record_test_failure(test_name, f"Wrong type returned: {type(settings_system)}")
                
        except Exception as e:
            self._record_test_failure(test_name, f"Exception: {str(e)}")
    
    def _test_settings_operations(self):
        """Test basic settings operations"""
        test_name = "settings_operations"
        
        try:
            settings_system = create_modern_settings_view(self.app_instance, migrate_if_needed=False)
            
            # Test getting a setting
            original_value = settings_system.settings_controller.get_setting('app', 'theme', 'light')
            
            # Test updating a setting
            settings_system.settings_controller.update_setting('app', 'theme', 'dark')
            new_value = settings_system.settings_controller.get_setting('app', 'theme', 'light')
            
            # Test restoring original value
            settings_system.settings_controller.update_setting('app', 'theme', original_value)
            restored_value = settings_system.settings_controller.get_setting('app', 'theme', 'light')
            
            if new_value == 'dark' and restored_value == original_value:
                self._record_test_success(test_name, "Settings CRUD operations working")
            else:
                self._record_test_failure(test_name, f"CRUD failed: {original_value} -> {new_value} -> {restored_value}")
                
        except Exception as e:
            self._record_test_failure(test_name, f"Exception: {str(e)}")
    
    def _test_search_functionality(self):
        """Test search functionality"""
        test_name = "search_functionality"
        
        try:
            settings_system = create_modern_settings_view(self.app_instance, migrate_if_needed=False)
            
            if hasattr(settings_system, 'search_engine'):
                # Test search
                results = settings_system.search_engine.search('email')
                
                # Test suggestions
                suggestions = settings_system.search_engine.get_suggestions('em')
                
                if results and suggestions:
                    self._record_test_success(test_name, f"Search working: {len(results)} results, {len(suggestions)} suggestions")
                else:
                    self._record_test_failure(test_name, "Search returned no results")
            else:
                self._record_test_failure(test_name, "Search engine not available")
                
        except Exception as e:
            self._record_test_failure(test_name, f"Exception: {str(e)}")
    
    def _test_validation_system(self):
        """Test validation system"""
        test_name = "validation_system"
        
        try:
            settings_system = create_modern_settings_view(self.app_instance, migrate_if_needed=False)
            
            if hasattr(settings_system, 'validation_engine'):
                # Test email validation
                email_results = settings_system.validation_engine.validate_field('smtp_username', '<EMAIL>')
                
                # Test invalid email
                invalid_results = settings_system.validation_engine.validate_field('smtp_username', 'invalid-email')
                
                # Test port validation
                port_results = settings_system.validation_engine.validate_field('smtp_port', '587')
                
                valid_count = sum(1 for r in email_results + port_results if r.is_valid)
                invalid_count = sum(1 for r in invalid_results if not r.is_valid)
                
                if valid_count > 0 and invalid_count > 0:
                    self._record_test_success(test_name, f"Validation working: {valid_count} valid, {invalid_count} invalid")
                else:
                    self._record_test_failure(test_name, "Validation not detecting valid/invalid properly")
            else:
                self._record_test_failure(test_name, "Validation engine not available")
                
        except Exception as e:
            self._record_test_failure(test_name, f"Exception: {str(e)}")
    
    def _test_theme_management(self):
        """Test theme management"""
        test_name = "theme_management"
        
        try:
            settings_system = create_modern_settings_view(self.app_instance, migrate_if_needed=False)
            
            if hasattr(settings_system, 'theme_manager'):
                from .components.theme_manager import ThemeMode
                
                # Test theme change
                original_mode = settings_system.theme_manager.current_mode
                settings_system.theme_manager.set_theme_mode(ThemeMode.DARK)
                new_mode = settings_system.theme_manager.current_mode
                
                # Test color retrieval
                primary_color = settings_system.theme_manager.get_color('primary')
                
                # Restore original
                settings_system.theme_manager.set_theme_mode(original_mode)
                
                if new_mode == ThemeMode.DARK and primary_color:
                    self._record_test_success(test_name, f"Theme management working: {original_mode.value} -> {new_mode.value}")
                else:
                    self._record_test_failure(test_name, "Theme change not working properly")
            else:
                self._record_test_failure(test_name, "Theme manager not available")
                
        except Exception as e:
            self._record_test_failure(test_name, f"Exception: {str(e)}")
    
    def _test_performance_features(self):
        """Test performance features"""
        test_name = "performance_features"
        
        try:
            settings_system = create_modern_settings_view(self.app_instance, migrate_if_needed=False)
            
            if hasattr(settings_system, 'performance_manager'):
                # Test performance stats
                stats = settings_system.performance_manager.get_performance_stats()
                
                # Test cache operations
                settings_system.performance_manager.cache_component_data('test_component', {'test': 'data'})
                cached_data = settings_system.performance_manager.get_cached_component_data('test_component')
                
                if stats and cached_data and cached_data.get('test') == 'data':
                    self._record_test_success(test_name, "Performance features working")
                else:
                    self._record_test_failure(test_name, "Performance features not working properly")
            else:
                self._record_test_failure(test_name, "Performance manager not available")
                
        except Exception as e:
            self._record_test_failure(test_name, f"Exception: {str(e)}")
    
    def _test_responsive_layout(self):
        """Test responsive layout"""
        test_name = "responsive_layout"
        
        try:
            settings_system = create_modern_settings_view(self.app_instance, migrate_if_needed=False)
            
            if hasattr(settings_system, 'responsive_manager'):
                from .components.responsive_layout import BreakPoint
                
                # Test layout config retrieval
                config = settings_system.responsive_manager.get_current_config()
                
                # Test size update (simulate)
                settings_system.responsive_manager.update_layout(800, 600)  # Tablet size
                new_breakpoint = settings_system.responsive_manager.current_breakpoint
                
                if config and new_breakpoint == BreakPoint.TABLET:
                    self._record_test_success(test_name, f"Responsive layout working: {new_breakpoint.value}")
                else:
                    self._record_test_failure(test_name, "Responsive layout not working properly")
            else:
                self._record_test_failure(test_name, "Responsive manager not available")
                
        except Exception as e:
            self._record_test_failure(test_name, f"Exception: {str(e)}")
    
    def _test_complete_system_integration(self):
        """Test complete system integration"""
        test_name = "complete_system_integration"
        
        try:
            settings_system = create_modern_settings_view(self.app_instance, migrate_if_needed=False)
            
            # Run built-in tests
            test_results = run_settings_system_tests(settings_system)
            
            passed = test_results['summary']['passed']
            total = test_results['summary']['total']
            success_rate = passed / total if total > 0 else 0
            
            if success_rate >= 0.8:  # 80% pass rate
                self._record_test_success(test_name, f"System integration tests passed: {passed}/{total} ({success_rate:.1%})")
            else:
                self._record_test_failure(test_name, f"System integration tests failed: {passed}/{total} ({success_rate:.1%})")
                
        except Exception as e:
            self._record_test_failure(test_name, f"Exception: {str(e)}")
    
    def _record_test_success(self, test_name: str, message: str):
        """Record a successful test"""
        self.test_results['tests'][test_name] = {
            'status': 'passed',
            'message': message,
            'timestamp': datetime.now().isoformat()
        }
        logger.info(f"✅ {test_name}: {message}")
    
    def _record_test_failure(self, test_name: str, message: str):
        """Record a failed test"""
        self.test_results['tests'][test_name] = {
            'status': 'failed',
            'message': message,
            'timestamp': datetime.now().isoformat()
        }
        logger.error(f"❌ {test_name}: {message}")
    
    def _calculate_summary(self):
        """Calculate test summary"""
        for test_name, test_result in self.test_results['tests'].items():
            self.test_results['summary']['total'] += 1
            
            if test_result['status'] == 'passed':
                self.test_results['summary']['passed'] += 1
            else:
                self.test_results['summary']['failed'] += 1
    
    def save_test_results(self, filename: str = None):
        """Save test results to file"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"migration_test_results_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📊 Test results saved to {filename}")

def run_migration_demo():
    """
    Run a complete migration demonstration
    """
    
    print("🚀 Settings System Migration Demonstration")
    print("=" * 50)
    
    # Create mock app
    app_instance = MockAppInstance()
    
    # Check current system status
    print("\n📊 System Status Check:")
    system_info = get_system_info()
    print(f"  • Version: {system_info['version']}")
    print(f"  • Modern System Available: {system_info['modern_system_available']}")
    print(f"  • Migration Required: {system_info['migration_required']}")
    
    migration_status = check_migration_status()
    print(f"  • Migration Status: {migration_status['message']}")
    
    # Test modern system creation
    print("\n🔧 Creating Modern Settings System:")
    try:
        settings_system = create_modern_settings_view(app_instance, migrate_if_needed=False)
        print(f"  ✅ Modern system created successfully")
        print(f"  • System Status: {'Initialized' if settings_system.initialized else 'Not Initialized'}")
        print(f"  • Current Section: {settings_system.current_section}")
        
        # Demonstrate key features
        print("\n🔍 Testing Search Functionality:")
        search_results = settings_system.search_engine.search('email')
        print(f"  • Search 'email': {len(search_results)} results")
        
        suggestions = settings_system.search_engine.get_suggestions('not')
        print(f"  • Suggestions for 'not': {suggestions[:3]}")
        
        print("\n✅ Testing Validation:")
        email_validation = settings_system.validation_engine.validate_field('smtp_username', '<EMAIL>')
        port_validation = settings_system.validation_engine.validate_field('smtp_port', '587')
        print(f"  • Email validation: {len(email_validation)} rules checked")
        print(f"  • Port validation: {len(port_validation)} rules checked")
        
        print("\n🎨 Testing Theme System:")
        current_theme = settings_system.theme_manager.current_mode.value
        primary_color = settings_system.theme_manager.get_color('primary')
        print(f"  • Current theme: {current_theme}")
        print(f"  • Primary color: {primary_color}")
        
        print("\n⚡ Testing Performance Features:")
        perf_stats = settings_system.performance_manager.get_performance_stats()
        print(f"  • Cache size: {perf_stats['cache']['size']}")
        print(f"  • Pending updates: {perf_stats['updates']['pending_updates']}")
        
        print("\n📊 System Health Check:")
        status = settings_system.get_system_status()
        print(f"  • Initialized: {status['initialized']}")
        print(f"  • Current section: {status['current_section']}")
        print(f"  • Theme: {status['theme']}")
        print(f"  • Responsive breakpoint: {status['responsive']['current_breakpoint']}")
        
        print("\n🧹 Cleanup:")
        settings_system.cleanup()
        print("  ✅ System cleanup completed")
        
    except Exception as e:
        print(f"  ❌ Error: {str(e)}")
    
    print("\n🎉 Migration demonstration completed!")

def run_comprehensive_tests():
    """
    Run comprehensive testing suite
    """
    
    print("🧪 Running Comprehensive Migration Tests")
    print("=" * 50)
    
    tester = MigrationTester()
    test_results = tester.run_all_tests()
    
    # Display results
    print(f"\n📊 Test Results Summary:")
    print(f"  • Total Tests: {test_results['summary']['total']}")
    print(f"  • Passed: {test_results['summary']['passed']}")
    print(f"  • Failed: {test_results['summary']['failed']}")
    
    success_rate = test_results['summary']['passed'] / test_results['summary']['total']
    print(f"  • Success Rate: {success_rate:.1%}")
    
    if test_results['summary']['failed'] > 0:
        print(f"\n❌ Failed Tests:")
        for test_name, test_data in test_results['tests'].items():
            if test_data['status'] == 'failed':
                print(f"  • {test_name}: {test_data['message']}")
    
    # Save results
    tester.save_test_results()
    
    return test_results

if __name__ == "__main__":
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "demo":
            run_migration_demo()
        elif command == "test":
            run_comprehensive_tests()
        elif command == "info":
            system_info = get_system_info()
            print(json.dumps(system_info, indent=2))
        else:
            print("Usage: python test_migration.py [demo|test|info]")
    else:
        # Run both demo and tests
        print("🚀 Running Migration Demo and Tests")
        print("=" * 50)
        
        run_migration_demo()
        
        print("\n" + "=" * 50)
        
        run_comprehensive_tests()
        
        print("\n✅ All operations completed!") 