#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple test to check incentives configuration
"""

import json
from pathlib import Path

def main():
    print("🔍 Checking Incentives Configuration...")
    
    # Load configuration from file
    config_path = Path("data/incentives_config.json")
    if not config_path.exists():
        print("❌ Configuration file not found!")
        return
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    print(f"✅ Loaded config with {len(config.get('websites', []))} websites")
    
    # Print configured websites
    print("\n📋 Configured Websites:")
    for website in config.get('websites', []):
        name = website.get('name', 'Unknown')
        url = website.get('url', 'No URL')
        enabled = website.get('enabled', False)
        search_paths = website.get('search_paths', [])
        
        status = "✅ Enabled" if enabled else "❌ Disabled"
        print(f"  {status} {name}: {url}")
        for path in search_paths:
            print(f"    └─ {url}{path}")
    
    print("\n📊 URL Comparison (Hardcoded vs Config):")
    
    # Hardcoded URLs from old implementation
    hardcoded_urls = {
        'MIMIT': ['/it/incentivi-mise', '/it/notizie-stampa', '/it/per-l-impresa'],
        'Invitalia': ['/per-le-imprese', '/news', '/per-chi-vuole-fare-impresa'],
        'SIMEST': ['/per-le-imprese', '/per-le-imprese/finanziamenti-agevolati', '/media']
    }
    
    config_urls = {}
    for website in config.get('websites', []):
        name = website.get('name', '')
        if 'MIMIT' in name.upper():
            config_urls['MIMIT'] = website.get('search_paths', [])
        elif 'INVITALIA' in name.upper():
            config_urls['Invitalia'] = website.get('search_paths', [])
        elif 'SIMEST' in name.upper():
            config_urls['SIMEST'] = website.get('search_paths', [])
    
    # Compare
    for website in ['MIMIT', 'Invitalia', 'SIMEST']:
        print(f"\n  {website}:")
        hardcoded = hardcoded_urls.get(website, [])
        configured = config_urls.get(website, [])
        
        print(f"    Hardcoded: {hardcoded}")
        print(f"    Configured: {configured}")
        
        if hardcoded == configured:
            print(f"    ✅ URLs match")
        else:
            print(f"    ⚠️  URLs differ")

if __name__ == "__main__":
    main()
