# -*- coding: utf-8 -*-
"""
Vista dettagli progetto per Agevolami PM
"""

import flet as ft
from datetime import datetime, date
from typing import Optional, List, Dict, Any
from uuid import UUID

from core.models import (
    Project, Client, SAL, Deadline, Document, Alert,
    ProjectStatus, DeadlineStatus, AlertStatus, ProjectType, SALType, SALMilestoneType
)
from core.services.sal_service import SALService
from core.utils import get_logger

logger = get_logger(__name__)

class ProjectDetailView:
    """Vista per visualizzare i dettagli di un progetto"""
    
    def __init__(self, app, project_id: UUID):
        self.app = app
        self.page = app.page
        self.project_id = project_id
        self.project: Optional[Project] = None
        self.client: Optional[Client] = None
        self.sals: List[SAL] = []
        self.deadlines: List[Deadline] = []
        self.documents: List[Document] = []
        self.alerts: List[Alert] = []
        self.tasks: List = []  # Direct project tasks

        # Inizializza il servizio SAL
        self.sal_service = SALService(app.db)
        
        # UI Components
        self.content = ft.Column()
        self.header_section = ft.Container()
        self.overview_section = ft.Container()
        self.progress_section = ft.Container()
        self.tasks_section = ft.Container()
        self.deadlines_section = ft.Container()
        self.documents_section = ft.Container()
        self.alerts_section = ft.Container()
        
    def build(self) -> ft.Control:
        """Costruisce la vista dettagli progetto"""
        try:
            self._load_data()
            self._build_header()
            self._build_overview()
            self._build_progress_section()
            self._build_deadlines_section()
            self._build_documents_section()
            self._build_alerts_section()
            
            # Build tasks section
            self._build_tasks_section()
            
            # Layout a tab moderno
            tabs = ft.Tabs(
                selected_index=0,
                animation_duration=200,
                indicator_color=ft.Colors.BLUE_600,
                label_color=ft.Colors.BLUE_600,
                unselected_label_color=ft.Colors.GREY_600,
                tabs=[
                    ft.Tab(
                        text="Panoramica",
                        icon=ft.Icons.DASHBOARD_OUTLINED,
                        content=ft.Container(
                            content=ft.Column(
                                controls=[
                                    self.overview_section,
                                    ft.Container(height=16),
                                    self.progress_section
                                ],
                                spacing=0,
                                scroll=ft.ScrollMode.AUTO
                            ),
                            padding=16
                        )
                    ),
                    ft.Tab(
                        text="Attività",
                        icon=ft.Icons.TASK_ALT_OUTLINED,
                        content=ft.Container(
                            content=self.tasks_section,
                            padding=16
                        )
                    ),
                    ft.Tab(
                        text="Scadenze",
                        icon=ft.Icons.SCHEDULE_OUTLINED,
                        content=ft.Container(
                            content=self.deadlines_section,
                            padding=16
                        )
                    ),
                    ft.Tab(
                        text="Documenti",
                        icon=ft.Icons.FOLDER_OUTLINED,
                        content=ft.Container(
                            content=self.documents_section,
                            padding=16
                        )
                    ),
                    ft.Tab(
                        text="Avvisi",
                        icon=ft.Icons.NOTIFICATIONS_OUTLINED,
                        content=ft.Container(
                            content=self.alerts_section,
                            padding=16
                        )
                    )
                ],
                expand=True
            )
            
            self.content = ft.Column(
                controls=[
                    self.header_section,
                    ft.Container(height=12),
                    tabs
                ],
                spacing=0,
                expand=True
            )

            return ft.Container(
                content=self.content,
                expand=True,
                bgcolor=ft.Colors.GREY_50,
                padding=12
            )
            
        except Exception as e:
            logger.error(f"Errore costruzione vista progetto: {e}")
            return ft.Container(
                content=ft.Text(f"Errore caricamento dettagli progetto: {e}", color=ft.Colors.RED),
                padding=20
            )
    
    def _load_data(self):
        """Carica i dati del progetto"""
        try:
            self.project = self.app.db.get_project(self.project_id)
            if not self.project:
                raise Exception("Progetto non trovato")
                
            self.client = self.app.db.get_client(self.project.client_id)
            self.sals = self.app.db.get_sals_by_project(self.project_id)
            self.deadlines = self.app.db.get_deadlines_by_project(self.project_id)
            self.documents = self.app.db.get_documents_by_project(self.project_id)
            self.tasks = self.app.db.get_tasks_by_project(self.project_id)
            
            # Carica gli alert relativi al progetto
            all_alerts = self.app.db.get_active_alerts()
            self.alerts = [alert for alert in all_alerts if hasattr(alert, 'project_id') and alert.project_id == self.project_id]
            
            logger.info(f"Caricati dati per progetto: {self.project.name}")
            
        except Exception as e:
            logger.error(f"Errore caricamento dati progetto: {e}")
            raise

    def _get_project_type_display(self) -> str:
        """Ottiene il display text per il tipo di progetto in modo sicuro"""
        if not self.project or not self.project.project_type:
            return "Non specificato"

        # Se è un enum, usa il valore
        if hasattr(self.project.project_type, 'value'):
            return self.project.project_type.value

        # Se è una stringa, usala direttamente
        if isinstance(self.project.project_type, str):
            return self.project.project_type

        # Fallback
        return str(self.project.project_type)

    def _build_header(self):
        """Costruisce la sezione header moderna con info progetto"""
        if not self.project:
            return

        # Status badge moderno
        status_config = {
            ProjectStatus.IN_PROGRESS: ("In Corso", ft.Colors.GREEN_600, ft.Icons.PLAY_CIRCLE),
            ProjectStatus.COMPLETED: ("Completato", ft.Colors.BLUE_600, ft.Icons.CHECK_CIRCLE),
            ProjectStatus.SUSPENDED: ("Sospeso", ft.Colors.ORANGE_600, ft.Icons.PAUSE_CIRCLE),
            ProjectStatus.CANCELLED: ("Annullato", ft.Colors.RED_600, ft.Icons.CANCEL)
        }

        status_text, status_color, status_icon = status_config.get(
            self.project.status,
            ("Sconosciuto", ft.Colors.GREY_600, ft.Icons.HELP_OUTLINE)
        )

        status_badge = ft.Container(
            content=ft.Row(
                controls=[
                    ft.Icon(status_icon, size=14, color=ft.Colors.WHITE),
                    ft.Text(status_text, size=11, color=ft.Colors.WHITE, weight=ft.FontWeight.BOLD)
                ],
                spacing=4,
                tight=True
            ),
            bgcolor=status_color,
            padding=ft.padding.symmetric(horizontal=10, vertical=5),
            border_radius=12
        )

        # Info principale compatta con gestione titoli lunghi
        project_info = ft.Column(
            controls=[
                ft.Row(
                    controls=[
                        ft.Container(
                            content=ft.Text(
                                self.project.name,
                                size=24,
                                weight=ft.FontWeight.BOLD,
                                color=ft.Colors.GREY_900,
                                max_lines=2,
                                overflow=ft.TextOverflow.ELLIPSIS,
                                tooltip=self.project.name if len(self.project.name) > 50 else None
                            ),
                            expand=True
                        ),
                        status_badge
                    ],
                    spacing=12,
                    vertical_alignment=ft.CrossAxisAlignment.START
                ),
                # Solo informazioni non vuote
                *([ft.Text(
                    self.project.reference_code,
                    size=13,
                    color=ft.Colors.GREY_600,
                    weight=ft.FontWeight.BOLD
                )] if self.project.reference_code else []),
                ft.Row(
                    controls=[
                        ft.Icon(ft.Icons.BUSINESS, size=14, color=ft.Colors.GREY_500),
                        ft.Text(
                            self.client.name if self.client else 'Cliente non trovato',
                            size=12,
                            color=ft.Colors.GREY_600
                        )
                    ],
                    spacing=6
                ),
                *([ft.Row(
                    controls=[
                        ft.Icon(ft.Icons.CATEGORY, size=14, color=ft.Colors.GREY_500),
                        ft.Text(
                            self._get_project_type_display(),
                            size=12,
                            color=ft.Colors.GREY_600
                        )
                    ],
                    spacing=6
                )] if self.project.project_type else [])
            ],
            spacing=4
        )

        # Pulsanti azione moderni e responsivi
        action_buttons = ft.Row(
            controls=[
                ft.ElevatedButton(
                    "Modifica",
                    icon=ft.Icons.EDIT_OUTLINED,
                    on_click=lambda _: self._edit_project(),
                    bgcolor=ft.Colors.BLUE_600,
                    color=ft.Colors.WHITE,
                    height=36
                ),
                ft.OutlinedButton(
                    "Scadenza",
                    icon=ft.Icons.SCHEDULE_OUTLINED,
                    on_click=lambda _: self._create_deadline(),
                    height=36
                ),
                *([ft.OutlinedButton(
                    "SAL",
                    icon=ft.Icons.TIMELINE_OUTLINED,
                    on_click=lambda _: self._create_sal(),
                    height=36
                )] if self.project.requires_sal else [])
            ],
            spacing=6,
            wrap=True,
            run_spacing=6
        )

        # Layout responsivo per header - usa ResponsiveRow per gestire meglio i titoli lunghi
        self.header_section = ft.Container(
            content=ft.ResponsiveRow(
                controls=[
                    ft.Container(
                        content=project_info,
                        col={"sm": 12, "md": 8, "lg": 9}
                    ),
                    ft.Container(
                        content=ft.Column(
                            controls=[action_buttons],
                            horizontal_alignment=ft.CrossAxisAlignment.END
                        ),
                        col={"sm": 12, "md": 4, "lg": 3}
                    )
                ],
                vertical_alignment=ft.CrossAxisAlignment.START
            ),
            bgcolor=ft.Colors.WHITE,
            padding=16,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )
    
    def _build_overview(self):
        """Costruisce la sezione panoramica"""
        # Statistiche generali
        total_sals = len(self.sals)
        completed_sals = len([sal for sal in self.sals if sal.status == "completed"])
        pending_deadlines = len([d for d in self.deadlines if d.status == DeadlineStatus.PENDING])
        overdue_deadlines = len([d for d in self.deadlines if d.status == DeadlineStatus.OVERDUE])
        
        # Statistiche tasks
        from core.models.base_models import TaskStatus
        total_tasks = len(self.tasks)
        completed_tasks = len([t for t in self.tasks if 
                             (t.status.value if hasattr(t.status, 'value') else str(t.status)) == "completato"])
        direct_tasks = len([t for t in self.tasks if not t.deadline_id])
        
        # Formattazione compatta del budget (come richiesto: 14M invece di €14.0M)
        budget_display = "N/A"
        budget_tooltip = None
        if self.project.budget:
            full_budget = f"€{self.project.budget:,.2f}"
            if self.project.budget >= 1000000:
                # Per milioni, formato compatto: 14M
                millions = self.project.budget / 1000000
                if millions == int(millions):
                    budget_display = f"{int(millions)}M"
                else:
                    budget_display = f"{millions:.1f}M"
                budget_tooltip = f"Budget completo: {full_budget}"
            elif self.project.budget >= 1000:
                # Per migliaia, formato compatto: 14K
                thousands = self.project.budget / 1000
                if thousands == int(thousands):
                    budget_display = f"{int(thousands)}K"
                else:
                    budget_display = f"{thousands:.1f}K"
                budget_tooltip = f"Budget completo: {full_budget}"
            else:
                budget_display = f"€{self.project.budget:,.0f}"

        stats_cards = [
            self._create_stat_card("Budget", budget_display, ft.Icons.EURO, ft.Colors.GREEN, budget_tooltip),
            self._create_stat_card("Attività Totali", str(total_tasks), ft.Icons.TASK_ALT, ft.Colors.TEAL),
            self._create_stat_card("Attività Completate", str(completed_tasks), ft.Icons.CHECK_CIRCLE, ft.Colors.BLUE),
            self._create_stat_card("Attività Dirette", str(direct_tasks), ft.Icons.WORK, ft.Colors.INDIGO),
            self._create_stat_card("SAL Totali", str(total_sals), ft.Icons.TIMELINE, ft.Colors.ORANGE),
            self._create_stat_card("Scadenze Pendenti", str(pending_deadlines), ft.Icons.SCHEDULE, ft.Colors.AMBER),
            self._create_stat_card("Scadenze Scadute", str(overdue_deadlines), ft.Icons.WARNING, ft.Colors.RED),
            self._create_stat_card("Documenti", str(len(self.documents)), ft.Icons.FOLDER, ft.Colors.PURPLE)
        ]
        
        # Informazioni dettagliate moderne
        detail_rows = []

        # Solo aggiungi righe con contenuto valido
        if self.project.description:
            detail_rows.append(self._create_detail_row("Descrizione", self.project.description))

        if self.project.start_date:
            detail_rows.append(self._create_detail_row("Data Inizio", self.project.start_date.strftime('%d/%m/%Y')))

        if self.project.end_date:
            detail_rows.append(self._create_detail_row("Data Fine", self.project.end_date.strftime('%d/%m/%Y')))

        if self.project.created_at:
            detail_rows.append(self._create_detail_row("Creato il", self.project.created_at.strftime('%d/%m/%Y %H:%M')))

        if self.project.updated_at:
            detail_rows.append(self._create_detail_row("Aggiornato il", self.project.updated_at.strftime('%d/%m/%Y %H:%M')))

        details_section = None
        if detail_rows:
            details_section = ft.Container(
                content=ft.Column(
                    controls=[
                        ft.Text("Dettagli Progetto", size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_800),
                        ft.Container(height=8),
                        *detail_rows
                    ],
                    spacing=8
                ),
                bgcolor=ft.Colors.WHITE,
                padding=16,
                border_radius=12,
                border=ft.border.all(1, ft.Colors.GREY_200)
            )
        
        overview_controls = [
            ft.Text("Statistiche Progetto", size=18, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_800),
            ft.ResponsiveRow(
                controls=[
                    ft.Container(
                        card,
                        col={"xs": 6, "sm": 4, "md": 3, "lg": 2, "xl": 2},
                        padding=ft.padding.all(4)  # Padding uniforme
                    ) for card in stats_cards
                ],
                spacing=0,  # Gestito dal padding dei container
                run_spacing=0
            )
        ]

        # Aggiungi dettagli solo se esistono
        if details_section:
            overview_controls.extend([
                ft.Container(height=16),
                details_section
            ])

        self.overview_section = ft.Column(
            controls=overview_controls,
            spacing=12
        )
    
    def _create_stat_card(self, title: str, value: str, icon: str, color: str, tooltip: str = None) -> ft.Card:
        """Crea una card statistica perfetta con testi visibili"""
        return ft.Card(
            content=ft.Container(
                content=ft.Column(
                    controls=[
                        # Header con icona
                        ft.Row(
                            controls=[
                                ft.Container(
                                    content=ft.Icon(icon, color=ft.Colors.WHITE, size=16),
                                    bgcolor=color,
                                    padding=6,
                                    border_radius=6
                                ),
                                ft.Container(expand=True)
                            ]
                        ),
                        ft.Container(height=6),
                        # Valore principale - più piccolo come richiesto
                        ft.Text(
                            value,
                            size=16,  # Ridotto per essere più piccolo
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.GREY_900,
                            tooltip=tooltip
                        ),
                        ft.Container(height=4),
                        # Titolo/Label - DEVE essere visibile
                        ft.Text(
                            title,
                            size=11,
                            color=ft.Colors.GREY_700,
                            max_lines=2,
                            overflow=ft.TextOverflow.ELLIPSIS,
                            weight=ft.FontWeight.NORMAL
                        )
                    ],
                    spacing=0,
                    horizontal_alignment=ft.CrossAxisAlignment.START
                ),
                padding=12,
                bgcolor=ft.Colors.WHITE,
                border_radius=10,
                border=ft.border.all(1, ft.Colors.GREY_200),
                width=135,
                height=90
            ),
            elevation=1,
            surface_tint_color=ft.Colors.TRANSPARENT
        )
    
    def _create_detail_row(self, label: str, value: str) -> ft.Container:
        """Crea una riga di dettaglio moderna"""
        return ft.Container(
            content=ft.Row(
                controls=[
                    ft.Text(f"{label}:", size=12, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_600),
                    ft.Text(value, size=12, color=ft.Colors.GREY_800, expand=True)
                ],
                spacing=8
            ),
            padding=ft.padding.symmetric(vertical=4)
        )
    
    def _build_progress_section(self):
        """Costruisce la sezione avanzamento (SAL) - manual control"""
        # Verifica se il progetto ha SAL abilitati manualmente
        if not self.project.requires_sal:
            # Per progetti senza SAL, mostra sezione servizi periodici
            self.progress_section = self._build_periodic_services_section()
            return

        # Calcola progresso complessivo
        progress_info = self.sal_service.calculate_project_progress(self.project_id)

        # Header con progresso complessivo
        progress_header = ft.Container(
            content=ft.Column(
                controls=[
                    ft.Row(
                        controls=[
                            ft.Text("Stato Avanzamento Lavori (SAL)", size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_800),
                            ft.Row(
                                controls=[
                                    ft.ElevatedButton(
                                        "Nuovo SAL",
                                        icon=ft.Icons.ADD_OUTLINED,
                                        on_click=lambda _: self._create_sal(),
                                        bgcolor=ft.Colors.BLUE_600,
                                        color=ft.Colors.WHITE,
                                        height=32
                                    )
                                ],
                                spacing=8
                            )
                        ],
                        alignment=ft.MainAxisAlignment.SPACE_BETWEEN
                    ),
                    ft.Container(height=12),
                    # Barra progresso complessiva
                    ft.Column(
                        controls=[
                            ft.Row(
                                controls=[
                                    ft.Text("Progresso Complessivo", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_700),
                                    ft.Text(f"{progress_info['total_progress']:.1f}%", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_600)
                                ],
                                alignment=ft.MainAxisAlignment.SPACE_BETWEEN
                            ),
                            ft.ProgressBar(
                                value=progress_info['total_progress'] / 100,
                                color=ft.Colors.BLUE_600,
                                bgcolor=ft.Colors.GREY_200,
                                height=8
                            ),
                            ft.Text(
                                f"{progress_info['completed_sals']}/{progress_info['total_sals']} SAL completati",
                                size=12,
                                color=ft.Colors.GREY_600
                            )
                        ],
                        spacing=4
                    )
                ],
                spacing=0
            ),
            bgcolor=ft.Colors.BLUE_50,
            padding=16,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.BLUE_200)
        )

        if not self.sals:
            # Mostra messaggio per progetti senza SAL
            sals_content = ft.Container(
                content=ft.Column(
                    controls=[
                        ft.Icon(ft.Icons.TIMELINE_OUTLINED, size=48, color=ft.Colors.GREY_400),
                        ft.Text("Nessun SAL registrato", size=14, color=ft.Colors.GREY_600, weight=ft.FontWeight.BOLD),
                        ft.Text(
                            "Questo progetto ha i SAL abilitati. Crea il primo SAL per iniziare il tracciamento dell'avanzamento.",
                            size=11,
                            color=ft.Colors.GREY_400,
                            text_align=ft.TextAlign.CENTER
                        )
                    ],
                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                    spacing=8
                ),
                padding=32,
                alignment=ft.alignment.center
            )
        else:
            # Ordina SAL per sequence_number
            sorted_sals = sorted(self.sals, key=lambda s: s.sequence_number)
            sal_cards = []

            for sal in sorted_sals:
                sal_cards.append(self._create_enhanced_sal_card(sal))

            sals_content = ft.Column(
                controls=sal_cards,
                spacing=10
            )

        self.progress_section = ft.Column(
            controls=[
                progress_header,
                ft.Container(height=12),
                sals_content
            ],
            spacing=0
        )

    def _build_tasks_section(self):
        """Costruisce la sezione attività dirette del progetto"""
        # Separa task dirette (senza deadline) da quelle con deadline
        direct_tasks = [task for task in self.tasks if not task.deadline_id]
        deadline_tasks = [task for task in self.tasks if task.deadline_id]

        task_cards = []
        
        # Header con bottone per creare nuova task
        header = ft.Row([
            ft.Text(
                "Attività del Progetto",
                size=18,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.GREY_800
            ),
            ft.Container(expand=True),
            ft.ElevatedButton(
                content=ft.Row([
                    ft.Icon(ft.Icons.ADD_TASK, size=16),
                    ft.Text("Nuova Attività", size=14)
                ], spacing=4, tight=True),
                on_click=lambda _: self._create_direct_task(),
                bgcolor=ft.Colors.TEAL_600,
                color=ft.Colors.WHITE,
                height=36
            )
        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN)

        # Sezione task dirette
        if direct_tasks:
            task_cards.append(ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Icon(ft.Icons.WORK, size=16, color=ft.Colors.BLUE_600),
                        ft.Text(
                            "Attività Dirette del Progetto",
                            size=14,
                            weight=ft.FontWeight.W_600,
                            color=ft.Colors.GREY_800
                        )
                    ], spacing=6),
                    ft.Container(height=8),
                    ft.Column([
                        self._create_task_card(task) for task in direct_tasks
                    ], spacing=8)
                ]),
                padding=16,
                bgcolor=ft.Colors.BLUE_50,
                border_radius=8,
                border=ft.border.all(1, ft.Colors.BLUE_200)
            ))

        # Sezione task con deadline (se esistono)
        if deadline_tasks:
            task_cards.append(ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Icon(ft.Icons.SCHEDULE, size=16, color=ft.Colors.ORANGE_600),
                        ft.Text(
                            "Attività con Scadenze",
                            size=14,
                            weight=ft.FontWeight.W_600,
                            color=ft.Colors.GREY_800
                        )
                    ], spacing=6),
                    ft.Container(height=8),
                    ft.Column([
                        self._create_task_card(task) for task in deadline_tasks
                    ], spacing=8)
                ]),
                padding=16,
                bgcolor=ft.Colors.ORANGE_50,
                border_radius=8,
                border=ft.border.all(1, ft.Colors.ORANGE_200)
            ))

        # Se non ci sono task
        if not self.tasks:
            empty_state = ft.Container(
                content=ft.Column([
                    ft.Icon(ft.Icons.TASK_ALT, size=48, color=ft.Colors.GREY_400),
                    ft.Text(
                        "Nessuna attività presente",
                        size=16,
                        color=ft.Colors.GREY_500,
                        weight=ft.FontWeight.W_500
                    ),
                    ft.Text(
                        "Crea la prima attività per questo progetto",
                        size=12,
                        color=ft.Colors.GREY_400
                    ),
                    ft.Container(height=16),
                    ft.ElevatedButton(
                        content=ft.Row([
                            ft.Icon(ft.Icons.ADD_TASK, size=16),
                            ft.Text("Crea Prima Attività", size=14)
                        ], spacing=4, tight=True),
                        on_click=lambda _: self._create_direct_task(),
                        bgcolor=ft.Colors.TEAL_600,
                        color=ft.Colors.WHITE
                    )
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=8),
                padding=ft.padding.all(40),
                alignment=ft.alignment.center
            )
            task_cards.append(empty_state)

        self.tasks_section = ft.Column([
            header,
            ft.Container(height=16),
            *task_cards
        ], spacing=12)
    
    def _build_periodic_services_section(self) -> ft.Column:
        """Costruisce la sezione per servizi periodici (progetti senza SAL)"""
        return ft.Column(
            controls=[
                ft.Row(
                    controls=[
                        ft.Text("Servizi Periodici", size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_800),
                        ft.Icon(ft.Icons.SCHEDULE, size=20, color=ft.Colors.GREY_600)
                    ],
                    spacing=8
                ),
                ft.Container(
                    content=ft.Column(
                        controls=[
                            ft.Icon(ft.Icons.CALENDAR_TODAY, size=48, color=ft.Colors.GREY_400),
                            ft.Text("Servizio Periodico", size=14, color=ft.Colors.GREY_600, weight=ft.FontWeight.BOLD),
                            ft.Text(
                                "Questo progetto non ha i SAL abilitati ed è basato su servizi periodici",
                                size=11,
                                color=ft.Colors.GREY_400,
                                text_align=ft.TextAlign.CENTER
                            ),
                            ft.Text(
                                "Utilizza le scadenze per tracciare le attività ricorrenti",
                                size=11,
                                color=ft.Colors.GREY_400,
                                text_align=ft.TextAlign.CENTER
                            )
                        ],
                        horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                        spacing=8
                    ),
                    padding=32,
                    alignment=ft.alignment.center,
                    bgcolor=ft.Colors.GREY_50,
                    border_radius=8,
                    border=ft.border.all(1, ft.Colors.GREY_200)
                )
            ],
            spacing=12
        )

    def _create_enhanced_sal_card(self, sal: SAL) -> ft.Container:
        """Crea una card SAL migliorata con più informazioni"""
        # Calcola il colore della progress bar basato sulla percentuale
        progress_color = ft.Colors.GREEN_600 if sal.percentage >= 80 else ft.Colors.BLUE_600 if sal.percentage >= 50 else ft.Colors.ORANGE_600

        # Status badge
        status_config = {
            ProjectStatus.DRAFT: ("Bozza", ft.Colors.GREY_600, ft.Icons.EDIT),
            ProjectStatus.IN_PROGRESS: ("In Corso", ft.Colors.BLUE_600, ft.Icons.PLAY_CIRCLE),
            ProjectStatus.COMPLETED: ("Completato", ft.Colors.GREEN_600, ft.Icons.CHECK_CIRCLE),
            ProjectStatus.SUSPENDED: ("Sospeso", ft.Colors.ORANGE_600, ft.Icons.PAUSE_CIRCLE)
        }

        status_text, status_color, status_icon = status_config.get(
            sal.status, ("Sconosciuto", ft.Colors.GREY_600, ft.Icons.HELP)
        )

        # Milestone badge se presente
        milestone_badge = None
        if sal.milestone_type:
            milestone_text = {
                SALMilestoneType.PROJECT_START: "Avvio",
                SALMilestoneType.MID_PROGRESS: "Intermedio",
                SALMilestoneType.COMPLETION: "Finale",
                SALMilestoneType.FIRST_DISBURSEMENT: "Prima Erogazione",
                SALMilestoneType.FINAL_DISBURSEMENT: "Saldo Finale",
                SALMilestoneType.REVIEW: "Revisione",
                SALMilestoneType.APPROVAL: "Approvazione"
            }.get(sal.milestone_type, sal.milestone_type.value)

            milestone_badge = ft.Container(
                content=ft.Text(milestone_text, size=9, color=ft.Colors.WHITE, weight=ft.FontWeight.BOLD),
                bgcolor=ft.Colors.PURPLE_600,
                padding=ft.padding.symmetric(horizontal=6, vertical=2),
                border_radius=8
            )

        # Date info
        date_info = []
        if sal.planned_end_date:
            date_info.append(
                ft.Row(
                    controls=[
                        ft.Icon(ft.Icons.SCHEDULE, size=12, color=ft.Colors.GREY_500),
                        ft.Text(f"Scadenza: {sal.planned_end_date.strftime('%d/%m/%Y')}", size=10, color=ft.Colors.GREY_600)
                    ],
                    spacing=4
                )
            )

        if sal.actual_end_date:
            date_info.append(
                ft.Row(
                    controls=[
                        ft.Icon(ft.Icons.CHECK, size=12, color=ft.Colors.GREEN_600),
                        ft.Text(f"Completato: {sal.actual_end_date.strftime('%d/%m/%Y')}", size=10, color=ft.Colors.GREEN_600)
                    ],
                    spacing=4
                )
            )

        return ft.Container(
            content=ft.Column(
                controls=[
                    # Header con numero SAL, nome e status
                    ft.Row(
                        controls=[
                            ft.Row(
                                controls=[
                                    ft.Container(
                                        content=ft.Text(f"#{sal.sequence_number}", size=12, color=ft.Colors.WHITE, weight=ft.FontWeight.BOLD),
                                        bgcolor=ft.Colors.BLUE_600,
                                        padding=ft.padding.symmetric(horizontal=8, vertical=4),
                                        border_radius=8
                                    ),
                                    ft.Text(sal.name, size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_900, expand=True)
                                ],
                                spacing=8,
                                expand=True
                            ),
                            ft.Row(
                                controls=[
                                    *([milestone_badge] if milestone_badge else []),
                                    ft.Container(
                                        content=ft.Row(
                                            controls=[
                                                ft.Icon(status_icon, size=12, color=ft.Colors.WHITE),
                                                ft.Text(status_text, size=10, color=ft.Colors.WHITE, weight=ft.FontWeight.BOLD)
                                            ],
                                            spacing=4,
                                            tight=True
                                        ),
                                        bgcolor=status_color,
                                        padding=ft.padding.symmetric(horizontal=8, vertical=4),
                                        border_radius=10
                                    )
                                ],
                                spacing=6
                            )
                        ],
                        alignment=ft.MainAxisAlignment.SPACE_BETWEEN
                    ),

                    # Progress bar
                    ft.Column(
                        controls=[
                            ft.Row(
                                controls=[
                                    ft.Text("Avanzamento", size=11, color=ft.Colors.GREY_600),
                                    ft.Text(f"{sal.percentage}%", size=11, weight=ft.FontWeight.BOLD, color=progress_color)
                                ],
                                alignment=ft.MainAxisAlignment.SPACE_BETWEEN
                            ),
                            ft.ProgressBar(
                                value=sal.percentage / 100,
                                color=progress_color,
                                bgcolor=ft.Colors.GREY_200,
                                height=6
                            )
                        ],
                        spacing=4
                    ),

                    # Descrizione se presente
                    *([ft.Text(sal.description, size=11, color=ft.Colors.GREY_600, max_lines=2)] if sal.description else []),

                    # Informazioni date
                    *date_info,

                    # Budget se presente
                    *([ft.Row(
                        controls=[
                            ft.Icon(ft.Icons.EURO, size=14, color=ft.Colors.GREEN_600),
                            ft.Text(f"Budget: €{sal.budget:,.2f}", size=12, weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN_600),
                            *([ft.Text(f"| Speso: €{sal.actual_cost:,.2f}", size=12, color=ft.Colors.ORANGE_600)] if sal.actual_cost else [])
                        ],
                        spacing=4
                    )] if sal.budget else []),

                    # Indicatori speciali
                    ft.Row(
                        controls=[
                            *([ft.Container(
                                content=ft.Text("Auto-generato", size=9, color=ft.Colors.WHITE),
                                bgcolor=ft.Colors.GREY_500,
                                padding=ft.padding.symmetric(horizontal=6, vertical=2),
                                border_radius=6
                            )] if sal.auto_generated else []),
                            *([ft.Container(
                                content=ft.Text("Richiede Approvazione", size=9, color=ft.Colors.WHITE),
                                bgcolor=ft.Colors.RED_500,
                                padding=ft.padding.symmetric(horizontal=6, vertical=2),
                                border_radius=6
                            )] if sal.requires_approval else []),
                            *([ft.Container(
                                content=ft.Text(f"Approvato da {sal.approved_by}", size=9, color=ft.Colors.WHITE),
                                bgcolor=ft.Colors.GREEN_500,
                                padding=ft.padding.symmetric(horizontal=6, vertical=2),
                                border_radius=6
                            )] if sal.approved_by else [])
                        ],
                        spacing=4,
                        wrap=True
                    )
                ],
                spacing=8
            ),
            bgcolor=ft.Colors.WHITE,
            padding=12,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )

    def _create_sal_card(self, sal: SAL) -> ft.Container:
        """Crea una card SAL moderna"""
        # Calcola il colore della progress bar basato sulla percentuale
        progress_color = ft.Colors.GREEN_600 if sal.percentage >= 80 else ft.Colors.BLUE_600 if sal.percentage >= 50 else ft.Colors.ORANGE_600

        return ft.Container(
            content=ft.Column(
                controls=[
                    ft.Row(
                        controls=[
                            ft.Container(
                                content=ft.Text(f"#{sal.number}", size=12, color=ft.Colors.WHITE, weight=ft.FontWeight.BOLD),
                                bgcolor=ft.Colors.BLUE_600,
                                padding=ft.padding.symmetric(horizontal=8, vertical=4),
                                border_radius=8
                            ),
                            ft.Text(sal.date.strftime('%d/%m/%Y'), size=12, color=ft.Colors.GREY_500)
                        ],
                        alignment=ft.MainAxisAlignment.SPACE_BETWEEN
                    ),
                    # Progress bar
                    ft.Column(
                        controls=[
                            ft.Row(
                                controls=[
                                    ft.Text("Avanzamento", size=11, color=ft.Colors.GREY_600),
                                    ft.Text(f"{sal.percentage}%", size=11, weight=ft.FontWeight.BOLD, color=progress_color)
                                ],
                                alignment=ft.MainAxisAlignment.SPACE_BETWEEN
                            ),
                            ft.ProgressBar(
                                value=sal.percentage / 100,
                                color=progress_color,
                                bgcolor=ft.Colors.GREY_200,
                                height=6
                            )
                        ],
                        spacing=4
                    ),
                    # Descrizione se presente
                    *([ft.Text(sal.description, size=11, color=ft.Colors.GREY_600, max_lines=2)] if sal.description else []),
                    # Importo se presente
                    *([ft.Row(
                        controls=[
                            ft.Icon(ft.Icons.EURO, size=14, color=ft.Colors.GREEN_600),
                            ft.Text(f"{sal.amount:,.2f}", size=12, weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN_600)
                        ],
                        spacing=4
                    )] if sal.amount else [])
                ],
                spacing=8
            ),
            bgcolor=ft.Colors.WHITE,
            padding=12,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )
    
    def _build_deadlines_section(self):
        """Costruisce la sezione scadenze"""
        if not self.deadlines:
            deadlines_content = ft.Container(
                content=ft.Column(
                    controls=[
                        ft.Icon(ft.Icons.SCHEDULE_OUTLINED, size=48, color=ft.Colors.GREY_400),
                        ft.Text("Nessuna scadenza registrata", size=14, color=ft.Colors.GREY_600, weight=ft.FontWeight.BOLD),
                        ft.Text("Aggiungi scadenze per tenere traccia delle date importanti", size=11, color=ft.Colors.GREY_400)
                    ],
                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                    spacing=8
                ),
                padding=32,
                alignment=ft.alignment.center
            )
        else:
            # Ordina per data di scadenza
            sorted_deadlines = sorted(self.deadlines, key=lambda d: d.due_date)
            deadline_cards = []
            
            for deadline in sorted_deadlines:
                deadline_cards.append(self._create_deadline_card(deadline))
            
            deadlines_content = ft.Column(
                controls=deadline_cards,
                spacing=10,
                scroll=ft.ScrollMode.AUTO
            )
        
        self.deadlines_section = ft.Column(
            controls=[
                ft.Row(
                    controls=[
                        ft.Text("Scadenze Progetto", size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_800),
                        ft.ElevatedButton(
                            "Nuova Scadenza",
                            icon=ft.Icons.ADD_OUTLINED,
                            on_click=lambda _: self._create_deadline(),
                            bgcolor=ft.Colors.BLUE_600,
                            color=ft.Colors.WHITE,
                            height=32
                        )
                    ],
                    alignment=ft.MainAxisAlignment.SPACE_BETWEEN
                ),
                deadlines_content
            ],
            spacing=12
        )
    
    def _create_deadline_card(self, deadline: Deadline) -> ft.Container:
        """Crea una card scadenza moderna"""
        status_config = {
            DeadlineStatus.PENDING: ("In Attesa", ft.Colors.ORANGE_600, ft.Icons.SCHEDULE),
            DeadlineStatus.COMPLETED: ("Completata", ft.Colors.GREEN_600, ft.Icons.CHECK_CIRCLE),
            DeadlineStatus.OVERDUE: ("Scaduta", ft.Colors.RED_600, ft.Icons.ERROR)
        }

        status_text, status_color, status_icon = status_config.get(
            deadline.status,
            ("Sconosciuto", ft.Colors.GREY_600, ft.Icons.HELP_OUTLINE)
        )

        days_until = (deadline.due_date - date.today()).days
        urgency_text = ""
        urgency_color = ft.Colors.GREY_700
        urgency_icon = ft.Icons.CALENDAR_TODAY

        if days_until < 0:
            urgency_text = f"Scaduta da {abs(days_until)} giorni"
            urgency_color = ft.Colors.RED_600
            urgency_icon = ft.Icons.ERROR_OUTLINE
        elif days_until == 0:
            urgency_text = "Scade oggi"
            urgency_color = ft.Colors.RED_600
            urgency_icon = ft.Icons.WARNING
        elif days_until <= 7:
            urgency_text = f"Scade tra {days_until} giorni"
            urgency_color = ft.Colors.ORANGE_600
            urgency_icon = ft.Icons.SCHEDULE
        else:
            urgency_text = f"Scade il {deadline.due_date.strftime('%d/%m/%Y')}"
            urgency_color = ft.Colors.GREY_600

        return ft.Container(
            content=ft.Column(
                controls=[
                    ft.Row(
                        controls=[
                            ft.Text(deadline.title, size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_800, expand=True),
                            ft.Container(
                                content=ft.Row(
                                    controls=[
                                        ft.Icon(status_icon, size=12, color=ft.Colors.WHITE),
                                        ft.Text(status_text, size=10, color=ft.Colors.WHITE, weight=ft.FontWeight.BOLD)
                                    ],
                                    spacing=4,
                                    tight=True
                                ),
                                bgcolor=status_color,
                                padding=ft.padding.symmetric(horizontal=8, vertical=4),
                                border_radius=10
                            )
                        ],
                        alignment=ft.MainAxisAlignment.SPACE_BETWEEN
                    ),
                    *([ft.Text(deadline.description, size=11, color=ft.Colors.GREY_600, max_lines=2)] if deadline.description else []),
                    ft.Row(
                        controls=[
                            ft.Icon(urgency_icon, size=14, color=urgency_color),
                            ft.Text(urgency_text, size=11, color=urgency_color, weight=ft.FontWeight.BOLD)
                        ],
                        spacing=6
                    )
                ],
                spacing=6
            ),
            bgcolor=ft.Colors.WHITE,
            padding=12,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )
    
    def _build_documents_section(self):
        """Costruisce la sezione documenti"""
        if not self.documents:
            documents_content = ft.Container(
                content=ft.Column(
                    controls=[
                        ft.Icon(ft.Icons.FOLDER_OUTLINED, size=48, color=ft.Colors.GREY_400),
                        ft.Text("Nessun documento caricato", size=14, color=ft.Colors.GREY_600, weight=ft.FontWeight.BOLD),
                        ft.Text("Carica documenti per organizzare i file del progetto", size=11, color=ft.Colors.GREY_400)
                    ],
                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                    spacing=8
                ),
                padding=32,
                alignment=ft.alignment.center
            )
        else:
            document_cards = []
            for document in self.documents:
                document_cards.append(self._create_document_card(document))
            
            documents_content = ft.Column(
                controls=document_cards,
                spacing=10,
                scroll=ft.ScrollMode.AUTO
            )
        
        self.documents_section = ft.Column(
            controls=[
                ft.Row(
                    controls=[
                        ft.Text("Documenti Progetto", size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_800),
                        ft.ElevatedButton(
                            "Carica Documento",
                            icon=ft.Icons.UPLOAD_FILE_OUTLINED,
                            on_click=lambda _: self._upload_document(),
                            bgcolor=ft.Colors.BLUE_600,
                            color=ft.Colors.WHITE,
                            height=32
                        )
                    ],
                    alignment=ft.MainAxisAlignment.SPACE_BETWEEN
                ),
                documents_content
            ],
            spacing=12
        )
    
    def _create_document_card(self, document: Document) -> ft.Card:
        """Crea una card documento migliorata"""
        # Determina l'icona in base al tipo di file
        file_extension = document.file_path.split('.')[-1].lower() if '.' in document.file_path else ''

        icon_map = {
            'pdf': (ft.Icons.PICTURE_AS_PDF, ft.Colors.RED_600),
            'doc': (ft.Icons.DESCRIPTION, ft.Colors.BLUE_600),
            'docx': (ft.Icons.DESCRIPTION, ft.Colors.BLUE_600),
            'xls': (ft.Icons.TABLE_CHART, ft.Colors.GREEN_600),
            'xlsx': (ft.Icons.TABLE_CHART, ft.Colors.GREEN_600),
            'jpg': (ft.Icons.IMAGE, ft.Colors.PURPLE_600),
            'jpeg': (ft.Icons.IMAGE, ft.Colors.PURPLE_600),
            'png': (ft.Icons.IMAGE, ft.Colors.PURPLE_600),
            'zip': (ft.Icons.ARCHIVE, ft.Colors.ORANGE_600),
            'txt': (ft.Icons.TEXT_SNIPPET, ft.Colors.GREY_600)
        }

        icon, icon_color = icon_map.get(file_extension, (ft.Icons.DESCRIPTION, ft.Colors.BLUE_600))

        # Formatta la dimensione del file
        file_size_mb = document.file_size / (1024 * 1024)
        if file_size_mb < 1:
            size_text = f"{document.file_size / 1024:.1f} KB"
        else:
            size_text = f"{file_size_mb:.1f} MB"

        # Tags
        tags_row = None
        if document.tags:
            tag_chips = []
            for tag in document.tags[:3]:  # Mostra max 3 tag
                tag_chips.append(
                    ft.Container(
                        content=ft.Text(tag, size=10, color=ft.Colors.WHITE),
                        bgcolor=ft.Colors.BLUE_400,
                        padding=ft.padding.symmetric(horizontal=8, vertical=2),
                        border_radius=10
                    )
                )

            if len(document.tags) > 3:
                tag_chips.append(
                    ft.Container(
                        content=ft.Text(f"+{len(document.tags) - 3}", size=10, color=ft.Colors.WHITE),
                        bgcolor=ft.Colors.GREY_400,
                        padding=ft.padding.symmetric(horizontal=8, vertical=2),
                        border_radius=10
                    )
                )

            tags_row = ft.Row(tag_chips, spacing=4, wrap=True)

        return ft.Card(
            content=ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Icon(icon, size=32, color=icon_color),
                        ft.Column([
                            ft.Text(document.name, size=14, weight=ft.FontWeight.BOLD, max_lines=2),
                            ft.Text(document.description or "Nessuna descrizione",
                                   size=12, color=ft.Colors.GREY_600, max_lines=2),
                        ], spacing=4, expand=True),
                        ft.Column([
                            ft.IconButton(
                                icon=ft.Icons.PREVIEW,
                                tooltip="Anteprima documento",
                                icon_size=20,
                                icon_color=ft.Colors.BLUE_600,
                                on_click=lambda _, doc=document: self._preview_document(doc)
                            ),
                            ft.IconButton(
                                icon=ft.Icons.OPEN_IN_NEW,
                                tooltip="Apri documento",
                                icon_size=20,
                                on_click=lambda _, doc=document: self._download_document(doc)
                            ),
                            ft.IconButton(
                                icon=ft.Icons.DELETE_OUTLINE,
                                tooltip="Elimina documento",
                                icon_size=20,
                                icon_color=ft.Colors.RED_400,
                                on_click=lambda _, doc=document: self._delete_document(doc)
                            )
                        ], spacing=0)
                    ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),

                    ft.Divider(height=8, color=ft.Colors.GREY_300),

                    ft.Row([
                        ft.Text(f"📅 {document.created_at.strftime('%d/%m/%Y %H:%M')}",
                               size=10, color=ft.Colors.GREY_500),
                        ft.Text(f"📦 {size_text}", size=10, color=ft.Colors.GREY_500),
                        ft.Text(f"📄 {file_extension.upper()}", size=10, color=icon_color)
                    ], spacing=16),

                    tags_row if tags_row else ft.Container(height=0)
                ], spacing=8),
                padding=16
            ),
            elevation=2,
            surface_tint_color=ft.Colors.BLUE_50
        )
    
    def _build_alerts_section(self):
        """Costruisce la sezione avvisi"""
        if not self.alerts:
            alerts_content = ft.Container(
                content=ft.Column(
                    controls=[
                        ft.Icon(ft.Icons.NOTIFICATIONS_OUTLINED, size=48, color=ft.Colors.GREY_400),
                        ft.Text("Nessun avviso attivo", size=14, color=ft.Colors.GREY_600, weight=ft.FontWeight.BOLD),
                        ft.Text("Gli avvisi importanti appariranno qui", size=11, color=ft.Colors.GREY_400)
                    ],
                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                    spacing=8
                ),
                padding=32,
                alignment=ft.alignment.center
            )
        else:
            alert_cards = []
            for alert in self.alerts:
                alert_cards.append(self._create_alert_card(alert))
            
            alerts_content = ft.Column(
                controls=alert_cards,
                spacing=10,
                scroll=ft.ScrollMode.AUTO
            )
        
        self.alerts_section = ft.Column(
            controls=[
                ft.Text("Avvisi Progetto", size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_800),
                alerts_content
            ],
            spacing=12
        )
    
    def _create_alert_card(self, alert: Alert) -> ft.Card:
        """Crea una card avviso"""
        status_color = {
            AlertStatus.ACTIVE: ft.Colors.RED,
            AlertStatus.RESOLVED: ft.Colors.GREEN,
            AlertStatus.DISMISSED: ft.Colors.GREY
        }.get(alert.status, ft.Colors.GREY)
        
        return ft.Card(
            content=ft.Container(
                content=ft.Row(
                    controls=[
                        ft.Icon(ft.Icons.WARNING, size=24, color=status_color),
                        ft.Column(
                            controls=[
                                ft.Text(alert.title, size=14, weight=ft.FontWeight.BOLD),
                                ft.Text(alert.message, size=12, color=ft.Colors.GREY_600),
                                ft.Text(f"Creato il: {alert.created_at.strftime('%d/%m/%Y %H:%M')}", size=10, color=ft.Colors.GREY_500)
                            ],
                            spacing=2,
                            expand=True
                        ),
                        ft.Container(
                            content=ft.Text(
                                alert.status.value if hasattr(alert.status, 'value') else str(alert.status),
                                size=10,
                                color=ft.Colors.WHITE,
                                weight=ft.FontWeight.BOLD
                            ),
                            bgcolor=status_color,
                            padding=ft.padding.symmetric(horizontal=8, vertical=4),
                            border_radius=12
                        )
                    ],
                    alignment=ft.MainAxisAlignment.SPACE_BETWEEN
                ),
                padding=15
            ),
            elevation=1
        )
    
    def _create_task_card(self, task) -> ft.Container:
        """Crea una card per una task"""
        from core.models.base_models import TaskStatus, Priority
        
        # Status colors
        status_colors = {
            TaskStatus.PENDING: ft.Colors.ORANGE_100,
            TaskStatus.IN_PROGRESS: ft.Colors.BLUE_100,
            TaskStatus.WAITING: ft.Colors.YELLOW_100,
            TaskStatus.COMPLETED: ft.Colors.GREEN_100,
            TaskStatus.CANCELLED: ft.Colors.RED_100
        }
        
        # Priority colors
        priority_colors = {
            Priority.LOW: ft.Colors.GREEN,
            Priority.MEDIUM: ft.Colors.BLUE,
            Priority.HIGH: ft.Colors.ORANGE,
            Priority.CRITICAL: ft.Colors.RED
        }
        
        # Status text
        status_text = {
            TaskStatus.PENDING: "In Attesa",
            TaskStatus.IN_PROGRESS: "In Corso",
            TaskStatus.WAITING: "In Pausa",
            TaskStatus.COMPLETED: "Completato",
            TaskStatus.CANCELLED: "Annullato"
        }
        
        # Handle both enum and string values
        task_status = task.status if hasattr(task.status, 'value') else TaskStatus(str(task.status)) if isinstance(task.status, str) else task.status
        task_priority = task.priority if hasattr(task.priority, 'value') else Priority(str(task.priority)) if isinstance(task.priority, str) else task.priority
        
        return ft.Container(
            content=ft.Column([
                # Header row
                ft.Row([
                    ft.Row([
                        ft.Container(
                            content=ft.Icon(
                                ft.Icons.CIRCLE,
                                size=8,
                                color=priority_colors.get(task_priority, ft.Colors.GREY)
                            ),
                            tooltip=f"Priorità: {(task_priority.value if hasattr(task_priority, 'value') else str(task_priority)).title()}"
                        ),
                        ft.Text(
                            task.title,
                            size=14,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.GREY_800,
                            expand=True
                        )
                    ], spacing=8, expand=True),
                    
                    ft.Container(
                        content=ft.Text(
                            status_text.get(task_status, (task_status.value if hasattr(task_status, 'value') else str(task_status)).title()),
                            size=11,
                            color=ft.Colors.GREY_700,
                            weight=ft.FontWeight.W_500
                        ),
                        bgcolor=status_colors.get(task_status, ft.Colors.GREY_100),
                        padding=ft.padding.symmetric(horizontal=8, vertical=4),
                        border_radius=12
                    ),
                    
                    ft.PopupMenuButton(
                        icon=ft.Icons.MORE_VERT,
                        items=[
                            ft.PopupMenuItem(
                                text="Modifica",
                                icon=ft.Icons.EDIT,
                                on_click=lambda _, t=task: self._edit_task(t)
                            ),
                            ft.PopupMenuItem(
                                text="Completa" if task_status != TaskStatus.COMPLETED else "Riapri",
                                icon=ft.Icons.CHECK_CIRCLE if task_status != TaskStatus.COMPLETED else ft.Icons.REPLAY,
                                on_click=lambda _, t=task: self._toggle_task_completion(t)
                            ),
                            ft.PopupMenuItem(),  # Divider
                            ft.PopupMenuItem(
                                text="Elimina",
                                icon=ft.Icons.DELETE,
                                on_click=lambda _, t=task: self._delete_task(t)
                            )
                        ]
                    )
                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                
                # Description
                ft.Text(
                    task.description or "Nessuna descrizione",
                    size=12,
                    color=ft.Colors.GREY_600,
                    max_lines=2
                ) if task.description else ft.Container(),
                
                # Progress bar
                ft.Container(
                    content=ft.ProgressBar(
                        value=task.progress_percentage / 100 if task.progress_percentage else 0,
                        color=ft.Colors.BLUE,
                        bgcolor=ft.Colors.GREY_200
                    ),
                    width=None
                ) if task.progress_percentage and task.progress_percentage > 0 else ft.Container(),
                
                # Details row
                ft.Row([
                    ft.Row([
                        ft.Icon(ft.Icons.ACCESS_TIME, size=14, color=ft.Colors.GREY_500),
                        ft.Text(
                            f"{task.estimated_hours}h" if task.estimated_hours else "N/A",
                            size=11,
                            color=ft.Colors.GREY_600
                        )
                    ], spacing=4) if task.estimated_hours else ft.Container(),
                    
                    ft.Container(expand=True),
                    
                    ft.Text(
                        task.due_date.strftime("%d/%m") if task.due_date else "",
                        size=11,
                        color=ft.Colors.ORANGE_600 if task.due_date and task.due_date < date.today() else ft.Colors.GREY_600,
                        weight=ft.FontWeight.BOLD if task.due_date and task.due_date < date.today() else ft.FontWeight.NORMAL
                    ) if task.due_date else ft.Container()
                ], spacing=12, alignment=ft.MainAxisAlignment.START)
            ], spacing=6),
            padding=ft.padding.all(10),
            margin=ft.margin.only(bottom=4),
            bgcolor=ft.Colors.WHITE,
            border_radius=6,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )

    def _create_direct_task(self):
        """Crea una nuova task direttamente collegata al progetto"""
        from ui.views.tasks import TasksView
        
        # Create a TasksView instance to use its form
        tasks_view = TasksView(self.app)
        
        # Set the current projects data so the form can show this project
        tasks_view.current_projects = {self.project_id: self.project}
        tasks_view.current_deadlines = {}  # No deadlines needed for direct tasks
        
        # Show the form with no deadline pre-selected to enable direct project mode
        tasks_view._show_task_form()

    def _edit_task(self, task):
        """Modifica una task esistente"""
        from ui.views.tasks import TasksView
        
        tasks_view = TasksView(self.app)
        tasks_view.current_projects = {self.project_id: self.project}
        tasks_view.current_deadlines = {}
        tasks_view._show_task_form(task=task)

    def _toggle_task_completion(self, task):
        """Cambia lo stato di completamento di una task"""
        from core.models.base_models import TaskStatus
        
        try:
            current_status = task.status.value if hasattr(task.status, 'value') else str(task.status)
            
            if current_status == "completato":
                task.status = TaskStatus.IN_PROGRESS
                task.progress_percentage = max(0, task.progress_percentage - 10)
                message = f"Attività '{task.title}' riaperta"
            else:
                task.status = TaskStatus.COMPLETED
                task.progress_percentage = 100
                message = f"Attività '{task.title}' completata!"
            
            task.updated_at = datetime.now()
            success = self.app.db.update_task(task)

            if success:
                self._rebuild_view()
                self._show_success_dialog(message)
                logger.info(f"Task status changed: {task.title}")
            else:
                self._show_error_dialog("Errore durante l'aggiornamento dell'attività")
            
        except Exception as e:
            logger.error(f"Errore toggle completamento task: {e}")
            self._show_error_dialog(f"Errore imprevisto: {str(e)}")

    def _delete_task(self, task):
        """Elimina una task con conferma"""
        def confirm_delete(e):
            self.page.close(confirm_dialog)
            
            try:
                success = self.app.db.delete_task(task.id)

                if success:
                    self._rebuild_view()
                    self._show_success_dialog(f"Attività '{task.title}' eliminata con successo!")
                    logger.info(f"Task eliminata: {task.title}")
                else:
                    self._show_error_dialog("Errore durante l'eliminazione dell'attività")
                    
            except Exception as ex:
                logger.error(f"Errore eliminazione task: {ex}")
                self._show_error_dialog(f"Errore imprevisto: {str(ex)}")
        
        def cancel_delete(e):
            self.page.close(confirm_dialog)
        
        confirm_dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text("Conferma eliminazione", color=ft.Colors.RED),
            content=ft.Text(f"Sei sicuro di voler eliminare l'attività '{task.title}'?\n\nQuesta azione non può essere annullata."),
            actions=[
                ft.TextButton("Annulla", on_click=cancel_delete),
                ft.ElevatedButton(
                    "Elimina",
                    on_click=confirm_delete,
                    bgcolor=ft.Colors.RED,
                    color=ft.Colors.WHITE
                )
            ],
            actions_alignment=ft.MainAxisAlignment.END
        )
        
        self.page.open(confirm_dialog)

    def _edit_project(self):
        """Apre il form di modifica progetto"""
        try:
            # Navigate back to projects view and open edit form
            self.app.main_layout.go_back()

            # Get the projects view and open edit form
            projects_view = self.app.main_layout._get_view("projects")
            if projects_view and hasattr(projects_view, '_show_project_form'):
                projects_view._show_project_form(self.project)
                logger.info(f"Apertura form modifica progetto: {self.project.name}")
            else:
                logger.error("Projects view not available or missing _show_project_form method")

        except Exception as e:
            logger.error(f"Errore apertura form modifica progetto: {e}")

    def _create_deadline(self):
        """Apre il form di creazione scadenza"""
        try:
            # Navigate to deadlines view with project pre-selected
            self.app.main_layout._navigate_to("deadlines")

            # Get the deadlines view and open create form
            deadlines_view = self.app.main_layout._get_view("deadlines")
            if deadlines_view and hasattr(deadlines_view, '_show_deadline_form'):
                # Pass project info to pre-fill the form
                deadlines_view._show_deadline_form(project_id=self.project_id)
                logger.info(f"Apertura form creazione scadenza per progetto: {self.project.name}")
            else:
                logger.info(f"Navigazione a scadenze per progetto: {self.project.name}")

        except Exception as e:
            logger.error(f"Errore apertura form scadenza: {e}")

    def _create_sal(self):
        """Apre il form di creazione SAL"""
        try:
            # Navigate to SALs view
            self.app.main_layout._navigate_to("sals")

            # Get the SALs view and open create form
            sals_view = self.app.main_layout._get_view("sals")
            if sals_view and hasattr(sals_view, '_show_sal_form'):
                # Set the project filter to this project before opening form
                if hasattr(sals_view, 'selected_project_filter'):
                    sals_view.selected_project_filter = str(self.project_id)
                    if hasattr(sals_view, '_apply_filters'):
                        sals_view._apply_filters()

                # Open the SAL form
                sals_view._show_sal_form()
                logger.info(f"Apertura form creazione SAL per progetto: {self.project.name}")
            else:
                logger.info(f"Navigazione a SAL per progetto: {self.project.name}")

        except Exception as e:
            logger.error(f"Errore apertura form SAL: {e}")



    def _rebuild_view(self):
        """Ricostruisce completamente la vista per aggiornare i contenuti"""
        try:
            # Ricarica i dati
            self._load_data()

            # Ricostruisci il contenuto principale
            new_content = self.build()

            # Aggiorna il content area del layout principale
            self.app.main_layout.content_area.content = new_content
            self.app.main_layout.page.update()

            logger.debug("Vista progetto ricostruita con successo")

        except Exception as e:
            logger.error(f"Errore ricostruzione vista: {e}")

    def _upload_document(self):
        """Apre il dialog di caricamento documento"""
        try:
            # File picker dialog
            def on_file_selected(e):
                if e.files:
                    file_path = e.files[0].path
                    file_name = e.files[0].name

                    # Clear overlay after file selection to avoid conflicts
                    self.page.overlay.clear()
                    self.page.update()

                    self._show_document_upload_dialog(file_path, file_name)

            file_picker = ft.FilePicker(on_result=on_file_selected)
            self.page.overlay.append(file_picker)
            self.page.update()

            # Apri il file picker
            file_picker.pick_files(
                dialog_title="Seleziona documento da caricare",
                allow_multiple=False,
                allowed_extensions=["pdf", "doc", "docx", "txt", "xls", "xlsx", "jpg", "png", "zip"]
            )

        except Exception as e:
            logger.error(f"Errore apertura file picker: {e}")
            self._show_error_dialog(f"Errore apertura file picker: {str(e)}")

    def _show_document_upload_dialog(self, file_path: str, file_name: str):
        """Mostra il dialog per configurare l'upload del documento"""
        try:
            # Campi del form
            name_field = ft.TextField(
                label="Nome documento",
                value=file_name,
                width=400
            )

            description_field = ft.TextField(
                label="Descrizione",
                multiline=True,
                min_lines=3,
                max_lines=5,
                width=400
            )

            tags_field = ft.TextField(
                label="Tag (separati da virgola)",
                hint_text="es: contratto, importante, finale",
                width=400
            )

            def upload_document(e):
                try:
                    # Chiudi dialog
                    if self.page.dialog:
                        self.page.dialog.open = False
                        self.page.update()

                    # Mostra loading
                    self._show_loading_dialog("Caricamento documento in corso...")

                    # Prepara i dati
                    name = name_field.value.strip() or file_name
                    description = description_field.value.strip() or None
                    tags = [tag.strip() for tag in tags_field.value.split(",") if tag.strip()] if tags_field.value else []

                    # Carica il documento usando il servizio
                    from core.services.document_service import DocumentService
                    doc_service = DocumentService(self.app.config.data_dir, self.app.db)

                    document = doc_service.upload_document(
                        file_path=file_path,
                        name=name,
                        description=description,
                        project_id=self.project.id,
                        tags=tags
                    )

                    if document:
                        # Ricarica i documenti e ricostruisci la vista
                        self.documents = self.app.db.get_documents_by_project(self.project_id)
                        self._rebuild_view()

                        self._show_success_dialog(f"Documento '{name}' caricato con successo!")
                    else:
                        self._show_error_dialog("Errore durante il caricamento del documento")

                except Exception as ex:
                    logger.error(f"Errore upload documento: {ex}")
                    self._show_error_dialog(f"Errore upload: {str(ex)}")

            def cancel_upload(e):
                if self.page.dialog:
                    self.page.dialog.open = False
                    self.page.update()

            # Dialog di upload (NON-MODAL per evitare grey screen)
            upload_dialog = ft.AlertDialog(
                modal=False,  # NON-MODAL per evitare grey screen
                title=ft.Text("Carica Documento", weight=ft.FontWeight.BOLD),
                content=ft.Container(
                    content=ft.Column([
                        ft.Text(f"File selezionato: {file_name}", size=12, color=ft.Colors.GREY_600),
                        ft.Divider(height=10),
                        name_field,
                        description_field,
                        tags_field
                    ], spacing=16, tight=True),
                    width=450,
                    height=300
                ),
                actions=[
                    ft.TextButton("Annulla", on_click=cancel_upload),
                    ft.ElevatedButton(
                        text="Carica",
                        icon=ft.Icons.UPLOAD,
                        on_click=upload_document,
                        bgcolor=ft.Colors.BLUE_600,
                        color=ft.Colors.WHITE
                    )
                ],
                actions_alignment=ft.MainAxisAlignment.END
            )

            # Use page.dialog instead of overlay to avoid conflicts
            self.page.dialog = upload_dialog
            upload_dialog.open = True
            self.page.update()

        except Exception as e:
            logger.error(f"Errore dialog upload: {e}")
            self._show_error_dialog(f"Errore: {str(e)}")

    def _download_document(self, document: Document):
        """Scarica un documento"""
        try:
            from core.services.document_service import DocumentService
            doc_service = DocumentService(self.app.config.data_dir, self.app.db)

            file_path = doc_service.get_document_path(document)

            if file_path.exists():
                # Apri il file con l'applicazione predefinita del sistema
                import subprocess
                import platform

                if platform.system() == 'Windows':
                    subprocess.run(['start', str(file_path)], shell=True)
                elif platform.system() == 'Darwin':  # macOS
                    subprocess.run(['open', str(file_path)])
                else:  # Linux
                    subprocess.run(['xdg-open', str(file_path)])

                logger.info(f"Documento aperto: {document.name}")
            else:
                self._show_error_dialog(f"File non trovato: {document.name}")

        except Exception as e:
            logger.error(f"Errore apertura documento: {e}")
            self._show_error_dialog(f"Errore apertura documento: {str(e)}")

    def _delete_document(self, document: Document):
        """Elimina un documento"""
        def confirm_delete(e):
            try:
                if self.page.dialog:
                    self.page.dialog.open = False
                    self.page.update()

                from core.services.document_service import DocumentService
                doc_service = DocumentService(self.app.config.data_dir, self.app.db)

                if doc_service.delete_document(document.id):
                    # Ricarica i documenti e ricostruisci la vista
                    self.documents = self.app.db.get_documents_by_project(self.project_id)
                    self._rebuild_view()

                    self._show_success_dialog(f"Documento '{document.name}' eliminato con successo!")
                else:
                    self._show_error_dialog("Errore durante l'eliminazione del documento")

            except Exception as ex:
                logger.error(f"Errore eliminazione documento: {ex}")
                self._show_error_dialog(f"Errore eliminazione: {str(ex)}")

        def cancel_delete(e):
            if self.page.dialog:
                self.page.dialog.open = False
                self.page.update()

        # Dialog di conferma (NON-MODAL per evitare grey screen)
        confirm_dialog = ft.AlertDialog(
            modal=False,  # NON-MODAL per evitare grey screen
            title=ft.Text("Conferma Eliminazione", weight=ft.FontWeight.BOLD),
            content=ft.Text(f"Sei sicuro di voler eliminare il documento '{document.name}'?\n\nQuesta operazione non può essere annullata."),
            actions=[
                ft.TextButton("Annulla", on_click=cancel_delete),
                ft.ElevatedButton(
                    text="Elimina",
                    icon=ft.Icons.DELETE,
                    on_click=confirm_delete,
                    bgcolor=ft.Colors.RED_600,
                    color=ft.Colors.WHITE
                )
            ],
            actions_alignment=ft.MainAxisAlignment.END
        )

        # Use page.dialog instead of overlay to avoid conflicts
        self.page.dialog = confirm_dialog
        confirm_dialog.open = True
        self.page.update()

    def _show_loading_dialog(self, message: str):
        """Mostra una notifica di loading usando SnackBar"""
        self.page.open(
            ft.SnackBar(
                content=ft.Row([
                    ft.ProgressRing(width=16, height=16, stroke_width=2, color=ft.Colors.WHITE),
                    ft.Text(message, color=ft.Colors.WHITE, size=14)
                ], spacing=16, alignment=ft.MainAxisAlignment.CENTER),
                bgcolor=ft.Colors.BLUE_600,
                duration=2000
            )
        )

    def _show_success_dialog(self, message: str):
        """Mostra una notifica di successo usando SnackBar"""
        self.page.open(
            ft.SnackBar(
                content=ft.Row([
                    ft.Icon(ft.Icons.CHECK_CIRCLE, color=ft.Colors.WHITE, size=20),
                    ft.Text(message, color=ft.Colors.WHITE, size=14)
                ], spacing=8),
                bgcolor=ft.Colors.GREEN_600,
                duration=4000,
                action="OK",
                action_color=ft.Colors.WHITE
            )
        )

    def _show_error_dialog(self, message: str):
        """Mostra una notifica di errore usando SnackBar"""
        self.page.open(
            ft.SnackBar(
                content=ft.Row([
                    ft.Icon(ft.Icons.ERROR, color=ft.Colors.WHITE, size=20),
                    ft.Text(message, color=ft.Colors.WHITE, size=14)
                ], spacing=8),
                bgcolor=ft.Colors.RED_600,
                duration=5000,
                action="OK",
                action_color=ft.Colors.WHITE
            )
        )

    def _rebuild_view(self):
        """Ricostruisce completamente la vista per aggiornare i contenuti"""
        try:
            # Ricostruisci il contenuto principale
            new_content = self.build()

            # Aggiorna il content area del layout principale
            self.app.main_layout.content_area.content = new_content
            self.app.main_layout.page.update()

            logger.debug("Vista progetto ricostruita con successo")

        except Exception as e:
            logger.error(f"Errore ricostruzione vista: {e}")

    def _preview_document(self, document: Document):
        """Mostra un'anteprima del documento"""
        try:
            from core.services.document_service import DocumentService
            doc_service = DocumentService(self.app.config.data_dir, self.app.db)

            file_path = doc_service.get_document_path(document)

            if not file_path.exists():
                self._show_error_dialog(f"File non trovato: {document.name}")
                return

            # Determina il tipo di anteprima in base all'estensione
            file_extension = document.file_path.split('.')[-1].lower() if '.' in document.file_path else ''

            preview_content = None

            if file_extension in ['jpg', 'jpeg', 'png', 'gif', 'bmp']:
                # Anteprima immagine
                preview_content = ft.Image(
                    src=str(file_path),
                    width=500,
                    height=400,
                    fit=ft.ImageFit.CONTAIN
                )
            elif file_extension == 'txt':
                # Anteprima testo
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()[:2000]  # Primi 2000 caratteri
                        if len(content) == 2000:
                            content += "\n\n... (contenuto troncato)"

                    preview_content = ft.Container(
                        content=ft.Text(
                            content,
                            size=12,
                            selectable=True
                        ),
                        width=500,
                        height=400,
                        padding=16,
                        bgcolor=ft.Colors.GREY_100,
                        border_radius=8
                    )
                except Exception:
                    preview_content = ft.Text("Impossibile leggere il contenuto del file", color=ft.Colors.RED)
            else:
                # Per altri tipi di file, mostra informazioni dettagliate
                file_size_mb = document.file_size / (1024 * 1024)
                size_text = f"{file_size_mb:.2f} MB" if file_size_mb >= 1 else f"{document.file_size / 1024:.1f} KB"

                # Icone specifiche per tipo di file
                file_icons = {
                    'pdf': (ft.Icons.PICTURE_AS_PDF, ft.Colors.RED_600, "Documento PDF"),
                    'doc': (ft.Icons.DESCRIPTION, ft.Colors.BLUE_600, "Documento Word"),
                    'docx': (ft.Icons.DESCRIPTION, ft.Colors.BLUE_600, "Documento Word"),
                    'xls': (ft.Icons.TABLE_CHART, ft.Colors.GREEN_600, "Foglio di calcolo Excel"),
                    'xlsx': (ft.Icons.TABLE_CHART, ft.Colors.GREEN_600, "Foglio di calcolo Excel"),
                    'ppt': (ft.Icons.SLIDESHOW, ft.Colors.ORANGE_600, "Presentazione PowerPoint"),
                    'pptx': (ft.Icons.SLIDESHOW, ft.Colors.ORANGE_600, "Presentazione PowerPoint"),
                    'zip': (ft.Icons.ARCHIVE, ft.Colors.PURPLE_600, "Archivio compresso"),
                    'rar': (ft.Icons.ARCHIVE, ft.Colors.PURPLE_600, "Archivio compresso"),
                }

                icon, icon_color, file_type_desc = file_icons.get(
                    file_extension,
                    (ft.Icons.DESCRIPTION, ft.Colors.BLUE_600, f"File {file_extension.upper()}")
                )

                # Informazioni aggiuntive per Excel
                additional_info = []
                if file_extension in ['xls', 'xlsx']:
                    additional_info = [
                        ft.Container(
                            content=ft.Column([
                                ft.Text("📊 Foglio di calcolo Excel", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN_700),
                                ft.Text("• Contiene dati tabulari e formule", size=12, color=ft.Colors.GREY_600),
                                ft.Text("• Supporta grafici e analisi", size=12, color=ft.Colors.GREY_600),
                                ft.Text("• Formato Microsoft Excel", size=12, color=ft.Colors.GREY_600),
                            ], spacing=4),
                            bgcolor=ft.Colors.GREEN_50,
                            padding=12,
                            border_radius=8,
                            border=ft.border.all(1, ft.Colors.GREEN_200)
                        )
                    ]
                elif file_extension == 'pdf':
                    additional_info = [
                        ft.Container(
                            content=ft.Column([
                                ft.Text("📄 Documento PDF", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.RED_700),
                                ft.Text("• Formato portatile per documenti", size=12, color=ft.Colors.GREY_600),
                                ft.Text("• Mantiene la formattazione originale", size=12, color=ft.Colors.GREY_600),
                                ft.Text("• Visualizzabile su qualsiasi dispositivo", size=12, color=ft.Colors.GREY_600),
                            ], spacing=4),
                            bgcolor=ft.Colors.RED_50,
                            padding=12,
                            border_radius=8,
                            border=ft.border.all(1, ft.Colors.RED_200)
                        )
                    ]

                # Tags del documento
                tags_display = []
                if document.tags:
                    tags_chips = []
                    for tag in document.tags:
                        tags_chips.append(
                            ft.Container(
                                content=ft.Text(tag, size=10, color=ft.Colors.WHITE),
                                bgcolor=ft.Colors.BLUE_400,
                                padding=ft.padding.symmetric(horizontal=8, vertical=4),
                                border_radius=12
                            )
                        )

                    tags_display = [
                        ft.Text("🏷️ Tag:", size=12, weight=ft.FontWeight.BOLD),
                        ft.Row(tags_chips, spacing=4, wrap=True)
                    ]

                preview_content = ft.Container(
                    content=ft.Column([
                        # Icona e nome file
                        ft.Icon(icon, size=64, color=icon_color),
                        ft.Text(document.name, size=16, weight=ft.FontWeight.BOLD,
                               text_align=ft.TextAlign.CENTER, max_lines=2),

                        ft.Divider(height=20),

                        # Informazioni base
                        ft.Container(
                            content=ft.Column([
                                ft.Row([
                                    ft.Text("📁 Tipo:", size=12, weight=ft.FontWeight.BOLD),
                                    ft.Text(file_type_desc, size=12, color=ft.Colors.GREY_700)
                                ], spacing=8),
                                ft.Row([
                                    ft.Text("📦 Dimensione:", size=12, weight=ft.FontWeight.BOLD),
                                    ft.Text(size_text, size=12, color=ft.Colors.GREY_700)
                                ], spacing=8),
                                ft.Row([
                                    ft.Text("📅 Creato:", size=12, weight=ft.FontWeight.BOLD),
                                    ft.Text(document.created_at.strftime('%d/%m/%Y %H:%M'), size=12, color=ft.Colors.GREY_700)
                                ], spacing=8),
                            ], spacing=8),
                            bgcolor=ft.Colors.GREY_50,
                            padding=12,
                            border_radius=8
                        ),

                        # Informazioni aggiuntive specifiche per tipo file
                        *additional_info,

                        # Tags se presenti
                        *tags_display,

                        ft.Divider(height=10),

                        # Descrizione se presente
                        ft.Text(
                            document.description or "Nessuna descrizione disponibile",
                            size=12,
                            color=ft.Colors.GREY_600,
                            text_align=ft.TextAlign.CENTER,
                            max_lines=3
                        ),

                        ft.Divider(height=10),

                        # Pulsante di apertura
                        ft.ElevatedButton(
                            text=f"Apri {file_extension.upper()}",
                            icon=ft.Icons.OPEN_IN_NEW,
                            on_click=lambda _: self._download_document(document),
                            bgcolor=icon_color,
                            color=ft.Colors.WHITE,
                            style=ft.ButtonStyle(
                                shape=ft.RoundedRectangleBorder(radius=8)
                            )
                        )
                    ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=8, scroll=ft.ScrollMode.AUTO),
                    width=500,
                    height=400,
                    padding=20
                )

            def close_preview(e):
                try:
                    # Chiudi il dialog usando page.dialog
                    if self.page.dialog:
                        self.page.dialog.open = False
                        self.page.update()

                    logger.debug("Preview dialog chiuso correttamente")

                except Exception as ex:
                    logger.error(f"Errore chiusura preview: {ex}")
                    # Fallback: chiudi comunque il dialog
                    if self.page.dialog:
                        self.page.dialog.open = False
                        self.page.update()

            # Dialog di anteprima (NON-MODAL per evitare grey screen)
            preview_dialog = ft.AlertDialog(
                modal=False,  # NON-MODAL per evitare grey screen
                title=ft.Row([
                    ft.Icon(ft.Icons.PREVIEW, color=ft.Colors.BLUE_600),
                    ft.Text(f"Anteprima: {document.name}", weight=ft.FontWeight.BOLD)
                ], spacing=8),
                content=ft.Container(
                    content=preview_content,
                    width=520,
                    height=420
                ),
                actions=[
                    ft.TextButton("Chiudi", on_click=close_preview),
                    ft.ElevatedButton(
                        text="Apri File",
                        icon=ft.Icons.OPEN_IN_NEW,
                        on_click=lambda _: self._open_and_close_preview(document),
                        bgcolor=ft.Colors.BLUE_600,
                        color=ft.Colors.WHITE
                    )
                ],
                actions_alignment=ft.MainAxisAlignment.END
            )

            # Use page.dialog instead of overlay to avoid conflicts
            self.page.dialog = preview_dialog
            preview_dialog.open = True
            self.page.update()

        except Exception as e:
            logger.error(f"Errore anteprima documento: {e}")
            self._show_error_dialog(f"Errore anteprima: {str(e)}")

    def _open_and_close_preview(self, document: Document):
        """Apre il documento e chiude il preview senza grey screen"""
        try:
            # Chiudi il dialog prima usando page.dialog
            if self.page.dialog:
                self.page.dialog.open = False
                self.page.update()

            # Apri il documento
            self._download_document(document)

            logger.debug("Documento aperto e preview chiuso correttamente")

        except Exception as e:
            logger.error(f"Errore apertura documento da preview: {e}")
            # Chiudi comunque il dialog
            if self.page.dialog:
                self.page.dialog.open = False
                self.page.update()
            self._show_error_dialog(f"Errore apertura documento: {str(e)}")