#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Professional Gantt Chart View using Plotly
Modern, interactive timeline visualization for Agevolami PM
"""

import flet as ft
import plotly.express as px
import plotly.graph_objects as go
import pandas as pd
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Any
import math

from core import get_logger
from core.models.base_models import Project, Deadline, Client, ProjectStatus, DeadlineStatus, Priority

logger = get_logger(__name__)

class ProGanttView:
    """Professional Gantt Chart with Plotly integration"""
    
    def __init__(self, app_instance):
        self.app = app_instance
        self.db = app_instance.db_manager
        
        # Configuration
        self.time_range = "3months"  # 1month, 3months, 6months, 1year
        self.filter_client = None
        self.filter_status = None
        self.show_deadlines = True
        self.show_projects = True
        
        # Data
        self.projects = []
        self.deadlines = []
        self.clients = []
        
        # Colors
        self.project_colors = {
            ProjectStatus.DRAFT: "#9E9E9E",           # Grey
            ProjectStatus.SUBMITTED: "#FF9800",       # Orange
            ProjectStatus.APPROVED: "#2196F3",        # Blue
            ProjectStatus.IN_PROGRESS: "#4CAF50",     # Green
            ProjectStatus.COMPLETED: "#607D8B",       # Blue Grey
            ProjectStatus.SUSPENDED: "#FF5722",       # Deep Orange
            ProjectStatus.CANCELLED: "#F44336"        # Red
        }
        
        self.priority_colors = {
            Priority.LOW: "#4CAF50",       # Green
            Priority.MEDIUM: "#2196F3",    # Blue
            Priority.HIGH: "#FF9800",      # Orange
            Priority.CRITICAL: "#F44336"   # Red
        }
        
        # Components
        self.plotly_chart = None
        self.main_container = None
        
        self._load_data()
        self._init_components()
    
    def _load_data(self):
        """Load projects and deadlines data"""
        try:
            self.projects = self.db.get_all_projects()
            
            # Calculate date range
            today = date.today()
            if self.time_range == "1month":
                start_date = today - timedelta(days=15)
                end_date = today + timedelta(days=45)
            elif self.time_range == "3months":
                start_date = today - timedelta(days=30)
                end_date = today + timedelta(days=90)
            elif self.time_range == "6months":
                start_date = today - timedelta(days=60)
                end_date = today + timedelta(days=180)
            else:  # 1year
                start_date = today.replace(month=1, day=1)
                end_date = today.replace(month=12, day=31)
            
            self.deadlines = self.db.get_deadlines_by_date_range(start_date, end_date)
            self.clients = self.db.get_all_clients()
            
            # Apply filters
            if self.filter_client:
                self.projects = [p for p in self.projects if str(p.client_id) == str(self.filter_client)]
                self.deadlines = [d for d in self.deadlines if str(d.client_id) == str(self.filter_client)]
            
            if self.filter_status:
                status_map = {
                    "bozza": ProjectStatus.DRAFT,
                    "presentato": ProjectStatus.SUBMITTED,
                    "approvato": ProjectStatus.APPROVED,
                    "in_corso": ProjectStatus.IN_PROGRESS,
                    "completato": ProjectStatus.COMPLETED,
                    "sospeso": ProjectStatus.SUSPENDED,
                    "cancellato": ProjectStatus.CANCELLED
                }
                if self.filter_status in status_map:
                    self.projects = [p for p in self.projects if p.status == status_map[self.filter_status]]
            
            logger.info(f"Loaded {len(self.projects)} projects and {len(self.deadlines)} deadlines for Pro Gantt")
            
        except Exception as e:
            logger.error(f"Error loading Pro Gantt data: {e}")
            self.projects = []
            self.deadlines = []
            self.clients = []
    
    def _init_components(self):
        """Initialize components"""
        self.plotly_chart = self._create_plotly_gantt()
    
    def _create_plotly_gantt(self) -> ft.PlotlyChart:
        """Create the main Plotly Gantt chart"""
        # Prepare data for Plotly
        gantt_data = []
        
        # Add projects
        if self.show_projects:
            for project in self.projects:
                if not project.start_date:
                    continue
                    
                # Calculate end date
                end_date = project.end_date or (project.start_date + timedelta(days=30))
                
                # Get client name
                client_name = "Sconosciuto"
                for client in self.clients:
                    if client.id == project.client_id:
                        client_name = client.name
                        break
                
                # Status text
                status_text = {
                    ProjectStatus.DRAFT: "Bozza",
                    ProjectStatus.SUBMITTED: "Presentato",
                    ProjectStatus.APPROVED: "Approvato",
                    ProjectStatus.IN_PROGRESS: "In Corso",
                    ProjectStatus.COMPLETED: "Completato",
                    ProjectStatus.SUSPENDED: "Sospeso",
                    ProjectStatus.CANCELLED: "Cancellato"
                }.get(project.status, "Sconosciuto")
                
                gantt_data.append({
                    'Task': f"📋 {project.name}",
                    'Start': project.start_date,
                    'Finish': end_date,
                    'Resource': f"Progetto - {client_name}",
                    'Type': 'Project',
                    'Status': status_text,
                    'Color': self.project_colors.get(project.status, "#9E9E9E"),
                    'Description': project.description or "Nessuna descrizione",
                    'Client': client_name,
                    'Priority': 'N/A'
                })
        
        # Add deadlines
        if self.show_deadlines:
            for deadline in self.deadlines:
                # Get client name
                client_name = "Sconosciuto"
                for client in self.clients:
                    if client.id == deadline.client_id:
                        client_name = client.name
                        break
                
                # Priority text
                priority_text = {
                    Priority.LOW: "Bassa",
                    Priority.MEDIUM: "Media",
                    Priority.HIGH: "Alta",
                    Priority.CRITICAL: "Critica"
                }.get(deadline.priority, "Sconosciuto")
                
                # Status text
                status_text = {
                    DeadlineStatus.PENDING: "In Attesa",
                    DeadlineStatus.COMPLETED: "Completato",
                    DeadlineStatus.OVERDUE: "Scaduto",
                    DeadlineStatus.CANCELLED: "Cancellato"
                }.get(deadline.status, "Sconosciuto")
                
                # Create a small duration for deadlines (1 day visual representation)
                start_date = deadline.due_date - timedelta(hours=12)
                end_date = deadline.due_date + timedelta(hours=12)
                
                gantt_data.append({
                    'Task': f"⏰ {deadline.title}",
                    'Start': start_date,
                    'Finish': end_date,
                    'Resource': f"Scadenza - {client_name}",
                    'Type': 'Deadline',
                    'Status': status_text,
                    'Color': self.priority_colors.get(deadline.priority, "#9E9E9E"),
                    'Description': deadline.description or "Nessuna descrizione",
                    'Client': client_name,
                    'Priority': priority_text
                })
        
        # Create DataFrame
        if not gantt_data:
            # Empty state
            df = pd.DataFrame([{
                'Task': 'Nessun dato disponibile',
                'Start': date.today(),
                'Finish': date.today() + timedelta(days=1),
                'Resource': 'Sistema',
                'Type': 'Info',
                'Status': 'N/A',
                'Color': '#E0E0E0'
            }])
        else:
            df = pd.DataFrame(gantt_data)
        
        # Create Plotly Gantt chart
        if df.empty or df.iloc[0]['Task'] == 'Nessun dato disponibile':
            # Empty state chart
            fig = go.Figure()
            fig.add_annotation(
                text="Nessun progetto o scadenza da visualizzare<br>Aggiungi progetti o modifica i filtri",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                showarrow=False,
                font=dict(size=16, color="grey")
            )
            fig.update_layout(
                title="Gantt Chart - Nessun Dato",
                xaxis=dict(showgrid=False, showticklabels=False),
                yaxis=dict(showgrid=False, showticklabels=False),
                plot_bgcolor='white'
            )
        else:
            # Create timeline chart
            fig = px.timeline(
                df, 
                x_start="Start", 
                x_end="Finish", 
                y="Task", 
                color="Resource",
                hover_data=["Type", "Status", "Client", "Priority", "Description"],
                title="📊 Gantt Chart - Timeline Progetti e Scadenze"
            )
            
            # Customize appearance
            fig.update_yaxes(autorange="reversed")  # Tasks from top to bottom
            fig.update_layout(
                height=max(400, len(df) * 40 + 100),  # Dynamic height based on data
                xaxis=dict(
                    title="Timeline",
                    showgrid=True,
                    gridwidth=1,
                    gridcolor='lightgrey'
                ),
                yaxis=dict(
                    title="Progetti e Scadenze",
                    showgrid=True,
                    gridwidth=1,
                    gridcolor='lightgrey'
                ),
                plot_bgcolor='white',
                paper_bgcolor='white',
                font=dict(size=12),
                title=dict(
                    text="📊 Gantt Chart - Timeline Progetti e Scadenze",
                    x=0.5,
                    font=dict(size=18, color='#2E3440')
                ),
                legend=dict(
                    orientation="h",
                    yanchor="bottom",
                    y=1.02,
                    xanchor="right",
                    x=1
                ),
                margin=dict(l=50, r=50, t=80, b=50),
                hovermode='closest'
            )
            
            # Add today line
            today = date.today()
            fig.add_shape(
                type="line",
                x0=today, x1=today,
                y0=0, y1=1,
                yref="paper",
                line=dict(color="red", width=2, dash="dash")
            )
            
            # Add today annotation
            fig.add_annotation(
                x=today,
                y=1,
                yref="paper",
                text="OGGI",
                showarrow=False,
                yshift=10,
                font=dict(color="red", size=12)
            )
            
            # Update traces for better styling
            for i, trace in enumerate(fig.data):
                trace.marker.line.width = 1
                trace.marker.line.color = 'white'
        
        return ft.PlotlyChart(
            figure=fig,
            expand=True,
            isolated=True  # For better performance
        )
    
    def _create_header(self) -> ft.Container:
        """Create modern header with controls"""
        # Time range selector
        time_range_dropdown = ft.Dropdown(
            label="📅 Periodo",
            options=[
                ft.dropdown.Option(text="1 Mese", key="1month"),
                ft.dropdown.Option(text="3 Mesi", key="3months"),
                ft.dropdown.Option(text="6 Mesi", key="6months"),
                ft.dropdown.Option(text="1 Anno", key="1year")
            ],
            value=self.time_range,
            width=140,
            on_change=lambda e: self._change_time_range(e.control.value)
        )
        
        # Client filter
        client_options = [ft.dropdown.Option(text="Tutti i Clienti", key=None)]
        for client in self.clients:
            client_options.append(ft.dropdown.Option(text=client.name, key=str(client.id)))
        
        filter_client = ft.Dropdown(
            label="👤 Cliente",
            options=client_options,
            value=str(self.filter_client) if self.filter_client else None,
            width=200,
            on_change=lambda e: self._update_filter('client', e.control.value)
        )
        
        # Status filter
        filter_status = ft.Dropdown(
            label="📊 Stato",
            options=[
                ft.dropdown.Option(text="Tutti gli Stati", key=None),
                ft.dropdown.Option(text="Bozza", key="bozza"),
                ft.dropdown.Option(text="Presentato", key="presentato"),
                ft.dropdown.Option(text="Approvato", key="approvato"),
                ft.dropdown.Option(text="In Corso", key="in_corso"),
                ft.dropdown.Option(text="Completato", key="completato"),
                ft.dropdown.Option(text="Sospeso", key="sospeso"),
                ft.dropdown.Option(text="Cancellato", key="cancellato")
            ],
            value=self.filter_status,
            width=160,
            on_change=lambda e: self._update_filter('status', e.control.value)
        )
        
        # View toggles
        projects_switch = ft.Switch(
            label="📋 Progetti",
            value=self.show_projects,
            on_change=lambda e: self._toggle_view('projects', e.control.value)
        )
        
        deadlines_switch = ft.Switch(
            label="⏰ Scadenze",
            value=self.show_deadlines,
            on_change=lambda e: self._toggle_view('deadlines', e.control.value)
        )
        
        # Export button
        export_btn = ft.ElevatedButton(
            text="📤 Esporta",
            icon=ft.Icons.DOWNLOAD,
            on_click=lambda _: self._export_chart(),
            style=ft.ButtonStyle(
                bgcolor=ft.Colors.GREEN_600,
                color=ft.Colors.WHITE
            )
        )
        
        # Refresh button
        refresh_btn = ft.ElevatedButton(
            text="🔄 Aggiorna",
            icon=ft.Icons.REFRESH,
            on_click=lambda _: self._refresh_data(),
            style=ft.ButtonStyle(
                bgcolor=ft.Colors.BLUE_600,
                color=ft.Colors.WHITE
            )
        )
        
        # Stats info
        stats_text = ft.Text(
            f"📋 {len(self.projects)} progetti • ⏰ {len(self.deadlines)} scadenze",
            size=14,
            color=ft.Colors.GREY_600,
            weight=ft.FontWeight.W_500
        )
        
        return ft.Container(
            content=ft.Column([
                # Title
                ft.Row([
                    ft.Text(
                        "📊 Gantt Chart Professionale",
                        size=24,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.INDIGO_700
                    ),
                    ft.Container(expand=True),
                    export_btn,
                    refresh_btn
                ]),
                
                # Controls row
                ft.Row([
                    time_range_dropdown,
                    filter_client,
                    filter_status,
                    ft.VerticalDivider(width=1),
                    projects_switch,
                    deadlines_switch,
                    ft.Container(expand=True),
                    stats_text
                ], spacing=16, alignment=ft.MainAxisAlignment.START),
                
                ft.Divider()
            ], spacing=12),
            padding=ft.padding.all(20),
            bgcolor=ft.Colors.WHITE,
            border_radius=16,
            border=ft.border.all(1, ft.Colors.GREY_200),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=8,
                color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
                offset=ft.Offset(0, 2)
            )
        )
    
    def _create_legend(self) -> ft.Container:
        """Create a legend for the chart"""
        # Project status legend
        project_legend = ft.Row([
            ft.Text("Progetti:", size=14, weight=ft.FontWeight.BOLD),
            *[
                ft.Container(
                    content=ft.Row([
                        ft.Container(
                            width=12,
                            height=12,
                            bgcolor=color,
                            border_radius=2
                        ),
                        ft.Text(status_text, size=11)
                    ], spacing=4),
                    margin=ft.margin.only(right=8)
                )
                for (status, color), status_text in zip(
                    self.project_colors.items(),
                    ["Bozza", "Presentato", "Approvato", "In Corso", "Completato", "Sospeso", "Cancellato"]
                )
            ]
        ], wrap=True, spacing=8)
        
        # Priority legend
        priority_legend = ft.Row([
            ft.Text("Priorità Scadenze:", size=14, weight=ft.FontWeight.BOLD),
            *[
                ft.Container(
                    content=ft.Row([
                        ft.Container(
                            width=12,
                            height=12,
                            bgcolor=color,
                            border_radius=2
                        ),
                        ft.Text(priority_text, size=11)
                    ], spacing=4),
                    margin=ft.margin.only(right=8)
                )
                for (priority, color), priority_text in zip(
                    self.priority_colors.items(),
                    ["Bassa", "Media", "Alta", "Critica"]
                )
            ]
        ], wrap=True, spacing=8)
        
        return ft.Container(
            content=ft.Column([
                project_legend,
                ft.Divider(height=1),
                priority_legend
            ], spacing=8),
            padding=ft.padding.all(16),
            bgcolor=ft.Colors.GREY_50,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )
    
    # Event handlers
    def _change_time_range(self, time_range: str):
        """Change time range and refresh chart"""
        self.time_range = time_range
        self._refresh_data()
    
    def _update_filter(self, filter_type: str, value: Any):
        """Update filters and refresh chart"""
        if filter_type == 'client':
            self.filter_client = value if value else None
        elif filter_type == 'status':
            self.filter_status = value if value else None
        
        self._refresh_data()
    
    def _toggle_view(self, view_type: str, value: bool):
        """Toggle project/deadline visibility"""
        if view_type == 'projects':
            self.show_projects = value
        elif view_type == 'deadlines':
            self.show_deadlines = value
        
        self._refresh_chart()
    
    def _refresh_data(self):
        """Refresh data and chart"""
        self._load_data()
        self._refresh_chart()
    
    def _refresh_chart(self):
        """Refresh only the chart"""
        if self.main_container:
            self.plotly_chart = self._create_plotly_gantt()
            # Update the chart in the container
            self.main_container.content.controls[1] = self.plotly_chart
            
            # Update stats in header
            stats_text = self.main_container.content.controls[0].content.controls[1].controls[-1]
            stats_text.value = f"📋 {len(self.projects)} progetti • ⏰ {len(self.deadlines)} scadenze"
            
            try:
                if hasattr(self.app, 'page') and self.app.page:
                    self.app.page.update()
                elif hasattr(self.app, 'main_layout') and self.app.main_layout:
                    self.app.main_layout.page.update()
            except Exception as e:
                logger.warning(f"Could not update page: {e}")
    
    def _export_chart(self):
        """Export chart (placeholder for now)"""
        # This could be implemented to export the Plotly chart as PNG/PDF
        logger.info("Export functionality - to be implemented")
    
    def build(self) -> ft.Container:
        """Build the Pro Gantt view"""
        self.main_container = ft.Container(
            content=ft.Column([
                self._create_header(),
                self.plotly_chart,
                ft.Container(height=8),
                self._create_legend()
            ], spacing=0, scroll=ft.ScrollMode.AUTO),
            padding=ft.padding.all(20),
            bgcolor=ft.Colors.GREY_50,
            expand=True
        )
        
        return self.main_container
    
    def refresh_data(self):
        """Public method to refresh data"""
        self._refresh_data()