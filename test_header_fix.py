#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to isolate and fix header issues
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import flet as ft

def test_basic_header(page: ft.Page):
    """Test basic header without search functionality"""
    page.title = "Header Test"
    page.window_width = 1200
    page.window_height = 800
    
    # Simple header without search
    header = ft.Container(
        content=ft.Row([
            ft.Text("Agevolami PM", size=24, weight=ft.FontWeight.BOLD),
            ft.Container(expand=True),
            ft.Text("Test Header", size=16),
            ft.Container(expand=True),
            ft.IconButton(icon=ft.Icons.SETTINGS)
        ]),
        height=80,
        bgcolor=ft.Colors.WHITE,
        padding=ft.padding.symmetric(horizontal=20, vertical=16)
    )
    
    content = ft.Column([
        header,
        ft.Container(
            content=ft.Text("Header test successful!", size=20),
            expand=True,
            alignment=ft.alignment.center
        )
    ])
    
    page.add(content)

def test_search_field(page: ft.Page):
    """Test just the search field"""
    page.title = "Search Field Test"
    page.window_width = 1200
    page.window_height = 800
    
    # Simple search field
    search_field = ft.TextField(
        hint_text="🔍 Test search...",
        border_radius=12,
        height=44,
        text_size=14,
        content_padding=ft.padding.symmetric(horizontal=16, vertical=12),
        border_color=ft.Colors.GREY_300,
        focused_border_color=ft.Colors.BLUE_500,
        bgcolor=ft.Colors.GREY_50,
        focused_bgcolor=ft.Colors.WHITE,
        cursor_color=ft.Colors.BLUE_500
    )
    
    content = ft.Column([
        ft.Container(
            content=ft.Text("Search Field Test", size=24, weight=ft.FontWeight.BOLD),
            padding=ft.padding.all(20)
        ),
        ft.Container(
            content=search_field,
            width=400,
            padding=ft.padding.all(20)
        ),
        ft.Container(
            content=ft.Text("If you can see this, the search field works!", size=16),
            padding=ft.padding.all(20)
        )
    ])
    
    page.add(content)

def test_full_header(page: ft.Page):
    """Test the full header with minimal search"""
    page.title = "Full Header Test"
    page.window_width = 1200
    page.window_height = 800
    
    try:
        # Import the header component
        from src.ui.components.header import Header
        
        # Mock app for testing
        class MockApp:
            def __init__(self):
                self.page = page
                self.db = None
        
        mock_app = MockApp()
        
        # Create header
        header = Header(
            on_search=lambda query: print(f"Search: {query}"),
            on_notification_click=lambda: print("Notifications"),
            on_settings_click=lambda: print("Settings"),
            app_instance=mock_app
        )
        
        # Build header
        header_content = header.build()
        
        content = ft.Column([
            header_content,
            ft.Container(
                content=ft.Text("Full header test successful!", size=20),
                expand=True,
                alignment=ft.alignment.center
            )
        ])
        
        page.add(content)
        
    except Exception as e:
        error_content = ft.Column([
            ft.Text(f"Header Error: {str(e)}", size=16, color=ft.Colors.RED),
            ft.Text("Check console for details", size=14)
        ])
        page.add(error_content)
        print(f"Header test error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🧪 Testing Header Components...")
    
    # Test 1: Basic header
    print("Test 1: Basic Header")
    try:
        ft.app(target=test_basic_header, port=8550)
    except Exception as e:
        print(f"Basic header test failed: {e}")
    
    # Test 2: Search field only
    print("Test 2: Search Field")
    try:
        ft.app(target=test_search_field, port=8551)
    except Exception as e:
        print(f"Search field test failed: {e}")
    
    # Test 3: Full header
    print("Test 3: Full Header")
    try:
        ft.app(target=test_full_header, port=8552)
    except Exception as e:
        print(f"Full header test failed: {e}")
