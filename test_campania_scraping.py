#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to verify that Campania website is being scraped
"""

import json
from pathlib import Path

def test_campania_config():
    """Test that Campania is properly configured"""
    
    print("🔍 Testing Campania Website Configuration...")
    
    # Load configuration from file
    config_path = Path("data/incentives_config.json")
    if not config_path.exists():
        print("❌ Configuration file not found!")
        return False
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # Find Campania website
    campania_found = False
    for website in config.get('websites', []):
        name = website.get('name', '')
        if 'CAMPANIA' in name.upper():
            campania_found = True
            print(f"✅ Found Campania website: {name}")
            print(f"   URL: {website.get('url')}")
            print(f"   Enabled: {website.get('enabled')}")
            print(f"   Search paths: {website.get('search_paths', [])}")
            
            # Check if enabled
            if website.get('enabled', False):
                print("✅ Campania website is enabled")
            else:
                print("❌ Campania website is disabled")
                
            # Check search paths
            search_paths = website.get('search_paths', [])
            if search_paths:
                print(f"✅ Campania has {len(search_paths)} search paths configured")
                for path in search_paths:
                    full_url = f"{website.get('url')}{path}"
                    print(f"   └─ {full_url}")
            else:
                print("⚠️  No search paths configured for Campania")
            break
    
    if not campania_found:
        print("❌ Campania website not found in configuration")
        return False
    
    print("\n📊 Summary:")
    print(f"   Total websites configured: {len(config.get('websites', []))}")
    
    enabled_websites = [w for w in config.get('websites', []) if w.get('enabled', False)]
    print(f"   Enabled websites: {len(enabled_websites)}")
    
    for website in enabled_websites:
        name = website.get('name', 'Unknown')
        print(f"     - {name}")
    
    return True

def show_scraping_logic():
    """Show how the scraping logic works"""
    
    print("\n🔧 Scraping Logic:")
    print("1. The scraper will call scrape_all_sources()")
    print("2. It will first scrape the enum sources (MIMIT, INVITALIA, SIMEST)")
    print("3. Then it will call _scrape_custom_websites()")
    print("4. _scrape_custom_websites() will:")
    print("   - Loop through all websites in config")
    print("   - Skip disabled websites")
    print("   - Skip MIMIT, INVITALIA, SIMEST (they have dedicated methods)")
    print("   - Scrape remaining websites (including Campania)")
    print("   - Assign IncentiveSource.CAMPANIA to Campania items")
    print("   - Assign IncentiveSource.OTHER to other custom websites")

if __name__ == "__main__":
    success = test_campania_config()
    if success:
        show_scraping_logic()
        print("\n✅ Campania website should now be scraped automatically!")
    else:
        print("\n❌ Campania configuration issues found")
