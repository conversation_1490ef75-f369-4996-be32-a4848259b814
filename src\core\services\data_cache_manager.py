#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Data Cache Manager for Agevolami PM
Provides caching for frequently accessed database data
"""

import threading
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
from uuid import UUID

from .cache_manager import get_cache
from ..utils import get_logger

logger = get_logger(__name__)

class DataCacheManager:
    """Manages caching of frequently accessed database data"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.cache = get_cache()
        self._lock = threading.RLock()
        
        # Cache TTL settings (in seconds)
        self.ttl_settings = {
            "clients": 600,      # 10 minutes
            "projects": 300,     # 5 minutes
            "tasks": 180,        # 3 minutes
            "deadlines": 180,    # 3 minutes
            "documents": 300,    # 5 minutes
        }
    
    def get_all_clients(self, force_refresh: bool = False) -> List[Any]:
        """Get all clients with caching"""
        cache_key = "db_all_clients"
        
        if not force_refresh:
            cached_data = self.cache.get(cache_key)
            if cached_data is not None:
                logger.debug(f"Retrieved {len(cached_data)} clients from cache")
                return cached_data
        
        # Load from database
        clients = self.db_manager.get_all_clients()
        
        # Cache the results
        self.cache.set(cache_key, clients, self.ttl_settings["clients"])
        logger.info(f"Loaded and cached {len(clients)} clients from database")
        
        return clients
    
    def get_all_projects(self, force_refresh: bool = False) -> List[Any]:
        """Get all projects with caching"""
        cache_key = "db_all_projects"
        
        if not force_refresh:
            cached_data = self.cache.get(cache_key)
            if cached_data is not None:
                logger.debug(f"Retrieved {len(cached_data)} projects from cache")
                return cached_data
        
        # Load from database
        projects = self.db_manager.get_all_projects()
        
        # Cache the results
        self.cache.set(cache_key, projects, self.ttl_settings["projects"])
        logger.info(f"Loaded and cached {len(projects)} projects from database")
        
        return projects
    
    def get_all_tasks(self, force_refresh: bool = False) -> List[Any]:
        """Get all tasks with caching"""
        cache_key = "db_all_tasks"
        
        if not force_refresh:
            cached_data = self.cache.get(cache_key)
            if cached_data is not None:
                logger.debug(f"Retrieved {len(cached_data)} tasks from cache")
                return cached_data
        
        # Load from database
        tasks = self.db_manager.get_all_tasks()
        
        # Cache the results
        self.cache.set(cache_key, tasks, self.ttl_settings["tasks"])
        logger.info(f"Loaded and cached {len(tasks)} tasks from database")
        
        return tasks
    
    def get_all_deadlines(self, force_refresh: bool = False) -> List[Any]:
        """Get all deadlines with caching"""
        cache_key = "db_all_deadlines"
        
        if not force_refresh:
            cached_data = self.cache.get(cache_key)
            if cached_data is not None:
                logger.debug(f"Retrieved {len(cached_data)} deadlines from cache")
                return cached_data
        
        # Load from database
        deadlines = self.db_manager.get_all_deadlines()
        
        # Cache the results
        self.cache.set(cache_key, deadlines, self.ttl_settings["deadlines"])
        logger.info(f"Loaded and cached {len(deadlines)} deadlines from database")
        
        return deadlines
    
    def get_deadlines_by_date_range(self, start_date, end_date, force_refresh: bool = False) -> List[Any]:
        """Get deadlines by date range with caching"""
        cache_key = f"db_deadlines_range_{start_date.isoformat()}_{end_date.isoformat()}"
        
        if not force_refresh:
            cached_data = self.cache.get(cache_key)
            if cached_data is not None:
                logger.debug(f"Retrieved {len(cached_data)} deadlines from cache for date range")
                return cached_data
        
        # Load from database
        deadlines = self.db_manager.get_deadlines_by_date_range(start_date, end_date)
        
        # Cache the results with shorter TTL for date-specific queries
        self.cache.set(cache_key, deadlines, self.ttl_settings["deadlines"] // 2)
        logger.info(f"Loaded and cached {len(deadlines)} deadlines from database for date range")
        
        return deadlines
    
    def get_projects_by_client(self, client_id: UUID, force_refresh: bool = False) -> List[Any]:
        """Get projects by client with caching"""
        cache_key = f"db_projects_client_{client_id}"
        
        if not force_refresh:
            cached_data = self.cache.get(cache_key)
            if cached_data is not None:
                logger.debug(f"Retrieved {len(cached_data)} projects from cache for client {client_id}")
                return cached_data
        
        # Load from database
        projects = self.db_manager.get_projects_by_client(client_id)
        
        # Cache the results
        self.cache.set(cache_key, projects, self.ttl_settings["projects"])
        logger.info(f"Loaded and cached {len(projects)} projects from database for client {client_id}")
        
        return projects
    
    def get_tasks_by_deadline(self, deadline_id: UUID, force_refresh: bool = False) -> List[Any]:
        """Get tasks by deadline with caching"""
        cache_key = f"db_tasks_deadline_{deadline_id}"
        
        if not force_refresh:
            cached_data = self.cache.get(cache_key)
            if cached_data is not None:
                logger.debug(f"Retrieved {len(cached_data)} tasks from cache for deadline {deadline_id}")
                return cached_data
        
        # Load from database
        tasks = self.db_manager.get_tasks_by_deadline(deadline_id)
        
        # Cache the results
        self.cache.set(cache_key, tasks, self.ttl_settings["tasks"])
        logger.info(f"Loaded and cached {len(tasks)} tasks from database for deadline {deadline_id}")
        
        return tasks
    
    def invalidate_clients_cache(self):
        """Invalidate clients cache"""
        self.cache.delete("db_all_clients")
        # Also invalidate related caches
        keys_to_delete = [key for key in self.cache._cache.keys() if key.startswith("db_projects_client_")]
        for key in keys_to_delete:
            self.cache.delete(key)
        logger.info("Clients cache invalidated")
    
    def invalidate_projects_cache(self, client_id: UUID = None):
        """Invalidate projects cache"""
        self.cache.delete("db_all_projects")
        if client_id:
            self.cache.delete(f"db_projects_client_{client_id}")
        else:
            # Invalidate all client-specific project caches
            keys_to_delete = [key for key in self.cache._cache.keys() if key.startswith("db_projects_client_")]
            for key in keys_to_delete:
                self.cache.delete(key)
        logger.info("Projects cache invalidated")
    
    def invalidate_tasks_cache(self, deadline_id: UUID = None):
        """Invalidate tasks cache"""
        self.cache.delete("db_all_tasks")
        if deadline_id:
            self.cache.delete(f"db_tasks_deadline_{deadline_id}")
        else:
            # Invalidate all deadline-specific task caches
            keys_to_delete = [key for key in self.cache._cache.keys() if key.startswith("db_tasks_deadline_")]
            for key in keys_to_delete:
                self.cache.delete(key)
        logger.info("Tasks cache invalidated")
    
    def invalidate_deadlines_cache(self):
        """Invalidate deadlines cache"""
        self.cache.delete("db_all_deadlines")
        # Invalidate date range caches
        keys_to_delete = [key for key in self.cache._cache.keys() if key.startswith("db_deadlines_range_")]
        for key in keys_to_delete:
            self.cache.delete(key)
        logger.info("Deadlines cache invalidated")
    
    def invalidate_all_cache(self):
        """Invalidate all database caches"""
        self.invalidate_clients_cache()
        self.invalidate_projects_cache()
        self.invalidate_tasks_cache()
        self.invalidate_deadlines_cache()
        logger.info("All database caches invalidated")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        stats = self.cache.get_stats()
        
        # Count database-specific cache entries
        db_cache_keys = [key for key in stats["cache_keys"] if key.startswith("db_")]
        
        return {
            **stats,
            "db_cache_entries": len(db_cache_keys),
            "db_cache_keys": db_cache_keys
        }

# Global data cache instance
_data_cache = None
_data_cache_lock = threading.Lock()

def get_data_cache(db_manager) -> DataCacheManager:
    """Get global data cache instance (singleton)"""
    global _data_cache
    if _data_cache is None:
        with _data_cache_lock:
            if _data_cache is None:
                _data_cache = DataCacheManager(db_manager)
    return _data_cache
