# -*- coding: utf-8 -*-
"""
Utility per la categorizzazione dei progetti
"""

from typing import Dict, <PERSON><PERSON>
from ..models import ProjectType, ProjectCategory, ProjectSubcategory

# Mapping dei tipi di progetto esistenti alle nuove categorie
PROJECT_TYPE_MAPPING: Dict[ProjectType, Tuple[ProjectCategory, ProjectSubcategory]] = {
    # Ricerca & Innovazione
    ProjectType.ACCORDI_INNOVAZIONE: (ProjectCategory.INCENTIVI_PUBBLICI, ProjectSubcategory.RICERCA_INNOVAZIONE),
    ProjectType.TRANSIZIONE_50: (ProjectCategory.INCENTIVI_PUBBLICI, ProjectSubcategory.RICERCA_INNOVAZIONE),
    ProjectType.CREDITI_RS: (ProjectCategory.INCENTIVI_PUBBLICI, ProjectSubcategory.RICERCA_INNOVAZIONE),
    
    # Investimenti Produttivi
    ProjectType.INVESTIMENTI_SOSTENIBILI_40: (ProjectCategory.INCENTIVI_PUBBLICI, ProjectSubcategory.INVESTIMENTI_PRODUTTIVI),
    ProjectType.CONTRATTI_SVILUPPO: (ProjectCategory.INCENTIVI_PUBBLICI, ProjectSubcategory.INVESTIMENTI_PRODUTTIVI),
    ProjectType.MINI_CONTRATTI_SVILUPPO: (ProjectCategory.INCENTIVI_PUBBLICI, ProjectSubcategory.INVESTIMENTI_PRODUTTIVI),
    ProjectType.CREDITI_BENI_STRUMENTALI: (ProjectCategory.INCENTIVI_PUBBLICI, ProjectSubcategory.INVESTIMENTI_PRODUTTIVI),
    
    # Adempimenti Fiscali
    ProjectType.SCADENZE_FISCALI: (ProjectCategory.FISCALE_CONTABILE, ProjectSubcategory.ADEMPIMENTI_FISCALI),
    ProjectType.DICHIARAZIONI: (ProjectCategory.FISCALE_CONTABILE, ProjectSubcategory.ADEMPIMENTI_FISCALI),
    ProjectType.VERSAMENTI: (ProjectCategory.FISCALE_CONTABILE, ProjectSubcategory.ADEMPIMENTI_FISCALI),
    
    # Contabilità
    ProjectType.BILANCI: (ProjectCategory.FISCALE_CONTABILE, ProjectSubcategory.CONTABILITA),
    ProjectType.REVISIONI: (ProjectCategory.FISCALE_CONTABILE, ProjectSubcategory.CONTABILITA),
    
    # Crediti d'Imposta
    ProjectType.CREDITO_TRANSIZIONE_40: (ProjectCategory.CREDITI_FINANZA, ProjectSubcategory.CREDITI_IMPOSTA),
    ProjectType.CREDITO_FORMAZIONE_40: (ProjectCategory.CREDITI_FINANZA, ProjectSubcategory.CREDITI_IMPOSTA),
    ProjectType.ALTRI_CREDITI: (ProjectCategory.CREDITI_FINANZA, ProjectSubcategory.CREDITI_IMPOSTA),
    
    # Finanziamenti
    ProjectType.PRESTITI_AGEVOLATI: (ProjectCategory.CREDITI_FINANZA, ProjectSubcategory.FINANZIAMENTI),
    ProjectType.GARANZIE: (ProjectCategory.CREDITI_FINANZA, ProjectSubcategory.FINANZIAMENTI),
}

# Mapping inverso: da categoria/sottocategoria ai tipi di progetto
CATEGORY_TO_TYPES: Dict[Tuple[ProjectCategory, ProjectSubcategory], list] = {}
for project_type, (category, subcategory) in PROJECT_TYPE_MAPPING.items():
    key = (category, subcategory)
    if key not in CATEGORY_TO_TYPES:
        CATEGORY_TO_TYPES[key] = []
    CATEGORY_TO_TYPES[key].append(project_type)

# Icone per le categorie (ASCII-safe for built applications)
CATEGORY_ICONS = {
    ProjectCategory.INCENTIVI_PUBBLICI: "[GOV]",
    ProjectCategory.FISCALE_CONTABILE: "[TAX]",
    ProjectCategory.CREDITI_FINANZA: "[FIN]"
}

# Icone per le sottocategorie
SUBCATEGORY_ICONS = {
    ProjectSubcategory.RICERCA_INNOVAZIONE: "🔬",
    ProjectSubcategory.INVESTIMENTI_PRODUTTIVI: "🏭",
    ProjectSubcategory.ADEMPIMENTI_FISCALI: "📋",
    ProjectSubcategory.CONTABILITA: "📈",
    ProjectSubcategory.CREDITI_IMPOSTA: "💳",
    ProjectSubcategory.FINANZIAMENTI: "🏦"
}

# Nomi user-friendly per le categorie
CATEGORY_NAMES = {
    ProjectCategory.INCENTIVI_PUBBLICI: "Incentivi Pubblici",
    ProjectCategory.FISCALE_CONTABILE: "Fiscale & Contabile",
    ProjectCategory.CREDITI_FINANZA: "Crediti & Finanza"
}

# Nomi user-friendly per le sottocategorie
SUBCATEGORY_NAMES = {
    ProjectSubcategory.RICERCA_INNOVAZIONE: "Ricerca & Innovazione",
    ProjectSubcategory.INVESTIMENTI_PRODUTTIVI: "Investimenti Produttivi",
    ProjectSubcategory.ADEMPIMENTI_FISCALI: "Adempimenti Fiscali",
    ProjectSubcategory.CONTABILITA: "Contabilità",
    ProjectSubcategory.CREDITI_IMPOSTA: "Crediti d'Imposta",
    ProjectSubcategory.FINANZIAMENTI: "Finanziamenti"
}

def get_category_and_subcategory(project_type: ProjectType) -> Tuple[ProjectCategory, ProjectSubcategory]:
    """
    Restituisce categoria e sottocategoria per un tipo di progetto
    """
    return PROJECT_TYPE_MAPPING.get(project_type, 
                                   (ProjectCategory.INCENTIVI_PUBBLICI, ProjectSubcategory.RICERCA_INNOVAZIONE))

def get_project_types_for_category(category: ProjectCategory, subcategory: ProjectSubcategory) -> list:
    """
    Restituisce tutti i tipi di progetto per una categoria/sottocategoria
    """
    return CATEGORY_TO_TYPES.get((category, subcategory), [])

def get_category_display_name(category: ProjectCategory) -> str:
    """
    Restituisce il nome user-friendly di una categoria
    """
    if category is None:
        return "N/A"
    icon = CATEGORY_ICONS.get(category, "")
    name = CATEGORY_NAMES.get(category, category.value if hasattr(category, 'value') else str(category))
    return f"{icon} {name}"

def get_subcategory_display_name(subcategory: ProjectSubcategory) -> str:
    """
    Restituisce il nome user-friendly di una sottocategoria
    """
    if subcategory is None:
        return "N/A"
    icon = SUBCATEGORY_ICONS.get(subcategory, "")
    name = SUBCATEGORY_NAMES.get(subcategory, subcategory.value if hasattr(subcategory, 'value') else str(subcategory))
    return f"{icon} {name}"