#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Restart script for Agevolami PM
"""

import subprocess
import sys
import os
from pathlib import Path

def restart_app():
    """Restarts the Agevolami PM application"""
    print("🔄 Restarting Agevolami PM...")
    
    # Get the current directory
    current_dir = Path(__file__).parent
    
    # Look for the main app file
    main_files = ['main.py', 'app.py', 'agevolami_pm.py']
    main_file = None
    
    for file in main_files:
        if (current_dir / file).exists():
            main_file = file
            break
    
    if not main_file:
        print("❌ Could not find main application file")
        print("Please run the application manually")
        return
    
    try:
        # Kill any existing Python processes (optional)
        print("🛑 Stopping any existing processes...")
        
        # Start the application
        print(f"🚀 Starting {main_file}...")
        subprocess.run([sys.executable, main_file], cwd=current_dir)
        
    except Exception as e:
        print(f"❌ Error restarting application: {e}")
        print("Please restart the application manually")

if __name__ == "__main__":
    restart_app() 