#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Selenium WebDriver Setup Script
Automatically downloads and configures Chrome WebDriver for enhanced web scraping
"""

import os
import sys
import subprocess
from pathlib import Path

def install_selenium_dependencies():
    """Install Selenium and WebDriver Manager"""
    try:
        print("Installing Selenium dependencies...")
        
        # Install selenium and webdriver-manager
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", 
            "selenium>=4.15.0", 
            "webdriver-manager>=4.0.0"
        ])
        
        print("✅ Selenium dependencies installed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing Selenium dependencies: {e}")
        return False

def setup_chrome_driver():
    """Setup Chrome WebDriver using webdriver-manager"""
    try:
        print("Setting up Chrome WebDriver...")
        
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from webdriver_manager.chrome import ChromeDriverManager
        from selenium.webdriver.chrome.service import Service
        
        # Setup Chrome options
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        
        # Download and setup ChromeDriver
        service = Service(ChromeDriverManager().install())
        
        # Test the driver
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.get("https://www.google.com")
        driver.quit()
        
        print("✅ Chrome WebDriver setup completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error setting up Chrome WebDriver: {e}")
        print("Note: Chrome browser must be installed on your system")
        return False

def test_enhanced_scraping():
    """Test the enhanced web scraping functionality"""
    try:
        print("Testing enhanced web scraping...")
        
        # Add the src directory to Python path
        src_path = Path(__file__).parent.parent / "src"
        sys.path.insert(0, str(src_path))
        
        from core.services.enhanced_web_scraping_service import EnhancedWebScrapingService
        
        # Test configuration
        config = {
            'scraping_method': 'hybrid',
            'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'request_delay_seconds': 1.0,
            'timeout_seconds': 10,
            'validation': {
                'min_title_length': 5,
                'max_title_length': 100,
                'min_description_length': 10,
                'max_description_length': 500
            },
            'retry': {
                'max_retries': 2,
                'base_delay': 1.0,
                'max_delay': 10.0
            },
            'websites': []
        }
        
        # Create service instance
        scraper = EnhancedWebScrapingService(config)
        
        # Test health check
        health = scraper.check_website_health("https://www.google.com")
        print(f"Health check test: {'✅' if health.is_healthy else '❌'}")
        
        # Cleanup
        scraper.cleanup()
        
        print("✅ Enhanced web scraping test completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error testing enhanced web scraping: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 Setting up Enhanced Web Scraping with Selenium...")
    print("=" * 60)
    
    success = True
    
    # Step 1: Install dependencies
    if not install_selenium_dependencies():
        success = False
    
    print()
    
    # Step 2: Setup Chrome WebDriver
    if success and not setup_chrome_driver():
        success = False
    
    print()
    
    # Step 3: Test functionality
    if success and not test_enhanced_scraping():
        success = False
    
    print()
    print("=" * 60)
    
    if success:
        print("🎉 Enhanced Web Scraping setup completed successfully!")
        print()
        print("Features now available:")
        print("  ✅ Selenium WebDriver for JavaScript-heavy sites")
        print("  ✅ Content validation with quality thresholds")
        print("  ✅ Website health monitoring")
        print("  ✅ CSS selector validation")
        print("  ✅ Advanced content deduplication")
        print("  ✅ Intelligent retry logic with exponential backoff")
        print()
        print("The enhanced scraping service will automatically:")
        print("  • Use Selenium for sites that require JavaScript")
        print("  • Fall back to requests for simple sites")
        print("  • Monitor website health and skip unhealthy sites")
        print("  • Validate CSS selectors and adapt when they break")
        print("  • Filter out duplicate and low-quality content")
        print("  • Retry failed requests with smart backoff")
    else:
        print("❌ Setup failed. Please check the errors above.")
        print()
        print("Common issues:")
        print("  • Chrome browser not installed")
        print("  • Network connectivity issues")
        print("  • Permission issues (try running as administrator)")
        
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
