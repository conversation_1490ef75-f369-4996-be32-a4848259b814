#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for Enhanced Web Scraping Service
Tests all the enhanced features including Selenium, validation, health monitoring, etc.
"""

import sys
import os
from pathlib import Path

# Add src to path
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

from core.services.enhanced_web_scraping_service import EnhancedWebScrapingService, ScrapingMethod
from core.models.incentive_models import IncentiveItem, IncentiveSource
import time

def test_basic_functionality():
    """Test basic enhanced scraping functionality"""
    print("🧪 Testing basic functionality...")
    
    config = {
        'scraping_method': 'requests',  # Start with requests only
        'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'request_delay_seconds': 1.0,
        'timeout_seconds': 10,
        'validation': {
            'min_title_length': 5,
            'max_title_length': 100,
            'min_description_length': 10,
            'max_description_length': 500,
            'forbidden_keywords': ['cookie', 'privacy'],
            'max_duplicate_similarity': 0.85
        },
        'retry': {
            'max_retries': 2,
            'base_delay': 1.0,
            'max_delay': 10.0,
            'exponential_base': 2.0,
            'jitter': True
        },
        'websites': []
    }
    
    try:
        scraper = EnhancedWebScrapingService(config)
        print("✅ Enhanced scraping service created successfully")
        
        # Test health check
        health = scraper.check_website_health("https://www.google.com")
        print(f"✅ Health check: {'Healthy' if health.is_healthy else 'Unhealthy'} ({health.response_time:.2f}s)")
        
        scraper.cleanup()
        return True
        
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        return False

def test_content_validation():
    """Test content validation features"""
    print("🧪 Testing content validation...")
    
    config = {
        'validation': {
            'min_title_length': 10,
            'max_title_length': 100,
            'min_description_length': 20,
            'max_description_length': 500,
            'forbidden_keywords': ['test_forbidden'],
            'max_duplicate_similarity': 0.85
        },
        'websites': []
    }
    
    try:
        scraper = EnhancedWebScrapingService(config)
        
        # Test valid content
        valid_item = IncentiveItem(
            title="Valid incentive title for testing",
            description="This is a valid description that meets the minimum length requirements for testing purposes.",
            source=IncentiveSource.OTHER
        )
        
        is_valid = scraper.validate_content(valid_item)
        print(f"✅ Valid content validation: {'Passed' if is_valid else 'Failed'}")
        
        # Test invalid content (too short title)
        invalid_item = IncentiveItem(
            title="Short",
            description="This is a valid description that meets the minimum length requirements for testing purposes.",
            source=IncentiveSource.OTHER
        )
        
        is_invalid = not scraper.validate_content(invalid_item)
        print(f"✅ Invalid content rejection: {'Passed' if is_invalid else 'Failed'}")
        
        # Test forbidden keywords
        forbidden_item = IncentiveItem(
            title="Title with test_forbidden keyword",
            description="This description contains forbidden content that should be rejected by the validation system.",
            source=IncentiveSource.OTHER
        )
        
        is_forbidden = not scraper.validate_content(forbidden_item)
        print(f"✅ Forbidden keyword rejection: {'Passed' if is_forbidden else 'Failed'}")
        
        scraper.cleanup()
        return True
        
    except Exception as e:
        print(f"❌ Content validation test failed: {e}")
        return False

def test_deduplication():
    """Test advanced deduplication features"""
    print("🧪 Testing deduplication...")
    
    config = {
        'validation': {
            'max_duplicate_similarity': 0.85
        },
        'websites': []
    }
    
    try:
        scraper = EnhancedWebScrapingService(config)
        
        # Test exact duplicate
        item1 = IncentiveItem(
            title="Exact duplicate title",
            description="Exact duplicate description for testing purposes.",
            source=IncentiveSource.OTHER
        )
        
        item2 = IncentiveItem(
            title="Exact duplicate title",
            description="Exact duplicate description for testing purposes.",
            source=IncentiveSource.OTHER
        )
        
        is_duplicate_1 = scraper.is_duplicate_content(item1)
        is_duplicate_2 = scraper.is_duplicate_content(item2)
        
        print(f"✅ Exact duplicate detection: {'Passed' if not is_duplicate_1 and is_duplicate_2 else 'Failed'}")
        
        # Test similar content
        item3 = IncentiveItem(
            title="Very similar title for testing",
            description="Very similar description for testing purposes with slight changes.",
            source=IncentiveSource.OTHER
        )
        
        item4 = IncentiveItem(
            title="Very similar title for testing purposes",
            description="Very similar description for testing purposes with minor modifications.",
            source=IncentiveSource.OTHER
        )
        
        is_similar_1 = scraper.is_duplicate_content(item3)
        is_similar_2 = scraper.is_duplicate_content(item4)
        
        print(f"✅ Similar content detection: {'Passed' if not is_similar_1 and is_similar_2 else 'Failed'}")
        
        scraper.cleanup()
        return True
        
    except Exception as e:
        print(f"❌ Deduplication test failed: {e}")
        return False

def test_health_monitoring():
    """Test website health monitoring"""
    print("🧪 Testing health monitoring...")
    
    config = {'websites': []}
    
    try:
        scraper = EnhancedWebScrapingService(config)
        
        # Test healthy site
        health_google = scraper.check_website_health("https://www.google.com")
        print(f"✅ Healthy site detection: {'Passed' if health_google.is_healthy else 'Failed'}")
        
        # Test unhealthy site (non-existent domain)
        health_invalid = scraper.check_website_health("https://this-domain-does-not-exist-12345.com")
        print(f"✅ Unhealthy site detection: {'Passed' if not health_invalid.is_healthy else 'Failed'}")
        
        # Test health report
        report = scraper.get_health_report()
        has_report_data = 'total_sites' in report and 'healthy_sites' in report
        print(f"✅ Health report generation: {'Passed' if has_report_data else 'Failed'}")
        
        scraper.cleanup()
        return True
        
    except Exception as e:
        print(f"❌ Health monitoring test failed: {e}")
        return False

def test_selector_validation():
    """Test CSS selector validation"""
    print("🧪 Testing selector validation...")
    
    config = {'websites': []}
    
    try:
        scraper = EnhancedWebScrapingService(config)
        
        # Test selector validation on Google (should have basic HTML elements)
        selectors = ['title', 'body', 'div', 'nonexistent-element']
        validations = scraper.validate_selectors("https://www.google.com", selectors)
        
        # Check that some selectors work and some don't
        working_selectors = [v for v in validations if v.is_valid and v.elements_found > 0]
        failing_selectors = [v for v in validations if not v.is_valid or v.elements_found == 0]
        
        has_working = len(working_selectors) >= 2  # title, body, div should work
        has_failing = len(failing_selectors) >= 1  # nonexistent-element should fail
        
        print(f"✅ Selector validation: {'Passed' if has_working and has_failing else 'Failed'}")
        print(f"   Working selectors: {len(working_selectors)}, Failing: {len(failing_selectors)}")
        
        scraper.cleanup()
        return True
        
    except Exception as e:
        print(f"❌ Selector validation test failed: {e}")
        return False

def test_retry_logic():
    """Test intelligent retry logic"""
    print("🧪 Testing retry logic...")
    
    config = {
        'retry': {
            'max_retries': 2,
            'base_delay': 0.1,  # Fast for testing
            'max_delay': 1.0,
            'exponential_base': 2.0,
            'jitter': False  # Disable for predictable testing
        },
        'websites': []
    }
    
    try:
        scraper = EnhancedWebScrapingService(config)
        
        # Test retry with invalid URL (should fail after retries)
        start_time = time.time()
        result = scraper.scrape_with_retry("https://invalid-url-for-testing-12345.com")
        end_time = time.time()
        
        # Should fail and take some time due to retries
        failed_correctly = result is None
        took_time = (end_time - start_time) > 0.2  # Should take at least some time for retries
        
        print(f"✅ Retry logic: {'Passed' if failed_correctly and took_time else 'Failed'}")
        print(f"   Failed correctly: {failed_correctly}, Took time for retries: {took_time}")
        
        scraper.cleanup()
        return True
        
    except Exception as e:
        print(f"❌ Retry logic test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Running Enhanced Web Scraping Tests")
    print("=" * 50)
    
    tests = [
        test_basic_functionality,
        test_content_validation,
        test_deduplication,
        test_health_monitoring,
        test_selector_validation,
        test_retry_logic
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Enhanced web scraping is working correctly.")
    else:
        print(f"⚠️  {total - passed} tests failed. Check the output above for details.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
