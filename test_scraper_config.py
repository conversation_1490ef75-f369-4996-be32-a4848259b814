#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to verify that the scraper is using URLs from incentives_config.json
"""

import sys
import os
import json
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from core.services.web_scraping_service import WebScrapingService
from core.models.incentive_models import IncentiveSource

def test_scraper_config():
    """Test that the scraper uses URLs from config file"""
    
    print("🔍 Testing Scraper Configuration...")
    
    # Load configuration from file
    config_path = Path("data/incentives_config.json")
    if not config_path.exists():
        print("❌ Configuration file not found!")
        return False
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    print(f"✅ Loaded config with {len(config.get('websites', []))} websites")
    
    # Print configured websites
    print("\n📋 Configured Websites:")
    for website in config.get('websites', []):
        name = website.get('name', 'Unknown')
        url = website.get('url', 'No URL')
        enabled = website.get('enabled', False)
        search_paths = website.get('search_paths', [])
        
        status = "✅ Enabled" if enabled else "❌ Disabled"
        print(f"  {status} {name}: {url}")
        for path in search_paths:
            print(f"    └─ {url}{path}")
    
    # Create scraper with config
    scraper_config = {
        'sources': [IncentiveSource.MIMIT, IncentiveSource.INVITALIA, IncentiveSource.SIMEST],
        'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'request_delay_seconds': 1.0,
        'timeout_seconds': 15,
        'websites': config.get('websites', [])
    }
    
    scraper = WebScrapingService(scraper_config)
    
    print(f"\n🔧 Scraper initialized with {len(scraper.websites_config)} website configs")
    
    # Test website config retrieval
    print("\n🧪 Testing Website Config Retrieval:")
    
    test_websites = ['MIMIT', 'Invitalia', 'SIMEST', 'Campania']
    for website_name in test_websites:
        website_config = scraper._get_website_config(website_name)
        if website_config:
            print(f"  ✅ {website_name}: Found config")
            print(f"     URL: {website_config.get('url')}")
            print(f"     Paths: {website_config.get('search_paths', [])}")
        else:
            print(f"  ❌ {website_name}: No config found")
    
    # Test connection to configured URLs
    print("\n🌐 Testing Connections:")
    for website in config.get('websites', []):
        if not website.get('enabled', True):
            continue
            
        name = website.get('name', 'Unknown')
        url = website.get('url', '')
        
        try:
            # Test basic connection
            success = scraper.validate_url(url)
            status = "✅ Connected" if success else "❌ Failed"
            print(f"  {status} {name}: {url}")
        except Exception as e:
            print(f"  ❌ {name}: Error - {e}")
    
    scraper.close()
    
    print("\n✅ Configuration test completed!")
    return True

def compare_urls():
    """Compare hardcoded URLs vs config URLs"""
    
    print("\n📊 URL Comparison (Hardcoded vs Config):")
    
    # Hardcoded URLs from old implementation
    hardcoded_urls = {
        'MIMIT': [
            '/it/incentivi-mise',
            '/it/notizie-stampa', 
            '/it/per-l-impresa'
        ],
        'Invitalia': [
            '/per-le-imprese',
            '/news',
            '/per-chi-vuole-fare-impresa'
        ],
        'SIMEST': [
            '/per-le-imprese',
            '/per-le-imprese/finanziamenti-agevolati',
            '/media'
        ]
    }
    
    # Load config URLs
    config_path = Path("data/incentives_config.json")
    if config_path.exists():
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        config_urls = {}
        for website in config.get('websites', []):
            name = website.get('name', '')
            if 'MIMIT' in name.upper():
                config_urls['MIMIT'] = website.get('search_paths', [])
            elif 'INVITALIA' in name.upper():
                config_urls['Invitalia'] = website.get('search_paths', [])
            elif 'SIMEST' in name.upper():
                config_urls['SIMEST'] = website.get('search_paths', [])
        
        # Compare
        for website in ['MIMIT', 'Invitalia', 'SIMEST']:
            print(f"\n  {website}:")
            hardcoded = hardcoded_urls.get(website, [])
            configured = config_urls.get(website, [])
            
            print(f"    Hardcoded: {hardcoded}")
            print(f"    Configured: {configured}")
            
            if hardcoded == configured:
                print(f"    ✅ URLs match")
            else:
                print(f"    ⚠️  URLs differ")
                # Show differences
                only_hardcoded = set(hardcoded) - set(configured)
                only_configured = set(configured) - set(hardcoded)
                
                if only_hardcoded:
                    print(f"    📌 Only in hardcoded: {list(only_hardcoded)}")
                if only_configured:
                    print(f"    📌 Only in config: {list(only_configured)}")

if __name__ == "__main__":
    test_scraper_config()
    compare_urls()
