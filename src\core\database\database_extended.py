#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Estensione del DatabaseManager per SAL, Deadline, Alert e Document
"""

from datetime import datetime, date, timedelta
from typing import Optional, List, Dict, Any
from uuid import UUID

from .database import DatabaseManager
from ..models import SAL, Deadline, Alert, Document, EmailTemplate, DeadlineStatus, Task, TaskStatus
from ..utils import get_logger

logger = get_logger(__name__)

class DatabaseManagerExtended(DatabaseManager):
    """Estensione del DatabaseManager con operazioni aggiuntive"""
    
    # CRUD Operations per SAL
    def create_sal(self, sal: SAL) -> SAL:
        """Crea un nuovo SAL"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            data = self._model_to_dict(sal)
            
            columns = ', '.join(data.keys())
            placeholders = ', '.join(['?' for _ in data])
            
            cursor.execute(
                f"INSERT INTO sals ({columns}) VALUES ({placeholders})",
                list(data.values())
            )
            conn.commit()
            
            logger.info(f"SAL creato: {sal.name}")
            return sal
    
    def get_sal(self, sal_id: UUID) -> Optional[SAL]:
        """Recupera un SAL per ID"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM sals WHERE id = ?", (str(sal_id),))
            row = cursor.fetchone()
            
            if row:
                return self._dict_to_model(dict(row), SAL)
            return None
    
    def get_sals_by_project(self, project_id: UUID) -> List[SAL]:
        """Recupera tutti i SAL di un progetto"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT * FROM sals WHERE project_id = ? ORDER BY sequence_number",
                (str(project_id),)
            )
            rows = cursor.fetchall()
            
            return [self._dict_to_model(dict(row), SAL) for row in rows]

    def get_documents_by_sal(self, sal_id: UUID) -> List[Document]:
        """Recupera tutti i documenti di un SAL"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT * FROM documents WHERE sal_id = ? ORDER BY created_at DESC",
                (str(sal_id),)
            )
            rows = cursor.fetchall()

            return [self._dict_to_model(dict(row), Document) for row in rows]

    def get_deadlines_by_sal(self, sal_id: UUID) -> List[Deadline]:
        """Recupera tutte le scadenze di un SAL"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT * FROM deadlines WHERE sal_id = ? ORDER BY due_date",
                (str(sal_id),)
            )
            rows = cursor.fetchall()

            return [self._dict_to_model(dict(row), Deadline) for row in rows]
    
    def update_sal(self, sal: SAL) -> SAL:
        """Aggiorna un SAL"""
        sal.updated_at = datetime.now()
        
        with self.get_connection() as conn:
            cursor = conn.cursor()
            data = self._model_to_dict(sal)
            
            set_clause = ', '.join([f"{key} = ?" for key in data.keys() if key != 'id'])
            values = [value for key, value in data.items() if key != 'id']
            values.append(str(sal.id))
            
            cursor.execute(
                f"UPDATE sals SET {set_clause} WHERE id = ?",
                values
            )
            conn.commit()
            
            logger.info(f"SAL aggiornato: {sal.name}")
            return sal
    
    def delete_sal(self, sal_id: UUID) -> bool:
        """Elimina un SAL"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM sals WHERE id = ?", (str(sal_id),))
            conn.commit()

            logger.info(f"SAL eliminato: {sal_id}")
            return cursor.rowcount > 0

    def get_all_sals(self) -> List[SAL]:
        """Recupera tutti i SAL"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM sals ORDER BY created_at DESC")
            rows = cursor.fetchall()

            return [self._dict_to_model(dict(row), SAL) for row in rows]
    
    # CRUD Operations per Deadline
    def create_deadline(self, deadline: Deadline) -> Deadline:
        """Crea una nuova scadenza"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            data = self._model_to_dict(deadline)
            
            columns = ', '.join(data.keys())
            placeholders = ', '.join(['?' for _ in data])
            
            cursor.execute(
                f"INSERT INTO deadlines ({columns}) VALUES ({placeholders})",
                list(data.values())
            )
            conn.commit()

            # Invalidate cache
            self._invalidate_deadlines_cache()

            logger.info(f"Scadenza creata: {deadline.title}")
            return deadline
    
    def get_deadline(self, deadline_id: UUID) -> Optional[Deadline]:
        """Recupera una scadenza per ID"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM deadlines WHERE id = ?", (str(deadline_id),))
            row = cursor.fetchone()
            
            if row:
                return self._dict_to_model(dict(row), Deadline)
            return None
    
    def get_deadlines_by_client(self, client_id: UUID) -> List[Deadline]:
        """Recupera tutte le scadenze di un cliente"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT * FROM deadlines WHERE client_id = ? ORDER BY due_date",
                (str(client_id),)
            )
            rows = cursor.fetchall()
            
            return [self._dict_to_model(dict(row), Deadline) for row in rows]
    
    def get_deadlines_by_project(self, project_id: UUID) -> List[Deadline]:
        """Recupera tutte le scadenze di un progetto"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT * FROM deadlines WHERE project_id = ? ORDER BY due_date",
                (str(project_id),)
            )
            rows = cursor.fetchall()

            return [self._dict_to_model(dict(row), Deadline) for row in rows]

    def get_deadlines_by_sal(self, sal_id: UUID) -> List[Deadline]:
        """Recupera tutte le scadenze di un SAL"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT * FROM deadlines WHERE sal_id = ? ORDER BY due_date",
                (str(sal_id),)
            )
            rows = cursor.fetchall()

            return [self._dict_to_model(dict(row), Deadline) for row in rows]
    
    def get_upcoming_deadlines(self, days_ahead: int = 30) -> List[Deadline]:
        """Recupera le scadenze imminenti"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            end_date = (datetime.now().date() + timedelta(days=days_ahead)).isoformat()
            
            cursor.execute(
                """
                SELECT * FROM deadlines 
                WHERE due_date <= ? AND status = 'in_attesa'
                ORDER BY due_date
                """,
                (end_date,)
            )
            rows = cursor.fetchall()
            
            return [self._dict_to_model(dict(row), Deadline) for row in rows]
    
    def get_overdue_deadlines(self) -> List[Deadline]:
        """Recupera le scadenze scadute"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            today = datetime.now().date().isoformat()
            
            cursor.execute(
                """
                SELECT * FROM deadlines 
                WHERE due_date < ? AND status = 'in_attesa'
                ORDER BY due_date
                """,
                (today,)
            )
            rows = cursor.fetchall()
            
            return [self._dict_to_model(dict(row), Deadline) for row in rows]
    
    def update_deadline(self, deadline: Deadline) -> Deadline:
        """Aggiorna una scadenza"""
        deadline.updated_at = datetime.now()
        
        with self.get_connection() as conn:
            cursor = conn.cursor()
            data = self._model_to_dict(deadline)
            
            set_clause = ', '.join([f"{key} = ?" for key in data.keys() if key != 'id'])
            values = [value for key, value in data.items() if key != 'id']
            values.append(str(deadline.id))
            
            cursor.execute(
                f"UPDATE deadlines SET {set_clause} WHERE id = ?",
                values
            )
            conn.commit()

            # Invalidate cache
            self._invalidate_deadlines_cache()

            logger.info(f"Scadenza aggiornata: {deadline.title}")
            return deadline
    
    def complete_deadline(self, deadline_id: UUID, completed_by: str = "Sistema") -> bool:
        """Marca una scadenza come completata"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute(
                """
                UPDATE deadlines 
                SET status = 'completato', 
                    completed_date = ?, 
                    completed_by = ?,
                    updated_at = ?
                WHERE id = ?
                """,
                (
                    datetime.now().date().isoformat(),
                    completed_by,
                    datetime.now().isoformat(),
                    str(deadline_id)
                )
            )
            conn.commit()
            
            logger.info(f"Scadenza completata: {deadline_id}")
            return cursor.rowcount > 0
    
    def delete_deadline(self, deadline_id: UUID) -> bool:
        """Elimina una scadenza"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM deadlines WHERE id = ?", (str(deadline_id),))
            conn.commit()

            # Invalidate cache
            self._invalidate_deadlines_cache()

            logger.info(f"Scadenza eliminata: {deadline_id}")
            return cursor.rowcount > 0
    
    def get_all_deadlines(self) -> List[Deadline]:
        """Recupera tutte le scadenze"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM deadlines ORDER BY due_date")
            rows = cursor.fetchall()
            
            return [self._dict_to_model(dict(row), Deadline) for row in rows]
    
    def get_deadlines_by_date_range(self, start_date: date, end_date: date) -> List[Deadline]:
        """Recupera le scadenze in un range di date"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                """
                SELECT * FROM deadlines 
                WHERE due_date >= ? AND due_date <= ?
                ORDER BY due_date
                """,
                (start_date.isoformat(), end_date.isoformat())
            )
            rows = cursor.fetchall()
            
            return [self._dict_to_model(dict(row), Deadline) for row in rows]
    
    # CRUD Operations per Alert
    def create_alert(self, alert: Alert) -> Alert:
        """Crea un nuovo alert"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            data = self._model_to_dict(alert)
            
            columns = ', '.join(data.keys())
            placeholders = ', '.join(['?' for _ in data])
            
            cursor.execute(
                f"INSERT INTO alerts ({columns}) VALUES ({placeholders})",
                list(data.values())
            )
            conn.commit()
            
            logger.info(f"Alert creato: {alert.title}")
            return alert
    
    def get_alert(self, alert_id: UUID) -> Optional[Alert]:
        """Recupera un alert per ID"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM alerts WHERE id = ?", (str(alert_id),))
            row = cursor.fetchone()
            
            if row:
                return self._dict_to_model(dict(row), Alert)
            return None
    
    def get_active_alerts(self) -> List[Alert]:
        """Recupera tutti gli alert attivi"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT * FROM alerts WHERE status = 'attivo' ORDER BY created_at DESC"
            )
            rows = cursor.fetchall()
            
            return [self._dict_to_model(dict(row), Alert) for row in rows]
    
    def get_alerts_by_deadline(self, deadline_id: UUID) -> List[Alert]:
        """Recupera tutti gli alert di una scadenza"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT * FROM alerts WHERE deadline_id = ? ORDER BY created_at DESC",
                (str(deadline_id),)
            )
            rows = cursor.fetchall()
            
            return [self._dict_to_model(dict(row), Alert) for row in rows]
    
    def update_alert(self, alert: Alert) -> Alert:
        """Aggiorna un alert"""
        alert.updated_at = datetime.now()
        
        with self.get_connection() as conn:
            cursor = conn.cursor()
            data = self._model_to_dict(alert)
            
            set_clause = ', '.join([f"{key} = ?" for key in data.keys() if key != 'id'])
            values = [value for key, value in data.items() if key != 'id']
            values.append(str(alert.id))
            
            cursor.execute(
                f"UPDATE alerts SET {set_clause} WHERE id = ?",
                values
            )
            conn.commit()
            
            logger.info(f"Alert aggiornato: {alert.title}")
            return alert
    
    def dismiss_alert(self, alert_id: UUID, dismissed_by: str = "Utente") -> bool:
        """Ignora un alert"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute(
                """
                UPDATE alerts 
                SET status = 'ignorato', 
                    dismissed_at = ?, 
                    dismissed_by = ?,
                    updated_at = ?
                WHERE id = ?
                """,
                (
                    datetime.now().isoformat(),
                    dismissed_by,
                    datetime.now().isoformat(),
                    str(alert_id)
                )
            )
            conn.commit()
            
            logger.info(f"Alert ignorato: {alert_id}")
            return cursor.rowcount > 0
    
    def mark_email_sent(self, alert_id: UUID) -> bool:
        """Marca un alert come email inviata"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute(
                """
                UPDATE alerts 
                SET email_sent = 1, 
                    email_sent_at = ?,
                    updated_at = ?
                WHERE id = ?
                """,
                (
                    datetime.now().isoformat(),
                    datetime.now().isoformat(),
                    str(alert_id)
                )
            )
            conn.commit()
            
            return cursor.rowcount > 0
    
    # CRUD Operations per Document
    def create_document(self, document: Document) -> Document:
        """Crea un nuovo documento"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            data = self._model_to_dict(document)
            
            columns = ', '.join(data.keys())
            placeholders = ', '.join(['?' for _ in data])
            
            cursor.execute(
                f"INSERT INTO documents ({columns}) VALUES ({placeholders})",
                list(data.values())
            )
            conn.commit()
            
            logger.info(f"Documento creato: {document.name}")
            return document
    
    def get_document(self, document_id: UUID) -> Optional[Document]:
        """Recupera un documento per ID"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM documents WHERE id = ?", (str(document_id),))
            row = cursor.fetchone()
            
            if row:
                return self._dict_to_model(dict(row), Document)
            return None
    
    def get_documents_by_project(self, project_id: UUID) -> List[Document]:
        """Recupera tutti i documenti di un progetto"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT * FROM documents WHERE project_id = ? ORDER BY created_at DESC",
                (str(project_id),)
            )
            rows = cursor.fetchall()

            return [self._dict_to_model(dict(row), Document) for row in rows]

    def get_documents_by_client(self, client_id: UUID) -> List[Document]:
        """Recupera tutti i documenti di un cliente"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT * FROM documents WHERE client_id = ? ORDER BY created_at DESC",
                (str(client_id),)
            )
            rows = cursor.fetchall()

            return [self._dict_to_model(dict(row), Document) for row in rows]

    def get_documents_by_sal(self, sal_id: UUID) -> List[Document]:
        """Recupera tutti i documenti di un SAL"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT * FROM documents WHERE sal_id = ? ORDER BY created_at DESC",
                (str(sal_id),)
            )
            rows = cursor.fetchall()

            return [self._dict_to_model(dict(row), Document) for row in rows]

    def get_all_documents(self) -> List[Document]:
        """Recupera tutti i documenti"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM documents ORDER BY created_at DESC")
            rows = cursor.fetchall()

            return [self._dict_to_model(dict(row), Document) for row in rows]

    def update_document(self, document: Document) -> bool:
        """Aggiorna un documento"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                document.updated_at = datetime.now()
                data = self._model_to_dict(document)

                # Rimuovi l'ID dai dati da aggiornare
                document_id = data.pop('id')

                set_clause = ', '.join([f"{key} = ?" for key in data.keys()])
                values = list(data.values()) + [document_id]

                cursor.execute(
                    f"UPDATE documents SET {set_clause} WHERE id = ?",
                    values
                )
                conn.commit()

                logger.info(f"Documento aggiornato: {document.name}")
                return True
        except Exception as e:
            logger.error(f"Errore aggiornamento documento: {e}")
            return False

    def delete_document(self, document_id: UUID) -> bool:
        """Elimina un documento"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM documents WHERE id = ?", (str(document_id),))
                conn.commit()

                logger.info(f"Documento eliminato: {document_id}")
                return True
        except Exception as e:
            logger.error(f"Errore eliminazione documento: {e}")
            return False
    
    # Operazioni per Email Templates
    def create_email_template(self, template: EmailTemplate) -> EmailTemplate:
        """Crea un nuovo template email"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            data = self._model_to_dict(template)
            
            columns = ', '.join(data.keys())
            placeholders = ', '.join(['?' for _ in data])
            
            cursor.execute(
                f"INSERT INTO email_templates ({columns}) VALUES ({placeholders})",
                list(data.values())
            )
            conn.commit()
            
            logger.info(f"Template email creato: {template.name}")
            return template
    
    def get_email_templates(self, active_only: bool = True) -> List[EmailTemplate]:
        """Recupera tutti i template email"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            query = "SELECT * FROM email_templates"
            if active_only:
                query += " WHERE is_active = 1"
            query += " ORDER BY name"
            
            cursor.execute(query)
            rows = cursor.fetchall()
            
            return [self._dict_to_model(dict(row), EmailTemplate) for row in rows]
    
    # CRUD Operations per Tasks
    def create_task(self, task: Task) -> bool:
        """Crea una nuova task"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                data = self._model_to_dict(task)
                
                columns = ', '.join(data.keys())
                placeholders = ', '.join(['?' for _ in data])
                
                cursor.execute(
                    f"INSERT INTO tasks ({columns}) VALUES ({placeholders})",
                    list(data.values())
                )
                conn.commit()

                # Invalidate cache
                self._invalidate_tasks_cache(str(task.deadline_id))

                logger.info(f"Task creata: {task.title}")
                return True
        except Exception as e:
            logger.error(f"Errore creazione task: {e}")
            return False
    
    def get_task(self, task_id: UUID) -> Optional[Task]:
        """Recupera una task per ID"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM tasks WHERE id = ?", (str(task_id),))
                row = cursor.fetchone()
                
                if row:
                    return self._dict_to_model(dict(row), Task)
                return None
        except Exception as e:
            logger.error(f"Errore recupero task {task_id}: {e}")
            return None
    
    def get_all_tasks(self) -> List[Task]:
        """Recupera tutte le task"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM tasks ORDER BY created_at DESC")
                rows = cursor.fetchall()
                
                return [self._dict_to_model(dict(row), Task) for row in rows]
        except Exception as e:
            logger.error(f"Errore recupero tasks: {e}")
            return []
    
    def get_tasks_by_deadline(self, deadline_id: UUID) -> List[Task]:
        """Recupera tutte le task di una scadenza"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT * FROM tasks WHERE deadline_id = ? ORDER BY created_at",
                    (str(deadline_id),)
                )
                rows = cursor.fetchall()
                
                return [self._dict_to_model(dict(row), Task) for row in rows]
        except Exception as e:
            logger.error(f"Errore recupero tasks per deadline {deadline_id}: {e}")
            return []
    
    def get_tasks_by_project(self, project_id: UUID) -> List[Task]:
        """Recupera tutte le task di un progetto"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT * FROM tasks WHERE project_id = ? ORDER BY created_at",
                    (str(project_id),)
                )
                rows = cursor.fetchall()
                
                return [self._dict_to_model(dict(row), Task) for row in rows]
        except Exception as e:
            logger.error(f"Errore recupero tasks per progetto {project_id}: {e}")
            return []
    
    def get_tasks_by_status(self, status: TaskStatus) -> List[Task]:
        """Recupera tutte le task con un determinato status"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT * FROM tasks WHERE status = ? ORDER BY created_at",
                    (status.value,)
                )
                rows = cursor.fetchall()

                return [self._dict_to_model(dict(row), Task) for row in rows]
        except Exception as e:
            logger.error(f"Errore recupero tasks per status {status}: {e}")
            return []

    def get_tasks_by_date_range(self, start_date: date, end_date: date) -> List[Task]:
        """Recupera le task in un range di date (basato su due_date, start_date o created_at)"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """
                    SELECT * FROM tasks
                    WHERE (
                        (due_date IS NOT NULL AND date(due_date) >= ? AND date(due_date) <= ?) OR
                        (start_date IS NOT NULL AND start_date >= ? AND start_date <= ?) OR
                        (due_date IS NULL AND start_date IS NULL AND date(created_at) >= ? AND date(created_at) <= ?)
                    )
                    ORDER BY COALESCE(due_date, start_date, created_at)
                    """,
                    (start_date.isoformat(), end_date.isoformat(),
                     start_date.isoformat(), end_date.isoformat(),
                     start_date.isoformat(), end_date.isoformat())
                )
                rows = cursor.fetchall()

                return [self._dict_to_model(dict(row), Task) for row in rows]
        except Exception as e:
            logger.error(f"Errore recupero tasks per range di date: {e}")
            return []
    
    def update_task(self, task: Task) -> bool:
        """Aggiorna una task"""
        try:
            task.updated_at = datetime.now()
            
            with self.get_connection() as conn:
                cursor = conn.cursor()
                data = self._model_to_dict(task)
                
                set_clause = ', '.join([f"{key} = ?" for key in data.keys() if key != 'id'])
                values = [value for key, value in data.items() if key != 'id']
                values.append(str(task.id))
                
                cursor.execute(
                    f"UPDATE tasks SET {set_clause} WHERE id = ?",
                    values
                )
                conn.commit()
                
                logger.info(f"Task aggiornata: {task.title}")
                return True
        except Exception as e:
            logger.error(f"Errore aggiornamento task {task.id}: {e}")
            return False
    
    def delete_task(self, task_id: UUID) -> bool:
        """Elimina una task"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM tasks WHERE id = ?", (str(task_id),))
                conn.commit()
                
                logger.info(f"Task eliminata: {task_id}")
                return cursor.rowcount > 0
        except Exception as e:
            logger.error(f"Errore eliminazione task {task_id}: {e}")
            return False
    
    def complete_task(self, task_id: UUID) -> bool:
        """Completa una task"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """UPDATE tasks 
                       SET status = ?, progress_percentage = 100, updated_at = ?
                       WHERE id = ?""",
                    (TaskStatus.COMPLETED.value, datetime.now().isoformat(), str(task_id))
                )
                conn.commit()
                
                logger.info(f"Task completata: {task_id}")
                return cursor.rowcount > 0
        except Exception as e:
            logger.error(f"Errore completamento task {task_id}: {e}")
            return False

    # Subtask Management Methods
    def get_subtasks(self, parent_task_id: UUID) -> List[Task]:
        """Recupera tutte le sottotask di una task padre"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT * FROM tasks WHERE parent_task_id = ? ORDER BY created_at",
                    (str(parent_task_id),)
                )
                rows = cursor.fetchall()
                
                return [self._dict_to_model(dict(row), Task) for row in rows]
        except Exception as e:
            logger.error(f"Errore recupero subtasks per task {parent_task_id}: {e}")
            return []
    
    def get_parent_task(self, task_id: UUID) -> Optional[Task]:
        """Recupera la task padre di una subtask"""
        try:
            task = self.get_task(task_id)
            if task and task.parent_task_id:
                return self.get_task(task.parent_task_id)
            return None
        except Exception as e:
            logger.error(f"Errore recupero parent task per {task_id}: {e}")
            return None
    
    def get_task_hierarchy(self, task_id: UUID) -> Dict[str, Any]:
        """Recupera l'intera gerarchia di una task (parent e subtasks)"""
        try:
            task = self.get_task(task_id)
            if not task:
                return {}
            
            # Find the root parent
            root_task = task
            while root_task.parent_task_id:
                parent = self.get_parent_task(root_task.id)
                if not parent:
                    break
                root_task = parent
            
            # Build hierarchy recursively
            def build_tree(task_obj: Task) -> Dict[str, Any]:
                subtasks = self.get_subtasks(task_obj.id)
                return {
                    "task": task_obj,
                    "subtasks": [build_tree(subtask) for subtask in subtasks]
                }
            
            return build_tree(root_task)
        except Exception as e:
            logger.error(f"Errore creazione gerarchia task {task_id}: {e}")
            return {}
    
    def create_subtask(self, parent_task_id: UUID, subtask: Task) -> bool:
        """Crea una sottotask collegata a una task padre"""
        try:
            # Inherit project and deadline from parent if not specified
            parent_task = self.get_task(parent_task_id)
            if parent_task:
                if not subtask.project_id:
                    subtask.project_id = parent_task.project_id
                if not subtask.deadline_id:
                    subtask.deadline_id = parent_task.deadline_id
            
            subtask.parent_task_id = parent_task_id
            return self.create_task(subtask)
        except Exception as e:
            logger.error(f"Errore creazione subtask per parent {parent_task_id}: {e}")
            return False
    
    def update_parent_task_progress(self, parent_task_id: UUID) -> bool:
        """Aggiorna il progresso di una task padre basato sulle sottotask"""
        try:
            subtasks = self.get_subtasks(parent_task_id)
            if not subtasks:
                return True  # No subtasks, no update needed
            
            # Calculate average progress
            total_progress = sum(task.progress_percentage for task in subtasks)
            avg_progress = total_progress // len(subtasks)
            
            # Update parent task
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """UPDATE tasks 
                       SET progress_percentage = ?, updated_at = ?
                       WHERE id = ?""",
                    (avg_progress, datetime.now().isoformat(), str(parent_task_id))
                )
                conn.commit()
                
                # Auto-complete parent if all subtasks are completed
                if all(task.status == TaskStatus.COMPLETED for task in subtasks):
                    cursor.execute(
                        """UPDATE tasks 
                           SET status = ?, progress_percentage = 100, updated_at = ?
                           WHERE id = ?""",
                        (TaskStatus.COMPLETED.value, datetime.now().isoformat(), str(parent_task_id))
                    )
                    conn.commit()
                    logger.info(f"Parent task {parent_task_id} auto-completed")
                
                logger.info(f"Parent task progress updated: {avg_progress}%")
                return True
        except Exception as e:
            logger.error(f"Errore aggiornamento progresso parent task {parent_task_id}: {e}")
            return False
    
    def delete_task_with_subtasks(self, task_id: UUID) -> bool:
        """Elimina una task e tutte le sue sottotask"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Delete all subtasks first (recursive)
                subtasks = self.get_subtasks(task_id)
                for subtask in subtasks:
                    self.delete_task_with_subtasks(subtask.id)
                
                # Delete the task itself
                cursor.execute("DELETE FROM tasks WHERE id = ?", (str(task_id),))
                conn.commit()
                
                logger.info(f"Task e subtasks eliminate: {task_id}")
                return cursor.rowcount > 0
        except Exception as e:
            logger.error(f"Errore eliminazione task con subtasks {task_id}: {e}")
            return False
    
    def get_top_level_tasks(self) -> List[Task]:
        """Recupera solo le task di primo livello (senza parent)"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT * FROM tasks WHERE parent_task_id IS NULL ORDER BY created_at DESC"
                )
                rows = cursor.fetchall()
                
                return [self._dict_to_model(dict(row), Task) for row in rows]
        except Exception as e:
            logger.error(f"Errore recupero top-level tasks: {e}")
            return []

    # Cache invalidation methods
    def _invalidate_cache(self, cache_type: str, entity_id: str = None):
        """Invalidate cache when data changes"""
        try:
            from core.services.data_cache_manager import get_data_cache
            data_cache = get_data_cache(self)

            if cache_type == "clients":
                data_cache.invalidate_clients_cache()
            elif cache_type == "projects":
                data_cache.invalidate_projects_cache(entity_id)
            elif cache_type == "tasks":
                data_cache.invalidate_tasks_cache(entity_id)
            elif cache_type == "deadlines":
                data_cache.invalidate_deadlines_cache()
            elif cache_type == "all":
                data_cache.invalidate_all_cache()

        except Exception as e:
            logger.warning(f"Error invalidating cache: {e}")

    def _invalidate_clients_cache(self):
        """Invalidate clients cache"""
        self._invalidate_cache("clients")

    def _invalidate_projects_cache(self, client_id: str = None):
        """Invalidate projects cache"""
        self._invalidate_cache("projects", client_id)

    def _invalidate_tasks_cache(self, deadline_id: str = None):
        """Invalidate tasks cache"""
        self._invalidate_cache("tasks", deadline_id)

    def _invalidate_deadlines_cache(self):
        """Invalidate deadlines cache"""
        self._invalidate_cache("deadlines")