#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for the new tabbed settings interface
"""

import flet as ft
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from core import AppConfig
from core.database import DatabaseManagerExtended
from ui.views.settings import SettingsView

class MockApp:
    """Mock app instance for testing"""
    def __init__(self):
        self.config = AppConfig()
        self.db_manager = DatabaseManagerExtended(self.config.database_path)
        self.page = None

def main(page: ft.Page):
    """Main test function"""
    page.title = "Test Tabbed Settings"
    page.window.width = 1200
    page.window.height = 800
    page.padding = 0
    page.spacing = 0
    
    # Create mock app
    mock_app = MockApp()
    mock_app.page = page
    
    # Create settings view
    settings_view = SettingsView(mock_app)
    
    # Build and add to page
    settings_container = settings_view.build()
    page.add(settings_container)
    
    page.update()

if __name__ == "__main__":
    print("🚀 Testing Tabbed Settings Interface...")
    print("📋 Features to test:")
    print("   ✅ Tab navigation (Essenziali, Integrazioni, Report, Incentivi, Avanzate)")
    print("   ✅ Smooth tab switching")
    print("   ✅ Incentives section with configuration options")
    print("   ✅ Modern UI design")
    print()
    
    ft.app(target=main)
