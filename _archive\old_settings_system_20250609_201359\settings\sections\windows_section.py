#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Windows Integration Settings Section
Modern, self-contained Windows system integration configuration component
"""

import flet as ft
from typing import Dict, Any, List
from ..components import SettingsCard, SettingsCardSection, FormField, SwitchField, DropdownField, ButtonGroup, InfoBox, StatusIndicator
from core import get_logger

logger = get_logger(__name__)

class WindowsSection:
    """
    Self-contained Windows integration settings section with modern UX
    Handles startup, notifications, and system integration features
    """
    
    def __init__(self, controller):
        self.controller = controller
        self.windows_settings = controller.get_setting('windows')
        
        # Windows integration status
        self.integration_status = {
            'startup': 'unknown',
            'notifications': 'unknown',
            'system_tray': 'unknown'
        }
        
        # Register for setting changes
        controller.register_change_callback('windows', self._on_setting_changed)
        
        # Update initial status
        self._update_integration_status()
    
    def _on_setting_changed(self, key: str, new_value: Any, old_value: Any):
        """Handle setting changes"""
        logger.debug(f"Windows setting changed: {key} = {new_value}")
        self._update_integration_status()
    
    def _update_integration_status(self):
        """Update Windows integration status"""
        settings = self.controller.get_setting('windows')
        
        # Update status based on current settings
        self.integration_status['startup'] = 'enabled' if settings.get('startup_enabled') else 'disabled'
        self.integration_status['notifications'] = 'enabled' if settings.get('notifications_enabled') else 'disabled'
        self.integration_status['system_tray'] = 'enabled'  # Always available
    
    def _create_startup_section(self) -> SettingsCardSection:
        """Create Windows startup configuration section"""
        
        def on_startup_enabled_change(e):
            try:
                enabled = e.control.value
                success = self.controller.configure_windows_startup(enabled)
                if success:
                    self._show_success_notification(f"✅ Avvio automatico {'abilitato' if enabled else 'disabilitato'}")
                    self.integration_status['startup'] = 'enabled' if enabled else 'disabled'
                else:
                    self._show_error_notification("❌ Errore configurazione avvio automatico")
                    # Revert the switch if the operation failed
                    e.control.value = not enabled
                    e.control.update()
            except Exception as ex:
                logger.error(f"Error configuring startup: {ex}")
                self._show_error_notification(f"❌ Errore configurazione avvio: {str(ex)}")
                # Revert the switch if the operation failed
                e.control.value = not enabled
                e.control.update()
        
        def on_startup_minimized_change(e):
            self.controller.set_setting('windows', 'startup_minimized', e.control.value)
        
        settings = self.controller.get_setting('windows')
        
        # Startup status indicator
        startup_status = StatusIndicator.create(
            label="Avvio Automatico",
            status=self.integration_status['startup'],
            description="Avvia Agevolami PM insieme a Windows"
        )
        
        # Startup configuration switches
        startup_enabled_switch = SwitchField.create(
            label="Avvia con Windows",
            description="Avvia automaticamente l'applicazione all'avvio di Windows",
            value=settings.get('startup_enabled', False),
            on_change=on_startup_enabled_change,
            color=ft.Colors.BLUE_600
        )
        
        startup_minimized_switch = SwitchField.create(
            label="Avvia Minimizzato",
            description="Avvia l'applicazione minimizzata nella system tray",
            value=settings.get('startup_minimized', True),
            on_change=on_startup_minimized_change,
            disabled=not settings.get('startup_enabled', False)
        )
        
        return SettingsCardSection(
            title="Avvio Automatico",
            description="Configurazione dell'avvio automatico con Windows",
            controls=[
                startup_status,
                startup_enabled_switch,
                startup_minimized_switch,
                InfoBox.create(
                    title="⚠️ Requisiti Sistema",
                    message="Informazioni importanti sull'avvio automatico",
                    type="warning",
                    items=[
                        "Richiede privilegi di scrittura nel registro di Windows",
                        "L'applicazione deve essere installata in una posizione stabile",
                        "Potrebbe essere bloccata da antivirus o politiche aziendali",
                        "Usa 'Avvia Minimizzato' per non disturbare l'avvio"
                    ]
                )
            ]
        )
    
    def _create_notifications_section(self) -> SettingsCardSection:
        """Create Windows notifications configuration section"""
        
        def on_notifications_enabled_change(e):
            self.controller.set_setting('windows', 'notifications_enabled', e.control.value)
        
        def on_notification_sound_change(e):
            self.controller.set_setting('windows', 'notification_sound', e.control.value)
        
        def on_desktop_notifications_change(e):
            self.controller.set_setting('windows', 'notification_desktop_enabled', e.control.value)
        
        def on_priority_filter_change(e):
            self.controller.set_setting('windows', 'notification_priority_filter', e.control.value)
        
        def test_notification(e):
            """Test Windows notification system using real service"""
            try:
                success = self.controller.send_windows_test_notification(
                    "Agevolami PM - Test",
                    "Questa è una notifica di test dal sistema Agevolami PM"
                )
                if success:
                    self._show_success_notification("✅ Notifica Windows inviata con successo!")
                else:
                    self._show_error_notification("❌ Errore invio notifica Windows")
            except Exception as ex:
                self._show_error_notification(f"❌ Errore notifica: {ex}")
        
        def test_deadline_notification(e):
            """Test deadline notification"""
            self._show_success_notification("📅 Test notifica scadenza inviata!")
        
        settings = self.controller.get_setting('windows')
        
        # Notifications status indicator
        notifications_status = StatusIndicator.create(
            label="Notifiche Desktop",
            status=self.integration_status['notifications'],
            description="Sistema notifiche native di Windows"
        )
        
        # Main notification switches
        notifications_enabled_switch = SwitchField.create(
            label="Notifiche Desktop",
            description="Abilita le notifiche native di Windows",
            value=settings.get('notifications_enabled', True),
            on_change=on_notifications_enabled_change,
            color=ft.Colors.ORANGE_600
        )
        
        notification_sound_switch = SwitchField.create(
            label="Suoni Notifiche",
            description="Riproduci suoni con le notifiche",
            value=settings.get('notification_sound', True),
            on_change=on_notification_sound_change,
            disabled=not settings.get('notifications_enabled', True)
        )
        
        desktop_notifications_switch = SwitchField.create(
            label="Toast Notifications",
            description="Mostra notifiche pop-up nell'area notifiche",
            value=settings.get('notification_desktop_enabled', True),
            on_change=on_desktop_notifications_change,
            disabled=not settings.get('notifications_enabled', True)
        )
        
        # Priority filter dropdown
        priority_filter_dropdown = DropdownField.create(
            label="Filtro Priorità",
            options=[
                {"text": "Tutte le notifiche", "value": "all"},
                {"text": "Normali e superiori", "value": "normal"},
                {"text": "Solo importanti", "value": "high"},
                {"text": "Solo urgenti", "value": "urgent"}
            ],
            value=settings.get('notification_priority_filter', 'normal'),
            width=250,
            on_change=on_priority_filter_change
        )
        
        # Test buttons
        test_buttons = ButtonGroup.create_action_group([
            {
                "text": "Test Notifica",
                "icon": ft.Icons.NOTIFICATIONS_ACTIVE,
                "on_click": test_notification,
                "style": "primary"
            },
            {
                "text": "Test Scadenza",
                "icon": ft.Icons.SCHEDULE,
                "on_click": test_deadline_notification,
                "style": "secondary"
            }
        ])
        
        config_row = ft.Row([
            notification_sound_switch,
            desktop_notifications_switch
        ], spacing=32)
        
        return SettingsCardSection(
            title="Notifiche Desktop",
            description="Configurazione delle notifiche native di Windows",
            controls=[
                notifications_status,
                notifications_enabled_switch,
                config_row,
                priority_filter_dropdown,
                test_buttons
            ]
        )
    
    def _create_quiet_hours_section(self) -> SettingsCardSection:
        """Create quiet hours configuration section"""
        
        def on_quiet_hours_enabled_change(e):
            self.controller.set_setting('windows', 'notification_quiet_hours_enabled', e.control.value)
        
        def on_quiet_start_change(e):
            # Validate time format
            time_value = e.control.value
            if self._validate_time_format(time_value):
                self.controller.set_setting('windows', 'notification_quiet_start', time_value)
            else:
                self._show_error_notification("Formato orario non valido (usa HH:MM)")
        
        def on_quiet_end_change(e):
            # Validate time format
            time_value = e.control.value
            if self._validate_time_format(time_value):
                self.controller.set_setting('windows', 'notification_quiet_end', time_value)
            else:
                self._show_error_notification("Formato orario non valido (usa HH:MM)")
        
        settings = self.controller.get_setting('windows')
        
        # Quiet hours switch
        quiet_hours_switch = SwitchField.create(
            label="Orari Silenziosi",
            description="Sospendi le notifiche durante gli orari configurati",
            value=settings.get('notification_quiet_hours_enabled', False),
            on_change=on_quiet_hours_enabled_change,
            color=ft.Colors.PURPLE_600
        )
        
        # Time fields
        quiet_start_field = FormField.create_text_field(
            label="Inizio Silenzioso",
            value=settings.get('notification_quiet_start', '22:00'),
            hint_text="22:00",
            width=120,
            on_change=on_quiet_start_change,
            disabled=not settings.get('notification_quiet_hours_enabled', False),
            prefix_icon=ft.Icons.BEDTIME
        )
        
        quiet_end_field = FormField.create_text_field(
            label="Fine Silenzioso",
            value=settings.get('notification_quiet_end', '08:00'),
            hint_text="08:00",
            width=120,
            on_change=on_quiet_end_change,
            disabled=not settings.get('notification_quiet_hours_enabled', False),
            prefix_icon=ft.Icons.WB_SUNNY
        )
        
        time_row = ft.Row([
            quiet_start_field,
            quiet_end_field
        ], spacing=16)
        
        return SettingsCardSection(
            title="Orari Silenziosi",
            description="Configura quando sospendere le notifiche non urgenti",
            controls=[
                quiet_hours_switch,
                time_row,
                InfoBox.create(
                    title="🌙 Funzionamento Orari Silenziosi",
                    message="Come funzionano gli orari silenziosi",
                    type="info",
                    items=[
                        "Le notifiche urgenti passano sempre",
                        "Le notifiche normali vengono sospese",
                        "Formato orario: HH:MM (24 ore)",
                        "Supporta orari che attraversano la mezzanotte"
                    ]
                )
            ]
        )
    
    def _create_system_integration_section(self) -> SettingsCardSection:
        """Create system integration section"""
        
        def open_task_manager(e):
            """Open Windows Task Manager"""
            self._show_success_notification("Apertura Task Manager...")
        
        def open_startup_folder(e):
            """Open Windows startup folder"""
            self._show_success_notification("Apertura cartella Esecuzione automatica...")
        
        def show_system_info(e):
            """Show system information"""
            self._show_success_notification("Informazioni sistema in arrivo...")
        
        # System integration actions
        system_actions = ButtonGroup.create_action_group([
            {
                "text": "Task Manager",
                "icon": ft.Icons.SETTINGS_APPLICATIONS,
                "on_click": open_task_manager,
                "style": "secondary"
            },
            {
                "text": "Cartella Startup",
                "icon": ft.Icons.FOLDER_OPEN,
                "on_click": open_startup_folder,
                "style": "secondary"
            },
            {
                "text": "Info Sistema",
                "icon": ft.Icons.INFO,
                "on_click": show_system_info,
                "style": "secondary"
            }
        ])
        
        # System status summary
        settings = self.controller.get_setting('windows')
        status_items = []
        
        if settings.get('startup_enabled'):
            status_items.append("✅ Avvio automatico configurato")
        else:
            status_items.append("❌ Avvio automatico disabilitato")
        
        if settings.get('notifications_enabled'):
            status_items.append("✅ Notifiche desktop abilitate")
        else:
            status_items.append("❌ Notifiche desktop disabilitate")
        
        if settings.get('notification_quiet_hours_enabled'):
            start = settings.get('notification_quiet_start', '22:00')
            end = settings.get('notification_quiet_end', '08:00')
            status_items.append(f"🌙 Orari silenziosi: {start} - {end}")
        else:
            status_items.append("🔔 Orari silenziosi disabilitati")
        
        system_status = InfoBox.create(
            title="🖥️ Stato Integrazione Windows",
            message="Riepilogo configurazione sistema",
            type="info",
            items=status_items
        )
        
        return SettingsCardSection(
            title="Integrazione Sistema",
            description="Strumenti per la gestione dell'integrazione con Windows",
            controls=[
                system_status,
                system_actions
            ]
        )
    
    def _validate_time_format(self, time_str: str) -> bool:
        """Validate time format (HH:MM)"""
        try:
            parts = time_str.split(':')
            if len(parts) != 2:
                return False
            
            hour = int(parts[0])
            minute = int(parts[1])
            
            return 0 <= hour <= 23 and 0 <= minute <= 59
        except (ValueError, IndexError):
            return False
    
    def _show_success_notification(self, message: str):
        """Show success notification"""
        logger.info(f"SUCCESS: {message}")
    
    def _show_error_notification(self, message: str):
        """Show error notification"""
        logger.error(f"ERROR: {message}")
    
    def build(self) -> SettingsCard:
        """Build the Windows integration settings card"""
        
        # Update integration status
        self._update_integration_status()
        
        # Create sections
        startup_section = self._create_startup_section()
        notifications_section = self._create_notifications_section()
        quiet_hours_section = self._create_quiet_hours_section()
        system_section = self._create_system_integration_section()
        
        # Create card content
        card_content = [
            startup_section.build(),
            notifications_section.build(),
            quiet_hours_section.build(),
            system_section.build()
        ]
        
        # Create save/reset actions
        def save_settings(e):
            success = self.controller.save_settings()
            if success:
                self._show_success_notification("Impostazioni Windows salvate!")
            else:
                self._show_error_notification("Errore durante il salvataggio")
        
        def reset_settings(e):
            self.controller.reset_to_defaults('windows')
            self._show_success_notification("Impostazioni Windows ripristinate")
        
        card_actions = [
            ft.ElevatedButton(
                text="Salva Windows",
                icon=ft.Icons.SAVE,
                on_click=save_settings,
                bgcolor=ft.Colors.BLUE_600,
                color=ft.Colors.WHITE
            ),
            ft.OutlinedButton(
                text="Ripristina",
                icon=ft.Icons.RESTORE,
                on_click=reset_settings
            )
        ]
        
        # Determine card status based on integration level
        settings = self.controller.get_setting('windows')
        enabled_features = sum([
            settings.get('startup_enabled', False),
            settings.get('notifications_enabled', True),
            settings.get('notification_quiet_hours_enabled', False)
        ])
        
        if enabled_features >= 2:
            card_icon = ft.Icons.INTEGRATION_INSTRUCTIONS
            card_icon_color = ft.Colors.GREEN_600
        elif enabled_features >= 1:
            card_icon = ft.Icons.COMPUTER
            card_icon_color = ft.Colors.BLUE_600
        else:
            card_icon = ft.Icons.DESKTOP_WINDOWS
            card_icon_color = ft.Colors.GREY_600
        
        return SettingsCard(
            title="Integrazione Windows",
            description="Configura l'integrazione con il sistema operativo Windows",
            icon=card_icon,
            icon_color=card_icon_color,
            expanded=True,
            content=card_content,
            actions=card_actions,
            accent_color=ft.Colors.BLUE_600
        ) 