#!/usr/bin/env python3
"""
Test the simplified settings system
"""

import sys
import os
sys.path.append('src')

def test_settings_import():
    """Test that settings can be imported successfully"""
    try:
        # Test basic Python imports first
        import json
        import os
        print("✅ Basic imports successful")

        # Test core imports
        from core import get_logger
        print("✅ Core imports successful")

        # Test settings import
        from ui.views.settings import SettingsView
        print("✅ SettingsView import successful")
        return True
    except Exception as e:
        print(f"❌ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_settings_creation():
    """Test that settings view can be created"""
    try:
        from ui.views.settings import SettingsView
        
        # Mock app instance
        class MockApp:
            def __init__(self):
                self.config = MockConfig()
        
        class MockConfig:
            def __init__(self):
                self.data_dir = "test_data"
        
        # Create settings view
        app = MockApp()
        settings_view = SettingsView(app)
        
        print("✅ SettingsView creation successful")
        print(f"   - Current section: {settings_view.current_section}")
        print(f"   - Settings categories: {list(settings_view.settings.keys())}")
        return True
        
    except Exception as e:
        print(f"❌ SettingsView creation failed: {e}")
        return False

def test_settings_build():
    """Test that settings view can be built"""
    try:
        from ui.views.settings import SettingsView
        
        # Mock app instance
        class MockApp:
            def __init__(self):
                self.config = MockConfig()
                self.page = MockPage()
        
        class MockConfig:
            def __init__(self):
                self.data_dir = "test_data"
        
        class MockPage:
            def update(self):
                pass
        
        # Create and build settings view
        app = MockApp()
        settings_view = SettingsView(app)
        
        # This should not crash
        container = settings_view.build()
        
        print("✅ SettingsView build successful")
        print(f"   - Container type: {type(container)}")
        return True
        
    except Exception as e:
        print(f"❌ SettingsView build failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Simplified Settings System")
    print("=" * 50)
    
    tests = [
        test_settings_import,
        test_settings_creation,
        test_settings_build
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All tests passed! Settings system is working correctly.")
    else:
        print("⚠️ Some tests failed. Check the errors above.")
