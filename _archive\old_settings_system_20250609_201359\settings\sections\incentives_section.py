#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Incentives Monitoring Section for Settings
Configuration interface for Italian financial incentives monitoring
"""

import flet as ft
from typing import Dict, Any, Optional, List
from core import get_logger

logger = get_logger(__name__)

class IncentivesSection:
    """Settings section for incentives monitoring configuration"""
    
    def __init__(self, controller):
        self.controller = controller
        self.settings = self._load_settings()

        # References for dynamic controls
        self.llm_model_dropdown = ft.Ref[ft.Dropdown]()
        
    def _load_settings(self) -> Dict[str, Any]:
        """Load incentives monitoring settings"""
        try:
            if self.controller:
                return self.controller.get_settings().get('incentives', {})
            return {}
        except Exception as e:
            logger.error(f"Error loading incentives settings: {e}")
            return {}
    
    def build(self) -> ft.Container:
        """Build the incentives settings section"""
        return ft.Container(
            content=ft.Column([
                # Section header
                self._create_section_header(),
                
                # Configuration cards
                self._create_monitoring_config(),
                self._create_llm_config(),
                self._create_notification_config(),
                self._create_websites_config(),
                
                # Action buttons
                self._create_action_buttons()
            ], spacing=16),
            padding=ft.padding.all(20),
            bgcolor=ft.Colors.WHITE,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )
    
    def _create_section_header(self) -> ft.Container:
        """Create section header with title and description"""
        return ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Icon(
                        ft.Icons.TRENDING_UP,
                        size=32,
                        color=ft.Colors.PURPLE_600
                    ),
                    ft.Column([
                        ft.Text(
                            "Monitoraggio Incentivi Automatico",
                            size=20,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.GREY_800
                        ),
                        ft.Text(
                            "Configurazione del sistema di monitoraggio automatico per incentivi finanziari italiani",
                            size=14,
                            color=ft.Colors.GREY_600
                        )
                    ], spacing=4, expand=True)
                ], spacing=12),
                
                ft.Divider(color=ft.Colors.GREY_300)
            ], spacing=12),
            padding=ft.padding.only(bottom=8)
        )
    
    def _create_monitoring_config(self) -> ft.Container:
        """Create monitoring configuration card"""
        return ft.Container(
            content=ft.Column([
                ft.Text(
                    "🔍 Configurazione Monitoraggio",
                    size=16,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.GREY_800
                ),
                
                ft.Row([
                    ft.Column([
                        ft.Text("Frequenza controllo:", size=12, color=ft.Colors.GREY_600),
                        ft.Dropdown(
                            width=200,
                            options=[
                                ft.dropdown.Option("daily", "Giornaliero"),
                                ft.dropdown.Option("weekly", "Settimanale"),
                                ft.dropdown.Option("monthly", "Mensile")
                            ],
                            value=self.settings.get('frequency', 'weekly')
                        )
                    ], spacing=4),
                    
                    ft.Container(width=20),
                    
                    ft.Column([
                        ft.Text("Parole chiave:", size=12, color=ft.Colors.GREY_600),
                        ft.TextField(
                            hint_text="incentivi, finanziamenti, bandi...",
                            width=300,
                            value=self.settings.get('keywords', '')
                        )
                    ], spacing=4)
                ], alignment=ft.MainAxisAlignment.START),
                
                ft.Row([
                    ft.Switch(
                        label="Monitoraggio attivo",
                        value=self.settings.get('enabled', False)
                    ),
                    ft.Container(width=20),
                    ft.Switch(
                        label="Solo nuovi contenuti",
                        value=self.settings.get('only_new', True)
                    )
                ])
            ], spacing=12),
            padding=ft.padding.all(16),
            bgcolor=ft.Colors.BLUE_50,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.BLUE_200)
        )
    
    def _create_llm_config(self) -> ft.Container:
        """Create LLM configuration card"""
        return ft.Container(
            content=ft.Column([
                ft.Text(
                    "🤖 Configurazione LLM (OpenRouter)",
                    size=16,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.GREY_800
                ),
                
                ft.Row([
                    ft.Column([
                        ft.Text("API Key:", size=12, color=ft.Colors.GREY_600),
                        ft.TextField(
                            hint_text="sk-or-...",
                            width=300,
                            password=True,
                            value=self.settings.get('openrouter_api_key', '')
                        )
                    ], spacing=4),
                    
                    ft.Container(width=20),
                    
                    ft.Column([
                        ft.Text("Modello:", size=12, color=ft.Colors.GREY_600),
                        ft.Row([
                            ft.Dropdown(
                                width=180,
                                options=self._get_llm_model_options(),
                                value=self.settings.get('llm_model', 'deepseek/deepseek-r1-0528-qwen3-8b:free'),
                                ref=self.llm_model_dropdown
                            ),
                            ft.IconButton(
                                icon=ft.Icons.ADD,
                                tooltip="Aggiungi modello personalizzato",
                                icon_size=16,
                                on_click=self._add_custom_model
                            )
                        ], spacing=4)
                    ], spacing=4)
                ]),
                
                ft.Text(
                    "💡 L'LLM analizzerà i contenuti trovati e genererà riassunti intelligenti",
                    size=12,
                    color=ft.Colors.GREY_500,
                    italic=True
                )
            ], spacing=12),
            padding=ft.padding.all(16),
            bgcolor=ft.Colors.GREEN_50,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.GREEN_200)
        )
    
    def _create_notification_config(self) -> ft.Container:
        """Create notification configuration card"""
        return ft.Container(
            content=ft.Column([
                ft.Text(
                    "📧 Configurazione Notifiche",
                    size=16,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.GREY_800
                ),
                
                ft.Row([
                    ft.Switch(
                        label="Email automatiche",
                        value=self.settings.get('email_notifications', True)
                    ),
                    ft.Container(width=20),
                    ft.Switch(
                        label="Notifiche desktop",
                        value=self.settings.get('desktop_notifications', False)
                    )
                ]),
                
                ft.Row([
                    ft.Column([
                        ft.Text("Email destinatario:", size=12, color=ft.Colors.GREY_600),
                        ft.TextField(
                            hint_text="<EMAIL>",
                            width=300,
                            value=self.settings.get('notification_email', '')
                        )
                    ], spacing=4)
                ])
            ], spacing=12),
            padding=ft.padding.all(16),
            bgcolor=ft.Colors.ORANGE_50,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.ORANGE_200)
        )
    
    def _create_websites_config(self) -> ft.Container:
        """Create websites monitoring configuration"""
        return ft.Container(
            content=ft.Column([
                ft.Text(
                    "🌐 Siti Web Monitorati",
                    size=16,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.GREY_800
                ),
                
                ft.Column([
                    ft.Row([
                        ft.Checkbox(value=True, disabled=True),
                        ft.Text("MIMIT - Ministero Imprese", expand=True),
                        ft.Text("mimit.gov.it", size=12, color=ft.Colors.GREY_500)
                    ]),
                    ft.Row([
                        ft.Checkbox(value=True, disabled=True),
                        ft.Text("Invitalia", expand=True),
                        ft.Text("invitalia.it", size=12, color=ft.Colors.GREY_500)
                    ]),
                    ft.Row([
                        ft.Checkbox(value=True, disabled=True),
                        ft.Text("SIMEST", expand=True),
                        ft.Text("simest.it", size=12, color=ft.Colors.GREY_500)
                    ]),
                    ft.Row([
                        ft.Checkbox(value=True, disabled=True),
                        ft.Text("Regione Campania", expand=True),
                        ft.Text("regione.campania.it", size=12, color=ft.Colors.GREY_500)
                    ])
                ], spacing=8),
                
                ft.Container(height=12),

                ft.Row([
                    ft.Column([
                        ft.Text("URL personalizzati (opzionale):", size=12, color=ft.Colors.GREY_600),
                        ft.TextField(
                            hint_text="https://example.com/incentivi (uno per riga)",
                            width=400,
                            multiline=True,
                            min_lines=2,
                            max_lines=4,
                            value=self.settings.get('custom_urls', '')
                        )
                    ], spacing=4)
                ]),

                ft.Text(
                    "ℹ️ Altri siti web potranno essere aggiunti nelle prossime versioni",
                    size=12,
                    color=ft.Colors.GREY_500,
                    italic=True
                )
            ], spacing=12),
            padding=ft.padding.all(16),
            bgcolor=ft.Colors.PURPLE_50,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.PURPLE_200)
        )
    
    def _create_action_buttons(self) -> ft.Container:
        """Create action buttons"""
        def test_configuration(e):
            """Test the monitoring configuration"""
            try:
                # Test web scraping connections
                from core.services.web_scraping_service import WebScrapingService
                from core.models.incentive_models import IncentiveSource

                config = {
                    'sources': [IncentiveSource.MIMIT, IncentiveSource.INVITALIA, IncentiveSource.SIMEST],
                    'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'request_delay_seconds': 1.0
                }

                scraper = WebScrapingService(config)

                # Test each source
                results = []
                for source in [IncentiveSource.MIMIT, IncentiveSource.INVITALIA, IncentiveSource.SIMEST, IncentiveSource.CAMPANIA]:
                    success = scraper.test_connection(source)
                    results.append(f"{source.value}: {'✅' if success else '❌'}")

                scraper.close()

                # Show results
                result_text = "Test Connessioni:\n" + "\n".join(results)
                logger.info(f"Connection test results: {result_text}")

                # TODO: Show dialog with results

            except Exception as ex:
                logger.error(f"Test configuration error: {ex}")

        def save_settings(e):
            """Save incentives settings"""
            try:
                # TODO: Collect values from form fields and save to config
                logger.info("Saving incentives settings...")

                # For now, just show success message
                logger.info("✅ Incentives settings saved successfully")

            except Exception as ex:
                logger.error(f"Save settings error: {ex}")
        
        return ft.Container(
            content=ft.Row([
                ft.OutlinedButton(
                    text="Test Configurazione",
                    icon=ft.Icons.PLAY_ARROW,
                    on_click=test_configuration
                ),
                ft.Container(expand=True),
                ft.ElevatedButton(
                    text="Salva Impostazioni",
                    icon=ft.Icons.SAVE,
                    bgcolor=ft.Colors.PURPLE_600,
                    color=ft.Colors.WHITE,
                    on_click=save_settings
                )
            ]),
            padding=ft.padding.only(top=16)
        )
    
    def _get_llm_model_options(self) -> List[ft.dropdown.Option]:
        """Get available LLM model options"""
        # Default models
        default_models = [
            ("deepseek/deepseek-r1-0528-qwen3-8b:free", "DeepSeek R1 (Free)"),
            ("gpt-4o-mini", "GPT-4o Mini"),
            ("gpt-3.5-turbo", "GPT-3.5 Turbo"),
            ("claude-3-haiku", "Claude 3 Haiku"),
            ("meta-llama/llama-3.1-8b-instruct:free", "Llama 3.1 8B (Free)"),
            ("microsoft/wizardlm-2-8x22b", "WizardLM 2 8x22B"),
            ("anthropic/claude-3-sonnet", "Claude 3 Sonnet"),
            ("openai/gpt-4-turbo", "GPT-4 Turbo")
        ]

        # Add custom models from settings
        custom_models = self.settings.get('custom_models', {})

        options = []
        for model_id, display_name in default_models:
            options.append(ft.dropdown.Option(model_id, display_name))

        for model_id, display_name in custom_models.items():
            options.append(ft.dropdown.Option(model_id, f"{display_name} (Custom)"))

        return options

    def _add_custom_model(self, e):
        """Add a custom LLM model"""
        def add_model(e):
            model_id = model_id_field.value.strip()
            display_name = display_name_field.value.strip()

            if model_id and display_name:
                # Add to custom models
                custom_models = self.settings.get('custom_models', {})
                custom_models[model_id] = display_name

                # Update dropdown options
                if self.llm_model_dropdown.current:
                    self.llm_model_dropdown.current.options = self._get_llm_model_options()
                    self.llm_model_dropdown.current.value = model_id
                    self.llm_model_dropdown.current.update()

                # Close dialog
                dialog.open = False
                e.page.update()

                logger.info(f"Added custom model: {model_id} - {display_name}")

        def cancel(e):
            dialog.open = False
            e.page.update()

        model_id_field = ft.TextField(
            label="Model ID",
            hint_text="es: anthropic/claude-3-opus",
            width=300
        )

        display_name_field = ft.TextField(
            label="Nome visualizzato",
            hint_text="es: Claude 3 Opus",
            width=300
        )

        dialog = ft.AlertDialog(
            title=ft.Text("Aggiungi Modello Personalizzato"),
            content=ft.Column([
                ft.Text("Inserisci i dettagli del modello LLM personalizzato:"),
                model_id_field,
                display_name_field,
                ft.Text(
                    "💡 Assicurati che il modello sia disponibile su OpenRouter",
                    size=12,
                    color=ft.Colors.GREY_600,
                    italic=True
                )
            ], spacing=12, tight=True),
            actions=[
                ft.TextButton("Annulla", on_click=cancel),
                ft.ElevatedButton("Aggiungi", on_click=add_model)
            ]
        )

        e.page.overlay.append(dialog)
        dialog.open = True
        e.page.update()

    def refresh_data(self):
        """Refresh section data"""
        self.settings = self._load_settings()
