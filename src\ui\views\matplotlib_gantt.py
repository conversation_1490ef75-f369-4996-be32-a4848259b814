#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Professional Gantt Chart View using Matplotlib
Modern, clean timeline visualization for Agevolami PM
"""

import flet as ft
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend to prevent GUI issues
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle
from matplotlib.backends.backend_pdf import PdfPages
import pandas as pd
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Any
import io
import base64
import os
import threading
import time

from core import get_logger
from core.models.base_models import Project, Deadline, Client, ProjectStatus, DeadlineStatus, Priority

logger = get_logger(__name__)

class MatplotlibGanttView:
    """Professional Gantt Chart with Matplotlib integration"""
    
    def __init__(self, app_instance):
        self.app = app_instance
        self.db = app_instance.db_manager
        
        # Configuration
        self.time_range = "3months"  # 1month, 3months, 6months, 1year
        self.filter_client = None
        self.filter_status = None
        self.show_deadlines = True
        self.show_projects = True
        self.show_completed_deadlines = False  # Hide completed deadlines by default
        
        # Data
        self.projects = []
        self.deadlines = []
        self.clients = []
        
        # Beautiful gradient colors with transparency
        self.project_colors = {
            ProjectStatus.DRAFT: "#B0BEC5",           # Light Blue Grey
            ProjectStatus.SUBMITTED: "#FFB74D",       # Light Orange
            ProjectStatus.APPROVED: "#64B5F6",        # Light Blue
            ProjectStatus.IN_PROGRESS: "#81C784",     # Light Green
            ProjectStatus.COMPLETED: "#90A4AE",       # Blue Grey
            ProjectStatus.SUSPENDED: "#FF8A65",       # Light Deep Orange
            ProjectStatus.CANCELLED: "#E57373"        # Light Red
        }
        
        self.priority_colors = {
            Priority.LOW: "#A5D6A7",       # Light Green
            Priority.MEDIUM: "#90CAF9",    # Light Blue
            Priority.HIGH: "#FFCC02",      # Amber
            Priority.CRITICAL: "#FF6B6B"   # Coral Red
        }
        
        # Edge colors for better definition
        self.project_edge_colors = {
            ProjectStatus.DRAFT: "#78909C",
            ProjectStatus.SUBMITTED: "#F57C00",
            ProjectStatus.APPROVED: "#1976D2",
            ProjectStatus.IN_PROGRESS: "#388E3C",
            ProjectStatus.COMPLETED: "#455A64",
            ProjectStatus.SUSPENDED: "#D84315",
            ProjectStatus.CANCELLED: "#C62828"
        }
        
        # Components
        self.chart_image = None
        self.main_container = None
        self._current_figure = None  # Store current figure for export
        
        # Performance optimization
        self._chart_cache = None
        self._last_update = 0
        self._update_debounce = 1.0  # 1 second debounce
        self._is_updating = False
        
        # Set matplotlib style for performance (simplified)
        plt.rcParams.update({
            'font.size': 9,
            'font.family': 'sans-serif',
            'axes.labelsize': 10,
            'axes.titlesize': 12,
            'xtick.labelsize': 8,
            'ytick.labelsize': 9,
            'legend.fontsize': 9,
            'figure.titlesize': 14,
            'axes.grid': True,
            'grid.alpha': 0.2,
            'axes.facecolor': '#FAFAFA',
            'figure.facecolor': 'white',
            'figure.max_open_warning': 0  # Disable warnings about too many figures
        })
        
        # Try to use a beautiful style
        try:
            plt.style.use('seaborn-v0_8-whitegrid')
        except:
            try:
                plt.style.use('seaborn-whitegrid')
            except:
                plt.style.use('default')
        
        self._load_data()
        self._init_components()
    
    def _load_data(self):
        """Load projects and deadlines data"""
        try:
            self.projects = self.db.get_all_projects()
            
            # Calculate date range
            today = date.today()
            if self.time_range == "1month":
                start_date = today - timedelta(days=15)
                end_date = today + timedelta(days=45)
            elif self.time_range == "3months":
                start_date = today - timedelta(days=30)
                end_date = today + timedelta(days=90)
            elif self.time_range == "6months":
                start_date = today - timedelta(days=60)
                end_date = today + timedelta(days=180)
            else:  # 1year
                start_date = today.replace(month=1, day=1)
                end_date = today.replace(month=12, day=31)
            
            self.deadlines = self.db.get_deadlines_by_date_range(start_date, end_date)
            self.clients = self.db.get_all_clients()
            
            # Apply filters
            if self.filter_client:
                self.projects = [p for p in self.projects if str(p.client_id) == str(self.filter_client)]
                self.deadlines = [d for d in self.deadlines if str(d.client_id) == str(self.filter_client)]
            
            if self.filter_status:
                status_map = {
                    "bozza": ProjectStatus.DRAFT,
                    "presentato": ProjectStatus.SUBMITTED,
                    "approvato": ProjectStatus.APPROVED,
                    "in_corso": ProjectStatus.IN_PROGRESS,
                    "completato": ProjectStatus.COMPLETED,
                    "sospeso": ProjectStatus.SUSPENDED,
                    "cancellato": ProjectStatus.CANCELLED
                }
                if self.filter_status in status_map:
                    self.projects = [p for p in self.projects if p.status == status_map[self.filter_status]]
            
            logger.info(f"Loaded {len(self.projects)} projects and {len(self.deadlines)} deadlines for Matplotlib Gantt")
            
        except Exception as e:
            logger.error(f"Error loading Matplotlib Gantt data: {e}")
            self.projects = []
            self.deadlines = []
            self.clients = []
    
    def _init_components(self):
        """Initialize components"""
        self.chart_image = self._create_matplotlib_gantt()
    
    def _create_matplotlib_gantt(self) -> ft.Image:
        """Create the main Matplotlib Gantt chart with performance optimization"""
        # Create figure with optimized size for performance
        fig, ax = plt.subplots(figsize=(12, 8))
        fig.patch.set_facecolor('#FFFFFF')
        ax.set_facecolor('#FAFAFA')
        
        # Prepare data
        all_items = []
        
        # Add projects
        if self.show_projects:
            for project in self.projects:
                if not project.start_date:
                    continue
                    
                # Calculate end date
                end_date = project.end_date or (project.start_date + timedelta(days=30))
                
                # Get client name
                client_name = "Sconosciuto"
                for client in self.clients:
                    if client.id == project.client_id:
                        client_name = client.name
                        break
                
                all_items.append({
                    'name': f"📋 {project.name}",
                    'start': project.start_date,
                    'end': end_date,
                    'type': 'project',
                    'status': project.status,
                    'client': client_name,
                    'color': self.project_colors.get(project.status, "#9E9E9E")
                })
        
        # Add deadlines
        if self.show_deadlines:
            for deadline in self.deadlines:
                # Skip completed deadlines unless explicitly requested
                if not self.show_completed_deadlines and deadline.status == DeadlineStatus.COMPLETED:
                    continue
                
                # Get client name
                client_name = "Sconosciuto"
                for client in self.clients:
                    if client.id == deadline.client_id:
                        client_name = client.name
                        break
                
                # Add status indicator for different deadline statuses
                status_indicator = ""
                if deadline.status == DeadlineStatus.COMPLETED and self.show_completed_deadlines:
                    status_indicator = " ✅"
                elif deadline.status == DeadlineStatus.OVERDUE:
                    status_indicator = " ⚠️"
                elif deadline.status == DeadlineStatus.CANCELLED:
                    status_indicator = " ❌"
                
                all_items.append({
                    'name': f"⏰ {deadline.title}{status_indicator}",
                    'start': deadline.due_date,
                    'end': deadline.due_date,
                    'type': 'deadline',
                    'priority': deadline.priority,
                    'status': deadline.status,
                    'client': client_name,
                    'color': self.priority_colors.get(deadline.priority, "#9E9E9E")
                })
        
        if not all_items:
            # Empty state
            ax.text(0.5, 0.5, 'Nessun progetto o scadenza da visualizzare\nAggiungi progetti o modifica i filtri', 
                   horizontalalignment='center', verticalalignment='center', 
                   transform=ax.transAxes, fontsize=16, color='grey')
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.set_xticks([])
            ax.set_yticks([])
        else:
            # Sort items by start date
            all_items.sort(key=lambda x: x['start'])
            
            # Create Gantt chart
            y_pos = range(len(all_items))
            today = date.today()
            
            for i, item in enumerate(all_items):
                start_date = item['start']
                
                if item['type'] == 'project':
                    # Simplified project bar for performance
                    end_date = item['end']
                    duration = (end_date - start_date).days
                    
                    # Main bar (no shadow for performance)
                    bar = Rectangle((mdates.date2num(start_date), i - 0.3),
                                  duration, 0.6,
                                  facecolor=item['color'],
                                  edgecolor=self.project_edge_colors.get(item['status'], '#666666'),
                                  alpha=0.8,
                                  linewidth=1)
                    ax.add_patch(bar)
                    
                else:
                    # Simplified deadline marker for performance
                    ax.scatter(mdates.date2num(start_date), i, 
                             color=item['color'], s=100, marker='D',
                             edgecolor='white', linewidth=1.5, zorder=3)
            
            # Simplified today line for performance
            ax.axvline(mdates.date2num(today), color='#FF0000', linestyle='--', 
                      alpha=0.8, linewidth=2, label='OGGI', zorder=2)
            
            # Format axes with beautiful styling
            ax.set_yticks(y_pos)
            ax.set_yticklabels([item['name'] for item in all_items], 
                              fontsize=10, fontweight='500')
            ax.invert_yaxis()
            
            # Beautiful x-axis (dates)
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%d/%m'))
            ax.xaxis.set_major_locator(mdates.WeekdayLocator(interval=1))
            ax.xaxis.set_minor_locator(mdates.DayLocator())
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, fontweight='500')
            
            # Set date limits with padding
            if all_items:
                min_date = min(item['start'] for item in all_items) - timedelta(days=3)
                max_date = max(item['end'] if item['type'] == 'project' else item['start'] 
                              for item in all_items) + timedelta(days=3)
                ax.set_xlim(mdates.date2num(min_date), mdates.date2num(max_date))
            
            # Beautiful styling
            ax.grid(True, alpha=0.2, linestyle='-', linewidth=0.5)
            ax.grid(True, which='minor', alpha=0.1, linestyle='-', linewidth=0.3)
            ax.set_xlabel('Timeline', fontsize=14, fontweight='bold', color='#2E3440')
            ax.set_ylabel('Progetti e Scadenze', fontsize=14, fontweight='bold', color='#2E3440')
            
            # Beautiful legend
            legend = ax.legend(loc='upper right', frameon=True, fancybox=True, shadow=True,
                             framealpha=0.9, facecolor='white', edgecolor='#CCCCCC')
            legend.get_frame().set_linewidth(0.5)
            
            # Remove top and right spines for cleaner look
            ax.spines['top'].set_visible(False)
            ax.spines['right'].set_visible(False)
            ax.spines['left'].set_color('#CCCCCC')
            ax.spines['bottom'].set_color('#CCCCCC')
        
        # Beautiful title with subtitle
        main_title = "📊 Gantt Chart - Gestione Progetti"
        subtitle = ""
        if self.filter_client and self.filter_client != "Tutti":
            # Get client name
            client_name = self.filter_client
            for client in self.clients:
                if str(client.id) == str(self.filter_client):
                    client_name = client.name
                    break
            subtitle = f"Cliente: {client_name}"
        if self.filter_status and self.filter_status != "Tutti":
            subtitle += f" | Status: {self.filter_status}" if subtitle else f"Status: {self.filter_status}"
        
        fig.suptitle(main_title, fontsize=18, fontweight='bold', y=0.95, color='#2E3440')
        if subtitle:
            plt.figtext(0.5, 0.91, subtitle, ha='center', fontsize=12, 
                       color='#5E81AC', style='italic')
        
        # Professional layout
        plt.tight_layout()
        plt.subplots_adjust(top=0.88 if subtitle else 0.92)
        
        # Store figure for export functionality
        self._current_figure = fig
        
        # Convert to image for Flet with optimized quality
        img_buffer = io.BytesIO()
        plt.savefig(img_buffer, format='png', dpi=120, bbox_inches='tight',
                   facecolor='white', edgecolor='none', pad_inches=0.05)
        img_buffer.seek(0)
        
        # Encode to base64
        img_base64 = base64.b64encode(img_buffer.getvalue()).decode()
        
        # Don't close the figure yet - we need it for export
        # Store a copy for potential later export
        if hasattr(self, '_current_figure') and self._current_figure:
            plt.close(self._current_figure)
        
        return ft.Image(
            src_base64=img_base64,
            width=800,
            height=500,
            fit=ft.ImageFit.CONTAIN,
            expand=True
        )
    
    def _create_header(self) -> ft.Container:
        """Create modern header with controls"""
        # Time range selector
        time_range_dropdown = ft.Dropdown(
            label="📅 Periodo",
            options=[
                ft.dropdown.Option(text="1 Mese", key="1month"),
                ft.dropdown.Option(text="3 Mesi", key="3months"),
                ft.dropdown.Option(text="6 Mesi", key="6months"),
                ft.dropdown.Option(text="1 Anno", key="1year")
            ],
            value=self.time_range,
            width=140,
            on_change=lambda e: self._change_time_range(e.control.value)
        )
        
        # Client filter
        client_options = [ft.dropdown.Option(text="Tutti i Clienti", key=None)]
        for client in self.clients:
            client_options.append(ft.dropdown.Option(text=client.name, key=str(client.id)))
        
        filter_client = ft.Dropdown(
            label="👤 Cliente",
            options=client_options,
            value=str(self.filter_client) if self.filter_client else None,
            width=200,
            on_change=lambda e: self._update_filter('client', e.control.value)
        )
        
        # Status filter
        filter_status = ft.Dropdown(
            label="📊 Stato",
            options=[
                ft.dropdown.Option(text="Tutti gli Stati", key=None),
                ft.dropdown.Option(text="Bozza", key="bozza"),
                ft.dropdown.Option(text="Presentato", key="presentato"),
                ft.dropdown.Option(text="Approvato", key="approvato"),
                ft.dropdown.Option(text="In Corso", key="in_corso"),
                ft.dropdown.Option(text="Completato", key="completato"),
                ft.dropdown.Option(text="Sospeso", key="sospeso"),
                ft.dropdown.Option(text="Cancellato", key="cancellato")
            ],
            value=self.filter_status,
            width=160,
            on_change=lambda e: self._update_filter('status', e.control.value)
        )
        
        # View toggles
        projects_switch = ft.Switch(
            label="📋 Progetti",
            value=self.show_projects,
            on_change=lambda e: self._toggle_view('projects', e.control.value)
        )
        
        deadlines_switch = ft.Switch(
            label="⏰ Scadenze",
            value=self.show_deadlines,
            on_change=lambda e: self._toggle_view('deadlines', e.control.value)
        )
        
        completed_deadlines_switch = ft.Switch(
            label="✅ Completate",
            value=self.show_completed_deadlines,
            on_change=lambda e: self._toggle_view('completed_deadlines', e.control.value),
            tooltip="Mostra anche le scadenze completate"
        )
        
        # Action buttons
        refresh_btn = ft.ElevatedButton(
            text="🔄 Aggiorna",
            icon=ft.Icons.REFRESH,
            on_click=lambda _: self._refresh_data(),
            style=ft.ButtonStyle(
                bgcolor=ft.Colors.BLUE_600,
                color=ft.Colors.WHITE
            )
        )
        
        # Export buttons
        export_pdf_btn = ft.ElevatedButton(
            text="📄 PDF",
            icon=ft.Icons.PICTURE_AS_PDF,
            on_click=self._export_to_pdf,
            style=ft.ButtonStyle(
                bgcolor=ft.Colors.RED_600,
                color=ft.Colors.WHITE
            ),
            tooltip="Esporta come PDF (alta qualità)"
        )
        
        export_png_btn = ft.ElevatedButton(
            text="🖼️ PNG",
            icon=ft.Icons.IMAGE,
            on_click=self._export_to_png,
            style=ft.ButtonStyle(
                bgcolor=ft.Colors.GREEN_600,
                color=ft.Colors.WHITE
            ),
            tooltip="Esporta come PNG (immagine)"
        )
        
        export_svg_btn = ft.ElevatedButton(
            text="🎨 SVG",
            icon=ft.Icons.BRUSH,
            on_click=self._export_to_svg,
            style=ft.ButtonStyle(
                bgcolor=ft.Colors.PURPLE_600,
                color=ft.Colors.WHITE
            ),
            tooltip="Esporta come SVG (vettoriale)"
        )
        
        # Stats info with filtering details
        active_deadlines = [d for d in self.deadlines if d.status != DeadlineStatus.COMPLETED] if not self.show_completed_deadlines else self.deadlines
        completed_deadlines = [d for d in self.deadlines if d.status == DeadlineStatus.COMPLETED]
        
        stats_parts = [f"📋 {len(self.projects)} progetti"]
        if self.show_completed_deadlines:
            stats_parts.append(f"⏰ {len(self.deadlines)} scadenze")
        else:
            stats_parts.append(f"⏰ {len(active_deadlines)} scadenze attive")
            if completed_deadlines:
                stats_parts.append(f"✅ {len(completed_deadlines)} completate (nascoste)")
        
        stats_text = ft.Text(
            " • ".join(stats_parts),
            size=14,
            color=ft.Colors.GREY_600,
            weight=ft.FontWeight.W_500
        )
        
        return ft.Container(
            content=ft.Column([
                # Title and action buttons
                ft.Row([
                    ft.Text(
                        "📊 Gantt Chart Professionale (Matplotlib)",
                        size=24,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.INDIGO_700
                    ),
                    ft.Container(expand=True),
                    ft.Row([
                        export_pdf_btn,
                        export_png_btn,
                        export_svg_btn,
                        ft.VerticalDivider(width=1),
                        refresh_btn
                    ], spacing=8)
                ]),
                
                # Controls row
                ft.Row([
                    time_range_dropdown,
                    filter_client,
                    filter_status,
                    ft.VerticalDivider(width=1),
                    projects_switch,
                    deadlines_switch,
                    completed_deadlines_switch,
                    ft.Container(expand=True),
                    stats_text
                ], spacing=16, alignment=ft.MainAxisAlignment.START),
                
                ft.Divider()
            ], spacing=12),
            padding=ft.padding.all(20),
            bgcolor=ft.Colors.WHITE,
            border_radius=16,
            border=ft.border.all(1, ft.Colors.GREY_200),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=8,
                color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
                offset=ft.Offset(0, 2)
            )
        )
    
    def _create_legend(self) -> ft.Container:
        """Create a legend for the chart"""
        # Project status legend
        project_legend = ft.Row([
            ft.Text("Progetti:", size=14, weight=ft.FontWeight.BOLD),
            *[
                ft.Container(
                    content=ft.Row([
                        ft.Container(
                            width=12,
                            height=12,
                            bgcolor=color,
                            border_radius=2
                        ),
                        ft.Text(status_text, size=11)
                    ], spacing=4),
                    margin=ft.margin.only(right=8)
                )
                for (status, color), status_text in zip(
                    self.project_colors.items(),
                    ["Bozza", "Presentato", "Approvato", "In Corso", "Completato", "Sospeso", "Cancellato"]
                )
            ]
        ], wrap=True, spacing=8)
        
        # Priority legend
        priority_legend = ft.Row([
            ft.Text("Priorità Scadenze:", size=14, weight=ft.FontWeight.BOLD),
            *[
                ft.Container(
                    content=ft.Row([
                        ft.Container(
                            width=12,
                            height=12,
                            bgcolor=color,
                            border_radius=2
                        ),
                        ft.Text(priority_text, size=11)
                    ], spacing=4),
                    margin=ft.margin.only(right=8)
                )
                for (priority, color), priority_text in zip(
                    self.priority_colors.items(),
                    ["Bassa", "Media", "Alta", "Critica"]
                )
            ]
        ], wrap=True, spacing=8)
        
        return ft.Container(
            content=ft.Column([
                project_legend,
                ft.Divider(height=1),
                priority_legend,
                ft.Divider(height=1),
                ft.Column([
                    ft.Text("💡 Suggerimento: Le barre rappresentano la durata dei progetti, i diamanti sono le scadenze. La linea rossa tratteggiata indica 'Oggi'.", 
                            size=12, color=ft.Colors.GREY_600, italic=True),
                    ft.Text("📌 Stati scadenze: ✅ Completate (nascoste) • ⚠️ Scadute • ❌ Annullate • (nessun simbolo) Attive", 
                            size=11, color=ft.Colors.GREY_500, italic=True)
                ], spacing=4)
            ], spacing=8),
            padding=ft.padding.all(16),
            bgcolor=ft.Colors.GREY_50,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )
    
    # Event handlers
    def _change_time_range(self, time_range: str):
        """Change time range and refresh chart"""
        self.time_range = time_range
        self._refresh_data()
    
    def _update_filter(self, filter_type: str, value: Any):
        """Update filters and refresh chart"""
        if filter_type == 'client':
            self.filter_client = value if value else None
        elif filter_type == 'status':
            self.filter_status = value if value else None
        
        self._refresh_data()
    
    def _toggle_view(self, view_type: str, value: bool):
        """Toggle project/deadline visibility"""
        if view_type == 'projects':
            self.show_projects = value
        elif view_type == 'deadlines':
            self.show_deadlines = value
        elif view_type == 'completed_deadlines':
            self.show_completed_deadlines = value
        
        self._refresh_chart()
    
    def _refresh_data(self):
        """Refresh data and chart"""
        self._load_data()
        self._refresh_chart()
    
    def _refresh_chart(self):
        """Refresh only the chart with debouncing"""
        current_time = time.time()
        
        # Debounce updates to prevent too frequent refreshes
        if current_time - self._last_update < self._update_debounce:
            return
            
        if self._is_updating:
            return
            
        self._is_updating = True
        self._last_update = current_time
        
        try:
            if self.main_container:
                # Close previous figure to prevent memory leaks
                if hasattr(self, '_current_figure') and self._current_figure:
                    plt.close(self._current_figure)
                
                self.chart_image = self._create_matplotlib_gantt()
                # Update the chart in the container
                self.main_container.content.controls[1] = self.chart_image
                
                # Update stats in header with proper counting
                active_deadlines = [d for d in self.deadlines if d.status != DeadlineStatus.COMPLETED] if not self.show_completed_deadlines else self.deadlines
                completed_deadlines = [d for d in self.deadlines if d.status == DeadlineStatus.COMPLETED]
                
                stats_parts = [f"📋 {len(self.projects)} progetti"]
                if self.show_completed_deadlines:
                    stats_parts.append(f"⏰ {len(self.deadlines)} scadenze")
                else:
                    stats_parts.append(f"⏰ {len(active_deadlines)} scadenze attive")
                    if completed_deadlines:
                        stats_parts.append(f"✅ {len(completed_deadlines)} completate")
                
                stats_text = self.main_container.content.controls[0].content.controls[1].controls[-1]
                stats_text.value = " • ".join(stats_parts)
                
                try:
                    if hasattr(self.app, 'page') and self.app.page:
                        self.app.page.update()
                    elif hasattr(self.app, 'main_layout') and self.app.main_layout:
                        self.app.main_layout.page.update()
                except Exception as e:
                    logger.warning(f"Could not update page: {e}")
        finally:
            self._is_updating = False
    
    def build(self) -> ft.Container:
        """Build the Matplotlib Gantt view"""
        # Create loading indicator as fallback
        loading_indicator = ft.Container(
            content=ft.Column([
                ft.ProgressRing(),
                ft.Text("Caricamento grafico...", size=14, color=ft.Colors.GREY_600)
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
            alignment=ft.alignment.center,
            height=400
        )
        
        # Use chart if available, otherwise show loading
        chart_content = self.chart_image if self.chart_image else loading_indicator
        
        self.main_container = ft.Container(
            content=ft.Column([
                self._create_header(),
                chart_content,
                ft.Container(height=8),
                self._create_legend()
            ], spacing=0, scroll=ft.ScrollMode.AUTO),
            padding=ft.padding.all(20),
            bgcolor=ft.Colors.GREY_50,
            expand=True
        )
        
        return self.main_container
    
    def _export_to_pdf(self, e):
        """Export current chart to PDF"""
        try:
            if not self._current_figure:
                if hasattr(self.app, 'show_error'):
                    self.app.show_error("Nessun grafico da esportare")
                return
            
            # Create export directory
            export_dir = "data/exports"
            os.makedirs(export_dir, exist_ok=True)
            
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"gantt_chart_{timestamp}.pdf"
            filepath = os.path.join(export_dir, filename)
            
            # Export to PDF with high quality
            with PdfPages(filepath) as pdf:
                self._current_figure.savefig(pdf, format='pdf', dpi=300, 
                                           bbox_inches='tight', facecolor='white',
                                           edgecolor='none', pad_inches=0.2)
            
            if hasattr(self.app, 'show_success'):
                self.app.show_success(f"Grafico esportato in: {filepath}")
            logger.info(f"Gantt chart exported to PDF: {filepath}")
            
        except Exception as e:
            error_msg = f"Errore durante l'esportazione PDF: {str(e)}"
            if hasattr(self.app, 'show_error'):
                self.app.show_error(error_msg)
            logger.error(error_msg)
    
    def _export_to_png(self, e):
        """Export current chart to PNG"""
        try:
            if not self._current_figure:
                if hasattr(self.app, 'show_error'):
                    self.app.show_error("Nessun grafico da esportare")
                return
            
            # Create export directory
            export_dir = "data/exports"
            os.makedirs(export_dir, exist_ok=True)
            
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"gantt_chart_{timestamp}.png"
            filepath = os.path.join(export_dir, filename)
            
            # Export to PNG with high quality
            self._current_figure.savefig(filepath, format='png', dpi=300, 
                                       bbox_inches='tight', facecolor='white',
                                       edgecolor='none', pad_inches=0.2)
            
            if hasattr(self.app, 'show_success'):
                self.app.show_success(f"Grafico esportato in: {filepath}")
            logger.info(f"Gantt chart exported to PNG: {filepath}")
            
        except Exception as e:
            error_msg = f"Errore durante l'esportazione PNG: {str(e)}"
            if hasattr(self.app, 'show_error'):
                self.app.show_error(error_msg)
            logger.error(error_msg)
    
    def _export_to_svg(self, e):
        """Export current chart to SVG"""
        try:
            if not self._current_figure:
                if hasattr(self.app, 'show_error'):
                    self.app.show_error("Nessun grafico da esportare")
                return
            
            # Create export directory
            export_dir = "data/exports"
            os.makedirs(export_dir, exist_ok=True)
            
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"gantt_chart_{timestamp}.svg"
            filepath = os.path.join(export_dir, filename)
            
            # Export to SVG (vector format)
            self._current_figure.savefig(filepath, format='svg', 
                                       bbox_inches='tight', facecolor='white',
                                       edgecolor='none', pad_inches=0.2)
            
            if hasattr(self.app, 'show_success'):
                self.app.show_success(f"Grafico esportato in: {filepath}")
            logger.info(f"Gantt chart exported to SVG: {filepath}")
            
        except Exception as e:
            error_msg = f"Errore durante l'esportazione SVG: {str(e)}"
            if hasattr(self.app, 'show_error'):
                self.app.show_error(error_msg)
            logger.error(error_msg)

    def refresh_data(self):
        """Public method to refresh data"""
        self._refresh_data()