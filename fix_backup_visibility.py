#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script per diagnosticare e correggere i problemi di visibilità dei backup
"""

import sys
import os
from pathlib import Path

# Aggiungi il percorso src al PYTHONPATH
sys.path.insert(0, str(Path(__file__).parent / "src"))

from core.services.google_drive_service import GoogleDriveService
from core.config import AppConfig

def diagnose_backup_visibility():
    """Diagnostica i problemi di visibilità dei backup"""
    print("🔍 Diagnosi visibilità backup Google Drive")
    print("=" * 60)
    
    # Inizializza servizi
    config = AppConfig()
    drive_service = GoogleDriveService(config.data_dir / "config")
    
    if not drive_service.is_authenticated:
        print("❌ Google Drive non autenticato")
        return False
    
    print("✅ Google Drive autenticato")
    
    try:
        # 1. Controlla cartella app
        print(f"\n📁 Cartella app: '{drive_service.APP_FOLDER_NAME}'")
        print(f"📁 ID cartella: {drive_service.app_folder_id}")
        
        # 2. Cerca tutti i backup ovunque
        print(f"\n🔍 Ricerca backup ovunque in Google Drive...")
        all_results = drive_service.service.files().list(
            q="name contains 'agevolami_backup' and trashed=false",
            fields='files(id,name,parents,size,createdTime,webViewLink)'
        ).execute()
        
        all_backups = all_results.get('files', [])
        print(f"📊 Backup totali trovati: {len(all_backups)}")
        
        # 3. Analizza ogni backup
        for i, backup in enumerate(all_backups, 1):
            print(f"\n📦 Backup {i}: {backup['name']}")
            print(f"   • ID: {backup['id']}")
            print(f"   • Dimensione: {backup.get('size', 'N/A')} bytes")
            print(f"   • Creato: {backup.get('createdTime', 'N/A')}")
            print(f"   • Parents: {backup.get('parents', [])}")
            print(f"   • Link: {backup.get('webViewLink', 'N/A')}")
            
            # Controlla se è nella cartella corretta
            parents = backup.get('parents', [])
            if drive_service.app_folder_id in parents:
                print(f"   ✅ Nella cartella corretta")
            else:
                print(f"   ❌ NON nella cartella corretta")
                print(f"   🔧 Dovrebbe essere in: {drive_service.app_folder_id}")
        
        # 4. Controlla backup nella cartella app
        print(f"\n📂 Backup nella cartella app...")
        app_results = drive_service.service.files().list(
            q=f"parents in '{drive_service.app_folder_id}' and name contains 'agevolami_backup'",
            fields='files(id,name,size,createdTime)'
        ).execute()
        
        app_backups = app_results.get('files', [])
        print(f"📊 Backup nella cartella app: {len(app_backups)}")
        
        # 5. Controlla info cartella
        if drive_service.app_folder_id:
            try:
                folder_info = drive_service.service.files().get(
                    fileId=drive_service.app_folder_id,
                    fields='id,name,parents,webViewLink,shared'
                ).execute()
                
                print(f"\n📁 Info cartella app:")
                print(f"   • Nome: {folder_info.get('name')}")
                print(f"   • ID: {folder_info.get('id')}")
                print(f"   • Parents: {folder_info.get('parents', [])}")
                print(f"   • Link: {folder_info.get('webViewLink', 'N/A')}")
                print(f"   • Condivisa: {folder_info.get('shared', False)}")
                
            except Exception as e:
                print(f"❌ Errore info cartella: {e}")
        
        # 6. Raccomandazioni
        print(f"\n🎯 Raccomandazioni:")
        
        if len(all_backups) == 0:
            print("❌ Nessun backup trovato")
            print("   • Crea un nuovo backup dall'app")
            
        elif len(app_backups) == 0 and len(all_backups) > 0:
            print("⚠️ Backup esistono ma non sono nella cartella corretta")
            print("   • Usa il pulsante 'Fix Visibilità' nell'app")
            print("   • Oppure esegui fix_backup_visibility() da questo script")
            
        elif len(app_backups) != len(all_backups):
            print("⚠️ Alcuni backup non sono nella cartella corretta")
            print("   • Usa il pulsante 'Fix Visibilità' nell'app")
            
        else:
            print("✅ Tutti i backup sono nella cartella corretta")
            print("   • Se non li vedi in Google Drive, cerca la cartella:")
            print(f"   • '{drive_service.APP_FOLDER_NAME}'")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore durante la diagnosi: {e}")
        import traceback
        traceback.print_exc()
        return False

def fix_backup_visibility():
    """Corregge automaticamente la visibilità dei backup"""
    print("\n🔧 Correzione automatica visibilità backup")
    print("=" * 60)
    
    config = AppConfig()
    drive_service = GoogleDriveService(config.data_dir / "config")
    
    if not drive_service.is_authenticated:
        print("❌ Google Drive non autenticato")
        return False
    
    try:
        results = drive_service.fix_backup_visibility()
        
        if results.get('success'):
            print(f"✅ Fix completato!")
            print(f"   • Backup controllati: {results['checked']}")
            print(f"   • Backup spostati: {results['moved']}")
            
            if results.get('errors'):
                print(f"   ⚠️ Errori: {len(results['errors'])}")
                for error in results['errors']:
                    print(f"     - {error}")
            
            if results['moved'] > 0:
                print(f"\n🎉 {results['moved']} backup sono stati spostati nella cartella corretta!")
                print(f"   Ora dovrebbero essere visibili in Google Drive nella cartella:")
                print(f"   '{drive_service.APP_FOLDER_NAME}'")
            else:
                print(f"\n✅ Tutti i backup erano già nella posizione corretta")
                
        else:
            print(f"❌ Errore durante il fix: {results.get('error', 'Unknown')}")
            
        return results.get('success', False)
        
    except Exception as e:
        print(f"❌ Errore durante il fix: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Funzione principale"""
    print("🚀 Strumento diagnosi e fix backup Google Drive")
    print("=" * 60)
    
    # Diagnosi
    if not diagnose_backup_visibility():
        return 1
    
    # Chiedi se eseguire il fix
    print(f"\n❓ Vuoi eseguire la correzione automatica? (y/n): ", end="")
    response = input().lower().strip()
    
    if response in ['y', 'yes', 's', 'si']:
        if fix_backup_visibility():
            print(f"\n🎉 Correzione completata con successo!")
            print(f"   Controlla ora Google Drive per vedere i tuoi backup.")
        else:
            print(f"\n❌ Correzione fallita. Controlla i log per dettagli.")
            return 1
    else:
        print(f"\n👍 Correzione saltata. Puoi eseguirla dall'app con il pulsante 'Fix Visibilità'")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
