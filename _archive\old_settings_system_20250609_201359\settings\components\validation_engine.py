#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Advanced Validation Engine
Provides real-time validation, user-friendly error messages, and smart suggestions
"""

import re
import socket
from typing import Dict, List, Any, Optional, Union, Callable
from dataclasses import dataclass
from enum import Enum
from core import get_logger

logger = get_logger(__name__)

class ValidationLevel(Enum):
    """Validation severity levels"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    SUCCESS = "success"

@dataclass
class ValidationResult:
    """Result of a validation check"""
    is_valid: bool
    level: ValidationLevel
    message: str
    field_name: str
    suggestion: Optional[str] = None
    auto_fix_value: Optional[Any] = None

class ValidationRule:
    """Base class for validation rules"""
    
    def __init__(self, field_name: str, message: str, level: ValidationLevel = ValidationLevel.ERROR):
        self.field_name = field_name
        self.message = message
        self.level = level
    
    def validate(self, value: Any, context: Dict[str, Any] = None) -> ValidationResult:
        """Validate a value - to be implemented by subclasses"""
        raise NotImplementedError

class RequiredRule(ValidationRule):
    """Validation rule for required fields"""
    
    def validate(self, value: Any, context: Dict[str, Any] = None) -> ValidationResult:
        is_valid = value is not None and str(value).strip() != ""
        
        return ValidationResult(
            is_valid=is_valid,
            level=self.level,
            message=self.message if not is_valid else "Campo obbligatorio compilato",
            field_name=self.field_name,
            suggestion="Questo campo è obbligatorio" if not is_valid else None
        )

class EmailRule(ValidationRule):
    """Validation rule for email addresses"""
    
    EMAIL_PATTERN = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
    
    def validate(self, value: Any, context: Dict[str, Any] = None) -> ValidationResult:
        if not value or str(value).strip() == "":
            return ValidationResult(
                is_valid=True,  # Empty is valid if not required
                level=ValidationLevel.INFO,
                message="Email non specificata",
                field_name=self.field_name
            )
        
        email = str(value).strip()
        is_valid = bool(self.EMAIL_PATTERN.match(email))
        
        suggestion = None
        auto_fix = None
        
        if not is_valid:
            # Smart suggestions
            if '@' not in email:
                suggestion = "Aggiungi '@' e il dominio (es. @gmail.com)"
            elif email.count('@') > 1:
                suggestion = "L'email deve contenere una sola '@'"
                auto_fix = email.split('@')[0] + '@' + email.split('@')[-1]
            elif not email.split('@')[1] if '@' in email else '':
                suggestion = "Aggiungi il dominio dopo '@' (es. gmail.com)"
            elif '.' not in email.split('@')[1] if '@' in email else '':
                suggestion = "Il dominio deve contenere un punto (es. .com, .it)"
        
        return ValidationResult(
            is_valid=is_valid,
            level=self.level if not is_valid else ValidationLevel.SUCCESS,
            message=self.message if not is_valid else "Email valida",
            field_name=self.field_name,
            suggestion=suggestion,
            auto_fix_value=auto_fix
        )

class PortRule(ValidationRule):
    """Validation rule for network ports"""
    
    def validate(self, value: Any, context: Dict[str, Any] = None) -> ValidationResult:
        try:
            port = int(value)
            is_valid = 1 <= port <= 65535
            
            suggestion = None
            auto_fix = None
            
            if not is_valid:
                if port < 1:
                    suggestion = "Il numero di porta deve essere maggiore di 0"
                    auto_fix = 587  # Common SMTP port
                elif port > 65535:
                    suggestion = "Il numero di porta deve essere minore di 65536"
                    auto_fix = 587
            else:
                # Provide info about common ports
                common_ports = {
                    25: "SMTP standard (non crittografato)",
                    587: "SMTP con STARTTLS (raccomandato)",
                    465: "SMTP con SSL/TLS",
                    993: "IMAP con SSL",
                    995: "POP3 con SSL"
                }
                
                if port in common_ports:
                    suggestion = f"Porta comune: {common_ports[port]}"
            
            return ValidationResult(
                is_valid=is_valid,
                level=self.level if not is_valid else ValidationLevel.SUCCESS,
                message=self.message if not is_valid else f"Porta {port} valida",
                field_name=self.field_name,
                suggestion=suggestion,
                auto_fix_value=auto_fix
            )
            
        except (ValueError, TypeError):
            return ValidationResult(
                is_valid=False,
                level=self.level,
                message="Deve essere un numero intero",
                field_name=self.field_name,
                suggestion="Inserisci un numero valido (es. 587)",
                auto_fix_value=587
            )

class HostnameRule(ValidationRule):
    """Validation rule for hostnames/server addresses"""
    
    HOSTNAME_PATTERN = re.compile(
        r'^(?!-)[A-Za-z0-9-]{1,63}(?<!-)$|'
        r'^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)*[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$'
    )
    
    def validate(self, value: Any, context: Dict[str, Any] = None) -> ValidationResult:
        if not value or str(value).strip() == "":
            return ValidationResult(
                is_valid=False,
                level=self.level,
                message="Hostname/server richiesto",
                field_name=self.field_name,
                suggestion="Inserisci l'indirizzo del server (es. smtp.gmail.com)"
            )
        
        hostname = str(value).strip()
        
        # Check if it's a valid IP address
        try:
            socket.inet_aton(hostname)
            return ValidationResult(
                is_valid=True,
                level=ValidationLevel.SUCCESS,
                message=f"Indirizzo IP valido: {hostname}",
                field_name=self.field_name
            )
        except socket.error:
            pass
        
        # Check if it's a valid hostname
        is_valid = bool(self.HOSTNAME_PATTERN.match(hostname))
        
        suggestion = None
        auto_fix = None
        
        if not is_valid:
            if ' ' in hostname:
                suggestion = "Rimuovi gli spazi dall'hostname"
                auto_fix = hostname.replace(' ', '')
            elif hostname.startswith('.') or hostname.endswith('.'):
                suggestion = "L'hostname non può iniziare o finire con un punto"
                auto_fix = hostname.strip('.')
            elif '..' in hostname:
                suggestion = "L'hostname non può contenere punti consecutivi"
                auto_fix = re.sub(r'\.+', '.', hostname)
        else:
            # Provide suggestions for common SMTP servers
            common_servers = {
                'gmail': 'smtp.gmail.com',
                'outlook': 'smtp-mail.outlook.com',
                'hotmail': 'smtp-mail.outlook.com',
                'yahoo': 'smtp.mail.yahoo.com'
            }
            
            for provider, server in common_servers.items():
                if provider in hostname.lower() and hostname != server:
                    suggestion = f"Server comune per {provider}: {server}"
                    break
        
        return ValidationResult(
            is_valid=is_valid,
            level=self.level if not is_valid else ValidationLevel.SUCCESS,
            message=self.message if not is_valid else f"Hostname valido: {hostname}",
            field_name=self.field_name,
            suggestion=suggestion,
            auto_fix_value=auto_fix
        )

class TimeFormatRule(ValidationRule):
    """Validation rule for time format (HH:MM)"""
    
    TIME_PATTERN = re.compile(r'^([01]?[0-9]|2[0-3]):[0-5][0-9]$')
    
    def validate(self, value: Any, context: Dict[str, Any] = None) -> ValidationResult:
        if not value or str(value).strip() == "":
            return ValidationResult(
                is_valid=False,
                level=self.level,
                message="Formato orario richiesto",
                field_name=self.field_name,
                suggestion="Formato: HH:MM (es. 09:30)",
                auto_fix_value="09:00"
            )
        
        time_str = str(value).strip()
        is_valid = bool(self.TIME_PATTERN.match(time_str))
        
        suggestion = None
        auto_fix = None
        
        if not is_valid:
            # Try to fix common issues
            if ':' not in time_str and len(time_str) in [3, 4]:
                # Convert "930" or "0930" to "09:30"
                if len(time_str) == 3:
                    auto_fix = f"0{time_str[0]}:{time_str[1:3]}"
                else:
                    auto_fix = f"{time_str[:2]}:{time_str[2:4]}"
                suggestion = f"Formato suggerito: {auto_fix}"
            elif len(time_str.split(':')) == 2:
                parts = time_str.split(':')
                try:
                    hours = int(parts[0])
                    minutes = int(parts[1])
                    if 0 <= hours <= 23 and 0 <= minutes <= 59:
                        auto_fix = f"{hours:02d}:{minutes:02d}"
                        suggestion = f"Formato normalizzato: {auto_fix}"
                    else:
                        suggestion = "Ore devono essere 0-23, minuti 0-59"
                except ValueError:
                    suggestion = "Formato: HH:MM con numeri (es. 14:30)"
            else:
                suggestion = "Formato richiesto: HH:MM (es. 09:30 o 14:45)"
        
        return ValidationResult(
            is_valid=is_valid,
            level=self.level if not is_valid else ValidationLevel.SUCCESS,
            message=self.message if not is_valid else f"Orario valido: {time_str}",
            field_name=self.field_name,
            suggestion=suggestion,
            auto_fix_value=auto_fix
        )

class NumericRangeRule(ValidationRule):
    """Validation rule for numeric ranges"""
    
    def __init__(self, field_name: str, min_value: Union[int, float], max_value: Union[int, float], 
                 message: str, level: ValidationLevel = ValidationLevel.ERROR):
        super().__init__(field_name, message, level)
        self.min_value = min_value
        self.max_value = max_value
    
    def validate(self, value: Any, context: Dict[str, Any] = None) -> ValidationResult:
        try:
            num_value = float(value)
            is_valid = self.min_value <= num_value <= self.max_value
            
            suggestion = None
            auto_fix = None
            
            if not is_valid:
                if num_value < self.min_value:
                    suggestion = f"Valore minimo: {self.min_value}"
                    auto_fix = self.min_value
                elif num_value > self.max_value:
                    suggestion = f"Valore massimo: {self.max_value}"
                    auto_fix = self.max_value
            
            return ValidationResult(
                is_valid=is_valid,
                level=self.level if not is_valid else ValidationLevel.SUCCESS,
                message=self.message if not is_valid else f"Valore {num_value} valido",
                field_name=self.field_name,
                suggestion=suggestion,
                auto_fix_value=auto_fix
            )
            
        except (ValueError, TypeError):
            return ValidationResult(
                is_valid=False,
                level=self.level,
                message="Deve essere un numero valido",
                field_name=self.field_name,
                suggestion=f"Inserisci un numero tra {self.min_value} e {self.max_value}",
                auto_fix_value=(self.min_value + self.max_value) / 2
            )

class EmailListRule(ValidationRule):
    """Validation rule for comma-separated email lists"""
    
    def validate(self, value: Any, context: Dict[str, Any] = None) -> ValidationResult:
        if not value or str(value).strip() == "":
            return ValidationResult(
                is_valid=True,  # Empty list is valid
                level=ValidationLevel.INFO,
                message="Nessun destinatario specificato",
                field_name=self.field_name
            )
        
        email_list = [email.strip() for email in str(value).split(',') if email.strip()]
        email_rule = EmailRule("email", "Email non valida")
        
        invalid_emails = []
        suggestions = []
        
        for email in email_list:
            result = email_rule.validate(email)
            if not result.is_valid:
                invalid_emails.append(email)
                if result.suggestion:
                    suggestions.append(f"{email}: {result.suggestion}")
        
        is_valid = len(invalid_emails) == 0
        
        if is_valid:
            message = f"{len(email_list)} email valide" if len(email_list) > 1 else "Email valida"
            level = ValidationLevel.SUCCESS
        else:
            message = f"{len(invalid_emails)} email non valide"
            level = self.level
        
        return ValidationResult(
            is_valid=is_valid,
            level=level,
            message=message,
            field_name=self.field_name,
            suggestion="; ".join(suggestions) if suggestions else None
        )

class SettingsValidationEngine:
    """
    Advanced validation engine for settings with real-time validation and smart suggestions
    """
    
    def __init__(self):
        self.validation_rules: Dict[str, List[ValidationRule]] = {}
        self.cross_field_validators: List[Callable] = []
        
        # Set up default validation rules
        self._setup_default_rules()
        
        logger.info("Settings Validation Engine initialized")
    
    def _setup_default_rules(self):
        """Set up default validation rules for common fields"""
        
        # Email settings
        self.add_rule('smtp_server', RequiredRule('smtp_server', 'Server SMTP richiesto'))
        self.add_rule('smtp_server', HostnameRule('smtp_server', 'Hostname del server non valido'))
        
        self.add_rule('smtp_port', RequiredRule('smtp_port', 'Porta SMTP richiesta'))
        self.add_rule('smtp_port', PortRule('smtp_port', 'Porta non valida'))
        
        self.add_rule('smtp_username', EmailRule('smtp_username', 'Username deve essere un\'email valida'))
        self.add_rule('sender_email', EmailRule('sender_email', 'Email mittente non valida'))
        
        # Time settings
        self.add_rule('morning_time', TimeFormatRule('morning_time', 'Formato orario non valido'))
        self.add_rule('evening_time', TimeFormatRule('evening_time', 'Formato orario non valido'))
        self.add_rule('quiet_start', TimeFormatRule('quiet_start', 'Orario inizio non valido'))
        self.add_rule('quiet_end', TimeFormatRule('quiet_end', 'Orario fine non valido'))
        
        # Numeric ranges
        self.add_rule('check_interval', NumericRangeRule('check_interval', 1, 168, 'Intervallo deve essere 1-168 ore'))
        self.add_rule('days_ahead', NumericRangeRule('days_ahead', 1, 365, 'Giorni anticipo deve essere 1-365'))
        self.add_rule('backup_interval', NumericRangeRule('backup_interval', 1, 30, 'Intervallo backup deve essere 1-30 giorni'))
        self.add_rule('max_backups', NumericRangeRule('max_backups', 1, 100, 'Numero massimo backup deve essere 1-100'))
        
        # Email lists
        self.add_rule('recipients', EmailListRule('recipients', 'Lista email non valida'))
        self.add_rule('reminder_recipients', EmailListRule('reminder_recipients', 'Lista destinatari non valida'))
    
    def add_rule(self, field_name: str, rule: ValidationRule):
        """Add a validation rule for a field"""
        if field_name not in self.validation_rules:
            self.validation_rules[field_name] = []
        self.validation_rules[field_name].append(rule)
    
    def validate_field(self, field_name: str, value: Any, context: Dict[str, Any] = None) -> List[ValidationResult]:
        """Validate a single field"""
        results = []
        
        if field_name in self.validation_rules:
            for rule in self.validation_rules[field_name]:
                result = rule.validate(value, context)
                results.append(result)
        
        return results
    
    def validate_all(self, data: Dict[str, Any]) -> Dict[str, List[ValidationResult]]:
        """Validate all fields in data"""
        all_results = {}
        
        for field_name, value in data.items():
            results = self.validate_field(field_name, value, data)
            if results:
                all_results[field_name] = results
        
        # Run cross-field validations
        for validator in self.cross_field_validators:
            try:
                cross_results = validator(data)
                if cross_results:
                    for field_name, results in cross_results.items():
                        if field_name not in all_results:
                            all_results[field_name] = []
                        all_results[field_name].extend(results)
            except Exception as e:
                logger.error(f"Error in cross-field validator: {e}")
        
        return all_results
    
    def get_field_status(self, field_name: str, value: Any, context: Dict[str, Any] = None) -> ValidationLevel:
        """Get overall validation status for a field"""
        results = self.validate_field(field_name, value, context)
        
        if not results:
            return ValidationLevel.INFO
        
        # Return highest severity level
        levels = [result.level for result in results if not result.is_valid]
        if ValidationLevel.ERROR in levels:
            return ValidationLevel.ERROR
        elif ValidationLevel.WARNING in levels:
            return ValidationLevel.WARNING
        else:
            return ValidationLevel.SUCCESS
    
    def get_field_suggestions(self, field_name: str, value: Any, context: Dict[str, Any] = None) -> List[str]:
        """Get suggestions for a field"""
        results = self.validate_field(field_name, value, context)
        return [result.suggestion for result in results if result.suggestion]
    
    def get_auto_fix_value(self, field_name: str, value: Any, context: Dict[str, Any] = None) -> Any:
        """Get auto-fix value for a field if available"""
        results = self.validate_field(field_name, value, context)
        
        for result in results:
            if result.auto_fix_value is not None:
                return result.auto_fix_value
        
        return None
    
    def add_cross_field_validator(self, validator: Callable):
        """Add cross-field validator function"""
        self.cross_field_validators.append(validator)

# Common cross-field validators
def validate_smtp_consistency(data: Dict[str, Any]) -> Dict[str, List[ValidationResult]]:
    """Validate SMTP settings consistency"""
    results = {}
    
    # Check if TLS is enabled for common secure ports
    port = data.get('smtp_port')
    use_tls = data.get('smtp_use_tls', True)
    
    try:
        port_num = int(port) if port else 0
        if port_num == 465 and not use_tls:
            results['smtp_use_tls'] = [ValidationResult(
                is_valid=False,
                level=ValidationLevel.WARNING,
                message="Porta 465 richiede solitamente TLS/SSL",
                field_name='smtp_use_tls',
                suggestion="Abilita TLS per la porta 465"
            )]
        elif port_num == 25 and use_tls:
            results['smtp_use_tls'] = [ValidationResult(
                is_valid=True,
                level=ValidationLevel.INFO,
                message="Porta 25 con TLS (meno comune)",
                field_name='smtp_use_tls',
                suggestion="Considera la porta 587 per STARTTLS"
            )]
    except (ValueError, TypeError):
        pass
    
    return results

def validate_time_consistency(data: Dict[str, Any]) -> Dict[str, List[ValidationResult]]:
    """Validate time settings consistency"""
    results = {}
    
    morning_time = data.get('morning_time')
    evening_time = data.get('evening_time')
    
    if morning_time and evening_time:
        try:
            morning_hour = int(morning_time.split(':')[0])
            evening_hour = int(evening_time.split(':')[0])
            
            if morning_hour >= evening_hour:
                results['evening_time'] = [ValidationResult(
                    is_valid=False,
                    level=ValidationLevel.WARNING,
                    message="Orario serale dovrebbe essere dopo quello mattutino",
                    field_name='evening_time',
                    suggestion="Imposta un orario serale successivo a quello mattutino"
                )]
        except (ValueError, IndexError):
            pass
    
    return results 