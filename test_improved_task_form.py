#!/usr/bin/env python3

import logging
import sys
import os
from datetime import datetime, date, timedelta
from uuid import uuid4
from dataclasses import dataclass

# Add project root to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

import flet as ft
from src.core.database.database import DatabaseManager
from src.core.models.base_models import Project, Deadline, Task, Priority, TaskStatus

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_test_data(db_manager: DatabaseManager):
    """Create test projects and deadlines"""
    logger.info("Creating test data...")
    
    project1 = Project(
        id=uuid4(),
        name="Progetto Marketing 2024",
        description="Campagna marketing per il nuovo prodotto",
        status="active",
        start_date=date.today(),
        end_date=date.today() + timedelta(days=90),
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    
    project2 = Project(
        id=uuid4(),
        name="Sviluppo Software v2.0", 
        description="Nuova versione dell'applicazione",
        status="active",
        start_date=date.today(),
        end_date=date.today() + timedelta(days=120),
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    
    db_manager.create_project(project1)
    db_manager.create_project(project2)
    
    deadline1 = Deadline(
        id=uuid4(),
        title="Lancio Campagna Social",
        description="Deadline per il lancio sui social media",
        due_date=date.today() + timedelta(days=30),
        project_id=project1.id,
        priority=Priority.HIGH,
        status="active",
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    
    deadline2 = Deadline(
        id=uuid4(),
        title="Alpha Release",
        description="Rilascio versione alpha per testing",
        due_date=date.today() + timedelta(days=45),
        project_id=project2.id,
        priority=Priority.CRITICAL,
        status="active", 
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    
    db_manager.create_deadline(deadline1)
    db_manager.create_deadline(deadline2)
    
    return project1, project2, deadline1, deadline2

def run_ui_test(page: ft.Page):
    """Run the improved task form UI test"""
    page.title = "Test - Improved Task Form UI"
    page.theme_mode = ft.ThemeMode.LIGHT
    page.window.width = 1400
    page.window.height = 900
    page.window.center()
    page.padding = 20
    
    # Initialize database
    db_manager = DatabaseManager()
    
    # Create test data
    create_test_data(db_manager)
    
    # Import TasksView
    from src.ui.views.tasks import TasksView
    
    @dataclass
    class MockApp:
        page: ft.Page
        
        def __init__(self, page):
            self.page = page
    
    mock_app = MockApp(page)
    tasks_view = TasksView(mock_app)
    
    # Create test instructions
    instructions = ft.Container(
        content=ft.Column([
            ft.Text(
                "🧪 Test - Improved Task Form UI",
                size=24,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.BLUE_800
            ),
            ft.Container(height=10),
            ft.Text(
                "Questa interfaccia testa il nuovo form flessibile per le attività.",
                size=16,
                color=ft.Colors.GREY_700
            ),
            ft.Container(height=15),
            ft.Card(
                content=ft.Container(
                    content=ft.Column([
                        ft.Text(
                            "🎯 Funzionalità da testare:",
                            size=16,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.ORANGE_800
                        ),
                        ft.Container(height=8),
                        ft.Text("1. Il dropdown 'Progetto' è nascosto di default", size=14),
                        ft.Text("2. Clicca '➕ Aggiungi a Progetto Direttamente' per mostrarlo", size=14),
                        ft.Text("3. Selezionando una scadenza si nasconde il progetto diretto", size=14),
                        ft.Text("4. Selezionando un progetto si mostra che la scadenza è opzionale", size=14),
                        ft.Text("5. Puoi creare attività: standalone, solo progetto, solo scadenza, complete", size=14),
                    ]),
                    padding=15
                ),
                elevation=2
            ),
            ft.Container(height=10),
            ft.Text(
                "Tipi di attività supportate:",
                size=14,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.GREEN_800
            ),
            ft.Text("• Standalone: senza progetto né scadenza", size=12, color=ft.Colors.GREY_600),
            ft.Text("• Solo progetto: progetto ma nessuna scadenza", size=12, color=ft.Colors.GREY_600),
            ft.Text("• Solo scadenza: scadenza con progetto ereditato", size=12, color=ft.Colors.GREY_600),
            ft.Text("• Completa: sia progetto che scadenza", size=12, color=ft.Colors.GREY_600),
        ]),
        padding=20,
        bgcolor=ft.Colors.WHITE,
        border_radius=10,
        border=ft.border.all(1, ft.Colors.BLUE_200)
    )
    
    # Add layout with instructions and tasks view
    page.add(
        ft.Column([
            instructions,
            ft.Container(height=20),
            ft.Divider(),
            ft.Container(height=10),
            tasks_view.build()
        ], scroll=ft.ScrollMode.AUTO)
    )
    
    logger.info("UI test loaded successfully")

if __name__ == "__main__":
    ft.app(target=run_ui_test, port=8550) 