#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Email Settings Section
Modern, self-contained email configuration component
"""

import flet as ft
from typing import Dict, Any, Callable
from ..components import SettingsCard, SettingsCardSection, FormField, SwitchField, ButtonGroup, InfoBox, StatusIndicator
from ..components.form_components import FormField
from core import get_logger

logger = get_logger(__name__)

class EmailSection:
    """
    Self-contained email settings section with modern UX
    """
    
    def __init__(self, controller):
        self.controller = controller
        self.email_settings = controller.get_setting('email')
        
        # Validation state
        self.validation_errors = {}
        self.connection_status = "disconnected"
        
        # Register for setting changes
        controller.register_change_callback('email', self._on_setting_changed)
    
    def _on_setting_changed(self, key: str, new_value: Any, old_value: Any):
        """Handle setting changes"""
        logger.debug(f"Email setting changed: {key} = {new_value}")
        self._validate_settings()
    
    def _validate_settings(self):
        """Validate current email settings"""
        settings = self.controller.get_setting('email')
        errors = {}
        
        # Basic validation
        if settings.get('server') and not settings.get('username'):
            errors['username'] = "Username richiesto quando server è configurato"
        
        if settings.get('server') and not settings.get('password'):
            errors['password'] = "Password richiesta quando server è configurato"
        
        if settings.get('port', 0) < 1 or settings.get('port', 0) > 65535:
            errors['port'] = "Porta deve essere tra 1 e 65535"
        
        self.validation_errors = errors
        return len(errors) == 0
    
    def _create_server_configuration_section(self) -> SettingsCardSection:
        """Create server configuration section"""
        
        def on_server_change(e):
            self.controller.set_setting('email', 'server', e.control.value)
        
        def on_port_change(value):
            self.controller.set_setting('email', 'port', value)
        
        def on_username_change(e):
            self.controller.set_setting('email', 'username', e.control.value)
        
        def on_password_change(e):
            self.controller.set_setting('email', 'password', e.control.value)
        
        settings = self.controller.get_setting('email')
        
        # Create form fields
        server_field = FormField.create_text_field(
            label="Server SMTP",
            value=settings.get('server', ''),
            hint_text="es. smtp.gmail.com",
            required=True,
            validation_message=self.validation_errors.get('server'),
            on_change=on_server_change,
            prefix_icon=ft.Icons.DNS
        )
        
        port_field = FormField.create_number_field(
            label="Porta",
            value=settings.get('port', 587),
            min_value=1,
            max_value=65535,
            width=120,
            on_change=on_port_change
        )
        
        username_field = FormField.create_text_field(
            label="Username",
            value=settings.get('username', ''),
            hint_text="<EMAIL>",
            required=True,
            validation_message=self.validation_errors.get('username'),
            on_change=on_username_change,
            prefix_icon=ft.Icons.PERSON
        )
        
        password_field = FormField.create_text_field(
            label="Password",
            value=settings.get('password', ''),
            password=True,
            required=True,
            validation_message=self.validation_errors.get('password'),
            on_change=on_password_change,
            prefix_icon=ft.Icons.LOCK
        )
        
        # Create server and port row
        server_row = ft.Row([
            server_field,
            port_field
        ], spacing=16)
        
        # Create credentials row
        credentials_row = ft.Row([
            username_field,
            password_field
        ], spacing=16)
        
        return SettingsCardSection(
            title="Configurazione Server",
            description="Inserisci i parametri del server SMTP per l'invio email",
            controls=[
                server_row,
                credentials_row
            ]
        )
    
    def _create_sender_configuration_section(self) -> SettingsCardSection:
        """Create sender configuration section"""
        
        def on_sender_name_change(e):
            self.controller.set_setting('email', 'sender_name', e.control.value)
        
        def on_sender_email_change(e):
            self.controller.set_setting('email', 'sender_email', e.control.value)
        
        def on_tls_change(e):
            self.controller.set_setting('email', 'use_tls', e.control.value)
        
        settings = self.controller.get_setting('email')
        
        sender_name_field = FormField.create_text_field(
            label="Nome Mittente",
            value=settings.get('sender_name', 'Agevolami PM'),
            hint_text="Nome visualizzato nelle email",
            on_change=on_sender_name_change,
            prefix_icon=ft.Icons.BADGE
        )
        
        sender_email_field = FormField.create_text_field(
            label="Email Mittente",
            value=settings.get('sender_email', ''),
            hint_text="<EMAIL>",
            on_change=on_sender_email_change,
            prefix_icon=ft.Icons.EMAIL
        )
        
        tls_switch = SwitchField.create(
            label="Usa TLS/SSL",
            description="Abilita crittografia TLS per maggiore sicurezza",
            value=settings.get('use_tls', True),
            on_change=on_tls_change,
            color=ft.Colors.GREEN_600
        )
        
        sender_row = ft.Row([
            sender_name_field,
            sender_email_field
        ], spacing=16)
        
        return SettingsCardSection(
            title="Configurazione Mittente",
            description="Personalizza come appariranno le email inviate",
            controls=[
                sender_row,
                tls_switch
            ]
        )
    
    def _create_connection_testing_section(self) -> SettingsCardSection:
        """Create connection testing section"""
        
        def test_connection(e):
            """Test SMTP connection"""
            self.connection_status = "loading"
            self._update_status_indicator()
            
            # Test connection using controller
            result = self.controller.test_email_connection()
            
            if result['success']:
                self.connection_status = "connected"
                self._show_success_notification("Connessione SMTP riuscita!")
            else:
                self.connection_status = "error"
                self._show_error_notification(f"Connessione fallita: {result['message']}")
            
            self._update_status_indicator()
        
        def send_test_email(e):
            """Send test email using real service"""
            settings = self.controller.get_setting('email')
            recipient = settings.get('username') or settings.get('sender_email')

            if not recipient:
                self._show_error_notification("❌ Configurare prima username/email mittente")
                return

            try:
                success = self.controller.send_test_email(recipient)
                if success:
                    self._show_success_notification(f"✅ Email di test inviata a {recipient}")
                else:
                    self._show_error_notification("❌ Errore invio email di test")
            except Exception as ex:
                self._show_error_notification(f"❌ Errore invio email: {ex}")
        
        # Status indicator
        status_indicator = StatusIndicator.create(
            label="Connessione SMTP",
            status=self.connection_status,
            description="Stato della connessione al server email"
        )
        
        # Action buttons
        test_buttons = ButtonGroup.create_action_group([
            {
                "text": "Testa Connessione",
                "icon": ft.Icons.WIFI_PROTECTED_SETUP,
                "on_click": test_connection,
                "style": "primary"
            },
            {
                "text": "Invia Email Test",
                "icon": ft.Icons.SEND,
                "on_click": send_test_email,
                "style": "secondary"
            }
        ])
        
        return SettingsCardSection(
            title="Test Connessione",
            description="Verifica la configurazione email prima di salvare",
            controls=[
                status_indicator,
                test_buttons
            ]
        )
    
    def _update_status_indicator(self):
        """Update the status indicator"""
        # This would update the UI status indicator
        # Implementation depends on how we manage state updates
        pass
    
    def _show_success_notification(self, message: str):
        """Show success notification"""
        # Implementation for showing notifications
        logger.info(f"SUCCESS: {message}")
    
    def _show_error_notification(self, message: str):
        """Show error notification"""
        # Implementation for showing error notifications
        logger.error(f"ERROR: {message}")
    
    def build(self) -> SettingsCard:
        """Build the email settings card"""
        
        # Validate current settings
        self._validate_settings()
        
        # Create sections
        server_section = self._create_server_configuration_section()
        sender_section = self._create_sender_configuration_section()
        testing_section = self._create_connection_testing_section()
        
        # Create info box with helpful tips
        info_box = InfoBox.create(
            title="💡 Suggerimenti Email",
            message="Configurazione sicura e affidabile per l'invio di notifiche",
            type="info",
            items=[
                "Per Gmail usa smtp.gmail.com porta 587 con TLS",
                "Potrebbe essere necessaria una password per app specifica",
                "Testa sempre la connessione prima di salvare",
                "Le email di test vengono inviate al tuo indirizzo"
            ]
        )
        
        # Create card content
        card_content = [
            server_section.build(),
            sender_section.build(),
            testing_section.build(),
            info_box
        ]
        
        # Create save/reset actions
        def save_settings(e):
            if self._validate_settings():
                success = self.controller.save_settings()
                if success:
                    self._show_success_notification("Impostazioni email salvate!")
                else:
                    self._show_error_notification("Errore durante il salvataggio")
        
        def reset_settings(e):
            # Reset email settings to defaults
            self.controller.reset_to_defaults('email')
            self._show_success_notification("Impostazioni email ripristinate")
        
        card_actions = [
            ft.ElevatedButton(
                text="Salva Email",
                icon=ft.Icons.SAVE,
                on_click=save_settings,
                bgcolor=ft.Colors.BLUE_600,
                color=ft.Colors.WHITE,
                disabled=len(self.validation_errors) > 0
            ),
            ft.OutlinedButton(
                text="Ripristina",
                icon=ft.Icons.RESTORE,
                on_click=reset_settings
            )
        ]
        
        # Determine card status based on configuration
        card_icon = ft.Icons.EMAIL
        card_icon_color = ft.Colors.BLUE_600
        
        if self.validation_errors:
            card_icon = ft.Icons.ERROR_OUTLINE
            card_icon_color = ft.Colors.RED_600
        elif all(self.controller.get_setting('email').get(key) for key in ['server', 'username', 'password']):
            card_icon = ft.Icons.MARK_EMAIL_READ
            card_icon_color = ft.Colors.GREEN_600
        
        return SettingsCard(
            title="Configurazione Email (SMTP)",
            description="Configura il server SMTP per l'invio delle notifiche email automatiche",
            icon=card_icon,
            icon_color=card_icon_color,
            expanded=True,
            content=card_content,
            actions=card_actions,
            accent_color=ft.Colors.BLUE_600
        ) 