#!/usr/bin/env python3
"""
Archive the old complex settings system
Run this script to move the old settings directory to an archive folder
"""

import os
import shutil
from datetime import datetime

def archive_old_settings():
    """Archive the old complex settings system"""
    
    # Paths
    old_settings_dir = "src/ui/views/settings"
    archive_dir = f"_archive/old_settings_system_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    # Check if old settings directory exists
    if not os.path.exists(old_settings_dir):
        print("❌ Old settings directory not found")
        return False
    
    # Create archive directory
    os.makedirs(archive_dir, exist_ok=True)
    
    try:
        # Move the entire settings directory to archive
        shutil.move(old_settings_dir, os.path.join(archive_dir, "settings"))
        
        print(f"✅ Old settings system archived to: {archive_dir}")
        print("\nArchived files:")
        
        # List archived files
        for root, dirs, files in os.walk(archive_dir):
            level = root.replace(archive_dir, '').count(os.sep)
            indent = ' ' * 2 * level
            print(f"{indent}{os.path.basename(root)}/")
            subindent = ' ' * 2 * (level + 1)
            for file in files:
                if not file.endswith('.pyc'):
                    print(f"{subindent}{file}")
        
        print(f"\n📁 Total archived: {archive_dir}")
        print("🎉 Settings system successfully simplified!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error archiving settings: {e}")
        return False

if __name__ == "__main__":
    print("🔄 Archiving old complex settings system...")
    archive_old_settings()
