#!/usr/bin/env python3
"""
Test the new simplified settings system
"""

import sys
import os
sys.path.append('src')

def test_new_settings():
    """Test the new simplified settings system"""
    print("🧪 Testing New Simplified Settings System")
    print("=" * 50)
    
    try:
        # Test import
        print("1. Testing import...")
        from ui.views.settings import SettingsView
        print("   ✅ Import successful")
        
        # Mock app instance
        class MockApp:
            def __init__(self):
                self.config = MockConfig()
                self.page = MockPage()
        
        class MockConfig:
            def __init__(self):
                self.data_dir = "test_data"
        
        class MockPage:
            def update(self):
                pass
        
        # Test creation
        print("2. Testing settings view creation...")
        app = MockApp()
        settings_view = SettingsView(app)
        print("   ✅ Settings view created successfully")
        print(f"   📁 Settings file: {settings_view.settings_file}")
        print(f"   📋 Current section: {settings_view.current_section}")
        print(f"   🔧 Settings categories: {list(settings_view.settings.keys())}")
        
        # Test settings structure
        print("3. Testing settings structure...")
        expected_categories = ["email", "notifications", "google_services", "reports", "incentives"]
        for category in expected_categories:
            if category in settings_view.settings:
                print(f"   ✅ {category} section found")
            else:
                print(f"   ❌ {category} section missing")
        
        # Test save/load
        print("4. Testing save/load functionality...")
        original_value = settings_view.settings["email"]["sender_name"]
        settings_view.settings["email"]["sender_name"] = "Test Sender"
        
        if settings_view._save_settings():
            print("   ✅ Settings saved successfully")
        else:
            print("   ❌ Settings save failed")
        
        # Reload settings
        settings_view.settings = settings_view._load_settings()
        if settings_view.settings["email"]["sender_name"] == "Test Sender":
            print("   ✅ Settings loaded successfully")
        else:
            print("   ❌ Settings load failed")
        
        # Restore original value
        settings_view.settings["email"]["sender_name"] = original_value
        settings_view._save_settings()
        
        print("\n🎉 All tests passed! New settings system is working correctly.")
        print("\n📋 Settings System Summary:")
        print("   • Single file implementation (settings.py)")
        print("   • JSON-based storage")
        print("   • Sidebar navigation interface")
        print("   • 5 main sections: Email, Notifications, Google, Reports, Incentives")
        print("   • Compatible with existing application")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_new_settings()
    if success:
        print("\n✅ Ready to use the new simplified settings system!")
    else:
        print("\n❌ Issues found with the new settings system.")
