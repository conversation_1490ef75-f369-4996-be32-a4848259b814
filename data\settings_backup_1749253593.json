{"email": {"server": "", "port": 587, "username": "", "password": "", "use_tls": true, "sender_name": "<PERSON><PERSON><PERSON><PERSON> PM", "sender_email": "<EMAIL>", "test_connection_status": "success"}, "notifications": {"enabled": true, "email_enabled": true, "check_interval": 24, "advance_days": [15], "working_hours_only": true, "weekend_alerts": false, "reminder_recipients": [], "priority_filter": "normal"}, "google_services": {"drive_enabled": false, "drive_authenticated": false, "drive_auto_backup": false, "drive_backup_frequency": "daily", "drive_retention_days": 30, "drive_include_logs": true, "drive_include_assets": true, "calendar_enabled": true, "calendar_authenticated": true, "calendar_auto_sync": true, "calendar_sync_completed": false, "calendar_delete_completed": true, "calendar_color_priority": true, "calendar_reminders": true, "tasks_enabled": true, "tasks_authenticated": true, "tasks_auto_sync": true, "tasks_sync_on_deadline_change": true, "tasks_create_list_per_deadline": true, "tasks_sync_completed": true}, "windows": {"startup_enabled": true, "startup_minimized": true, "notifications_enabled": true, "notification_sound": true, "notification_priority_filter": "normal", "notification_quiet_hours_enabled": false, "notification_quiet_start": "22:00", "notification_quiet_end": "08:00", "notification_desktop_enabled": true, "notification_email_enabled": true}, "reports": {"scheduled_enabled": false, "morning_enabled": true, "morning_time": "09:00", "evening_enabled": true, "evening_time": "17:30", "workdays_only": true, "content_types": {"project_completion": true, "overdue_items": true, "upcoming_deadlines": true, "incomplete_tasks": true, "client_summary": true, "statistics": true}, "recipients": ["<EMAIL>"], "days_ahead_filter": 7, "include_project_percentages": true, "include_overdue_items": true, "include_upcoming_deadlines": true}, "application": {"theme": "light", "language": "it", "auto_backup": true, "backup_interval": 7, "max_backups": 10, "auto_save": true, "confirmation_dialogs": true}}