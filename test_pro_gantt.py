#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Script for Professional Gantt Chart
Run this to see the new Plotly-powered Gantt visualization
"""

import flet as ft
import plotly.express as px
import plotly.graph_objects as go
import pandas as pd
from datetime import datetime, date, timedelta
from uuid import uuid4, UUID
from dataclasses import dataclass
from enum import Enum
from typing import List, Optional

# Mock data models for testing
class ProjectStatus(Enum):
    DRAFT = "bozza"
    SUBMITTED = "presentato"
    APPROVED = "approvato"
    IN_PROGRESS = "in_corso"
    COMPLETED = "completato"
    SUSPENDED = "sospeso"
    CANCELLED = "cancellato"

class Priority(Enum):
    LOW = "bassa"
    MEDIUM = "media"
    HIGH = "alta"
    CRITICAL = "critica"

class DeadlineStatus(Enum):
    PENDING = "in_attesa"
    COMPLETED = "completato"
    OVERDUE = "scaduto"
    CANCELLED = "cancellato"

@dataclass
class MockClient:
    id: UUID
    name: str

@dataclass
class MockProject:
    id: UUID
    name: str
    description: str
    client_id: UUID
    status: ProjectStatus
    start_date: Optional[date]
    end_date: Optional[date]

@dataclass
class MockDeadline:
    id: UUID
    title: str
    description: str
    client_id: UUID
    due_date: date
    priority: Priority
    status: DeadlineStatus

# Mock database manager
class MockDatabaseManager:
    def __init__(self):
        self.clients = [
            MockClient(id=uuid4(), name="Acme Corporation"),
            MockClient(id=uuid4(), name="TechStart SRL"),
            MockClient(id=uuid4(), name="Green Energy SpA"),
            MockClient(id=uuid4(), name="Digital Solutions"),
            MockClient(id=uuid4(), name="Consulting Plus")
        ]
        
        # Create projects with realistic timelines
        today = date.today()
        self.projects = [
            MockProject(
                id=uuid4(),
                name="Sviluppo App Mobile",
                description="Applicazione iOS/Android per gestione ordini",
                client_id=self.clients[0].id,
                status=ProjectStatus.IN_PROGRESS,
                start_date=today - timedelta(days=20),
                end_date=today + timedelta(days=40)
            ),
            MockProject(
                id=uuid4(),
                name="Sito Web E-commerce",
                description="Piattaforma vendita online con integrazione pagamenti",
                client_id=self.clients[1].id,
                status=ProjectStatus.APPROVED,
                start_date=today - timedelta(days=5),
                end_date=today + timedelta(days=60)
            ),
            MockProject(
                id=uuid4(),
                name="Sistema CRM",
                description="Customer Relationship Management personalizzato",
                client_id=self.clients[2].id,
                status=ProjectStatus.SUBMITTED,
                start_date=today + timedelta(days=10),
                end_date=today + timedelta(days=90)
            ),
            MockProject(
                id=uuid4(),
                name="Migrazione Database",
                description="Migrazione da SQL Server a PostgreSQL",
                client_id=self.clients[3].id,
                status=ProjectStatus.COMPLETED,
                start_date=today - timedelta(days=45),
                end_date=today - timedelta(days=5)
            ),
            MockProject(
                id=uuid4(),
                name="Dashboard Analytics",
                description="Cruscotto business intelligence",
                client_id=self.clients[4].id,
                status=ProjectStatus.DRAFT,
                start_date=today + timedelta(days=30),
                end_date=today + timedelta(days=100)
            ),
            MockProject(
                id=uuid4(),
                name="API Integration",
                description="Integrazione servizi terze parti",
                client_id=self.clients[0].id,
                status=ProjectStatus.SUSPENDED,
                start_date=today - timedelta(days=10),
                end_date=today + timedelta(days=20)
            )
        ]
        
        # Create deadlines
        self.deadlines = [
            MockDeadline(
                id=uuid4(),
                title="Consegna Prototipo",
                description="Primo prototipo app mobile",
                client_id=self.clients[0].id,
                due_date=today + timedelta(days=7),
                priority=Priority.HIGH,
                status=DeadlineStatus.PENDING
            ),
            MockDeadline(
                id=uuid4(),
                title="Testing UAT",
                description="User Acceptance Testing e-commerce",
                client_id=self.clients[1].id,
                due_date=today + timedelta(days=15),
                priority=Priority.CRITICAL,
                status=DeadlineStatus.PENDING
            ),
            MockDeadline(
                id=uuid4(),
                title="Presentazione Progetto",
                description="Presentazione finale CRM",
                client_id=self.clients[2].id,
                due_date=today + timedelta(days=25),
                priority=Priority.MEDIUM,
                status=DeadlineStatus.PENDING
            ),
            MockDeadline(
                id=uuid4(),
                title="Go Live",
                description="Messa in produzione dashboard",
                client_id=self.clients[4].id,
                due_date=today + timedelta(days=80),
                priority=Priority.HIGH,
                status=DeadlineStatus.PENDING
            ),
            MockDeadline(
                id=uuid4(),
                title="Documentazione",
                description="Consegna documentazione tecnica",
                client_id=self.clients[3].id,
                due_date=today - timedelta(days=2),
                priority=Priority.LOW,
                status=DeadlineStatus.COMPLETED
            )
        ]
    
    def get_all_projects(self) -> List[MockProject]:
        return self.projects
    
    def get_deadlines_by_date_range(self, start_date: date, end_date: date) -> List[MockDeadline]:
        return [d for d in self.deadlines if start_date <= d.due_date <= end_date]
    
    def get_all_clients(self) -> List[MockClient]:
        return self.clients

# Mock app instance
class MockApp:
    def __init__(self):
        self.db_manager = MockDatabaseManager()

# Create professional Gantt chart function
def create_professional_gantt_chart(app_instance):
    """Create a professional Gantt chart using Plotly"""
    
    # Get data
    projects = app_instance.db_manager.get_all_projects()
    today = date.today()
    start_date = today - timedelta(days=60)
    end_date = today + timedelta(days=120)
    deadlines = app_instance.db_manager.get_deadlines_by_date_range(start_date, end_date)
    clients = app_instance.db_manager.get_all_clients()
    
    # Colors
    project_colors = {
        ProjectStatus.DRAFT: "#9E9E9E",           # Grey
        ProjectStatus.SUBMITTED: "#FF9800",       # Orange
        ProjectStatus.APPROVED: "#2196F3",        # Blue
        ProjectStatus.IN_PROGRESS: "#4CAF50",     # Green
        ProjectStatus.COMPLETED: "#607D8B",       # Blue Grey
        ProjectStatus.SUSPENDED: "#FF5722",       # Deep Orange
        ProjectStatus.CANCELLED: "#F44336"        # Red
    }
    
    priority_colors = {
        Priority.LOW: "#4CAF50",       # Green
        Priority.MEDIUM: "#2196F3",    # Blue
        Priority.HIGH: "#FF9800",      # Orange
        Priority.CRITICAL: "#F44336"   # Red
    }
    
    # Prepare data for Plotly
    gantt_data = []
    
    # Add projects
    for project in projects:
        if not project.start_date:
            continue
            
        # Calculate end date
        end_date_proj = project.end_date or (project.start_date + timedelta(days=30))
        
        # Get client name
        client_name = "Sconosciuto"
        for client in clients:
            if client.id == project.client_id:
                client_name = client.name
                break
        
        # Status text
        status_text = {
            ProjectStatus.DRAFT: "Bozza",
            ProjectStatus.SUBMITTED: "Presentato",
            ProjectStatus.APPROVED: "Approvato",
            ProjectStatus.IN_PROGRESS: "In Corso",
            ProjectStatus.COMPLETED: "Completato",
            ProjectStatus.SUSPENDED: "Sospeso",
            ProjectStatus.CANCELLED: "Cancellato"
        }.get(project.status, "Sconosciuto")
        
        gantt_data.append({
            'Task': f"📋 {project.name}",
            'Start': project.start_date,
            'Finish': end_date_proj,
            'Resource': f"Progetto - {client_name}",
            'Type': 'Project',
            'Status': status_text,
            'Description': project.description,
            'Client': client_name
        })
    
    # Add deadlines
    for deadline in deadlines:
        # Get client name
        client_name = "Sconosciuto"
        for client in clients:
            if client.id == deadline.client_id:
                client_name = client.name
                break
        
        # Priority text
        priority_text = {
            Priority.LOW: "Bassa",
            Priority.MEDIUM: "Media",
            Priority.HIGH: "Alta",
            Priority.CRITICAL: "Critica"
        }.get(deadline.priority, "Sconosciuto")
        
        # Status text
        status_text = {
            DeadlineStatus.PENDING: "In Attesa",
            DeadlineStatus.COMPLETED: "Completato",
            DeadlineStatus.OVERDUE: "Scaduto",
            DeadlineStatus.CANCELLED: "Cancellato"
        }.get(deadline.status, "Sconosciuto")
        
        # Create a small duration for deadlines (1 day visual representation)
        start_deadline = deadline.due_date - timedelta(hours=12)
        end_deadline = deadline.due_date + timedelta(hours=12)
        
        gantt_data.append({
            'Task': f"⏰ {deadline.title}",
            'Start': start_deadline,
            'Finish': end_deadline,
            'Resource': f"Scadenza - {client_name}",
            'Type': 'Deadline',
            'Status': status_text,
            'Description': deadline.description,
            'Client': client_name
        })
    
    # Create DataFrame
    df = pd.DataFrame(gantt_data)
    
    # Create Plotly Gantt chart
    fig = px.timeline(
        df, 
        x_start="Start", 
        x_end="Finish", 
        y="Task", 
        color="Resource",
        hover_data=["Type", "Status", "Client", "Description"],
        title="📊 Gantt Chart Professionale - Demo"
    )
    
    # Customize appearance
    fig.update_yaxes(autorange="reversed")  # Tasks from top to bottom
    fig.update_layout(
        height=max(500, len(df) * 50 + 150),  # Dynamic height based on data
        xaxis=dict(
            title="Timeline",
            showgrid=True,
            gridwidth=1,
            gridcolor='lightgrey'
        ),
        yaxis=dict(
            title="Progetti e Scadenze",
            showgrid=True,
            gridwidth=1,
            gridcolor='lightgrey'
        ),
        plot_bgcolor='white',
        paper_bgcolor='white',
        font=dict(size=12),
        title=dict(
            text="📊 Gantt Chart Professionale - Demo Agevolami PM",
            x=0.5,
            font=dict(size=20, color='#2E3440')
        ),
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        ),
        margin=dict(l=50, r=50, t=100, b=50),
        hovermode='closest'
    )
    
    # Add today line
    today = date.today()
    fig.add_shape(
        type="line",
        x0=today, x1=today,
        y0=0, y1=1,
        yref="paper",
        line=dict(color="red", width=2, dash="dash")
    )
    
    # Add today annotation
    fig.add_annotation(
        x=today,
        y=1,
        yref="paper",
        text="OGGI",
        showarrow=False,
        yshift=10,
        font=dict(color="red", size=12)
    )
    
    # Update traces for better styling
    for trace in fig.data:
        trace.marker.line.width = 1
        trace.marker.line.color = 'white'
    
    return fig

def main(page: ft.Page):
    """Main function for Flet app"""
    page.title = "Pro Gantt Chart - Demo"
    page.theme_mode = ft.ThemeMode.LIGHT
    page.window.width = 1400
    page.window.height = 900
    page.padding = 20
    
    # Mock app instance
    app = MockApp()
    
    # Create stats info
    projects_count = len(app.db_manager.get_all_projects())
    deadlines_count = len(app.db_manager.get_deadlines_by_date_range(
        date.today() - timedelta(days=60),
        date.today() + timedelta(days=120)
    ))
    
    # Header
    header = ft.Container(
        content=ft.Column([
            ft.Row([
                ft.Text(
                    "📊 Professional Gantt Chart Demo",
                    size=28,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.INDIGO_700
                ),
                ft.Container(expand=True),
                ft.Text(
                    f"📋 {projects_count} progetti • ⏰ {deadlines_count} scadenze",
                    size=16,
                    color=ft.Colors.GREY_600,
                    weight=ft.FontWeight.W_500
                )
            ]),
            ft.Text(
                "Powered by Plotly - Interactive timeline visualization",
                size=14,
                color=ft.Colors.GREY_500,
                italic=True
            ),
            ft.Divider()
        ], spacing=10),
        padding=ft.padding.all(20),
        bgcolor=ft.Colors.WHITE,
        border_radius=16,
        border=ft.border.all(1, ft.Colors.GREY_200),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=8,
            color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
            offset=ft.Offset(0, 2)
        )
    )
    
    # Create Plotly chart
    fig = create_professional_gantt_chart(app)
    plotly_chart = ft.PlotlyChart(
        figure=fig,
        expand=True,
        isolated=True
    )
    
    # Features info
    features = ft.Container(
        content=ft.Column([
            ft.Text("✨ Caratteristiche Professional Gantt:", size=18, weight=ft.FontWeight.BOLD),
            ft.Column([
                ft.Row([ft.Icon(ft.Icons.CHECK_CIRCLE, color=ft.Colors.GREEN, size=16), ft.Text("Timeline interattiva con zoom e pan")]),
                ft.Row([ft.Icon(ft.Icons.CHECK_CIRCLE, color=ft.Colors.GREEN, size=16), ft.Text("Hover per dettagli completi di progetti e scadenze")]),
                ft.Row([ft.Icon(ft.Icons.CHECK_CIRCLE, color=ft.Colors.GREEN, size=16), ft.Text("Visualizzazione per cliente con colori automatici")]),
                ft.Row([ft.Icon(ft.Icons.CHECK_CIRCLE, color=ft.Colors.GREEN, size=16), ft.Text("Marcatore 'Oggi' per orientamento temporale")]),
                ft.Row([ft.Icon(ft.Icons.CHECK_CIRCLE, color=ft.Colors.GREEN, size=16), ft.Text("Layout responsivo con altezza dinamica")]),
                ft.Row([ft.Icon(ft.Icons.CHECK_CIRCLE, color=ft.Colors.GREEN, size=16), ft.Text("Esportazione integrata (PNG, PDF, SVG)")]),
            ], spacing=8)
        ], spacing=12),
        padding=ft.padding.all(20),
        bgcolor=ft.Colors.GREEN_50,
        border_radius=12,
        border=ft.border.all(1, ft.Colors.GREEN_200)
    )
    
    # Layout
    page.add(
        ft.Column([
            header,
            plotly_chart,
            features
        ], spacing=20, scroll=ft.ScrollMode.AUTO)
    )

if __name__ == "__main__":
    ft.app(target=main)