#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vista Calendar per Agevolami PM
"""

import flet as ft
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Any
from uuid import UUID
import calendar

from core import get_logger
from core.models.base_models import Deadline, Project, Client, DeadlineStatus, Priority

logger = get_logger(__name__)

class CalendarView:
    """Vista calendario per visualizzare scadenze e progetti"""
    
    def __init__(self, app_instance):
        self.app = app_instance
        self.db = app_instance.db_manager
        
        # Stato del calendario
        self.current_date = date.today()
        self.view_mode = "month"  # month, week, day
        self.selected_date = None
        self.filter_client = None
        self.filter_project = None
        self.filter_priority = None
        self.filter_status = None
        
        # Flag per prevenire loop infiniti
        self._rebuilding = False
        
        # Dati
        self.deadlines = []
        self.projects = []
        self.clients = []
        
        # Componenti UI - manterremo riferimenti per l'aggiornamento
        self.main_container = None
        self.header_container = None
        self.calendar_grid_container = None
        self.month_year_text = None
        self.view_mode_dropdown = None
        
        # Colori per priorità
        self.priority_colors = {
            Priority.LOW: ft.Colors.GREEN,
            Priority.MEDIUM: ft.Colors.BLUE,
            Priority.HIGH: ft.Colors.ORANGE,
            Priority.CRITICAL: ft.Colors.RED
        }
        
        # Colori per stati
        self.status_colors = {
            DeadlineStatus.PENDING: ft.Colors.BLUE,
            DeadlineStatus.COMPLETED: ft.Colors.GREEN,
            DeadlineStatus.OVERDUE: ft.Colors.RED,
            DeadlineStatus.CANCELLED: ft.Colors.GREY
        }
        
        self._load_data()
        self._init_components()
    
    def _init_components(self):
        """Inizializza i componenti principali"""
        # Inizializza i container principali
        self.calendar_grid_container = ft.Container(
            content=self._create_calendar_grid(),
            expand=True
        )
        
        self.header_container = ft.Container(
            content=self._create_header_content(),
            padding=ft.padding.all(0)  # Padding is now handled internally
        )
    
    def _load_data(self):
        """Carica i dati necessari"""
        try:
            # Carica scadenze del mese corrente (± 2 mesi per buffer)
            start_date = (self.current_date.replace(day=1) - timedelta(days=60))
            end_date = (self.current_date.replace(day=28) + timedelta(days=60))
            
            self.deadlines = self.db.get_deadlines_by_date_range(start_date, end_date)
            self.projects = self.db.get_all_projects()
            self.clients = self.db.get_all_clients()
            
            logger.info(f"Caricati {len(self.deadlines)} scadenze per il calendario")
            
        except Exception as e:
            logger.error(f"Errore caricamento dati calendario: {e}")
            self.deadlines = []
            self.projects = []
            self.clients = []
    
    def _create_header_content(self) -> ft.Column:
        """Crea il contenuto dell'header del calendario con design moderno"""
        # Navigazione mese/anno con stile migliorato
        self.month_year_text = ft.Text(
            self._get_view_title(),
            size=28,
            weight=ft.FontWeight.BOLD,
            color=ft.Colors.WHITE
        )

        # Pulsanti navigazione - testo dinamico basato sulla vista
        nav_tooltip_prev, nav_tooltip_next = self._get_navigation_tooltips()

        prev_btn = ft.IconButton(
            icon=ft.Icons.CHEVRON_LEFT,
            on_click=lambda _: self._navigate_month(-1),
            tooltip=nav_tooltip_prev,
            icon_color=ft.Colors.WHITE,
            bgcolor=ft.Colors.with_opacity(0.2, ft.Colors.WHITE),
            style=ft.ButtonStyle(
                shape=ft.CircleBorder()
            )
        )

        next_btn = ft.IconButton(
            icon=ft.Icons.CHEVRON_RIGHT,
            on_click=lambda _: self._navigate_month(1),
            tooltip=nav_tooltip_next,
            icon_color=ft.Colors.WHITE,
            bgcolor=ft.Colors.with_opacity(0.2, ft.Colors.WHITE),
            style=ft.ButtonStyle(
                shape=ft.CircleBorder()
            )
        )

        today_btn = ft.ElevatedButton(
            content=ft.Row([
                ft.Icon(ft.Icons.TODAY, size=16, color=ft.Colors.INDIGO_600),
                ft.Text("Oggi", size=14, weight=ft.FontWeight.W_600, color=ft.Colors.INDIGO_600)
            ], spacing=8, alignment=ft.MainAxisAlignment.CENTER),
            on_click=lambda _: self._navigate_to_today(),
            style=ft.ButtonStyle(
                bgcolor=ft.Colors.WHITE,
                color=ft.Colors.INDIGO_600,
                padding=ft.padding.symmetric(horizontal=20, vertical=12),
                elevation=2,
                shape=ft.RoundedRectangleBorder(radius=8)
            ),
            height=40
        )

        # View mode selector con icone
        view_mode_options = [
            ft.dropdown.Option(text="📅 Mese", key="month"),
            ft.dropdown.Option(text="📆 Settimana", key="week"),
            ft.dropdown.Option(text="📋 Giorno", key="day")
        ]

        self.view_mode_dropdown = ft.Dropdown(
            label="Vista",
            options=view_mode_options,
            value=self.view_mode,
            width=140,
            bgcolor=ft.Colors.WHITE,
            border_color=ft.Colors.GREY_300,
            border_radius=8,
            content_padding=ft.padding.symmetric(horizontal=12, vertical=8),
            on_change=lambda e: self._change_view_mode(e.control.value)
        )

        # Filtri con stile migliorato
        filter_client = ft.Dropdown(
            label="👤 Cliente",
            options=[ft.dropdown.Option(text="Tutti i clienti", key=None)] + [
                ft.dropdown.Option(text=client.name, key=str(client.id))
                for client in self.clients
            ],
            value=str(self.filter_client) if self.filter_client else None,
            width=220,
            bgcolor=ft.Colors.WHITE,
            border_color=ft.Colors.GREY_300,
            border_radius=8,
            content_padding=ft.padding.symmetric(horizontal=12, vertical=8),
            on_change=lambda e: self._update_filter('client', e.control.value)
        )

        filter_priority = ft.Dropdown(
            label="⚡ Priorità",
            options=[
                ft.dropdown.Option(text="🔍 Tutte", key=None),
                ft.dropdown.Option(text="🟢 Bassa", key="bassa"),
                ft.dropdown.Option(text="🔵 Media", key="media"),
                ft.dropdown.Option(text="🟠 Alta", key="alta"),
                ft.dropdown.Option(text="🔴 Critica", key="critica")
            ],
            value=self.filter_priority,
            width=140,
            bgcolor=ft.Colors.WHITE,
            border_color=ft.Colors.GREY_300,
            border_radius=8,
            content_padding=ft.padding.symmetric(horizontal=12, vertical=8),
            on_change=lambda e: self._update_filter('priority', e.control.value)
        )

        # Azioni con stile moderno
        add_deadline_btn = ft.ElevatedButton(
            content=ft.Row([
                ft.Icon(ft.Icons.ADD_CIRCLE, size=18, color=ft.Colors.WHITE),
                ft.Text("Nuova Scadenza", size=14, weight=ft.FontWeight.W_600, color=ft.Colors.WHITE)
            ], spacing=8, alignment=ft.MainAxisAlignment.CENTER),
            on_click=lambda _: self._create_new_deadline(),
            style=ft.ButtonStyle(
                bgcolor=ft.Colors.INDIGO_600,
                color=ft.Colors.WHITE,
                padding=ft.padding.symmetric(horizontal=20, vertical=12),
                elevation=3,
                shadow_color=ft.Colors.INDIGO_200,
                shape=ft.RoundedRectangleBorder(radius=8)
            ),
            height=40
        )

        return ft.Column([
            # Header principale con gradient
            ft.Container(
                content=ft.Row([
                    # Navigazione sinistra
                    ft.Row([
                        prev_btn,
                        ft.Container(width=16),
                        self.month_year_text,
                        ft.Container(width=16),
                        next_btn,
                        ft.Container(width=24),
                        today_btn
                    ], spacing=0, alignment=ft.MainAxisAlignment.START),

                    ft.Container(expand=True),

                    # Azioni destra
                    ft.Row([
                        self.view_mode_dropdown,
                        ft.Container(width=12),
                        add_deadline_btn
                    ], spacing=0)
                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                padding=ft.padding.all(24),
                gradient=ft.LinearGradient(
                    colors=[ft.Colors.INDIGO_600, ft.Colors.INDIGO_800],
                    begin=ft.alignment.top_left,
                    end=ft.alignment.bottom_right
                ),
                border_radius=16,
                margin=ft.margin.only(bottom=20)
            ),

            # Sezione filtri con design moderno
            ft.Container(
                content=ft.Row([
                    # Filtri sinistra
                    ft.Row([
                        filter_client,
                        ft.Container(width=16),
                        filter_priority
                    ], spacing=0),

                    ft.Container(expand=True),

                    # Legenda priorità moderna
                    ft.Container(
                        content=ft.Row([
                            ft.Text("Legenda:", size=12, color=ft.Colors.GREY_700, weight=ft.FontWeight.W_500),
                            ft.Container(width=12),
                            ft.Container(
                                content=ft.Row([
                                    ft.Text("🟢", size=16),
                                    ft.Text("Bassa", size=11, color=ft.Colors.GREY_700)
                                ], spacing=4),
                                tooltip="Priorità Bassa"
                            ),
                            ft.Container(
                                content=ft.Row([
                                    ft.Text("🔵", size=16),
                                    ft.Text("Media", size=11, color=ft.Colors.GREY_700)
                                ], spacing=4),
                                tooltip="Priorità Media"
                            ),
                            ft.Container(
                                content=ft.Row([
                                    ft.Text("🟠", size=16),
                                    ft.Text("Alta", size=11, color=ft.Colors.GREY_700)
                                ], spacing=4),
                                tooltip="Priorità Alta"
                            ),
                            ft.Container(
                                content=ft.Row([
                                    ft.Text("🔴", size=16),
                                    ft.Text("Critica", size=11, color=ft.Colors.GREY_700)
                                ], spacing=4),
                                tooltip="Priorità Critica"
                            )
                        ], spacing=8),
                        padding=ft.padding.symmetric(horizontal=16, vertical=8),
                        bgcolor=ft.Colors.WHITE,
                        border=ft.border.all(1, ft.Colors.GREY_200),
                        border_radius=8
                    )
                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                padding=ft.padding.symmetric(horizontal=4)
            )
        ], spacing=0)
    
    def _get_view_title(self) -> str:
        """Ottiene il titolo appropriato per la vista corrente"""
        if self.view_mode == "month":
            return self.current_date.strftime("%B %Y").title()
        elif self.view_mode == "week":
            start_of_week = self.current_date - timedelta(days=self.current_date.weekday())
            end_of_week = start_of_week + timedelta(days=6)
            return f"Settimana {start_of_week.strftime('%d %B')} - {end_of_week.strftime('%d %B %Y')}"
        elif self.view_mode == "day":
            return self.current_date.strftime("%A, %d %B %Y")
        return self.current_date.strftime("%B %Y")
    
    def _get_navigation_tooltips(self) -> tuple:
        """Ottiene i tooltip per i pulsanti di navigazione"""
        if self.view_mode == "month":
            return ("Mese precedente", "Mese successivo")
        elif self.view_mode == "week":
            return ("Settimana precedente", "Settimana successiva")
        elif self.view_mode == "day":
            return ("Giorno precedente", "Giorno successivo")
        return ("Precedente", "Successivo")

    def _create_header(self) -> ft.Container:
        """Crea l'header del calendario"""
        return self.header_container
    
    def _create_calendar_grid(self) -> ft.Container:
        """Crea la griglia del calendario"""
        if self.view_mode == "month":
            return self._create_month_view()
        elif self.view_mode == "week":
            return self._create_week_view()
        elif self.view_mode == "day":
            return self._create_day_view()
        else:
            return self._create_month_view()
    
    def _create_month_view(self) -> ft.Container:
        """Crea la vista mensile con design moderno"""
        # Header giorni della settimana con stile migliorato
        weekday_names = ["Lunedì", "Martedì", "Mercoledì", "Giovedì", "Venerdì", "Sabato", "Domenica"]
        weekday_short = ["Lun", "Mar", "Mer", "Gio", "Ven", "Sab", "Dom"]

        weekday_headers = ft.Row([
            ft.Container(
                content=ft.Column([
                    ft.Text(
                        weekday_short[i],
                        size=14,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.INDIGO_700,
                        text_align=ft.TextAlign.CENTER
                    ),
                    ft.Text(
                        weekday_names[i],
                        size=10,
                        color=ft.Colors.GREY_600,
                        text_align=ft.TextAlign.CENTER
                    )
                ], spacing=2, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                width=150,
                height=50,
                bgcolor=ft.Colors.INDIGO_50,
                border_radius=8,
                alignment=ft.alignment.center,
                border=ft.border.all(1, ft.Colors.INDIGO_100)
            )
            for i in range(7)
        ], spacing=4)

        # Griglia dei giorni
        cal = calendar.Calendar(firstweekday=0)  # Lunedì come primo giorno
        month_days = cal.monthdayscalendar(self.current_date.year, self.current_date.month)

        week_rows = []
        for week in month_days:
            day_cells = []
            for day_num in week:
                if day_num == 0:
                    # Giorno vuoto con stile migliorato
                    day_cells.append(
                        ft.Container(
                            width=150,
                            height=130,
                            bgcolor=ft.Colors.GREY_50,
                            border_radius=8,
                            border=ft.border.all(1, ft.Colors.GREY_100)
                        )
                    )
                else:
                    day_date = date(self.current_date.year, self.current_date.month, day_num)
                    day_cells.append(self._create_day_cell(day_date))

            week_rows.append(ft.Row(day_cells, spacing=4))

        return ft.Container(
            content=ft.Column([
                weekday_headers,
                ft.Container(height=12),  # Spaziatura
                ft.Column(week_rows, spacing=4)
            ], spacing=0),
            bgcolor=ft.Colors.WHITE,
            border_radius=16,
            border=ft.border.all(1, ft.Colors.GREY_200),
            padding=ft.padding.all(16),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=8,
                color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
                offset=ft.Offset(0, 4)
            )
        )
    
    def _create_day_cell(self, day_date: date) -> ft.Container:
        """Crea una cella per un giorno specifico con design moderno"""
        # Filtra scadenze per questo giorno
        day_deadlines = [
            d for d in self.deadlines
            if d.due_date == day_date and self._passes_filters(d)
        ]

        # Determina il colore di sfondo e stile
        is_today = day_date == date.today()
        is_weekend = day_date.weekday() >= 5
        is_current_month = day_date.month == self.current_date.month

        if is_today:
            bg_color = ft.Colors.INDIGO_50
            border_color = ft.Colors.INDIGO_400
            border_width = 3
        elif not is_current_month:
            bg_color = ft.Colors.GREY_50
            border_color = ft.Colors.GREY_100
            border_width = 1
        elif is_weekend:
            bg_color = ft.Colors.BLUE_50
            border_color = ft.Colors.BLUE_100
            border_width = 1
        else:
            bg_color = ft.Colors.WHITE
            border_color = ft.Colors.GREY_200
            border_width = 1

        # Numero del giorno con stile migliorato
        day_number_container = ft.Container(
            content=ft.Text(
                str(day_date.day),
                size=16,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.WHITE if is_today else (
                    ft.Colors.GREY_400 if not is_current_month else ft.Colors.GREY_800
                ),
                text_align=ft.TextAlign.CENTER
            ),
            width=28,
            height=28,
            bgcolor=ft.Colors.INDIGO_600 if is_today else ft.Colors.TRANSPARENT,
            border_radius=14,
            alignment=ft.alignment.center
        )

        # Scadenze del giorno con design moderno (max 3 visualizzate)
        deadline_widgets = []
        for i, deadline in enumerate(day_deadlines[:3]):
            if i >= 2 and len(day_deadlines) > 3:
                # Mostra "e altre X" con stile moderno
                deadline_widgets.append(
                    ft.Container(
                        content=ft.Text(
                            f"+{len(day_deadlines) - 2}",
                            size=9,
                            color=ft.Colors.GREY_700,
                            weight=ft.FontWeight.W_500,
                            text_align=ft.TextAlign.CENTER
                        ),
                        padding=ft.padding.symmetric(horizontal=6, vertical=2),
                        bgcolor=ft.Colors.GREY_100,
                        border=ft.border.all(1, ft.Colors.GREY_300),
                        border_radius=8,
                        margin=ft.margin.only(bottom=2),
                        tooltip=f"e altre {len(day_deadlines) - 2} scadenze"
                    )
                )
                break

            deadline_color = self.priority_colors.get(deadline.priority, ft.Colors.BLUE)

            # Emoji per priorità
            priority_emoji = {
                Priority.LOW: "🟢",
                Priority.MEDIUM: "🔵",
                Priority.HIGH: "🟠",
                Priority.CRITICAL: "🔴"
            }.get(deadline.priority, "📅")

            deadline_widgets.append(
                ft.Container(
                    content=ft.Row([
                        ft.Text(priority_emoji, size=10),
                        ft.Text(
                            deadline.title[:15] + ("..." if len(deadline.title) > 15 else ""),
                            size=9,
                            color=ft.Colors.WHITE,
                            weight=ft.FontWeight.W_600,
                            expand=True
                        )
                    ], spacing=4, alignment=ft.MainAxisAlignment.START),
                    padding=ft.padding.symmetric(horizontal=6, vertical=3),
                    bgcolor=deadline_color,
                    border_radius=8,
                    margin=ft.margin.only(bottom=2),
                    tooltip=f"{deadline.title}\n{deadline.description or ''}\nPriorità: {deadline.priority}",
                    on_click=lambda e, d=deadline: self._show_deadline_detail(d),
                    ink=True
                )
            )

        # Indicatore numero scadenze se ce ne sono
        deadline_count_indicator = None
        if len(day_deadlines) > 0:
            deadline_count_indicator = ft.Container(
                content=ft.Text(
                    str(len(day_deadlines)),
                    size=10,
                    color=ft.Colors.WHITE,
                    weight=ft.FontWeight.BOLD,
                    text_align=ft.TextAlign.CENTER
                ),
                width=18,
                height=18,
                bgcolor=ft.Colors.INDIGO_600,
                border_radius=9,
                alignment=ft.alignment.center
            )

        cell_content = ft.Column([
            # Header del giorno
            ft.Row([
                day_number_container,
                ft.Container(expand=True),
                deadline_count_indicator if deadline_count_indicator else ft.Container()
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),

            # Scadenze
            ft.Column(
                deadline_widgets,
                spacing=0,
                scroll=ft.ScrollMode.AUTO,
                expand=True
            )
        ], spacing=6, expand=True)

        return ft.Container(
            content=cell_content,
            width=150,
            height=130,
            bgcolor=bg_color,
            border=ft.border.all(border_width, border_color),
            border_radius=12,
            padding=ft.padding.all(8),
            on_click=lambda e: self._on_day_click(day_date),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=2,
                color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
                offset=ft.Offset(0, 1)
            ) if not is_today else ft.BoxShadow(
                spread_radius=0,
                blur_radius=8,
                color=ft.Colors.with_opacity(0.3, ft.Colors.INDIGO_600),
                offset=ft.Offset(0, 2)
            ),
            ink=True
        )
    
    def _create_week_view(self) -> ft.Container:
        """Crea la vista settimanale"""
        # Calcola l'inizio della settimana (lunedì)
        start_of_week = self.current_date - timedelta(days=self.current_date.weekday())
        
        # Header con i giorni della settimana
        day_headers = []
        week_days = []
        
        for i in range(7):
            day_date = start_of_week + timedelta(days=i)
            is_today = day_date == date.today()
            
            # Header
            day_headers.append(
                ft.Container(
                    content=ft.Column([
                        ft.Text(
                            ["Lun", "Mar", "Mer", "Gio", "Ven", "Sab", "Dom"][i],
                            size=12,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.GREY_600,
                            text_align=ft.TextAlign.CENTER
                        ),
                        ft.Text(
                            str(day_date.day),
                            size=16,
                            weight=ft.FontWeight.BOLD if is_today else ft.FontWeight.NORMAL,
                            color=ft.Colors.BLUE_600 if is_today else ft.Colors.GREY_800,
                            text_align=ft.TextAlign.CENTER
                        )
                    ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=2),
                    width=160,
                    height=50,
                    bgcolor=ft.Colors.BLUE_50 if is_today else ft.Colors.GREY_50,
                    border_radius=8,
                    alignment=ft.alignment.center
                )
            )
            
            # Colonna del giorno
            day_deadlines = [
                d for d in self.deadlines 
                if d.due_date == day_date and self._passes_filters(d)
            ]
            
            deadline_widgets = []
            for deadline in day_deadlines:
                deadline_color = self.priority_colors.get(deadline.priority, ft.Colors.BLUE)
                
                deadline_widgets.append(
                    ft.Container(
                        content=ft.Column([
                            ft.Text(
                                deadline.title,
                                size=12,
                                weight=ft.FontWeight.BOLD,
                                color=ft.Colors.WHITE
                            ),
                            ft.Text(
                                deadline.description[:50] + ("..." if deadline.description and len(deadline.description) > 50 else ""),
                                size=10,
                                color=ft.Colors.WHITE70
                            ) if deadline.description else None
                        ], spacing=2),
                        padding=ft.padding.all(8),
                        bgcolor=deadline_color,
                        border_radius=8,
                        margin=ft.margin.only(bottom=4),
                        on_click=lambda e, d=deadline: self._show_deadline_detail(d)
                    )
                )
            
            week_days.append(
                ft.Container(
                    content=ft.Column(
                        deadline_widgets,
                        scroll=ft.ScrollMode.AUTO
                    ),
                    width=160,
                    height=400,
                    bgcolor=ft.Colors.WHITE,
                    border=ft.border.all(1, ft.Colors.GREY_200),
                    border_radius=8,
                    padding=ft.padding.all(8)
                )
            )
        
        return ft.Container(
            content=ft.Column([
                ft.Row(day_headers, spacing=12),
                ft.Container(height=8),
                ft.Row(week_days, spacing=12)
            ], spacing=0),
            bgcolor=ft.Colors.WHITE,
            border_radius=16,
            border=ft.border.all(1, ft.Colors.GREY_200),
            padding=ft.padding.all(16),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=8,
                color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
                offset=ft.Offset(0, 4)
            )
        )
    
    def _create_day_view(self) -> ft.Container:
        """Crea la vista giornaliera"""
        day_deadlines = [
            d for d in self.deadlines 
            if d.due_date == self.current_date and self._passes_filters(d)
        ]
        
        # Header del giorno con design moderno
        day_header = ft.Container(
            content=ft.Row([
                ft.Icon(ft.Icons.TODAY, size=32, color=ft.Colors.WHITE),
                ft.Container(width=16),
                ft.Column([
                    ft.Text(
                        self.current_date.strftime("%A, %d %B %Y"),
                        size=24,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.WHITE
                    ),
                    ft.Text(
                        f"{len(day_deadlines)} scadenze programmate",
                        size=14,
                        color=ft.Colors.with_opacity(0.9, ft.Colors.WHITE)
                    )
                ], spacing=4, expand=True)
            ], alignment=ft.MainAxisAlignment.START),
            padding=ft.padding.all(24),
            gradient=ft.LinearGradient(
                colors=[ft.Colors.INDIGO_600, ft.Colors.INDIGO_800],
                begin=ft.alignment.top_left,
                end=ft.alignment.bottom_right
            ),
            border_radius=16,
            margin=ft.margin.only(bottom=16)
        )
        
        # Lista scadenze del giorno
        deadline_widgets = []
        for deadline in day_deadlines:
            deadline_color = self.priority_colors.get(deadline.priority, ft.Colors.BLUE)
            status_color = self.status_colors.get(deadline.status, ft.Colors.BLUE)
            
            # Trova il client e project associati
            client_name = "N/A"
            project_name = "N/A"
            
            if deadline.client_id:
                client = next((c for c in self.clients if c.id == deadline.client_id), None)
                if client:
                    client_name = client.name
            
            if deadline.project_id:
                project = next((p for p in self.projects if p.id == deadline.project_id), None)
                if project:
                    project_name = project.name
            
            deadline_widgets.append(
                ft.Container(
                    content=ft.Row([
                        # Indicatore priorità
                        ft.Container(
                            width=4,
                            height=80,
                            bgcolor=deadline_color,
                            border_radius=2
                        ),
                        
                        # Contenuto scadenza
                        ft.Column([
                            ft.Row([
                                ft.Text(
                                    deadline.title,
                                    size=16,
                                    weight=ft.FontWeight.BOLD,
                                    color=ft.Colors.GREY_800
                                ),
                                ft.Container(expand=True),
                                ft.Container(
                                    content=ft.Text(
                                        deadline.priority,
                                        size=10,
                                        color=ft.Colors.WHITE,
                                        weight=ft.FontWeight.BOLD
                                    ),
                                    padding=ft.padding.symmetric(horizontal=8, vertical=4),
                                    bgcolor=deadline_color,
                                    border_radius=12
                                ),
                                ft.Container(
                                    content=ft.Text(
                                        deadline.status,
                                        size=10,
                                        color=ft.Colors.WHITE,
                                        weight=ft.FontWeight.BOLD
                                    ),
                                    padding=ft.padding.symmetric(horizontal=8, vertical=4),
                                    bgcolor=status_color,
                                    border_radius=12
                                )
                            ]),
                            
                            ft.Text(
                                deadline.description or "Nessuna descrizione",
                                size=12,
                                color=ft.Colors.GREY_600
                            ),
                            
                            ft.Row([
                                ft.Icon(ft.Icons.PERSON, size=14, color=ft.Colors.GREY_500),
                                ft.Text(client_name, size=12, color=ft.Colors.GREY_600),
                                ft.Icon(ft.Icons.FOLDER, size=14, color=ft.Colors.GREY_500),
                                ft.Text(project_name, size=12, color=ft.Colors.GREY_600)
                            ], spacing=8)
                        ], spacing=4, expand=True),
                        
                        # Azioni
                        ft.Column([
                            ft.IconButton(
                                icon=ft.Icons.EDIT,
                                tooltip="Modifica",
                                on_click=lambda e, d=deadline: self._edit_deadline(d)
                            ),
                            ft.IconButton(
                                icon=ft.Icons.CHECK_CIRCLE if deadline.status == DeadlineStatus.PENDING else ft.Icons.UNDO,
                                tooltip="Completa" if deadline.status == DeadlineStatus.PENDING else "Riapri",
                                on_click=lambda e, d=deadline: self._toggle_deadline_status(d)
                            )
                        ], spacing=0)
                    ], spacing=12, alignment=ft.MainAxisAlignment.START),
                    padding=ft.padding.all(16),
                    bgcolor=ft.Colors.WHITE,
                    border=ft.border.all(1, ft.Colors.GREY_200),
                    border_radius=12,
                    margin=ft.margin.only(bottom=8),
                    on_click=lambda e, d=deadline: self._show_deadline_detail(d)
                )
            )
        
        if not deadline_widgets:
            deadline_widgets.append(
                ft.Container(
                    content=ft.Column([
                        ft.Icon(ft.Icons.EVENT_AVAILABLE, size=64, color=ft.Colors.GREY_400),
                        ft.Text(
                            "Nessuna scadenza per questo giorno",
                            size=16,
                            color=ft.Colors.GREY_600,
                            text_align=ft.TextAlign.CENTER
                        )
                    ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=16),
                    padding=ft.padding.all(40),
                    alignment=ft.alignment.center
                )
            )
        
        return ft.Container(
            content=ft.Column([
                day_header,
                ft.Container(
                    content=ft.Column(
                        deadline_widgets,
                        scroll=ft.ScrollMode.AUTO,
                        expand=True
                    ),
                    bgcolor=ft.Colors.WHITE,
                    border_radius=16,
                    border=ft.border.all(1, ft.Colors.GREY_200),
                    padding=ft.padding.all(16),
                    expand=True,
                    shadow=ft.BoxShadow(
                        spread_radius=0,
                        blur_radius=8,
                        color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
                        offset=ft.Offset(0, 4)
                    )
                )
            ], spacing=0),
            padding=ft.padding.all(0)
        )
    
    def _passes_filters(self, deadline: Any) -> bool:
        """Verifica se una scadenza passa i filtri attivi"""
        # Hide completed deadlines by default (following the same pattern as deadlines view)
        if hasattr(deadline, 'status') and deadline.status == DeadlineStatus.COMPLETED:
            return False

        if self.filter_client and str(deadline.client_id) != str(self.filter_client):
            return False

        if self.filter_priority and deadline.priority != self.filter_priority:
            return False

        if self.filter_status and deadline.status != self.filter_status:
            return False

        return True
    
    def _update_view(self):
        """Aggiorna la vista corrente"""
        try:
            # Aggiorna il testo del mese/anno
            if self.month_year_text:
                self.month_year_text.value = self._get_view_title()
            
            # Aggiorna il dropdown della modalità vista
            if self.view_mode_dropdown:
                self.view_mode_dropdown.value = self.view_mode
            
            # Ricostruisci la griglia del calendario
            if self.calendar_grid_container:
                self.calendar_grid_container.content = self._create_calendar_grid()
            
            # Aggiorna anche il contenuto dell'header per i tooltip dinamici
            if self.header_container:
                self.header_container.content = self._create_header_content()
            
            # Aggiorna i container con gestione errori migliorata
            updated = False
            
            # Prova prima con i singoli container
            try:
                if self.calendar_grid_container and hasattr(self.calendar_grid_container, 'update'):
                    self.calendar_grid_container.update()
                    updated = True
            except Exception as e:
                logger.debug(f"Errore aggiornamento calendar_grid_container: {e}")
            
            try:
                if self.header_container and hasattr(self.header_container, 'update'):
                    self.header_container.update()
                    updated = True
            except Exception as e:
                logger.debug(f"Errore aggiornamento header_container: {e}")
            
            try:
                if self.main_container and hasattr(self.main_container, 'update'):
                    self.main_container.update()
                    updated = True
            except Exception as e:
                logger.debug(f"Errore aggiornamento main_container: {e}")
            
            # Se l'aggiornamento dei container non funziona, prova con la pagina
            if not updated:
                try:
                    if (hasattr(self.app, 'main_layout') and 
                        self.app.main_layout and 
                        hasattr(self.app.main_layout, 'page') and 
                        self.app.main_layout.page and
                        hasattr(self.app.main_layout.page, 'update')):
                        self.app.main_layout.page.update()
                        updated = True
                except Exception as e:
                    logger.debug(f"Errore aggiornamento page tramite main_layout: {e}")
                    
                    # Ultimo tentativo: prova con app.page
                    try:
                        if (hasattr(self.app, 'page') and 
                            self.app.page and 
                            hasattr(self.app.page, 'update')):
                            self.app.page.update()
                            updated = True
                    except Exception as e:
                        logger.debug(f"Errore aggiornamento app.page: {e}")
            
            if not updated:
                logger.warning("Impossibile aggiornare la vista calendario - nessun metodo di aggiornamento disponibile")
                    
        except Exception as e:
            logger.error(f"Errore aggiornamento vista calendario: {e}")
    
    def _navigate_month(self, direction: int):
        """Naviga di mesi (±1)"""
        if self.view_mode == "month":
            if direction > 0:
                if self.current_date.month == 12:
                    self.current_date = self.current_date.replace(year=self.current_date.year + 1, month=1)
                else:
                    self.current_date = self.current_date.replace(month=self.current_date.month + 1)
            else:
                if self.current_date.month == 1:
                    self.current_date = self.current_date.replace(year=self.current_date.year - 1, month=12)
                else:
                    self.current_date = self.current_date.replace(month=self.current_date.month - 1)
        elif self.view_mode == "week":
            # Naviga di settimane
            self.current_date = self.current_date + timedelta(weeks=direction)
        elif self.view_mode == "day":
            # Naviga di giorni
            self.current_date = self.current_date + timedelta(days=direction)
        
        self._load_data()
        self._rebuild_view()
    
    def _navigate_to_today(self):
        """Naviga alla data odierna"""
        self.current_date = date.today()
        self._load_data()
        self._rebuild_view()
    
    def _change_view_mode(self, mode: str):
        """Cambia modalità di visualizzazione"""
        if mode != self.view_mode and not self._rebuilding:
            logger.info(f"Cambio modalità vista da {self.view_mode} a {mode}")
            self.view_mode = mode
            # Ricostruisci completamente i componenti per la nuova vista
            self._rebuild_view()
    
    def _rebuild_view(self):
        """Ricostruisce completamente la vista"""
        # Previeni loop infiniti
        if self._rebuilding:
            logger.warning("_rebuild_view chiamato durante ricostruzione, ignorato per prevenire loop")
            return
            
        self._rebuilding = True
        try:
            # Ricostruisci tutti i container con il nuovo contenuto
            if self.calendar_grid_container:
                self.calendar_grid_container.content = self._create_calendar_grid()
            
            if self.header_container:
                self.header_container.content = self._create_header_content()
            
            # Aggiorna la pagina con migliore controllo degli errori
            updated = False
            
            # Prova prima con i singoli container
            try:
                if self.calendar_grid_container and hasattr(self.calendar_grid_container, 'update'):
                    self.calendar_grid_container.update()
                    updated = True
            except Exception as e:
                logger.debug(f"Errore aggiornamento calendar_grid_container: {e}")
            
            try:
                if self.header_container and hasattr(self.header_container, 'update'):
                    self.header_container.update()
                    updated = True
            except Exception as e:
                logger.debug(f"Errore aggiornamento header_container: {e}")
            
            try:
                if self.main_container and hasattr(self.main_container, 'update'):
                    self.main_container.update()
                    updated = True
            except Exception as e:
                logger.debug(f"Errore aggiornamento main_container: {e}")
            
            # Se l'aggiornamento dei container non funziona, prova con la pagina
            if not updated:
                try:
                    if (hasattr(self.app, 'main_layout') and 
                        self.app.main_layout and 
                        hasattr(self.app.main_layout, 'page') and 
                        self.app.main_layout.page and
                        hasattr(self.app.main_layout.page, 'update')):
                        self.app.main_layout.page.update()
                        updated = True
                except Exception as e:
                    logger.debug(f"Errore aggiornamento page tramite main_layout: {e}")
                    
                    # Ultimo tentativo: prova con app.page
                    try:
                        if (hasattr(self.app, 'page') and 
                            self.app.page and 
                            hasattr(self.app.page, 'update')):
                            self.app.page.update()
                            updated = True
                    except Exception as e:
                        logger.debug(f"Errore aggiornamento app.page: {e}")
            
            if not updated:
                logger.warning("Impossibile aggiornare la vista calendario - nessun metodo di aggiornamento disponibile")
                
        except Exception as e:
            logger.error(f"Errore ricostruzione vista calendario: {e}")
        finally:
            # Rilascia sempre il flag
            self._rebuilding = False

    def refresh(self):
        """Metodo per refresh della vista (chiamato dal layout principale)"""
        self._load_data()
        self._rebuild_view()
    
    def refresh_data(self):
        """Aggiorna i dati del calendario"""
        self._load_data()
        self._rebuild_view()
    
    def _update_filter(self, filter_type: str, value: Any):
        """Aggiorna un filtro"""
        logger.debug(f"Updating filter: {filter_type} = {value}")
        if filter_type == "client":
            try:
                self.filter_client = UUID(value) if value and value != "None" else None
            except ValueError:
                logger.warning(f"Invalid UUID string for client filter: {value}")
                self.filter_client = None
        elif filter_type == "project":
            self.filter_project = UUID(value) if value and value != "None" else None
        elif filter_type == "priority":
            self.filter_priority = value if value != "None" else None
        elif filter_type == "status":
            self.filter_status = value if value != "None" else None
        
        self._rebuild_view()
    
    def _on_day_click(self, day_date: date):
        """Gestisce il click su un giorno"""
        self.selected_date = day_date
        self.current_date = day_date
        if self.view_mode != "day":
            self.view_mode = "day"
            if self.view_mode_dropdown:
                self.view_mode_dropdown.value = "day"
        self._rebuild_view()
    
    def _show_deadline_detail(self, deadline: Any):
        """Mostra i dettagli di una scadenza"""
        # TODO: Implementare vista dettaglio scadenza
        logger.info(f"Mostra dettagli scadenza: {deadline.title}")
    
    def _create_new_deadline(self):
        """Crea una nuova scadenza"""
        # TODO: Implementare dialog creazione scadenza
        logger.info("Crea nuova scadenza")
    
    def _edit_deadline(self, deadline: Any):
        """Modifica una scadenza"""
        # TODO: Implementare dialog modifica scadenza
        logger.info(f"Modifica scadenza: {deadline.title}")
    
    def _toggle_deadline_status(self, deadline: Any):
        """Cambia lo stato di una scadenza"""
        try:
            new_status = DeadlineStatus.COMPLETED if deadline.status == DeadlineStatus.PENDING else DeadlineStatus.PENDING
            
            # Aggiorna nel database
            deadline.status = new_status
            if new_status == DeadlineStatus.COMPLETED:
                deadline.completed_date = date.today()
            else:
                deadline.completed_date = None
            
            self.db.update_deadline(deadline)
            
            # Ricarica dati e aggiorna vista
            self._load_data()
            self._rebuild_view()
            
            logger.info(f"Stato scadenza cambiato: {deadline.title} -> {new_status}")
            
        except Exception as e:
            logger.error(f"Errore cambio stato scadenza: {e}")
    
    def build(self) -> ft.Container:
        """Costruisce la vista calendario con design moderno"""
        self.main_container = ft.Container(
            content=ft.Column([
                self._create_header(),
                ft.Container(height=8),  # Spaziatura ridotta
                self.calendar_grid_container
            ], spacing=0, scroll=ft.ScrollMode.AUTO),
            padding=ft.padding.all(24),
            bgcolor=ft.Colors.GREY_50,
            expand=True
        )
        return self.main_container