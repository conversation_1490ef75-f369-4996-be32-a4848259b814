#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Modern Header Component using Native Flet SearchBar
Designed for Flet 28.x+ with proper overlay and Material Design
"""

import flet as ft
from typing import Callable, Optional
from datetime import datetime
from core import get_logger
from .modern_search_component import ModernSearchComponent

logger = get_logger(__name__)

class ModernHeader:
    """
    Modern header component using Flet's native SearchBar
    Provides proper overlay behavior and Material Design styling
    """
    
    def __init__(
        self,
        on_search: Callable[[str], None],
        on_notification_click: Callable[[], None],
        on_settings_click: Callable[[], None],
        app_instance=None
    ):
        self.on_search = on_search
        self.on_notification_click = on_notification_click
        self.on_settings_click = on_settings_click
        self.app = app_instance
        
        # Header state
        self.title = "Dashboard"
        self.notification_count = 0
        self.show_back_button = False
        
        # Initialize modern search component
        self.search_component = ModernSearchComponent(
            app_instance=app_instance,
            on_result_selected=self._handle_search_result,
            placeholder="🔍 Cerca clienti, progetti, scadenze..."
        )
        
        logger.info("Modern header initialized with native SearchBar")
    
    def _handle_search_result(self, result):
        """Handle search result selection"""
        logger.info(f"Search result selected: {result.get('title', 'Unknown')}")
        # The navigation is handled by the search component itself
        # This callback can be used for additional analytics or actions
        
        # Notify the original search callback if needed
        if self.on_search:
            self.on_search(result.get('title', ''))
    
    def _create_title_section(self) -> ft.Container:
        """Create the title section with back button support"""
        title_controls = []
        
        # Add back button if needed
        if self.show_back_button:
            back_button = ft.IconButton(
                icon=ft.Icons.ARROW_BACK,
                icon_color=ft.Colors.BLUE_600,
                icon_size=24,
                tooltip="Torna indietro",
                on_click=lambda _: self._handle_back_click()
            )
            title_controls.append(back_button)
        
        # Logo and title section
        logo_title_row = ft.Row([
            ft.Image(
                src="assets/logo_v2.png",
                width=32,
                height=32,
                fit=ft.ImageFit.CONTAIN
            ),
            ft.Column([
                ft.Text(
                    self.title,
                    size=24,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.GREY_800
                ),
                ft.Text(
                    datetime.now().strftime("%A, %d %B %Y"),
                    size=12,
                    color=ft.Colors.GREY_500
                )
            ], spacing=2)
        ], spacing=12, vertical_alignment=ft.CrossAxisAlignment.CENTER)
        
        title_controls.append(logo_title_row)
        
        return ft.Container(
            content=ft.Row(
                controls=title_controls,
                spacing=10,
                vertical_alignment=ft.CrossAxisAlignment.CENTER
            ),
            padding=ft.padding.only(left=20)
        )
    
    def _create_search_section(self) -> ft.Container:
        """Create the modern search section using native SearchBar"""
        return ft.Container(
            content=self.search_component.build(),
            width=450,  # Slightly wider for better UX
            padding=ft.padding.symmetric(horizontal=20)
        )
    
    def _create_actions_section(self) -> ft.Container:
        """Create the actions section with modern styling"""
        
        # Notification button with badge
        notification_badge = None
        if self.notification_count > 0:
            notification_badge = ft.Container(
                content=ft.Text(
                    str(self.notification_count) if self.notification_count < 100 else "99+",
                    size=10,
                    color=ft.Colors.WHITE,
                    weight=ft.FontWeight.BOLD
                ),
                bgcolor=ft.Colors.RED,
                border_radius=10,
                padding=ft.padding.symmetric(horizontal=5, vertical=2),
                right=-8,
                top=-8
            )
        
        notification_button = ft.Container(
            content=ft.Stack([
                ft.IconButton(
                    icon=ft.Icons.NOTIFICATIONS_OUTLINED,
                    icon_color=ft.Colors.GREY_600,
                    icon_size=20,
                    tooltip="Notifiche",
                    on_click=lambda _: self.on_notification_click()
                ),
                notification_badge if notification_badge else ft.Container()
            ])
        )
        
        # Action buttons with modern styling
        action_buttons = [
            ft.IconButton(
                icon=ft.Icons.ANALYTICS_OUTLINED,
                icon_color=ft.Colors.BLUE_600,
                icon_size=20,
                tooltip="Report Completo",
                on_click=lambda _: self._handle_send_statistics('full')
            ),
            ft.IconButton(
                icon=ft.Icons.SCHEDULE_OUTLINED,
                icon_color=ft.Colors.ORANGE_600,
                icon_size=20,
                tooltip="Report Scadenze",
                on_click=lambda _: self._handle_send_statistics('deadlines')
            ),
            notification_button,
            ft.IconButton(
                icon=ft.Icons.SETTINGS_OUTLINED,
                icon_color=ft.Colors.GREY_600,
                icon_size=20,
                tooltip="Impostazioni",
                on_click=lambda _: self.on_settings_click()
            )
        ]
        
        # System status indicator
        status_indicator = ft.Container(
            content=ft.Row([
                ft.Container(
                    width=8,
                    height=8,
                    bgcolor=ft.Colors.GREEN,
                    border_radius=4
                ),
                ft.Text(
                    "Sistema attivo",
                    size=11,
                    color=ft.Colors.GREY_600
                )
            ], spacing=6),
            padding=ft.padding.symmetric(horizontal=12, vertical=6),
            bgcolor=ft.Colors.GREEN_50,
            border_radius=15,
            border=ft.border.all(1, ft.Colors.GREEN_200)
        )
        
        return ft.Container(
            content=ft.Row([
                status_indicator,
                ft.VerticalDivider(width=1, color=ft.Colors.GREY_300),
                *action_buttons
            ], spacing=8),
            padding=ft.padding.only(right=20)
        )
    
    def _create_quick_actions(self) -> ft.Container:
        """Create quick action buttons for dashboard"""
        if self.title != "Dashboard":
            return ft.Container()
        
        quick_actions = [
            {
                "icon": ft.Icons.ADD_CIRCLE_OUTLINE,
                "text": "Nuovo Cliente",
                "color": ft.Colors.BLUE_600,
                "action": "new_client"
            },
            {
                "icon": ft.Icons.WORK_OUTLINE,
                "text": "Nuovo Progetto",
                "color": ft.Colors.GREEN_600,
                "action": "new_project"
            },
            {
                "icon": ft.Icons.SCHEDULE,
                "text": "Nuova Scadenza",
                "color": ft.Colors.ORANGE_600,
                "action": "new_deadline"
            }
        ]
        
        action_buttons = []
        for action in quick_actions:
            button = ft.Container(
                content=ft.Row([
                    ft.Icon(
                        action["icon"],
                        color=action["color"],
                        size=16
                    ),
                    ft.Text(
                        action["text"],
                        size=12,
                        color=action["color"],
                        weight=ft.FontWeight.BOLD
                    )
                ], spacing=6),
                padding=ft.padding.symmetric(horizontal=12, vertical=6),
                border_radius=20,
                border=ft.border.all(1, action["color"]),
                bgcolor=ft.Colors.WHITE,
                on_click=lambda _, a=action["action"]: self._handle_quick_action(a),
                tooltip=f"Crea {action['text'].lower()}"
            )
            action_buttons.append(button)
        
        return ft.Container(
            content=ft.Row(
                controls=action_buttons,
                spacing=12
            ),
            padding=ft.padding.symmetric(horizontal=20, vertical=8)
        )
    
    def _handle_quick_action(self, action: str):
        """Handle quick action clicks"""
        logger.info(f"Quick action: {action}")
        
        if not self.app or not hasattr(self.app, 'main_layout'):
            return
        
        if action == "new_client":
            clients_view = self.app.main_layout.get_view("clients")
            if clients_view and hasattr(clients_view, '_show_client_form'):
                clients_view._show_client_form()
                self.app.main_layout._navigate_to("clients")
        
        elif action == "new_project":
            projects_view = self.app.main_layout.get_view("projects")
            if projects_view and hasattr(projects_view, '_show_project_form'):
                projects_view._show_project_form()
                self.app.main_layout._navigate_to("projects")
        
        elif action == "new_deadline":
            deadlines_view = self.app.main_layout.get_view("deadlines")
            if deadlines_view and hasattr(deadlines_view, '_show_deadline_form'):
                deadlines_view._show_deadline_form()
                self.app.main_layout._navigate_to("deadlines")
    
    def _handle_back_click(self):
        """Handle back button click"""
        if self.app and hasattr(self.app, 'main_layout'):
            self.app.main_layout.go_back()
    
    def _handle_send_statistics(self, report_type: str):
        """Handle statistics report sending"""
        try:
            import json
            import os
            from core.services.statistics_service import StatisticsService
            from core.config import AppConfig

            # Load email settings from JSON file instead of AppConfig
            settings_file = os.path.join("data", "settings.json")
            if not os.path.exists(settings_file):
                self._show_error_dialog("File impostazioni non trovato. Configura SMTP nelle impostazioni.")
                return

            with open(settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)

            email_settings = settings.get('email', {})

            # Verifica configurazione email
            if not (email_settings.get('server') and email_settings.get('username') and email_settings.get('password')):
                self._show_error_dialog("Configurazione email non valida. Configura SMTP nelle impostazioni.")
                return

            # Get recipients
            recipients = []
            if email_settings.get('username'):
                recipients.append(email_settings['username'])

            # Add recipients from reports and notifications settings
            reports_recipients = settings.get('reports', {}).get('recipients', [])
            if reports_recipients:
                recipients.extend(reports_recipients)

            notification_recipients = settings.get('notifications', {}).get('reminder_recipients', [])
            if notification_recipients:
                recipients.extend(notification_recipients)

            # Remove duplicates
            recipients = list(set(recipients))

            if not recipients:
                self._show_error_dialog("Nessun destinatario configurato. Configura gli indirizzi email nelle impostazioni.")
                return

            # Create statistics service with AppConfig
            config = AppConfig()
            stats_service = StatisticsService(self.app.db_manager, config)

            # Update email service configuration with JSON settings
            stats_service.email_service.smtp_config.update({
                'smtp_server': email_settings.get('server', ''),
                'smtp_port': email_settings.get('port', 587),
                'smtp_username': email_settings.get('username', ''),
                'smtp_password': email_settings.get('password', ''),
                'smtp_use_tls': email_settings.get('use_tls', True),
                'from_name': email_settings.get('sender_name', 'Agevolami PM'),
                'from_email': email_settings.get('sender_email', ''),
                'enabled': bool(email_settings.get('server'))
            })

            # Test connection first
            if not stats_service.email_service.test_connection():
                self._show_error_dialog("Configurazione email non valida. Verifica le impostazioni SMTP.")
                return

            self._show_send_confirmation_dialog(stats_service, recipients, report_type)

        except Exception as e:
            self._show_error_dialog(f"Errore durante l'invio delle statistiche: {str(e)}")
    
    def _show_send_confirmation_dialog(self, stats_service, recipients: list, report_type: str):
        """Show send confirmation dialog"""
        report_name = "Report Completo" if report_type == 'full' else "Report Scadenze"
        
        def send_report(e):
            dialog.open = False
            self.app.page.update()
            
            try:
                success = stats_service.send_statistics_email(recipients, report_type)
                if success:
                    self._show_success_dialog(f"{report_name} inviato con successo!")
                else:
                    self._show_error_dialog(f"Errore durante l'invio del {report_name.lower()}")
            except Exception as ex:
                self._show_error_dialog(f"Errore: {str(ex)}")
        
        dialog = ft.AlertDialog(
            title=ft.Text(f"Conferma Invio {report_name}"),
            content=ft.Column([
                ft.Text(f"Vuoi inviare il {report_name.lower()} ai seguenti destinatari?"),
                ft.Text(", ".join(recipients), weight=ft.FontWeight.BOLD),
            ], tight=True, spacing=10),
            actions=[
                ft.TextButton("Annulla", on_click=lambda _: setattr(dialog, 'open', False) or self.app.page.update()),
                ft.ElevatedButton("Invia", on_click=send_report)
            ],
            modal=True
        )
        
        self.app.page.overlay.append(dialog)
        dialog.open = True
        self.app.page.update()
    
    def _show_success_dialog(self, message: str):
        """Show success dialog"""
        dialog = ft.AlertDialog(
            title=ft.Row([ft.Icon(ft.Icons.CHECK_CIRCLE, color=ft.Colors.GREEN), ft.Text("Successo")]),
            content=ft.Text(message),
            actions=[ft.TextButton("OK", on_click=lambda _: setattr(dialog, 'open', False) or self.app.page.update())],
        )
        self.app.page.overlay.append(dialog)
        dialog.open = True
        self.app.page.update()
    
    def _show_error_dialog(self, message: str):
        """Show error dialog"""
        dialog = ft.AlertDialog(
            title=ft.Row([ft.Icon(ft.Icons.ERROR, color=ft.Colors.RED), ft.Text("Errore")]),
            content=ft.Text(message),
            actions=[ft.TextButton("OK", on_click=lambda _: setattr(dialog, 'open', False) or self.app.page.update())],
        )
        self.app.page.overlay.append(dialog)
        dialog.open = True
        self.app.page.update()
    
    def build(self) -> ft.Container:
        """Build the modern header with native SearchBar"""
        return ft.Container(
            content=ft.Column([
                # Main header row
                ft.Container(
                    content=ft.Row([
                        # Title section
                        self._create_title_section(),
                        
                        # Flexible spacer
                        ft.Container(expand=True),
                        
                        # Modern search section (native SearchBar)
                        self._create_search_section(),
                        
                        # Flexible spacer
                        ft.Container(expand=True),
                        
                        # Actions section
                        self._create_actions_section()
                    ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                    height=80,
                    padding=ft.padding.symmetric(vertical=16)
                ),
                
                # Quick actions (only for dashboard)
                self._create_quick_actions(),
                
                # Divider
                ft.Divider(color=ft.Colors.GREY_200, height=1)
            ], spacing=0),
            bgcolor=ft.Colors.WHITE,
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=4,
                color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
                offset=ft.Offset(0, 2)
            )
        )
    
    # Public interface methods
    def set_title(self, title: str):
        """Set header title"""
        self.title = title
    
    def set_back_button_visible(self, visible: bool):
        """Set back button visibility"""
        self.show_back_button = visible
    
    def update_notification_count(self, count: int):
        """Update notification count"""
        self.notification_count = count
    
    def clear_search(self):
        """Clear search"""
        if self.search_component:
            self.search_component.clear()
    
    def get_search_query(self) -> str:
        """Get current search query"""
        return self.search_component.get_current_query() if self.search_component else ""
    
    def set_search_query(self, query: str):
        """Set search query programmatically"""
        if self.search_component:
            self.search_component.set_query(query) 