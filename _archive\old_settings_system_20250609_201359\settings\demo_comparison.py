#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Settings Architecture Comparison Demo
Shows the difference between old monolithic and new modular approach
"""

import flet as ft
from core import get_logger

# New architecture imports
from .modern_settings_view import ModernSettingsView
from .settings_controller import SettingsController

# Old architecture (for comparison)
from ..settings import SettingsView

logger = get_logger(__name__)

class SettingsComparisonDemo:
    """
    Demonstration comparing old vs new settings architecture
    """
    
    def __init__(self, app_instance):
        self.app = app_instance
        self.current_view = "new"  # "old" or "new"
    
    def _create_header(self) -> ft.Container:
        """Create comparison header"""
        
        def switch_to_old(e):
            self.current_view = "old"
            self._refresh_content()
        
        def switch_to_new(e):
            self.current_view = "new"
            self._refresh_content()
        
        return ft.Container(
            content=ft.Column([
                ft.Text(
                    "⚙️ Settings Architecture Comparison",
                    size=24,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.GREY_800
                ),
                
                ft.Text(
                    "Compare the old monolithic settings with the new modular approach",
                    size=14,
                    color=ft.Colors.GREY_600
                ),
                
                ft.Container(height=16),
                
                # Toggle buttons
                ft.Row([
                    ft.ElevatedButton(
                        text="🔄 Vecchia Architettura (3758 righe)",
                        on_click=switch_to_old,
                        bgcolor=ft.Colors.RED_600 if self.current_view == "old" else ft.Colors.GREY_300,
                        color=ft.Colors.WHITE if self.current_view == "old" else ft.Colors.GREY_700,
                        style=ft.ButtonStyle(
                            shape=ft.RoundedRectangleBorder(radius=8)
                        )
                    ),
                    
                    ft.ElevatedButton(
                        text="✨ Nuova Architettura (Modulare)",
                        on_click=switch_to_new,
                        bgcolor=ft.Colors.GREEN_600 if self.current_view == "new" else ft.Colors.GREY_300,
                        color=ft.Colors.WHITE if self.current_view == "new" else ft.Colors.GREY_700,
                        style=ft.ButtonStyle(
                            shape=ft.RoundedRectangleBorder(radius=8)
                        )
                    )
                ], spacing=12),
                
                # Comparison info
                self._create_comparison_info()
            ], spacing=8),
            padding=ft.padding.only(bottom=24),
            bgcolor=ft.Colors.WHITE,
            border_radius=12,
            margin=ft.margin.only(bottom=16)
        )
    
    def _create_comparison_info(self) -> ft.Container:
        """Create comparison information"""
        
        old_features = [
            "❌ File singolo da 3758 righe",
            "❌ Logica UI/business mista",
            "❌ Difficile da testare",
            "❌ Difficile da mantenere",
            "❌ Sezioni tutte uguali",
            "❌ Nessuna validazione real-time",
            "❌ Feedback visivo limitato"
        ]
        
        new_features = [
            "✅ Architettura modulare",
            "✅ Separazione responsabilità",
            "✅ Componenti riutilizzabili",
            "✅ Controller centralizzato",
            "✅ Navigazione a tab",
            "✅ Validazione real-time",
            "✅ UX moderna e responsiva"
        ]
        
        return ft.Container(
            content=ft.Row([
                # Old architecture column
                ft.Container(
                    content=ft.Column([
                        ft.Text(
                            "🔄 Vecchia Architettura",
                            size=14,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.RED_600
                        ),
                        ft.Container(height=8),
                        *[ft.Text(feature, size=11, color=ft.Colors.GREY_700) for feature in old_features]
                    ], spacing=4),
                    padding=ft.padding.all(16),
                    bgcolor=ft.Colors.RED_50,
                    border_radius=8,
                    border=ft.border.all(1, ft.Colors.RED_200),
                    expand=True
                ),
                
                # New architecture column
                ft.Container(
                    content=ft.Column([
                        ft.Text(
                            "✨ Nuova Architettura",
                            size=14,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.GREEN_600
                        ),
                        ft.Container(height=8),
                        *[ft.Text(feature, size=11, color=ft.Colors.GREY_700) for feature in new_features]
                    ], spacing=4),
                    padding=ft.padding.all(16),
                    bgcolor=ft.Colors.GREEN_50,
                    border_radius=8,
                    border=ft.border.all(1, ft.Colors.GREEN_200),
                    expand=True
                )
            ], spacing=16),
            margin=ft.margin.only(top=16)
        )
    
    def _get_current_content(self) -> ft.Control:
        """Get content for current view"""
        if self.current_view == "old":
            # Create old settings view
            old_view = SettingsView(self.app)
            return old_view.build()
        else:
            # Create new modern settings view
            new_view = ModernSettingsView(self.app)
            return new_view.build()
    
    def _refresh_content(self):
        """Refresh the content"""
        # This would update the UI in a real implementation
        logger.info(f"Switching to {self.current_view} view")
    
    def build(self) -> ft.Container:
        """Build the comparison demo"""
        
        return ft.Container(
            content=ft.Column([
                self._create_header(),
                
                # Current view content
                ft.Container(
                    content=self._get_current_content(),
                    expand=True
                )
            ], spacing=0),
            padding=ft.padding.all(16),
            expand=True,
            bgcolor=ft.Colors.GREY_50
        )


# File structure comparison for documentation
FILE_STRUCTURE_COMPARISON = """
📁 VECCHIA ARCHITETTURA:
└── src/ui/views/settings.py (3758 righe!) 
    ├── SettingsView class (tutto insieme)
    ├── _create_smtp_section()
    ├── _create_alerts_section()
    ├── _create_reports_section()
    ├── _create_google_section()
    ├── _create_windows_section()
    └── ... 50+ metodi tutti nello stesso file

📁 NUOVA ARCHITETTURA MODULARE:
└── src/ui/views/settings/
    ├── modern_settings_view.py (300 righe)
    ├── settings_controller.py (400 righe)
    ├── components/
    │   ├── __init__.py
    │   ├── settings_card.py (card modulari)
    │   ├── form_components.py (form riutilizzabili)
    │   └── ... (componenti specifici)
    └── sections/
        ├── __init__.py
        ├── email_section.py (200 righe)
        ├── notifications_section.py
        ├── google_services_section.py
        ├── windows_section.py
        └── ... (sezioni modulari)

🎯 VANTAGGI NUOVA ARCHITETTURA:
✅ Ogni file ha una responsabilità specifica
✅ Componenti riutilizzabili
✅ Più facile da testare
✅ Più facile da mantenere
✅ UI moderna e responsive
✅ Validazione real-time
✅ Separazione logica/UI
""" 