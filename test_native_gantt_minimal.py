#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 Minimal Native Gantt Test
Test with simple mock objects
"""

import os
import sys
import flet as ft
from datetime import datetime, timedelta

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

print("🚀 Minimal Native Gantt Test...")

def main(page: ft.Page):
    page.title = "🚀 Native Gantt Test"
    page.window.width = 1200
    page.window.height = 800
    
    try:
        from ui.views.native_gantt import NativeGanttChart
        
        # Simple mock objects
        class MockProject:
            def __init__(self, name, start, end, status="in_corso"):
                self.id = name.lower().replace(' ', '_')
                self.name = name
                self.start_date = start
                self.end_date = end
                self.status = MockEnum(status)
        
        class MockDeadline:
            def __init__(self, title, due, status="in_attesa"):
                self.id = title.lower().replace(' ', '_')
                self.title = title
                self.due_date = due  
                self.status = MockEnum(status)
        
        class MockEnum:
            def __init__(self, value):
                self.value = value
            def __str__(self):
                return self.value
        
        class MockDB:
            def get_all_projects(self):
                return [
                    MockProject("Website Redesign", 
                              datetime.now().date() - timedelta(days=10),
                              datetime.now().date() + timedelta(days=20)),
                    MockProject("Mobile App", 
                              datetime.now().date() + timedelta(days=5),
                              datetime.now().date() + timedelta(days=35), "presentato"),
                    MockProject("Database Upgrade", 
                              datetime.now().date() - timedelta(days=15),
                              datetime.now().date() - timedelta(days=2), "completato")
                ]
            
            def get_all_deadlines(self):
                return [
                    MockDeadline("Proposal Due", datetime.now().date() + timedelta(days=7)),
                    MockDeadline("Beta Release", datetime.now().date() + timedelta(days=30)),
                    MockDeadline("Documentation", datetime.now().date() - timedelta(days=1), "scaduto")
                ]
        
        class MockApp:
            def __init__(self):
                self.db_manager = MockDB()
                self.page = page
        
        app = MockApp()
        
        # Create Native Gantt Chart
        gantt_chart = NativeGanttChart(app)
        
        # Build and display
        chart_view = gantt_chart.build()
        page.add(chart_view)
        
        print("✅ Native Gantt Chart loaded successfully!")
        print("🎯 Chart should display projects and deadlines!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        
        page.add(
            ft.Container(
                content=ft.Column([
                    ft.Icon(ft.Icons.ERROR, size=48, color=ft.Colors.RED),
                    ft.Text("❌ Error Loading Gantt Chart", size=24, weight=ft.FontWeight.BOLD),
                    ft.Text(f"Error: {str(e)}", size=14, color=ft.Colors.GREY_600),
                    ft.ElevatedButton("🔄 Close", on_click=lambda e: page.window.close())
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=15),
                alignment=ft.alignment.center,
                expand=True
            )
        )

if __name__ == "__main__":
    ft.app(target=main, port=8084) 