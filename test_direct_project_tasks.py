#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Direct Project Tasks - Tasks attached directly to projects without deadlines
"""

import flet as ft
from datetime import datetime, date
from uuid import uuid4

# Test data
TEST_PROJECT = {
    'id': str(uuid4()),
    'name': 'Test Project Direct Tasks',
    'description': 'Project for testing direct task attachment'
}

TEST_TASKS = [
    {
        'id': str(uuid4()),
        'title': 'Direct Task 1',
        'description': 'This task is attached directly to the project',
        'project_id': TEST_PROJECT['id'],
        'deadline_id': None,  # No deadline
        'status': 'in_attesa',
        'priority': 'media'
    },
    {
        'id': str(uuid4()),
        'title': 'Direct Task 2',
        'description': 'Another task without deadline',
        'project_id': TEST_PROJECT['id'],
        'deadline_id': None,  # No deadline
        'status': 'in_corso',
        'priority': 'alta'
    }
]

def create_test_task_card(task_data, project_data):
    """Create a test task card for direct project tasks"""
    
    # Status colors
    status_colors = {
        'in_attesa': ft.Colors.ORANGE_100,
        'in_corso': ft.Colors.BLUE_100,
        'completato': ft.Colors.GREEN_100
    }
    
    # Priority colors
    priority_colors = {
        'bassa': ft.Colors.GREEN,
        'media': ft.Colors.BLUE,
        'alta': ft.Colors.ORANGE,
        'critica': ft.Colors.RED
    }
    
    return ft.Container(
        content=ft.Column([
            # Header row
            ft.Row([
                ft.Row([
                    ft.Container(
                        content=ft.Icon(
                            ft.Icons.CIRCLE,
                            size=8,
                            color=priority_colors.get(task_data['priority'], ft.Colors.GREY)
                        ),
                        tooltip=f"Priorità: {task_data['priority'].title()}"
                    ),
                    ft.Text(
                        task_data['title'],
                        size=14,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.GREY_800,
                        expand=True
                    )
                ], spacing=8, expand=True),
                
                ft.Container(
                    content=ft.Text(
                        task_data['status'].replace('_', ' ').title(),
                        size=11,
                        color=ft.Colors.GREY_700,
                        weight=ft.FontWeight.W_500
                    ),
                    bgcolor=status_colors.get(task_data['status'], ft.Colors.GREY_100),
                    padding=ft.padding.symmetric(horizontal=8, vertical=4),
                    border_radius=12
                )
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            
            # Description
            ft.Text(
                task_data['description'],
                size=12,
                color=ft.Colors.GREY_600,
                max_lines=2
            ),
            
            # Details row - shows project instead of deadline
            ft.Row([
                ft.Row([
                    ft.Icon(ft.Icons.WORK, size=14, color=ft.Colors.GREY_500),
                    ft.Text(
                        f"Progetto: {project_data['name']}",
                        size=11,
                        color=ft.Colors.GREY_600
                    )
                ], spacing=4),
                
                ft.Container(expand=True),
                
                ft.Row([
                    ft.Icon(ft.Icons.LINK_OFF, size=14, color=ft.Colors.ORANGE_500),
                    ft.Text(
                        "Nessuna scadenza",
                        size=11,
                        color=ft.Colors.ORANGE_600
                    )
                ], spacing=4)
            ], spacing=12, alignment=ft.MainAxisAlignment.START)
        ], spacing=6),
        padding=ft.padding.all(10),
        margin=ft.margin.only(bottom=4),
        bgcolor=ft.Colors.WHITE,
        border_radius=6,
        border=ft.border.all(1, ft.Colors.GREY_200)
    )

def main(page: ft.Page):
    page.title = "Test Direct Project Tasks"
    page.theme_mode = ft.ThemeMode.LIGHT
    page.padding = 20
    
    # Header
    header = ft.Container(
        content=ft.Column([
            ft.Text(
                "Test: Tasks Attached Directly to Projects",
                size=24,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.GREY_800
            ),
            ft.Text(
                "This demonstrates tasks that can be attached directly to projects without requiring a deadline",
                size=14,
                color=ft.Colors.GREY_600
            )
        ], spacing=8),
        padding=ft.padding.only(bottom=20)
    )
    
    # Project info
    project_info = ft.Container(
        content=ft.Column([
            ft.Row([
                ft.Icon(ft.Icons.WORK, size=20, color=ft.Colors.BLUE_600),
                ft.Text(
                    "Project Information",
                    size=16,
                    weight=ft.FontWeight.W_600,
                    color=ft.Colors.GREY_800
                )
            ], spacing=8),
            ft.Container(height=8),
            ft.Text(f"Name: {TEST_PROJECT['name']}", size=14),
            ft.Text(f"Description: {TEST_PROJECT['description']}", size=14),
            ft.Text(f"ID: {TEST_PROJECT['id']}", size=12, color=ft.Colors.GREY_500)
        ]),
        padding=ft.padding.all(16),
        bgcolor=ft.Colors.BLUE_50,
        border_radius=8,
        border=ft.border.all(1, ft.Colors.BLUE_200),
        margin=ft.margin.only(bottom=20)
    )
    
    # Task cards
    task_cards = []
    for task_data in TEST_TASKS:
        task_cards.append(create_test_task_card(task_data, TEST_PROJECT))
    
    tasks_section = ft.Container(
        content=ft.Column([
            ft.Row([
                ft.Icon(ft.Icons.TASK_ALT, size=20, color=ft.Colors.GREEN_600),
                ft.Text(
                    "Direct Project Tasks",
                    size=16,
                    weight=ft.FontWeight.W_600,
                    color=ft.Colors.GREY_800
                )
            ], spacing=8),
            ft.Container(height=12),
            ft.Column(task_cards, spacing=8)
        ]),
        padding=ft.padding.all(16),
        bgcolor=ft.Colors.GREEN_50,
        border_radius=8,
        border=ft.border.all(1, ft.Colors.GREEN_200)
    )
    
    # Benefits section
    benefits = ft.Container(
        content=ft.Column([
            ft.Row([
                ft.Icon(ft.Icons.CHECK_CIRCLE, size=20, color=ft.Colors.ORANGE_600),
                ft.Text(
                    "Benefits of Direct Project Tasks",
                    size=16,
                    weight=ft.FontWeight.W_600,
                    color=ft.Colors.GREY_800
                )
            ], spacing=8),
            ft.Container(height=12),
            ft.Column([
                ft.Row([
                    ft.Icon(ft.Icons.SPEED, size=16, color=ft.Colors.ORANGE_600),
                    ft.Text("Faster task creation - no need to create deadlines first", size=14)
                ], spacing=8),
                ft.Row([
                    ft.Icon(ft.Icons.STRAIGHTEN, size=16, color=ft.Colors.ORANGE_600),
                    ft.Text("Simpler workflow for general project tasks", size=14)
                ], spacing=8),
                ft.Row([
                    ft.Icon(ft.Icons.CATEGORY, size=16, color=ft.Colors.ORANGE_600),
                    ft.Text("Better organization - tasks can be categorized by project or deadline", size=14)
                ], spacing=8),
                ft.Row([
                    ft.Icon(ft.Icons.SYNC, size=16, color=ft.Colors.ORANGE_600),
                    ft.Text("Google Tasks sync still works with virtual deadline grouping", size=14)
                ], spacing=8)
            ], spacing=8)
        ]),
        padding=ft.padding.all(16),
        bgcolor=ft.Colors.ORANGE_50,
        border_radius=8,
        border=ft.border.all(1, ft.Colors.ORANGE_200),
        margin=ft.margin.only(top=20)
    )
    
    # Add all components to page
    page.add(
        ft.Column([
            header,
            project_info,
            tasks_section,
            benefits
        ], spacing=0)
    )

if __name__ == "__main__":
    ft.app(target=main) 