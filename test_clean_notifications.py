#!/usr/bin/env python3
"""
Test script to verify Windows notifications work without WNDPROC errors
"""

import os
import sys
import time

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_clean_notifications():
    """Test that notifications work without WNDPROC errors"""
    print("🔔 Testing Clean Windows Notifications...")
    
    try:
        from core.utils.windows_utils import WindowsNotificationManager
        
        # Initialize notification manager
        notification_manager = WindowsNotificationManager("Agevolami PM")
        
        print("   📤 Sending test notification...")
        
        # Test plyer notification (should be clean now)
        result = notification_manager.show_notification_with_plyer(
            "✅ Agevolami PM - Test Pulito",
            "Questa notifica dovrebbe apparire senza errori WNDPROC!",
            timeout=5
        )
        
        if result:
            print("   ✅ Notification sent successfully!")
            print("   ⏳ Waiting 3 seconds to see if any errors appear...")
            time.sleep(3)
            print("   🎉 No WNDPROC errors detected!")
            return True
        else:
            print("   ❌ Notification failed to send")
            return False
            
    except Exception as e:
        print(f"   ❌ Test failed: {e}")
        return False

def test_priority_notification():
    """Test priority notification system"""
    print("🚨 Testing Priority Notification...")
    
    try:
        from core.utils.windows_utils import WindowsNotificationManager
        
        notification_manager = WindowsNotificationManager("Agevolami PM")
        
        # Test high priority notification
        result = notification_manager.show_priority_notification(
            "Scadenza Importante",
            "Hai una scadenza che scade tra 3 giorni!",
            priority="high",
            persistent=False
        )
        
        if result:
            print("   ✅ Priority notification sent successfully!")
            return True
        else:
            print("   ❌ Priority notification failed")
            return False
            
    except Exception as e:
        print(f"   ❌ Priority test failed: {e}")
        return False

def test_windows_integration():
    """Test full Windows integration"""
    print("🖥️ Testing Windows Integration...")
    
    try:
        from core.utils.windows_utils import WindowsSystemIntegration
        
        integration = WindowsSystemIntegration("Agevolami PM")
        
        # Test notification through integration
        result = integration.send_notification(
            "🔧 Sistema Integrato",
            "Test dell'integrazione completa Windows",
            priority="normal"
        )
        
        if result:
            print("   ✅ Windows integration notification sent!")
            return True
        else:
            print("   ❌ Windows integration notification failed")
            return False
            
    except Exception as e:
        print(f"   ❌ Integration test failed: {e}")
        return False

def main():
    """Run notification tests"""
    print("🚀 Testing Clean Windows Notifications")
    print("=" * 50)
    
    tests = [
        ("Clean Notifications", test_clean_notifications),
        ("Priority Notifications", test_priority_notification),
        ("Windows Integration", test_windows_integration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        result = test_func()
        results.append((test_name, result))
        
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {status}")
        
        # Small delay between tests
        time.sleep(1)
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 NOTIFICATION TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL NOTIFICATION TESTS PASSED!")
        print("💡 Notifications should now work without WNDPROC errors")
        return True
    else:
        print("⚠️  Some notification tests failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
