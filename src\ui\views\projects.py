#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vista Progetti per Agevolami PM
"""

import flet as ft
from typing import List, Dict, Any, Optional
from datetime import datetime, date
from decimal import Decimal
from uuid import UUID
import threading

from core import get_logger
from core.models import Project, ProjectType, ProjectStatus, Client, DeadlineStatus, ProjectCategory, ProjectSubcategory
from core.utils.project_categorization import (
    get_category_display_name, get_subcategory_display_name, 
    get_project_types_for_category, get_category_and_subcategory,
    CATEGORY_TO_TYPES
)
from core.config import AppConfig

logger = get_logger(__name__)

class ProjectsView:
    """Vista per la gestione dei progetti"""
    
    def __init__(self, app_instance):
        self.app = app_instance
        self.page = app_instance.page
        self.projects_data = []
        self.filtered_projects = []
        self.clients_data = []
        self.search_query = ""
        self.filter_status = None
        self.filter_category = None
        self.filter_type = None
        self.filter_client = None
        self.selected_project = None
        self.show_form = False
        
        # Search debouncing
        self.search_timer = None
        
        # Componenti UI
        self.search_field = None
        
        self._init_components()
    
    def _init_components(self):
        """Inizializza i componenti della vista"""
        # Campo di ricerca
        self.search_field = ft.TextField(
            label="Cerca progetti...",
            prefix_icon=ft.Icons.SEARCH,
            on_change=self._on_search_change,
            expand=True
        )
    
    def _on_search_change(self, e):
        """Gestisce il cambio del testo di ricerca con debouncing"""
        # Cancella il timer precedente se esiste
        if self.search_timer:
            self.search_timer.cancel()
        
        # Imposta la nuova query di ricerca
        self.search_query = e.control.value.lower()
        
        # Se la query è vuota, filtra immediatamente
        if not self.search_query.strip():
            self._filter_projects()
            return
        
        # Altrimenti, crea un nuovo timer per il debouncing (500ms)
        self.search_timer = threading.Timer(0.5, self._filter_projects)
        self.search_timer.start()
    
    def _filter_projects(self):
        """Filtra i progetti in base ai criteri"""
        try:
            filtered = self.projects_data.copy()
            
            # Filtro per testo
            if self.search_query:
                filtered = [
                    p for p in filtered
                    if (self.search_query in p.name.lower() or
                        self.search_query in (p.description or "").lower() or
                        (p.reference_code and self.search_query in p.reference_code.lower()))
                ]
            
            # Filtro per status
            if self.filter_status:
                filtered = [p for p in filtered if getattr(p, 'status', None) == self.filter_status]
            
            # Filtro per categoria
            if self.filter_category:
                filtered = [p for p in filtered if getattr(p, 'category', None) == self.filter_category]
            
            # Filtro per tipo
            if self.filter_type:
                filtered = [p for p in filtered if getattr(p, 'project_type', None) == self.filter_type]
            
            # Filtro per cliente
            if self.filter_client:
                filtered = [p for p in filtered if getattr(p, 'client_id', None) == self.filter_client]
            
            self.filtered_projects = filtered
            self._update_projects_list()
            
        except Exception as e:
            logger.error(f"Errore durante il filtraggio progetti: {e}")
            import traceback
            logger.error(f"Stack trace: {traceback.format_exc()}")
            self.filtered_projects = self.projects_data.copy()
            self._update_projects_list()
    
    def _create_header(self) -> ft.Container:
        """Crea l'header della vista"""
        return ft.Container(
            content=ft.Row([
                # Title and stats in compact layout
                ft.Column([
                    ft.Text(
                        "Gestione Progetti",
                        size=20,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.GREY_800
                    ),
                    ft.Text(
                        f"{len(self.projects_data)} progetti totali",
                        size=11,
                        color=ft.Colors.GREY_500
                    )
                ], spacing=2),

                ft.Container(expand=True),

                # Action button - more compact
                ft.ElevatedButton(
                    text="Nuovo Progetto",
                    icon=ft.Icons.ADD,
                    on_click=lambda _: self._show_project_form(),
                    bgcolor=ft.Colors.BLUE_600,
                    color=ft.Colors.WHITE,
                    height=36
                )
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            padding=ft.padding.only(bottom=12)
        )
    
    def _create_filters_bar(self) -> ft.Container:
        """Crea la barra dei filtri"""
        # Filtri status
        status_chips = []
        statuses = [
            ("Tutti", None, ft.Colors.GREY_600),
            ("In Corso", ProjectStatus.IN_PROGRESS, ft.Colors.GREEN_600),
            ("Sospeso", ProjectStatus.SUSPENDED, ft.Colors.ORANGE_600),
            ("Completato", ProjectStatus.COMPLETED, ft.Colors.BLUE_600),
            ("Annullato", ProjectStatus.CANCELLED, ft.Colors.RED_600)
        ]
        
        for label, status, color in statuses:
            is_selected = self.filter_status == status

            chip = ft.Container(
                content=ft.Text(
                    label,
                    size=10,
                    color=ft.Colors.WHITE if is_selected else color,
                    weight=ft.FontWeight.W_500
                ),
                padding=ft.padding.symmetric(horizontal=8, vertical=4),
                bgcolor=color if is_selected else ft.Colors.TRANSPARENT,
                border_radius=12,
                border=ft.border.all(1, color),
                on_click=lambda _, s=status: self._set_status_filter(s)
            )
            status_chips.append(chip)
        
        # Filtri categoria
        category_chips = []
        categories = [
            ("Tutte", None, ft.Colors.GREY_600),
            ("Incentivi Pubblici", ProjectCategory.INCENTIVI_PUBBLICI, ft.Colors.BLUE_600),
            ("Fiscale & Contabile", ProjectCategory.FISCALE_CONTABILE, ft.Colors.GREEN_600),
            ("Crediti & Finanza", ProjectCategory.CREDITI_FINANZA, ft.Colors.PURPLE_600)
        ]
        
        for label, category, color in categories:
            is_selected = getattr(self, 'filter_category', None) == category

            chip = ft.Container(
                content=ft.Text(
                    label,
                    size=10,
                    color=ft.Colors.WHITE if is_selected else color,
                    weight=ft.FontWeight.W_500
                ),
                padding=ft.padding.symmetric(horizontal=8, vertical=4),
                bgcolor=color if is_selected else ft.Colors.TRANSPARENT,
                border_radius=12,
                border=ft.border.all(1, color),
                on_click=lambda _, c=category: self._set_category_filter(c)
            )
            category_chips.append(chip)
        
        return ft.Container(
            content=ft.Column([
                # Search and refresh in one compact row
                ft.Row([
                    self.search_field,
                    ft.IconButton(
                        icon=ft.Icons.REFRESH,
                        tooltip="Aggiorna",
                        on_click=lambda _: self.refresh_data(),
                        icon_size=18
                    )
                ], spacing=8),

                # Filters in a more compact layout
                ft.Row([
                    # Status filters
                    ft.Text("Stato:", size=11, color=ft.Colors.GREY_600, weight=ft.FontWeight.W_500),
                    *status_chips,

                    # Separator
                    ft.Container(width=1, height=20, bgcolor=ft.Colors.GREY_300),

                    # Category filters
                    ft.Text("Categoria:", size=11, color=ft.Colors.GREY_600, weight=ft.FontWeight.W_500),
                    *category_chips
                ], spacing=6, wrap=True)
            ], spacing=8),
            padding=ft.padding.only(bottom=10)
        )
    
    def _set_status_filter(self, status: Optional[ProjectStatus]):
        """Imposta il filtro per status"""
        self.filter_status = status
        self._filter_projects()
    
    def _set_category_filter(self, category: Optional[ProjectCategory]):
        """Imposta il filtro per categoria"""
        self.filter_category = category
        self._filter_projects()
    
    def _create_projects_list(self) -> ft.Container:
        """Crea la lista dei progetti"""
        if not self.filtered_projects:
            return ft.Container(
                content=ft.Column([
                    ft.Icon(
                        ft.Icons.WORK_OUTLINE,
                        size=48,
                        color=ft.Colors.GREY_400
                    ),
                    ft.Text(
                        "Nessun progetto trovato" if self.search_query or self.filter_status else "Nessun progetto presente",
                        size=16,
                        color=ft.Colors.GREY_500,
                        weight=ft.FontWeight.W_500
                    ),
                    ft.Text(
                        "Prova a modificare i filtri" if self.search_query or self.filter_status else "Crea il primo progetto",
                        size=12,
                        color=ft.Colors.GREY_400
                    )
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=8),
                padding=ft.padding.all(40),
                alignment=ft.alignment.center,
                expand=True
            )
        
        # Crea gli elementi della lista
        project_items = []
        for project in self.filtered_projects:
            project_items.append(self._create_project_item(project))
        
        return ft.Container(
            content=ft.Column(
                controls=project_items,
                spacing=4,
                scroll=ft.ScrollMode.AUTO
            ),
            expand=True
        )
    
    def _create_project_item(self, project: Project) -> ft.Container:
        """Crea un elemento progetto"""
        # Trova il cliente
        client = None
        if project.client_id:
            try:
                client = self.app.db.get_client(project.client_id)
            except:
                pass
        
        # Colori per status
        status_colors = {
            ProjectStatus.IN_PROGRESS: ft.Colors.GREEN_600,
            ProjectStatus.SUSPENDED: ft.Colors.ORANGE_600,
            ProjectStatus.COMPLETED: ft.Colors.BLUE_600,
            ProjectStatus.CANCELLED: ft.Colors.RED_600
        }
        
        status_color = status_colors.get(project.status, ft.Colors.GREY_600)
        
        # Icone per categoria
        category_icons = {
            ProjectCategory.INCENTIVI_PUBBLICI: ft.Icons.LIGHTBULB,
            ProjectCategory.FISCALE_CONTABILE: ft.Icons.RECEIPT,
            ProjectCategory.CREDITI_FINANZA: ft.Icons.ACCOUNT_BALANCE
        }
        
        category_icon = category_icons.get(getattr(project, 'category', None), ft.Icons.WORK)
        
        # Ottieni nomi display per categoria e sottocategoria
        category_name = get_category_display_name(getattr(project, 'category', None)) if hasattr(project, 'category') else "N/A"
        subcategory_name = get_subcategory_display_name(getattr(project, 'subcategory', None)) if hasattr(project, 'subcategory') else "N/A"
        
        # Calcola progresso (placeholder)
        progress = 0.0
        if project.status == ProjectStatus.COMPLETED:
            progress = 1.0
        elif project.status == ProjectStatus.IN_PROGRESS:
            # TODO: Calcolare progresso reale basato su SAL/deadlines
            progress = 0.3
        
        # Calcola scadenze prossime
        deadlines = self.app.db.get_deadlines_by_project(project.id)
        upcoming_deadlines = len([
            d for d in deadlines
            if d.due_date >= date.today() and 
               (d.due_date - date.today()).days <= 15 and
               d.status != DeadlineStatus.COMPLETED
        ])
        
        return ft.Container(
            content=ft.Column([
                # Header del progetto
                ft.Row([
                    # Icona categoria
                    ft.Container(
                        content=ft.Icon(
                            category_icon,
                            size=20,
                            color=ft.Colors.WHITE
                        ),
                        width=40,
                        height=40,
                        bgcolor=ft.Colors.BLUE_600,
                        border_radius=8,
                        alignment=ft.alignment.center
                    ),
                    
                    # Informazioni principali
                    ft.Column([
                        ft.Row([
                            ft.Text(
                                project.name,
                                size=14,
                                weight=ft.FontWeight.W_500,
                                color=ft.Colors.GREY_800,
                                expand=True
                            ),
                            
                            # Badge status
                            ft.Container(
                                content=ft.Text(
                                    project.status.value.replace('_', ' ').title() if hasattr(project.status, 'value') else str(project.status).replace('_', ' ').title(),
                                    size=10,
                                    color=ft.Colors.WHITE,
                                    weight=ft.FontWeight.BOLD
                                ),
                                padding=ft.padding.symmetric(horizontal=8, vertical=4),
                                bgcolor=status_color,
                                border_radius=10
                            )
                        ]),
                        
                        ft.Row([
                            ft.Text(
                                client.name if client else "Cliente non trovato",
                                size=11,
                                color=ft.Colors.GREY_500
                            ),
                            ft.Text(
                                "•",
                                size=11,
                                color=ft.Colors.GREY_300
                            ),
                            ft.Text(
                                f"{category_name} > {subcategory_name}",
                                size=11,
                                color=ft.Colors.BLUE_600,
                                weight=ft.FontWeight.W_500
                            )
                        ], spacing=4),
                        
                        ft.Row([
                            ft.Text(
                                "Tipo:",
                                size=10,
                                color=ft.Colors.GREY_400
                            ),
                            ft.Text(
                                project.project_type.value.replace('_', ' ').title() if hasattr(project.project_type, 'value') else str(project.project_type).replace('_', ' ').title(),
                                size=10,
                                color=ft.Colors.GREY_600
                            )
                        ], spacing=4)
                    ], spacing=4, expand=True),
                    
                    # Azioni
                    ft.Row([
                        ft.IconButton(
                            icon=ft.Icons.VISIBILITY,
                            icon_size=16,
                            tooltip="Visualizza",
                            on_click=lambda _, p=project: self._view_project(p)
                        ),
                        ft.IconButton(
                            icon=ft.Icons.EDIT,
                            icon_size=16,
                            tooltip="Modifica",
                            on_click=lambda _, p=project: self._edit_project(p)
                        ),
                        ft.IconButton(
                            icon=ft.Icons.DELETE,
                            icon_size=16,
                            tooltip="Elimina",
                            on_click=lambda _, p=project: self._delete_project(p)
                        )
                    ], spacing=0)
                ], spacing=12),
                
                # Descrizione
                ft.Text(
                    project.description,
                    size=11,
                    color=ft.Colors.GREY_600,
                    max_lines=2
                ) if project.description else ft.Container(height=0),
                
                # Barra progresso e info
                ft.Row([
                    # Progresso
                    ft.Column([
                        ft.Text(
                            f"Progresso: {int(progress * 100)}%",
                            size=10,
                            color=ft.Colors.GREY_600
                        ),
                        ft.ProgressBar(
                            value=progress,
                            width=150,
                            height=4,
                            bgcolor=ft.Colors.GREY_200,
                            color=status_color
                        )
                    ], spacing=4),
                    
                    ft.Container(expand=True),
                    
                    # Info aggiuntive
                    ft.Column([
                        ft.Row([
                            ft.Icon(
                                ft.Icons.SCHEDULE,
                                size=12,
                                color=ft.Colors.ORANGE_600 if upcoming_deadlines > 0 else ft.Colors.GREY_400
                            ),
                            ft.Text(
                                f"{upcoming_deadlines} scadenze",
                                size=10,
                                color=ft.Colors.ORANGE_600 if upcoming_deadlines > 0 else ft.Colors.GREY_400
                            )
                        ], spacing=4),
                        
                        ft.Row([
                            ft.Icon(
                                ft.Icons.EURO,
                                size=12,
                                color=ft.Colors.GREEN_600
                            ),
                            ft.Text(
                                f"€{project.budget:,.0f}" if project.budget else "N/A",
                                size=10,
                                color=ft.Colors.GREEN_600
                            )
                        ], spacing=4)
                    ], spacing=2, horizontal_alignment=ft.CrossAxisAlignment.END)
                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN)
            ], spacing=6),
            padding=ft.padding.all(12),
            margin=ft.margin.symmetric(vertical=1),
            bgcolor=ft.Colors.WHITE,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.GREY_200),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=2,
                color=ft.Colors.BLACK12
            )
        )
    
    def _show_project_form(self, project: Optional[Project] = None):
        """Mostra il form per creare/modificare un progetto"""
        self.selected_project = project
        self.show_form = True
        self.refresh()
        logger.info(f"Mostra form progetto: {'modifica' if project else 'nuovo'}")
    
    def _view_project(self, project: Project):
        """Visualizza i dettagli di un progetto"""
        try:
            # Use the proper navigation system to show detail view
            self.app.main_layout.navigate_to_detail("project_detail", str(project.id))

            logger.info(f"Visualizza dettagli progetto: {project.name}")

        except Exception as e:
            logger.error(f"Errore apertura dettagli progetto: {e}")
            # Mostra dialog di errore
            error_dialog = ft.AlertDialog(
                modal=True,
                title=ft.Text("Errore"),
                content=ft.Text(f"Impossibile aprire i dettagli del progetto: {str(e)}"),
                actions=[
                    ft.TextButton("OK", on_click=lambda _: self.page.close(error_dialog))
                ],
                actions_alignment=ft.MainAxisAlignment.END,
            )
            self.page.open(error_dialog)

    def show_project_detail(self, project_id: str):
        """Mostra i dettagli di un progetto specifico - chiamato dal sistema di navigazione"""
        try:
            from .project_detail import ProjectDetailView

            # Crea la vista dettaglio
            detail_view = ProjectDetailView(self.app, UUID(project_id))

            # Costruisci il contenuto
            detail_content = detail_view.build()

            # Aggiorna il content area del layout principale direttamente
            self.app.main_layout.content_area.content = detail_content

            # Forza l'aggiornamento della pagina
            if hasattr(self.app.main_layout, 'page') and self.app.main_layout.page:
                self.app.main_layout.page.update()

            logger.info(f"Dettagli progetto caricati: {project_id}")

        except Exception as e:
            logger.error(f"Errore caricamento dettagli progetto: {e}")
            import traceback
            logger.error(f"Stack trace: {traceback.format_exc()}")

            # Mostra messaggio di errore
            error_content = ft.Container(
                content=ft.Column([
                    ft.Icon(ft.Icons.ERROR, color=ft.Colors.RED, size=48),
                    ft.Text(f"Errore caricamento dettagli progetto: {str(e)}",
                           color=ft.Colors.RED, text_align=ft.TextAlign.CENTER),
                    ft.ElevatedButton(
                        text="Torna ai Progetti",
                        on_click=lambda _: self.app.main_layout._navigate_to("projects"),
                        bgcolor=ft.Colors.BLUE_600,
                        color=ft.Colors.WHITE
                    )
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=16),
                padding=40
            )

            self.app.main_layout.content_area.content = error_content

            if hasattr(self.app.main_layout, 'page') and self.app.main_layout.page:
                self.app.main_layout.page.update()
    
    def _edit_project(self, project: Project):
        """Modifica un progetto"""
        self._show_project_form(project)
    
    def _delete_project(self, project: Project):
        """Elimina un progetto"""
        def confirm_delete(e):
            try:
                success = self.app.db.delete_project(project.id)
                self.page.close(confirm_dialog)
                
                if success:
                    logger.info(f"Progetto eliminato: {project.name}")
                    self.refresh_data()
                    
                    # Dialog di successo
                    success_dialog = ft.AlertDialog(
                        modal=True,
                        title=ft.Text("Successo"),
                        content=ft.Text(f"Progetto '{project.name}' eliminato con successo!"),
                        actions=[
                            ft.TextButton("OK", on_click=lambda _: self._handle_delete_success_dialog_close(success_dialog))
                        ],
                        actions_alignment=ft.MainAxisAlignment.END,
                    )
                    self.page.open(success_dialog)
                else:
                    # Dialog di errore
                    error_dialog = ft.AlertDialog(
                        modal=True,
                        title=ft.Text("Errore"),
                        content=ft.Text("Impossibile eliminare il progetto. Riprova."),
                        actions=[
                            ft.TextButton("OK", on_click=lambda _: self.page.close(error_dialog))
                        ],
                        actions_alignment=ft.MainAxisAlignment.END,
                    )
                    self.page.open(error_dialog)
            except Exception as ex:
                self.page.close(confirm_dialog)
                logger.error(f"Errore eliminazione progetto: {ex}")
                
                error_dialog = ft.AlertDialog(
                    modal=True,
                    title=ft.Text("Errore"),
                    content=ft.Text(f"Errore imprevisto: {str(ex)}"),
                    actions=[
                        ft.TextButton("OK", on_click=lambda _: self.page.close(error_dialog))
                    ],
                    actions_alignment=ft.MainAxisAlignment.END,
                )
                self.page.open(error_dialog)
        
        def cancel_delete(e):
            self.page.close(confirm_dialog)
        
        # Dialog di conferma
        confirm_dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text("Conferma eliminazione"),
            content=ft.Text(f"Sei sicuro di voler eliminare il progetto '{project.name}'?\n\nQuesta azione non può essere annullata."),
            actions=[
                ft.TextButton("Annulla", on_click=cancel_delete),
                ft.TextButton(
                    "Elimina",
                    on_click=confirm_delete,
                    style=ft.ButtonStyle(color=ft.Colors.RED)
                )
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        self.page.open(confirm_dialog)
        logger.info(f"Richiesta eliminazione progetto: {project.name}")
    
    def _update_projects_list(self):
        """Aggiorna la lista dei progetti (placeholder per rebuild)"""
        # Questo metodo sarà chiamato quando serve ricostruire la UI
        self.app.main_layout.content_area.content = self.build()
        self.page.update()
    
    def _handle_delete_success_dialog_close(self, dialog):
        """Gestisce la chiusura del dialog di successo eliminazione"""
        self.page.close(dialog)
        self._update_projects_list()
    
    def build(self) -> ft.Container:
        """Costruisce la vista progetti"""
        if self.show_form:
            return self._build_project_form()
        
        return ft.Container(
            content=ft.Column([
                # Header
                self._create_header(),

                # Filtri
                self._create_filters_bar(),

                # Lista progetti
                self._create_projects_list()
            ], spacing=0),
            padding=ft.padding.symmetric(horizontal=16, vertical=12),
            expand=True
        )
    
    def _build_project_form(self) -> ft.Container:
        """Costruisce il form per progetto con UI moderna"""
        is_edit = self.selected_project is not None
        title = "Modifica Progetto" if is_edit else "Nuovo Progetto"

        # Stile comune per i campi
        field_style = {
            "bgcolor": ft.Colors.WHITE,
            "border_color": ft.Colors.GREY_300,
            "border_radius": 8,
            "content_padding": ft.padding.symmetric(horizontal=12, vertical=8),
            "text_style": ft.TextStyle(size=14)
        }

        # Dropdown clienti
        client_options = [ft.dropdown.Option(text=c.name, key=str(c.id)) for c in self.clients_data] if self.clients_data else []
        if not client_options:
            client_options = [ft.dropdown.Option(text="Nessun cliente disponibile", key="")]

        client_dropdown = ft.Dropdown(
            label="👤 Cliente *",
            options=client_options,
            value=str(self.selected_project.client_id) if is_edit and self.selected_project.client_id else None,
            expand=True,
            disabled=len(self.clients_data) == 0,
            **field_style
        )

        # Dropdown categoria con icone
        category_options = []
        category_icons = {
            "incentivi_pubblici": "🏛️",
            "fiscale_contabile": "📊",
            "crediti_finanza": "💰"
        }

        for c in ProjectCategory:
            icon = category_icons.get(c.value, "📋")
            category_options.append(
                ft.dropdown.Option(
                    text=f"{icon} {get_category_display_name(c)}",
                    key=c.value
                )
            )

        category_dropdown = ft.Dropdown(
            label="📂 Categoria *",
            options=category_options,
            value=self.selected_project.category.value if is_edit and hasattr(self.selected_project, 'category') and hasattr(self.selected_project.category, 'value') else (str(self.selected_project.category) if is_edit and hasattr(self.selected_project, 'category') else None),
            expand=True,
            on_change=self._on_category_change,
            **field_style
        )

        # Dropdown sottocategoria con icone
        subcategory_options = []
        subcategory_icons = {
            "ricerca_sviluppo": "🔬",
            "innovazione_digitale": "💻",
            "sostenibilita": "🌱",
            "internazionalizzazione": "🌍"
        }

        if is_edit and hasattr(self.selected_project, 'category'):
            for s in ProjectSubcategory:
                if (self.selected_project.category, s) in CATEGORY_TO_TYPES:
                    icon = subcategory_icons.get(s.value, "📌")
                    subcategory_options.append(
                        ft.dropdown.Option(
                            text=f"{icon} {get_subcategory_display_name(s)}",
                            key=s.value
                        )
                    )
        else:
            # Per nuovi progetti, mostra tutte le sottocategorie
            for s in ProjectSubcategory:
                icon = subcategory_icons.get(s.value, "📌")
                subcategory_options.append(
                    ft.dropdown.Option(
                        text=f"{icon} {get_subcategory_display_name(s)}",
                        key=s.value
                    )
                )

        subcategory_dropdown = ft.Dropdown(
            label="📌 Sottocategoria *",
            options=subcategory_options,
            value=self.selected_project.subcategory.value if is_edit and hasattr(self.selected_project, 'subcategory') and hasattr(self.selected_project.subcategory, 'value') else (str(self.selected_project.subcategory) if is_edit and hasattr(self.selected_project, 'subcategory') else None),
            expand=True,
            on_change=self._on_subcategory_change,
            **field_style
        )

        # Dropdown tipo progetto con icone
        type_options = []
        type_icons = {
            "accordi_innovazione": "🤝",
            "investimenti_sostenibili_40": "🏭",
            "contratti_sviluppo": "📋",
            "fondo_crescita_sostenibile": "🌱"
        }

        if is_edit and hasattr(self.selected_project, 'category') and hasattr(self.selected_project, 'subcategory'):
            available_types = get_project_types_for_category(self.selected_project.category, self.selected_project.subcategory)
            for t in available_types:
                icon = type_icons.get(t.value, "🎯")
                type_options.append(
                    ft.dropdown.Option(
                        text=f"{icon} {t.value.replace('_', ' ').title()}",
                        key=t.value
                    )
                )
        else:
            # Per nuovi progetti, mostra tutti i tipi di progetto
            from core.models import ProjectType
            for t in ProjectType:
                icon = type_icons.get(t.value, "🎯")
                type_options.append(
                    ft.dropdown.Option(
                        text=f"{icon} {t.value.replace('_', ' ').title()}",
                        key=t.value
                    )
                )

        type_dropdown = ft.Dropdown(
            label="🎯 Tipo Progetto *",
            options=type_options,
            value=self.selected_project.project_type.value if is_edit and hasattr(self.selected_project, 'project_type') and hasattr(self.selected_project.project_type, 'value') else (str(self.selected_project.project_type) if is_edit and hasattr(self.selected_project, 'project_type') else None),
            expand=True,
            **field_style
        )

        # Campi del form con stile migliorato
        name_field = ft.TextField(
            label="🏗️ Nome Progetto *",
            value=self.selected_project.name if is_edit else "",
            expand=True,
            **field_style
        )

        description_field = ft.TextField(
            label="📝 Descrizione",
            value=self.selected_project.description if is_edit else "",
            multiline=True,
            min_lines=3,
            max_lines=5,
            **field_style
        )

        reference_field = ft.TextField(
            label="🏷️ Codice Riferimento",
            value=self.selected_project.reference_code if is_edit else "",
            expand=True,
            **field_style
        )

        budget_field = ft.TextField(
            label="💰 Budget (€)",
            value=str(self.selected_project.budget) if is_edit and self.selected_project.budget else "",
            expand=True,
            **field_style
        )

        start_date_field = ft.TextField(
            label="📅 Data Inizio (DD/MM/YYYY)",
            value=self.selected_project.start_date.strftime("%d/%m/%Y") if is_edit and self.selected_project.start_date else "",
            expand=True,
            **field_style
        )

        end_date_field = ft.TextField(
            label="🏁 Data Fine (DD/MM/YYYY)",
            value=self.selected_project.end_date.strftime("%d/%m/%Y") if is_edit and self.selected_project.end_date else "",
            expand=True,
            **field_style
        )

        # Checkbox per abilitare SAL
        requires_sal_checkbox = ft.Checkbox(
            label="📊 Questo progetto richiede SAL (Stato Avanzamento Lavori)",
            value=self.selected_project.requires_sal if is_edit else False,
            tooltip="Abilita questa opzione se il progetto richiede il tracciamento dell'avanzamento tramite SAL"
        )

        return ft.Container(
            content=ft.Column([
                # Modern Header with gradient background
                ft.Container(
                    content=ft.Row([
                        ft.IconButton(
                            icon=ft.Icons.ARROW_BACK_IOS,
                            on_click=lambda _: self._close_form(),
                            icon_color=ft.Colors.WHITE,
                            bgcolor=ft.Colors.with_opacity(0.2, ft.Colors.WHITE),
                            style=ft.ButtonStyle(
                                shape=ft.CircleBorder()
                            )
                        ),
                        ft.Container(width=12),
                        ft.Column([
                            ft.Text(
                                title,
                                size=26,
                                weight=ft.FontWeight.BOLD,
                                color=ft.Colors.WHITE
                            ),
                            ft.Text(
                                "Configura i dettagli del progetto e le categorie",
                                size=14,
                                color=ft.Colors.with_opacity(0.9, ft.Colors.WHITE)
                            )
                        ], spacing=4),
                        ft.Container(expand=True)
                    ]),
                    padding=ft.padding.all(24),
                    gradient=ft.LinearGradient(
                        colors=[ft.Colors.PURPLE_600, ft.Colors.PURPLE_800],
                        begin=ft.alignment.top_left,
                        end=ft.alignment.bottom_right
                    ),
                    border_radius=ft.border_radius.only(top_left=16, top_right=16),
                    margin=ft.margin.only(left=-24, right=-24, top=-24)
                ),

                # Scrollable form content with modern card-based layout
                ft.Container(
                    content=ft.ListView(
                        controls=[
                            # Basic Information Section
                            ft.Container(
                                content=ft.Column([
                                    ft.Row([
                                        ft.Icon(ft.Icons.INFO_OUTLINE, color=ft.Colors.PURPLE_600, size=20),
                                        ft.Text(
                                            "Informazioni Base",
                                            size=18,
                                            weight=ft.FontWeight.W_600,
                                            color=ft.Colors.GREY_800
                                        )
                                    ], spacing=8),
                                    ft.Container(height=16),
                                    name_field,
                                    ft.Container(height=12),
                                    ft.Row([
                                        client_dropdown,
                                        category_dropdown
                                    ], spacing=16),
                                    ft.Container(height=12),
                                    ft.Row([
                                        subcategory_dropdown,
                                        type_dropdown
                                    ], spacing=16),
                                    ft.Container(height=12),
                                    description_field
                                ]),
                                bgcolor=ft.Colors.WHITE,
                                border=ft.border.all(1, ft.Colors.GREY_200),
                                border_radius=16,
                                padding=24,
                                margin=ft.margin.only(bottom=16),
                                shadow=ft.BoxShadow(
                                    spread_radius=0,
                                    blur_radius=4,
                                    color=ft.Colors.with_opacity(0.05, ft.Colors.BLACK),
                                    offset=ft.Offset(0, 2)
                                )
                            ),

                            # Project Details Section
                            ft.Container(
                                content=ft.Column([
                                    ft.Row([
                                        ft.Icon(ft.Icons.SETTINGS, color=ft.Colors.BLUE_600, size=20),
                                        ft.Text(
                                            "Dettagli Progetto",
                                            size=18,
                                            weight=ft.FontWeight.W_600,
                                            color=ft.Colors.GREY_800
                                        )
                                    ], spacing=8),
                                    ft.Container(height=16),
                                    ft.Row([
                                        reference_field,
                                        budget_field
                                    ], spacing=16),
                                    ft.Container(height=12),
                                    ft.Row([
                                        start_date_field,
                                        end_date_field
                                    ], spacing=16),
                                    ft.Container(height=12),
                                    requires_sal_checkbox,
                                    ft.Container(height=8),
                                    ft.Text(
                                        "💡 Le date sono opzionali e possono essere aggiunte successivamente",
                                        size=12,
                                        color=ft.Colors.GREY_600,
                                        italic=True
                                    )
                                ]),
                                bgcolor=ft.Colors.WHITE,
                                border=ft.border.all(1, ft.Colors.GREY_200),
                                border_radius=16,
                                padding=24,
                                margin=ft.margin.only(bottom=24),
                                shadow=ft.BoxShadow(
                                    spread_radius=0,
                                    blur_radius=4,
                                    color=ft.Colors.with_opacity(0.05, ft.Colors.BLACK),
                                    offset=ft.Offset(0, 2)
                                )
                            ),

                            # Action Buttons with modern styling
                            ft.Container(
                                content=ft.Row([
                                    ft.OutlinedButton(
                                        content=ft.Row([
                                            ft.Icon(ft.Icons.CLOSE, size=18),
                                            ft.Text("Annulla", size=14, weight=ft.FontWeight.W_500)
                                        ], spacing=8, alignment=ft.MainAxisAlignment.CENTER),
                                        on_click=lambda _: self._close_form(),
                                        style=ft.ButtonStyle(
                                            color=ft.Colors.GREY_700,
                                            side=ft.BorderSide(2, ft.Colors.GREY_300),
                                            padding=ft.padding.symmetric(horizontal=32, vertical=16),
                                            shape=ft.RoundedRectangleBorder(radius=12)
                                        ),
                                        height=56,
                                        width=140
                                    ),
                                    ft.Container(expand=True),
                                    ft.ElevatedButton(
                                        content=ft.Row([
                                            ft.Icon(
                                                ft.Icons.SAVE if not is_edit else ft.Icons.UPDATE,
                                                size=18,
                                                color=ft.Colors.WHITE
                                            ),
                                            ft.Text(
                                                "Salva Progetto" if not is_edit else "Aggiorna Progetto",
                                                size=14,
                                                weight=ft.FontWeight.W_600,
                                                color=ft.Colors.WHITE
                                            )
                                        ], spacing=8, alignment=ft.MainAxisAlignment.CENTER),
                                        on_click=lambda _: self._save_project({
                                            'name': name_field.value,
                                            'client_id': client_dropdown.value,
                                            'category': category_dropdown.value,
                                            'subcategory': subcategory_dropdown.value,
                                            'project_type': type_dropdown.value,
                                            'description': description_field.value,
                                            'reference_code': reference_field.value,
                                            'budget': budget_field.value,
                                            'start_date': start_date_field.value,
                                            'end_date': end_date_field.value,
                                            'requires_sal': requires_sal_checkbox.value
                                        }),
                                        style=ft.ButtonStyle(
                                            bgcolor=ft.Colors.PURPLE_600,
                                            color=ft.Colors.WHITE,
                                            padding=ft.padding.symmetric(horizontal=32, vertical=16),
                                            elevation=4,
                                            shadow_color=ft.Colors.PURPLE_200,
                                            shape=ft.RoundedRectangleBorder(radius=12)
                                        ),
                                        height=56,
                                        width=180
                                    )
                                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                                padding=ft.padding.all(24),
                                bgcolor=ft.Colors.WHITE,
                                border=ft.border.only(top=ft.BorderSide(1, ft.Colors.GREY_200)),
                                margin=ft.margin.only(left=-24, right=-24, bottom=-24)
                            )
                        ],
                        spacing=0,
                        padding=ft.padding.all(24),
                        auto_scroll=False
                    ),
                    expand=True
                )
            ]),
            bgcolor=ft.Colors.GREY_50,
            border_radius=16,
            border=ft.border.all(1, ft.Colors.GREY_200),
            padding=24,
            shadow=ft.BoxShadow(
                spread_radius=2,
                blur_radius=20,
                color=ft.Colors.with_opacity(0.15, ft.Colors.BLACK),
                offset=ft.Offset(0, 8)
            )
        )
    
    def _close_form(self):
        """Chiude il form"""
        self.show_form = False
        self.selected_project = None
        self.refresh()

    def cancel_current_form(self):
        """Cancel current form - called by keyboard shortcuts"""
        if self.show_form:
            self._close_form()
            logger.info("Project form cancelled via keyboard shortcut")

    def save_current_form(self):
        """Save current form - called by keyboard shortcuts"""
        if self.show_form:
            logger.info("Save shortcut triggered in project form")
            if hasattr(self, 'app') and hasattr(self.app, 'main_layout'):
                self.app.main_layout._show_shortcut_feedback("Use the Save button in the form or focus on a field and press Ctrl+S")
    
    def _on_category_change(self, e):
        """Gestisce il cambio di categoria"""
        if not e.control.value:
            return
            
        # Trova i controlli nel form
        form_container = None
        for control in self.page.controls:
            if hasattr(control, 'content') and hasattr(control.content, 'controls'):
                for container in control.content.controls:
                    if hasattr(container, 'content') and hasattr(container.content, 'controls'):
                        for form_ctrl in container.content.controls:
                            if hasattr(form_ctrl, 'content') and hasattr(form_ctrl.content, 'controls'):
                                form_container = form_ctrl.content
                                break
        
        if not form_container:
            return
            
        # Trova i dropdown
        subcategory_dropdown = None
        type_dropdown = None
        
        for row in form_container.controls:
            if hasattr(row, 'controls'):
                for ctrl in row.controls:
                    if hasattr(ctrl, 'label'):
                        if ctrl.label == "Sottocategoria *":
                            subcategory_dropdown = ctrl
                        elif ctrl.label == "Tipo Progetto *":
                            type_dropdown = ctrl
        
        if subcategory_dropdown:
            # Aggiorna le opzioni della sottocategoria
            category = ProjectCategory(e.control.value)
            subcategory_options = [ft.dropdown.Option(text=get_subcategory_display_name(s), key=s.value) 
                                 for s in ProjectSubcategory 
                                 if (category, s) in CATEGORY_TO_TYPES]
            subcategory_dropdown.options = subcategory_options
            subcategory_dropdown.value = None
            
        if type_dropdown:
            # Resetta il tipo progetto
            type_dropdown.options = []
            type_dropdown.value = None
            
        self.page.update()
    
    def _on_subcategory_change(self, e):
        """Gestisce il cambio di sottocategoria"""
        if not e.control.value:
            return
            
        # Trova i controlli nel form
        form_container = None
        for control in self.page.controls:
            if hasattr(control, 'content') and hasattr(control.content, 'controls'):
                for container in control.content.controls:
                    if hasattr(container, 'content') and hasattr(container.content, 'controls'):
                        for form_ctrl in container.content.controls:
                            if hasattr(form_ctrl, 'content') and hasattr(form_ctrl.content, 'controls'):
                                form_container = form_ctrl.content
                                break
        
        if not form_container:
            return
            
        # Trova i dropdown
        category_dropdown = None
        type_dropdown = None
        
        for row in form_container.controls:
            if hasattr(row, 'controls'):
                for ctrl in row.controls:
                    if hasattr(ctrl, 'label'):
                        if ctrl.label == "Categoria *":
                            category_dropdown = ctrl
                        elif ctrl.label == "Tipo Progetto *":
                            type_dropdown = ctrl
        
        if category_dropdown and type_dropdown and category_dropdown.value:
            # Aggiorna le opzioni del tipo progetto
            category = ProjectCategory(category_dropdown.value)
            subcategory = ProjectSubcategory(e.control.value)
            available_types = get_project_types_for_category(category, subcategory)
            type_options = [ft.dropdown.Option(text=t.value.replace('_', ' ').title(), key=t.value) for t in available_types]
            type_dropdown.options = type_options
            type_dropdown.value = None
            
        self.page.update()
    
    def _save_project(self, data: Dict[str, str]):
        """Salva il progetto"""
        try:
            # Validazione base
            if not data['name'] or not data['client_id'] or not data['project_type'] or not data.get('category') or not data.get('subcategory'):
                logger.error("Nome, cliente, categoria, sottocategoria e tipo progetto sono obbligatori")
                return
            
            # Parsing date
            start_date = None
            end_date = None
            
            if data['start_date']:
                try:
                    start_date = datetime.strptime(data['start_date'], "%d/%m/%Y").date()
                except ValueError:
                    logger.error("Formato data inizio non valido")
                    return
            
            if data['end_date']:
                try:
                    end_date = datetime.strptime(data['end_date'], "%d/%m/%Y").date()
                except ValueError:
                    logger.error("Formato data fine non valido")
                    return
            
            # Parsing budget
            budget = None
            if data['budget']:
                try:
                    budget = Decimal(data['budget'].replace(',', '.'))
                except ValueError:
                    logger.error("Formato budget non valido")
                    return
            
            if self.selected_project:
                # Modifica progetto esistente
                self.selected_project.name = data['name']
                self.selected_project.client_id = data['client_id']
                self.selected_project.project_type = ProjectType(data['project_type'])
                self.selected_project.category = ProjectCategory(data['category'])
                self.selected_project.subcategory = ProjectSubcategory(data['subcategory'])
                self.selected_project.description = data['description']
                self.selected_project.reference_code = data['reference_code']
                self.selected_project.budget = budget
                self.selected_project.start_date = start_date
                self.selected_project.end_date = end_date
                self.selected_project.requires_sal = data.get('requires_sal', False)
                self.selected_project.updated_at = datetime.now()
                
                success = self.app.db.update_project(self.selected_project)
                action = "modificato"
            else:
                # Crea nuovo progetto
                new_project = Project(
                    name=data['name'],
                    client_id=data['client_id'],
                    project_type=ProjectType(data['project_type']),
                    category=ProjectCategory(data['category']),
                    subcategory=ProjectSubcategory(data['subcategory']),
                    description=data['description'],
                    reference_code=data['reference_code'],
                    budget=budget,
                    start_date=start_date,
                    end_date=end_date,
                    requires_sal=data.get('requires_sal', False),
                    status=ProjectStatus.IN_PROGRESS
                )
                
                success = self.app.db.create_project(new_project)
                action = "creato"
            
            if success:
                logger.info(f"Progetto {action} con successo")
                self._close_form()
                self.refresh_data()
                
                # Mostra dialog di successo
                success_dialog = ft.AlertDialog(
                    modal=True,
                    title=ft.Text("Successo"),
                    content=ft.Text(f"Progetto {action} con successo!"),
                    actions=[
                        ft.TextButton("OK", on_click=lambda _: self.page.close(success_dialog))
                    ],
                    actions_alignment=ft.MainAxisAlignment.END,
                )
                self.page.open(success_dialog)
            else:
                logger.error(f"Errore durante il salvataggio del progetto")
                
                # Mostra dialog di errore
                error_dialog = ft.AlertDialog(
                    modal=True,
                    title=ft.Text("Errore"),
                    content=ft.Text("Errore durante il salvataggio del progetto. Riprova."),
                    actions=[
                        ft.TextButton("OK", on_click=lambda _: self.page.close(error_dialog))
                    ],
                    actions_alignment=ft.MainAxisAlignment.END,
                )
                self.page.open(error_dialog)
                
        except Exception as e:
            logger.error(f"Errore salvataggio progetto: {e}")
            
            # Mostra dialog di errore per eccezioni
            error_dialog = ft.AlertDialog(
                modal=True,
                title=ft.Text("Errore"),
                content=ft.Text(f"Errore imprevisto: {str(e)}"),
                actions=[
                    ft.TextButton("OK", on_click=lambda _: self.page.close(error_dialog))
                ],
                actions_alignment=ft.MainAxisAlignment.END,
            )
            self.page.open(error_dialog)
    
    def refresh_data(self):
        """Aggiorna i dati dei progetti"""
        try:
            logger.info("Inizio caricamento dati progetti...")
            self.projects_data = self.app.db.get_all_projects()
            logger.info(f"Caricati {len(self.projects_data)} progetti")
            
            self.clients_data = self.app.db.get_all_clients()
            logger.info(f"Caricati {len(self.clients_data)} clienti")
            
            self._filter_projects()
            logger.info("Filtri applicati con successo")
            
        except Exception as e:
            logger.error(f"Errore caricamento progetti: {e}")
            import traceback
            logger.error(f"Stack trace: {traceback.format_exc()}")
            self.projects_data = []
            self.clients_data = []
    
    def refresh(self):
        """Aggiorna la vista progetti"""
        self.refresh_data()
    
    def cleanup(self):
        """Pulisce le risorse quando la vista viene distrutta"""
        if self.search_timer:
            self.search_timer.cancel()
            self.search_timer = None