#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to verify Google Calendar improvements for deadlines
"""

import sys
import os
from pathlib import Path

# Add src directory to path
src_dir = Path(__file__).parent / "src"
sys.path.insert(0, str(src_dir))

from core.services.google_calendar_service import GoogleCalendarService
from core import get_logger

logger = get_logger(__name__)

def test_google_calendar_authentication():
    """Test Google Calendar authentication status"""
    print("🔍 Testing Google Calendar Authentication Status...")
    
    try:
        # Initialize Google Calendar service with proper config
        from core.config.app_config import AppConfig
        config = AppConfig()
        config_dir = os.path.join(config.data_dir, "config")
        service = GoogleCalendarService(config_dir=config_dir)
        
        # Test basic functionality
        is_enabled = service.is_enabled()
        
        print("\n📊 Google Calendar Status:")
        print(f"  • Service Enabled: {is_enabled}")
        
        if is_enabled:
            print(f"  • Calendar ID: {service.calendar_id}")
            
            # Test connection
            try:
                # Try to list calendars to test connection
                calendars = service.service.calendarList().list().execute()
                calendar_count = len(calendars.get('items', []))
                print(f"  • Connection Test: ✅ Success")
                print(f"  • Available Calendars: {calendar_count}")
                
                # Find Agevolami calendar
                agevolami_calendar = None
                for cal in calendars.get('items', []):
                    if 'Agevolami' in cal.get('summary', ''):
                        agevolami_calendar = cal
                        break
                
                if agevolami_calendar:
                    print(f"  • Agevolami Calendar: ✅ Found - {agevolami_calendar['summary']}")
                else:
                    print(f"  • Agevolami Calendar: ⚠️ Not found")
                
            except Exception as e:
                print(f"  • Connection Test: ❌ Failed - {e}")
                return False
        else:
            print("  • Service not enabled")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing Google Calendar: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_settings_integration():
    """Test settings integration for Google Calendar"""
    print("\n🔍 Testing Settings Integration...")
    
    try:
        # This would normally be done by the app, but we'll simulate it
        from ui.views.settings import SettingsView
        from core.config.app_config import AppConfig
        
        # Create mock app instance
        class MockApp:
            def __init__(self):
                self.config = AppConfig()
        
        mock_app = MockApp()
        settings_view = SettingsView(mock_app)
        
        # Load Google services settings
        settings_view._load_google_services_settings()
        
        # Check the settings
        google_settings = settings_view.google_services_settings
        
        print("\n📊 Settings Status:")
        print(f"  • Calendar Enabled: {google_settings.get('calendar_enabled', False)}")
        print(f"  • Calendar Authenticated: {google_settings.get('calendar_authenticated', False)}")
        print(f"  • Calendar Auto Sync: {google_settings.get('calendar_auto_sync', True)}")
        print(f"  • Calendar Delete Completed: {google_settings.get('calendar_delete_completed', True)}")
        
        return google_settings.get('calendar_authenticated', False)
        
    except Exception as e:
        print(f"❌ Error testing settings integration: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_deadlines_view_integration():
    """Test deadlines view integration"""
    print("\n🔍 Testing Deadlines View Integration...")
    
    try:
        from ui.views.deadlines import DeadlinesView
        from core.config.app_config import AppConfig
        from core.database.database import DatabaseManager
        
        # Create mock app instance
        class MockApp:
            def __init__(self):
                self.config = AppConfig()
                self.db_manager = DatabaseManager(self.config)
                self.settings_view = None  # Will be created by deadlines view
        
        mock_app = MockApp()
        deadlines_view = DeadlinesView(mock_app)
        
        # Test Google Calendar integration methods
        is_enabled = deadlines_view._is_google_calendar_enabled()
        should_auto_sync = deadlines_view._should_auto_sync()
        should_delete_completed = deadlines_view._should_delete_completed()
        
        print("\n📊 Deadlines View Status:")
        print(f"  • Google Calendar Enabled: {is_enabled}")
        print(f"  • Should Auto Sync: {should_auto_sync}")
        print(f"  • Should Delete Completed: {should_delete_completed}")
        
        # Test settings access
        settings = deadlines_view.google_calendar_settings
        print(f"  • Settings Access: ✅ Working")
        print(f"  • Calendar Auto Sync Setting: {settings.get('calendar_auto_sync', True)}")
        print(f"  • Calendar Delete Completed Setting: {settings.get('calendar_delete_completed', True)}")
        
        return is_enabled
        
    except Exception as e:
        print(f"❌ Error testing deadlines view integration: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🚀 Google Calendar Improvements Test")
    print("=" * 50)
    
    # Test 1: Google Calendar Authentication
    auth_status = test_google_calendar_authentication()
    
    # Test 2: Settings Integration
    settings_status = test_settings_integration()
    
    # Test 3: Deadlines View Integration
    deadlines_status = test_deadlines_view_integration()
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 Test Summary:")
    print(f"  • Google Calendar Authentication: {'✅ Working' if auth_status else '❌ Not Working'}")
    print(f"  • Settings Integration: {'✅ Working' if settings_status else '❌ Not Working'}")
    print(f"  • Deadlines View Integration: {'✅ Working' if deadlines_status else '❌ Not Working'}")
    
    if auth_status and settings_status and deadlines_status:
        print("\n🎉 All tests passed! Google Calendar integration should work properly.")
        print("\n💡 New Features Available:")
        print("   1. ✅ Smooth CRUD operations with auto-sync")
        print("   2. ✅ Automatic deletion from calendar when deadline completed")
        print("   3. ✅ Enhanced user feedback for all operations")
        print("   4. ✅ Manual sync button in deadlines view")
        print("   5. ✅ Robust error handling and status checking")
        print("\n🎯 To test completion behavior:")
        print("   1. Create a deadline in the app")
        print("   2. Mark it as completed")
        print("   3. Check if it's removed from Google Calendar (if setting enabled)")
    else:
        print("\n⚠️  Some tests failed. Check the issues above.")
        if not auth_status:
            print("   • Google Calendar needs to be authenticated in Settings")
        if not settings_status:
            print("   • Settings integration needs to be fixed")
        if not deadlines_status:
            print("   • Deadlines view integration needs to be fixed")

if __name__ == "__main__":
    main()
