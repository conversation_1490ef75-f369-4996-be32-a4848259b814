#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web Scraping Service for Italian Financial Incentives
Monitors MIMIT, Invitalia, and SIMEST websites for new incentives
"""

import requests
from bs4 import BeautifulSoup
import time
import hashlib
from typing import List, Dict, Any, Optional, Tu<PERSON>
from datetime import datetime, date
import re
from urllib.parse import urljoin, urlparse
import feedparser
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from core.utils.logger import get_logger
from core.models.incentive_models import IncentiveItem, IncentiveSource, IncentiveStatus, IncentivePriority

logger = get_logger(__name__)

class WebScrapingService:
    """Service for scraping Italian financial incentives websites"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.session = requests.Session()

        # Configure retry strategy
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS"]
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)

        self.session.headers.update({
            'User-Agent': config.get('user_agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'it-IT,it;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        self.request_delay = config.get('request_delay_seconds', 2.0)
        self.timeout = config.get('timeout_seconds', 15)

        # Store websites configuration for dynamic URL handling
        self.websites_config = config.get('websites', [])
        
    def scrape_all_sources(self, keywords: List[str]) -> List[IncentiveItem]:
        """Scrape all configured sources for incentives"""
        all_items = []
        sources = self.config.get('sources', [])

        # Scrape the configured enum sources (MIMIT, INVITALIA, SIMEST)
        for source in sources:
            try:
                logger.info(f"Scraping {source.value}...")
                items = self._scrape_source(source, keywords)
                all_items.extend(items)
                logger.info(f"Found {len(items)} items from {source.value}")

                # Delay between sources
                time.sleep(self.request_delay)

            except Exception as e:
                logger.error(f"Error scraping {source.value}: {e}")

        # Also scrape custom websites from configuration
        try:
            logger.info("Scraping custom websites...")
            custom_items = self._scrape_custom_websites(keywords)
            all_items.extend(custom_items)
            logger.info(f"Found {len(custom_items)} items from custom websites")
        except Exception as e:
            logger.error(f"Error scraping custom websites: {e}")

        return all_items
    
    def _scrape_source(self, source: IncentiveSource, keywords: List[str]) -> List[IncentiveItem]:
        """Scrape a specific source"""
        if source == IncentiveSource.MIMIT:
            return self._scrape_mimit(keywords)
        elif source == IncentiveSource.INVITALIA:
            return self._scrape_invitalia(keywords)
        elif source == IncentiveSource.SIMEST:
            return self._scrape_simest(keywords)
        elif source == IncentiveSource.OTHER:
            # Handle custom websites from configuration
            return self._scrape_custom_websites(keywords)
        else:
            logger.warning(f"Unknown source: {source}")
            return []
    
    def _scrape_mimit(self, keywords: List[str]) -> List[IncentiveItem]:
        """Scrape MIMIT website for incentives"""
        items = []

        # Get MIMIT configuration from websites config
        mimit_config = self._get_website_config("MIMIT")
        if not mimit_config:
            logger.warning("MIMIT configuration not found in websites config")
            return items

        base_url = mimit_config['url']
        search_paths = mimit_config.get('search_paths', [])

        try:
            # Use configured URLs from incentives_config.json
            urls_to_check = [f"{base_url}{path}" for path in search_paths]

            if not urls_to_check:
                # Fallback to default paths if none configured
                urls_to_check = [
                    f"{base_url}/it/incentivi-mise",
                    f"{base_url}/it/notizie-stampa",
                    f"{base_url}/it/per-l-impresa"
                ]

            for url in urls_to_check:
                try:
                    logger.info(f"Scraping MIMIT URL: {url}")
                    response = self.session.get(url, timeout=self.timeout)
                    response.raise_for_status()

                    soup = BeautifulSoup(response.content, 'html.parser')

                    # Updated selectors based on actual MIMIT website structure
                    content_selectors = [
                        # MIMIT specific news items (from notizie-stampa page)
                        'li:has(img)', 'li:has(a[href*="/notizie-stampa/"])',
                        # Incentive items and cards
                        'a[href*="/incentivi/"]', '.card-wrapper', '.card-body',
                        # Generic content containers
                        'article', 'h2', 'h3', 'h4',
                        # Links and content areas
                        'a[href*="/it/"]', '.content-item', '.news-item'
                    ]

                    items_found_on_page = 0
                    for selector in content_selectors:
                        elements = soup.select(selector)
                        for element in elements:
                            item = self._extract_item_from_element(element, IncentiveSource.MIMIT, base_url, keywords)
                            if item:
                                items.append(item)
                                items_found_on_page += 1

                    logger.info(f"Found {items_found_on_page} items on {url}")
                    time.sleep(self.request_delay)

                except requests.exceptions.RequestException as e:
                    logger.error(f"Request error scraping MIMIT URL {url}: {e}")
                except Exception as e:
                    logger.error(f"Unexpected error scraping MIMIT URL {url}: {e}")

        except Exception as e:
            logger.error(f"Error scraping MIMIT: {e}")

        return items
    
    def _scrape_invitalia(self, keywords: List[str]) -> List[IncentiveItem]:
        """Scrape Invitalia website for incentives"""
        items = []

        # Get Invitalia configuration from websites config
        invitalia_config = self._get_website_config("Invitalia")
        if not invitalia_config:
            logger.warning("Invitalia configuration not found in websites config")
            return items

        base_url = invitalia_config['url']
        search_paths = invitalia_config.get('search_paths', [])

        try:
            # Use configured URLs from incentives_config.json
            urls_to_check = [f"{base_url}{path}" for path in search_paths]

            if not urls_to_check:
                # Fallback to default paths if none configured
                urls_to_check = [
                    f"{base_url}/per-le-imprese",
                    f"{base_url}/news",
                    f"{base_url}/per-chi-vuole-fare-impresa"
                ]

            for url in urls_to_check:
                try:
                    logger.info(f"Scraping Invitalia URL: {url}")
                    response = self.session.get(url, timeout=self.timeout)
                    response.raise_for_status()

                    soup = BeautifulSoup(response.content, 'html.parser')

                    # Look for incentive listings and news
                    content_selectors = [
                        '.incentive-card', '.news-card', '.content-card',
                        '.measure-item', 'article', '.post',
                        '.card-wrapper', '.card-body', '.news-item'
                    ]

                    items_found_on_page = 0
                    for selector in content_selectors:
                        elements = soup.select(selector)
                        for element in elements:
                            item = self._extract_item_from_element(element, IncentiveSource.INVITALIA, base_url, keywords)
                            if item:
                                items.append(item)
                                items_found_on_page += 1

                    logger.info(f"Found {items_found_on_page} items on {url}")
                    time.sleep(self.request_delay)

                except requests.exceptions.RequestException as e:
                    logger.error(f"Request error scraping Invitalia URL {url}: {e}")
                except Exception as e:
                    logger.error(f"Unexpected error scraping Invitalia URL {url}: {e}")

        except Exception as e:
            logger.error(f"Error scraping Invitalia: {e}")

        return items
    
    def _scrape_simest(self, keywords: List[str]) -> List[IncentiveItem]:
        """Scrape SIMEST website for incentives"""
        items = []

        # Get SIMEST configuration from websites config
        simest_config = self._get_website_config("SIMEST")
        if not simest_config:
            logger.warning("SIMEST configuration not found in websites config")
            return items

        base_url = simest_config['url']
        search_paths = simest_config.get('search_paths', [])

        try:
            # Use configured URLs from incentives_config.json
            urls_to_check = [f"{base_url}{path}" for path in search_paths]

            if not urls_to_check:
                # Fallback to default paths if none configured
                urls_to_check = [
                    f"{base_url}/per-le-imprese",
                    f"{base_url}/per-le-imprese/finanziamenti-agevolati",
                    f"{base_url}/media"
                ]

            for url in urls_to_check:
                try:
                    logger.info(f"Scraping SIMEST URL: {url}")
                    response = self.session.get(url, timeout=self.timeout)
                    response.raise_for_status()

                    soup = BeautifulSoup(response.content, 'html.parser')

                    # Look for service listings and news
                    content_selectors = [
                        '.service-item', '.news-item', '.content-item',
                        '.funding-item', 'article', '.post',
                        '.card-wrapper', '.card-body'
                    ]

                    items_found_on_page = 0
                    for selector in content_selectors:
                        elements = soup.select(selector)
                        for element in elements:
                            item = self._extract_item_from_element(element, IncentiveSource.SIMEST, base_url, keywords)
                            if item:
                                items.append(item)
                                items_found_on_page += 1

                    logger.info(f"Found {items_found_on_page} items on {url}")
                    time.sleep(self.request_delay)

                except requests.exceptions.RequestException as e:
                    logger.error(f"Request error scraping SIMEST URL {url}: {e}")
                except Exception as e:
                    logger.error(f"Unexpected error scraping SIMEST URL {url}: {e}")

        except Exception as e:
            logger.error(f"Error scraping SIMEST: {e}")

        return items

    def _get_website_config(self, website_name: str) -> Optional[Dict[str, Any]]:
        """Get configuration for a specific website by name"""
        for website in self.websites_config:
            if website.get('name', '').upper().startswith(website_name.upper()):
                if website.get('enabled', True):
                    return website
        return None

    def _scrape_custom_websites(self, keywords: List[str]) -> List[IncentiveItem]:
        """Scrape custom websites that are not MIMIT, Invitalia, or SIMEST"""
        items = []

        for website in self.websites_config:
            if not website.get('enabled', True):
                continue

            website_name = website.get('name', '')
            # Skip the main three websites as they have dedicated methods
            if any(name in website_name.upper() for name in ['MIMIT', 'INVITALIA', 'SIMEST']):
                continue

            try:
                logger.info(f"Scraping custom website: {website_name}")
                base_url = website['url']
                search_paths = website.get('search_paths', ['/'])

                # Determine the appropriate source based on website name/URL
                source = IncentiveSource.OTHER
                if 'CAMPANIA' in website_name.upper() or 'regione.campania.it' in base_url:
                    source = IncentiveSource.CAMPANIA

                urls_to_check = [f"{base_url}{path}" for path in search_paths]

                for url in urls_to_check:
                    try:
                        logger.info(f"Scraping custom URL: {url}")
                        response = self.session.get(url, timeout=self.timeout)
                        response.raise_for_status()

                        soup = BeautifulSoup(response.content, 'html.parser')

                        # Generic content selectors for custom websites
                        content_selectors = [
                            '.news-item', '.content-item', '.article-item',
                            'article', '.post', '.card', '.card-body',
                            'h2', 'h3', 'h4', 'a[href*="/"]'
                        ]

                        items_found_on_page = 0
                        for selector in content_selectors:
                            elements = soup.select(selector)
                            for element in elements:
                                item = self._extract_item_from_element(element, source, base_url, keywords)
                                if item:
                                    items.append(item)
                                    items_found_on_page += 1

                        logger.info(f"Found {items_found_on_page} items on {url}")
                        time.sleep(self.request_delay)

                    except requests.exceptions.RequestException as e:
                        logger.error(f"Request error scraping custom URL {url}: {e}")
                    except Exception as e:
                        logger.error(f"Unexpected error scraping custom URL {url}: {e}")

            except Exception as e:
                logger.error(f"Error scraping custom website {website_name}: {e}")

        return items

    def _extract_item_from_element(self, element, source: IncentiveSource, base_url: str, keywords: List[str]) -> Optional[IncentiveItem]:
        """Extract incentive item from HTML element"""
        try:
            # Extract title - improved for MIMIT structure
            title = ""

            # If element is a link, use its text
            if element.name == 'a':
                title = element.get_text(strip=True)
            else:
                # Try various title selectors
                title_selectors = ['h1', 'h2', 'h3', 'h4', '.title', '.headline', 'a']
                for selector in title_selectors:
                    title_elem = element.select_one(selector)
                    if title_elem:
                        title = title_elem.get_text(strip=True)
                        break

            # Skip if no meaningful title
            if not title or len(title.strip()) < 10:
                return None

            # Extract description
            description = ""
            desc_selectors = ['.description', '.summary', '.excerpt', 'p', 'div']
            for selector in desc_selectors:
                desc_elem = element.select_one(selector)
                if desc_elem and desc_elem != element:  # Avoid self-reference
                    desc_text = desc_elem.get_text(strip=True)
                    if desc_text and desc_text != title:
                        description = desc_text[:500]  # Limit description length
                        break

            # Extract URL
            source_url = ""
            if element.name == 'a' and element.get('href'):
                source_url = urljoin(base_url, element.get('href'))
            else:
                link_elem = element.select_one('a')
                if link_elem and link_elem.get('href'):
                    source_url = urljoin(base_url, link_elem.get('href'))

            # Check if content matches keywords (case insensitive)
            content_text = f"{title} {description}".lower()
            matched_keywords = [kw for kw in keywords if kw.lower() in content_text]

            # Only include if keywords match or no keywords specified
            if keywords and not matched_keywords:
                return None

            # Skip if title is too generic or short
            generic_titles = ['home', 'menu', 'cerca', 'contatti', 'privacy', 'cookie', 'seguici', 'social']
            if any(generic in title.lower() for generic in generic_titles):
                return None

            # Focus only on incentive-related content
            incentive_keywords = [
                'incentiv', 'finanziament', 'bando', 'agevolazion', 'contribut',
                'sostegno', 'fondo', 'credito', 'investiment', 'sviluppo',
                'startup', 'pmi', 'impres', 'innovazion', 'ricerca',
                'transizion', 'digital', 'energia', 'sostenibil'
            ]

            # Check if content is incentive-related (more flexible matching)
            content_lower = f"{title} {description}".lower()
            is_incentive_related = any(keyword in content_lower for keyword in incentive_keywords)

            # If keywords are specified, use them; otherwise, filter by incentive relevance
            if keywords:
                # Use original keyword matching
                if not matched_keywords:
                    return None
            else:
                # If no specific keywords, only include incentive-related content
                if not is_incentive_related:
                    return None

            # Create content hash for duplicate detection
            content_hash = hashlib.md5(f"{title}{description}".encode()).hexdigest()

            # Extract raw content (limited)
            raw_content = element.get_text(strip=True)[:1000]

            # Create incentive item
            item = IncentiveItem(
                title=title,
                description=description,
                source=source,
                source_url=source_url,
                content_hash=content_hash,
                keywords_matched=matched_keywords,
                raw_content=raw_content,
                status=IncentiveStatus.NEW,
                priority=IncentivePriority.MEDIUM
            )

            return item

        except Exception as e:
            logger.error(f"Error extracting item from element: {e}")
            return None

    def scrape_single_url(self, url: str) -> Optional[str]:
        """Scrape content from a single URL for re-analysis"""
        try:
            logger.info(f"Re-scraping URL: {url}")
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, 'html.parser')

            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()

            # Try to find main content area
            content_selectors = [
                'main', 'article', '.content', '.main-content',
                '.post-content', '.entry-content', '.article-content',
                '#content', '#main', '.container'
            ]

            content_text = ""
            for selector in content_selectors:
                content_elem = soup.select_one(selector)
                if content_elem:
                    content_text = content_elem.get_text(strip=True)
                    break

            # If no specific content area found, use body
            if not content_text:
                body = soup.find('body')
                if body:
                    content_text = body.get_text(strip=True)

            # Clean up the text
            content_text = re.sub(r'\s+', ' ', content_text)
            content_text = content_text.strip()

            # Limit content length
            if len(content_text) > 5000:
                content_text = content_text[:5000] + "..."

            logger.info(f"Successfully re-scraped {len(content_text)} characters from {url}")
            return content_text

        except Exception as e:
            logger.error(f"Error re-scraping URL {url}: {e}")
            return None
    
    def test_connection(self, source: IncentiveSource) -> bool:
        """Test connection to a specific source"""
        try:
            if source == IncentiveSource.MIMIT:
                url = "https://www.mimit.gov.it"
            elif source == IncentiveSource.INVITALIA:
                url = "https://www.invitalia.it"
            elif source == IncentiveSource.SIMEST:
                url = "https://www.simest.it"
            elif source == IncentiveSource.CAMPANIA:
                url = "https://www.regione.campania.it"
            else:
                return False

            logger.info(f"Testing connection to {source.value} at {url}")
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            logger.info(f"Connection test successful for {source.value}")
            return True

        except requests.exceptions.RequestException as e:
            logger.error(f"Connection test failed for {source.value}: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error testing connection to {source.value}: {e}")
            return False
    
    def test_all_connections(self) -> Dict[IncentiveSource, bool]:
        """Test connections to all configured sources"""
        results = {}
        for source in [IncentiveSource.MIMIT, IncentiveSource.INVITALIA, IncentiveSource.SIMEST, IncentiveSource.CAMPANIA]:
            results[source] = self.test_connection(source)
        return results

    def validate_url(self, url: str) -> bool:
        """Validate if a URL is accessible"""
        try:
            response = self.session.head(url, timeout=self.timeout)
            return response.status_code < 400
        except:
            return False

    def close(self):
        """Close the session"""
        self.session.close()
