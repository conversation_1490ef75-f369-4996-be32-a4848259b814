#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hybrid Calendar View - Native DatePicker + Custom Event Display
Combines Flet's native DatePicker with custom event management
"""

import flet as ft
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Any
import calendar

from core import get_logger
from core.models.base_models import Deadline, Project, Client, DeadlineStatus, Priority

logger = get_logger(__name__)

class HybridCalendarView:
    """
    Hybrid calendar combining native DatePicker with custom event display
    Provides better native experience while maintaining custom functionality
    """
    
    def __init__(self, app_instance):
        self.app = app_instance
        self.db = app_instance.db_manager
        
        # State
        self.selected_date = date.today()
        self.view_mode = "month"  # month, week, agenda
        
        # Data
        self.deadlines = []
        self.projects = []
        self.clients = []
        
        # Components
        self.date_picker = None
        self.event_display = None
        self.main_container = None
        
        # Load data and initialize
        self._load_data()
        self._init_components()
    
    def _load_data(self):
        """Load deadlines and related data"""
        try:
            # Load current month data
            start_date = self.selected_date.replace(day=1)
            end_date = (start_date + timedelta(days=32)).replace(day=1) - timedelta(days=1)
            
            self.deadlines = self.db.get_deadlines_by_date_range(start_date, end_date)
            self.projects = self.db.get_all_projects()
            self.clients = self.db.get_all_clients()
            
            logger.info(f"Loaded {len(self.deadlines)} deadlines for hybrid calendar")
            
        except Exception as e:
            logger.error(f"Error loading calendar data: {e}")
            self.deadlines = []
    
    def _init_components(self):
        """Initialize calendar components"""
        # Native date picker for better UX
        self.date_picker = ft.DatePicker(
            first_date=datetime(2020, 1, 1),
            last_date=datetime(2030, 12, 31),
            value=self.selected_date,
            on_change=self._on_date_selected
        )
        
        # Custom event display
        self.event_display = self._create_event_display()
    
    def _create_header(self) -> ft.Container:
        """Create modern header with native date picker"""
        # Date selection button (triggers native picker)
        date_button = ft.ElevatedButton(
            content=ft.Row([
                ft.Icon(ft.Icons.CALENDAR_TODAY, size=20),
                ft.Text(
                    self.selected_date.strftime("%B %d, %Y"),
                    size=16,
                    weight=ft.FontWeight.W_500
                )
            ], spacing=8),
            on_click=lambda _: self._show_date_picker(),
            style=ft.ButtonStyle(
                bgcolor=ft.Colors.BLUE_50,
                color=ft.Colors.BLUE_700,
                padding=ft.padding.symmetric(horizontal=20, vertical=12),
                shape=ft.RoundedRectangleBorder(radius=12)
            )
        )
        
        # View mode selector - using buttons instead of FilterChip (compatible with Flet 0.27.0)
        view_mode_chips = ft.Row([
            ft.ElevatedButton(
                text="Mese",
                bgcolor=ft.Colors.BLUE_600 if self.view_mode == "month" else ft.Colors.GREY_300,
                color=ft.Colors.WHITE if self.view_mode == "month" else ft.Colors.GREY_700,
                on_click=lambda _: self._change_view_mode("month"),
                style=ft.ButtonStyle(
                    shape=ft.RoundedRectangleBorder(radius=8),
                    padding=ft.padding.symmetric(horizontal=16, vertical=8)
                )
            ),
            ft.ElevatedButton(
                text="Settimana",
                bgcolor=ft.Colors.BLUE_600 if self.view_mode == "week" else ft.Colors.GREY_300,
                color=ft.Colors.WHITE if self.view_mode == "week" else ft.Colors.GREY_700,
                on_click=lambda _: self._change_view_mode("week"),
                style=ft.ButtonStyle(
                    shape=ft.RoundedRectangleBorder(radius=8),
                    padding=ft.padding.symmetric(horizontal=16, vertical=8)
                )
            ),
            ft.ElevatedButton(
                text="Agenda",
                bgcolor=ft.Colors.BLUE_600 if self.view_mode == "agenda" else ft.Colors.GREY_300,
                color=ft.Colors.WHITE if self.view_mode == "agenda" else ft.Colors.GREY_700,
                on_click=lambda _: self._change_view_mode("agenda"),
                style=ft.ButtonStyle(
                    shape=ft.RoundedRectangleBorder(radius=8),
                    padding=ft.padding.symmetric(horizontal=16, vertical=8)
                )
            )
        ], spacing=8)
        
        # Quick navigation
        quick_nav = ft.Row([
            ft.IconButton(
                icon=ft.Icons.CHEVRON_LEFT,
                on_click=lambda _: self._navigate_date(-1),
                tooltip="Giorno precedente"
            ),
            ft.TextButton(
                text="Oggi",
                on_click=lambda _: self._go_to_today(),
                style=ft.ButtonStyle(
                    color=ft.Colors.BLUE_600
                )
            ),
            ft.IconButton(
                icon=ft.Icons.CHEVRON_RIGHT,
                on_click=lambda _: self._navigate_date(1),
                tooltip="Giorno successivo"
            )
        ], spacing=4)
        
        return ft.Container(
            content=ft.Row([
                date_button,
                ft.Container(expand=True),
                view_mode_chips,
                ft.Container(expand=True),
                quick_nav
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            padding=ft.padding.all(20),
            bgcolor=ft.Colors.WHITE,
            border_radius=16,
            border=ft.border.all(1, ft.Colors.GREY_200),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=8,
                color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
                offset=ft.Offset(0, 2)
            )
        )
    
    def _create_event_display(self) -> ft.Container:
        """Create the event display area"""
        if self.view_mode == "month":
            return self._create_month_events()
        elif self.view_mode == "week":
            return self._create_week_events()
        else:  # agenda
            return self._create_agenda_view()
    
    def _create_month_events(self) -> ft.Container:
        """Create month view with mini calendar + event list"""
        # Mini calendar for the month
        mini_calendar = self._create_mini_calendar()
        
        # Event list for selected date
        events_list = self._create_events_list()
        
        return ft.Container(
            content=ft.Row([
                # Left: Mini calendar
                ft.Container(
                    content=mini_calendar,
                    width=350,
                    padding=ft.padding.all(16),
                    bgcolor=ft.Colors.WHITE,
                    border_radius=16,
                    border=ft.border.all(1, ft.Colors.GREY_200)
                ),
                # Right: Events for selected date
                ft.Container(
                    content=events_list,
                    expand=True,
                    padding=ft.padding.all(16),
                    bgcolor=ft.Colors.WHITE,
                    border_radius=16,
                    border=ft.border.all(1, ft.Colors.GREY_200)
                )
            ], spacing=16),
            padding=ft.padding.all(0)
        )
    
    def _create_mini_calendar(self) -> ft.Column:
        """Create a mini calendar with event indicators"""
        # Month header
        month_header = ft.Text(
            self.selected_date.strftime("%B %Y"),
            size=18,
            weight=ft.FontWeight.BOLD,
            text_align=ft.TextAlign.CENTER
        )
        
        # Weekday headers
        weekdays = ft.Row([
            ft.Container(
                content=ft.Text(day, size=12, weight=ft.FontWeight.W_500, text_align=ft.TextAlign.CENTER),
                width=40,
                height=30,
                alignment=ft.alignment.center
            )
            for day in ["Lu", "Ma", "Me", "Gi", "Ve", "Sa", "Do"]
        ])
        
        # Calendar grid
        cal = calendar.monthcalendar(self.selected_date.year, self.selected_date.month)
        week_rows = []
        
        for week in cal:
            day_cells = []
            for day_num in week:
                if day_num == 0:
                    day_cells.append(ft.Container(width=40, height=40))
                else:
                    day_date = date(self.selected_date.year, self.selected_date.month, day_num)
                    day_events = [d for d in self.deadlines if d.due_date == day_date]
                    
                    # Determine day styling
                    is_selected = day_date == self.selected_date
                    is_today = day_date == date.today()
                    has_events = len(day_events) > 0
                    
                    if is_selected:
                        bg_color = ft.Colors.BLUE_600
                        text_color = ft.Colors.WHITE
                    elif is_today:
                        bg_color = ft.Colors.BLUE_100
                        text_color = ft.Colors.BLUE_800
                    else:
                        bg_color = None
                        text_color = ft.Colors.GREY_800
                    
                    day_cell = ft.Container(
                        content=ft.Column([
                            ft.Text(
                                str(day_num),
                                size=14,
                                color=text_color,
                                text_align=ft.TextAlign.CENTER,
                                weight=ft.FontWeight.W_500 if is_today or is_selected else ft.FontWeight.NORMAL
                            ),
                            # Event indicator
                            ft.Container(
                                width=4,
                                height=4,
                                bgcolor=ft.Colors.RED_400 if has_events else ft.Colors.TRANSPARENT,
                                border_radius=2
                            ) if not is_selected else ft.Container()
                        ], spacing=2, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                        width=40,
                        height=40,
                        bgcolor=bg_color,
                        border_radius=20,
                        alignment=ft.alignment.center,
                        on_click=lambda _, d=day_date: self._select_date(d),
                        ink=True
                    )
                    
                    day_cells.append(day_cell)
            
            week_rows.append(ft.Row(day_cells, spacing=2))
        
        return ft.Column([
            month_header,
            ft.Divider(),
            weekdays,
            ft.Column(week_rows, spacing=2)
        ], spacing=8)
    
    def _create_events_list(self) -> ft.Column:
        """Create events list for selected date"""
        selected_events = [d for d in self.deadlines if d.due_date == self.selected_date]
        
        # Header
        header = ft.Row([
            ft.Text(
                f"Eventi per {self.selected_date.strftime('%d %B %Y')}",
                size=18,
                weight=ft.FontWeight.BOLD
            ),
            ft.Container(expand=True),
            ft.IconButton(
                icon=ft.Icons.ADD,
                tooltip="Aggiungi evento",
                on_click=lambda _: self._add_event()
            )
        ])
        
        # Events
        if not selected_events:
            content = ft.Container(
                content=ft.Column([
                    ft.Icon(ft.Icons.EVENT_NOTE_OUTLINED, size=48, color=ft.Colors.GREY_400),
                    ft.Text(
                        "Nessun evento per questa data",
                        size=16,
                        color=ft.Colors.GREY_600
                    ),
                    ft.ElevatedButton(
                        text="Aggiungi Evento",
                        icon=ft.Icons.ADD,
                        on_click=lambda _: self._add_event()
                    )
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=16),
                alignment=ft.alignment.center,
                expand=True
            )
        else:
            event_widgets = []
            for event in selected_events:
                event_card = self._create_event_card(event)
                event_widgets.append(event_card)
            
            content = ft.Column(
                event_widgets,
                spacing=8,
                scroll=ft.ScrollMode.AUTO
            )
        
        return ft.Column([
            header,
            ft.Divider(),
            content
        ], spacing=12, expand=True)
    
    def _create_event_card(self, event) -> ft.Container:
        """Create a modern event card"""
        # Priority color
        priority_color = {
            Priority.LOW: ft.Colors.GREEN_400,
            Priority.MEDIUM: ft.Colors.BLUE_400,
            Priority.HIGH: ft.Colors.ORANGE_400,
            Priority.CRITICAL: ft.Colors.RED_400
        }.get(event.priority, ft.Colors.GREY_400)
        
        return ft.Container(
            content=ft.Row([
                # Priority indicator
                ft.Container(
                    width=4,
                    height=60,
                    bgcolor=priority_color,
                    border_radius=2
                ),
                # Event content
                ft.Column([
                    ft.Text(
                        event.title,
                        size=16,
                        weight=ft.FontWeight.W_500,
                        max_lines=2,
                        overflow=ft.TextOverflow.ELLIPSIS
                    ),
                    ft.Text(
                        event.description or "",
                        size=14,
                        color=ft.Colors.GREY_600,
                        max_lines=2,
                        overflow=ft.TextOverflow.ELLIPSIS
                    ) if event.description else ft.Container(),
                    ft.Row([
                        ft.Icon(ft.Icons.SCHEDULE, size=16, color=ft.Colors.GREY_500),
                        ft.Text(
                            f"Scadenza: {event.due_date.strftime('%H:%M') if hasattr(event.due_date, 'time') else 'Tutto il giorno'}",
                            size=12,
                            color=ft.Colors.GREY_500
                        )
                    ], spacing=4)
                ], spacing=4, expand=True),
                # Actions
                ft.Column([
                    ft.IconButton(
                        icon=ft.Icons.EDIT_OUTLINED,
                        icon_size=16,
                        on_click=lambda _, e=event: self._edit_event(e),
                        tooltip="Modifica"
                    ),
                    ft.IconButton(
                        icon=ft.Icons.CHECK_CIRCLE_OUTLINE if event.status != DeadlineStatus.COMPLETED else ft.Icons.CHECK_CIRCLE,
                        icon_size=16,
                        icon_color=ft.Colors.GREEN_600 if event.status == DeadlineStatus.COMPLETED else ft.Colors.GREY_500,
                        on_click=lambda _, e=event: self._toggle_event_status(e),
                        tooltip="Completa/Riapri"
                    )
                ], spacing=0)
            ], spacing=12),
            padding=ft.padding.all(16),
            bgcolor=ft.Colors.GREY_50,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_200),
            on_click=lambda _, e=event: self._view_event_details(e)
        )
    
    def _create_week_events(self) -> ft.Container:
        """Create week view with events"""
        # Calculate week start (Monday)
        days_since_monday = self.selected_date.weekday()
        week_start = self.selected_date - timedelta(days=days_since_monday)
        
        # Create week header
        week_header = ft.Row([
            ft.Text(
                f"Settimana dal {week_start.strftime('%d %B')} al {(week_start + timedelta(days=6)).strftime('%d %B %Y')}",
                size=18,
                weight=ft.FontWeight.BOLD
            ),
            ft.Container(expand=True),
            ft.IconButton(
                icon=ft.Icons.ADD,
                tooltip="Aggiungi evento",
                on_click=lambda _: self._add_event()
            )
        ])
        
        # Create day columns for the week
        day_columns = []
        for i in range(7):
            day_date = week_start + timedelta(days=i)
            day_events = [d for d in self.deadlines if d.due_date == day_date]
            
            # Day header
            day_name = ["Lunedì", "Martedì", "Mercoledì", "Giovedì", "Venerdì", "Sabato", "Domenica"][i]
            is_today = day_date == date.today()
            is_selected = day_date == self.selected_date
            
            day_header = ft.Container(
                content=ft.Column([
                    ft.Text(
                        day_name,
                        size=12,
                        weight=ft.FontWeight.W_500,
                        color=ft.Colors.GREY_600
                    ),
                    ft.Text(
                        str(day_date.day),
                        size=18,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.BLUE_600 if is_today else ft.Colors.GREY_800
                    )
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=4),
                width=140,
                height=60,
                bgcolor=ft.Colors.BLUE_50 if is_today else (ft.Colors.GREY_100 if is_selected else ft.Colors.WHITE),
                border_radius=8,
                alignment=ft.alignment.center,
                border=ft.border.all(2 if is_selected else 1, ft.Colors.BLUE_400 if is_today else ft.Colors.GREY_300),
                on_click=lambda _, d=day_date: self._select_date(d)
            )
            
            # Day events
            event_widgets = []
            for event in day_events[:3]:  # Show max 3 events per day
                priority_color = {
                    Priority.LOW: ft.Colors.GREEN_400,
                    Priority.MEDIUM: ft.Colors.BLUE_400,
                    Priority.HIGH: ft.Colors.ORANGE_400,
                    Priority.CRITICAL: ft.Colors.RED_400
                }.get(event.priority, ft.Colors.GREY_400)
                
                event_widget = ft.Container(
                    content=ft.Column([
                        ft.Text(
                            event.title,
                            size=11,
                            weight=ft.FontWeight.W_500,
                            max_lines=2,
                            overflow=ft.TextOverflow.ELLIPSIS
                        ),
                        ft.Text(
                            "Tutto il giorno",
                            size=9,
                            color=ft.Colors.GREY_600
                        )
                    ], spacing=2),
                    width=130,
                    padding=ft.padding.all(6),
                    bgcolor=ft.Colors.with_opacity(0.1, priority_color),
                    border_radius=6,
                    border=ft.border.all(1, priority_color),
                    on_click=lambda _, e=event: self._view_event_details(e)
                )
                event_widgets.append(event_widget)
            
            # Show "+" button if more events
            if len(day_events) > 3:
                event_widgets.append(
                    ft.Container(
                        content=ft.Text(f"+{len(day_events) - 3} altri", size=10, color=ft.Colors.GREY_600),
                        width=130,
                        padding=ft.padding.symmetric(vertical=4),
                        alignment=ft.alignment.center
                    )
                )
            
            day_column = ft.Container(
                content=ft.Column([
                    day_header,
                    ft.Container(height=8),
                    ft.Column(event_widgets, spacing=4)
                ], spacing=0),
                width=140,
                expand=True
            )
            
            day_columns.append(day_column)
        
        return ft.Container(
            content=ft.Column([
                week_header,
                ft.Divider(),
                ft.Row(day_columns, spacing=8, scroll=ft.ScrollMode.AUTO)
            ], spacing=12),
            padding=ft.padding.all(16),
            bgcolor=ft.Colors.WHITE,
            border_radius=16,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )
    
    def _create_agenda_view(self) -> ft.Container:
        """Create agenda view with upcoming events"""
        # Get events for the next 30 days
        start_date = date.today()
        end_date = start_date + timedelta(days=30)
        
        upcoming_events = [
            d for d in self.deadlines 
            if start_date <= d.due_date <= end_date
        ]
        
        # Sort by date and then by priority
        priority_order = {Priority.CRITICAL: 0, Priority.HIGH: 1, Priority.MEDIUM: 2, Priority.LOW: 3}
        upcoming_events.sort(key=lambda x: (x.due_date, priority_order.get(x.priority, 4)))
        
        # Create header
        agenda_header = ft.Row([
            ft.Text(
                "Agenda - Prossimi 30 giorni",
                size=18,
                weight=ft.FontWeight.BOLD
            ),
            ft.Container(expand=True),
            ft.Text(
                f"{len(upcoming_events)} eventi",
                size=14,
                color=ft.Colors.GREY_600
            ),
            ft.IconButton(
                icon=ft.Icons.ADD,
                tooltip="Aggiungi evento",
                on_click=lambda _: self._add_event()
            )
        ])
        
        if not upcoming_events:
            content = ft.Container(
                content=ft.Column([
                    ft.Icon(ft.Icons.EVENT_AVAILABLE, size=64, color=ft.Colors.GREY_400),
                    ft.Text(
                        "Nessun evento nei prossimi 30 giorni",
                        size=16,
                        color=ft.Colors.GREY_600
                    ),
                    ft.ElevatedButton(
                        text="Aggiungi Evento",
                        icon=ft.Icons.ADD,
                        on_click=lambda _: self._add_event()
                    )
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=16),
                alignment=ft.alignment.center,
                expand=True
            )
        else:
            # Group events by date
            events_by_date = {}
            for event in upcoming_events:
                date_key = event.due_date
                if date_key not in events_by_date:
                    events_by_date[date_key] = []
                events_by_date[date_key].append(event)
            
            # Create agenda items
            agenda_items = []
            for event_date, events in events_by_date.items():
                # Date header
                is_today = event_date == date.today()
                is_tomorrow = event_date == date.today() + timedelta(days=1)
                
                if is_today:
                    date_display = "Oggi"
                elif is_tomorrow:
                    date_display = "Domani"
                else:
                    # Calculate days from today
                    days_diff = (event_date - date.today()).days
                    if days_diff < 7:
                        date_display = event_date.strftime("%A")
                    else:
                        date_display = event_date.strftime("%d %B")
                
                date_header = ft.Container(
                    content=ft.Row([
                        ft.Text(
                            date_display,
                            size=16,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.BLUE_700 if is_today else ft.Colors.GREY_800
                        ),
                        ft.Text(
                            event_date.strftime("%d/%m/%Y") if not is_today and not is_tomorrow else "",
                            size=12,
                            color=ft.Colors.GREY_500
                        ),
                        ft.Container(expand=True),
                        ft.Text(
                            f"{len(events)} evento{'i' if len(events) > 1 else ''}",
                            size=12,
                            color=ft.Colors.GREY_500
                        )
                    ]),
                    padding=ft.padding.symmetric(vertical=8, horizontal=12),
                    bgcolor=ft.Colors.BLUE_50 if is_today else ft.Colors.GREY_50,
                    border_radius=8
                )
                agenda_items.append(date_header)
                
                # Events for this date
                for event in events:
                    # Days until event
                    days_until = (event_date - date.today()).days
                    if days_until < 0:
                        urgency_text = "SCADUTO"
                        urgency_color = ft.Colors.RED_600
                    elif days_until == 0:
                        urgency_text = "OGGI"
                        urgency_color = ft.Colors.ORANGE_600
                    elif days_until == 1:
                        urgency_text = "DOMANI"
                        urgency_color = ft.Colors.BLUE_600
                    else:
                        urgency_text = f"In {days_until} giorni"
                        urgency_color = ft.Colors.GREY_600
                    
                    # Priority color
                    priority_color = {
                        Priority.LOW: ft.Colors.GREEN_400,
                        Priority.MEDIUM: ft.Colors.BLUE_400,
                        Priority.HIGH: ft.Colors.ORANGE_400,
                        Priority.CRITICAL: ft.Colors.RED_400
                    }.get(event.priority, ft.Colors.GREY_400)
                    
                    # Priority text
                    priority_text = {
                        Priority.LOW: "Bassa",
                        Priority.MEDIUM: "Media",
                        Priority.HIGH: "Alta",
                        Priority.CRITICAL: "Critica"
                    }.get(event.priority, "")
                    
                    event_card = ft.Container(
                        content=ft.Row([
                            # Priority indicator
                            ft.Container(
                                width=4,
                                height=50,
                                bgcolor=priority_color,
                                border_radius=2
                            ),
                            # Event content
                            ft.Column([
                                ft.Row([
                                    ft.Text(
                                        event.title,
                                        size=14,
                                        weight=ft.FontWeight.W_500,
                                        expand=True
                                    ),
                                    ft.Container(
                                        content=ft.Text(
                                            urgency_text,
                                            size=10,
                                            color=urgency_color,
                                            weight=ft.FontWeight.BOLD
                                        ),
                                        padding=ft.padding.symmetric(horizontal=8, vertical=2),
                                        bgcolor=ft.Colors.with_opacity(0.1, urgency_color),
                                        border_radius=4
                                    )
                                ]),
                                ft.Text(
                                    event.description or "Nessuna descrizione",
                                    size=12,
                                    color=ft.Colors.GREY_600,
                                    max_lines=2,
                                    overflow=ft.TextOverflow.ELLIPSIS
                                ),
                                ft.Row([
                                    ft.Icon(ft.Icons.FLAG, size=12, color=priority_color),
                                    ft.Text(
                                        f"Priorità {priority_text}",
                                        size=11,
                                        color=ft.Colors.GREY_500
                                    ),
                                    ft.Container(expand=True),
                                    ft.IconButton(
                                        icon=ft.Icons.CHECK_CIRCLE_OUTLINE if event.status != DeadlineStatus.COMPLETED else ft.Icons.CHECK_CIRCLE,
                                        icon_size=16,
                                        icon_color=ft.Colors.GREEN_600 if event.status == DeadlineStatus.COMPLETED else ft.Colors.GREY_500,
                                        on_click=lambda _, e=event: self._toggle_event_status(e),
                                        tooltip="Completa/Riapri"
                                    )
                                ], spacing=4)
                            ], spacing=4, expand=True),
                        ], spacing=12),
                        padding=ft.padding.all(12),
                        bgcolor=ft.Colors.WHITE,
                        border_radius=8,
                        border=ft.border.all(1, ft.Colors.GREY_200),
                        on_click=lambda _, e=event: self._view_event_details(e),
                        margin=ft.margin.only(left=16, bottom=8)
                    )
                    agenda_items.append(event_card)
                
                # Add spacing between dates
                agenda_items.append(ft.Container(height=8))
            
            content = ft.Column(
                agenda_items,
                spacing=0,
                scroll=ft.ScrollMode.AUTO,
                expand=True
            )
        
        return ft.Container(
            content=ft.Column([
                agenda_header,
                ft.Divider(),
                content
            ], spacing=12),
            padding=ft.padding.all(16),
            bgcolor=ft.Colors.WHITE,
            border_radius=16,
            border=ft.border.all(1, ft.Colors.GREY_200),
            expand=True
        )
    
    # Event handlers
    def _show_date_picker(self):
        """Show native date picker"""
        if hasattr(self.app, 'page'):
            self.app.page.overlay.append(self.date_picker)
            self.date_picker.open = True
            self.app.page.update()
    
    def _on_date_selected(self, e):
        """Handle date selection from picker"""
        if e.control.value:
            self.selected_date = e.control.value.date()
            self._load_data()
            self._refresh_view()
    
    def _select_date(self, selected_date: date):
        """Handle date selection from mini calendar"""
        self.selected_date = selected_date
        self._refresh_view()
    
    def _navigate_date(self, days: int):
        """Navigate by days"""
        self.selected_date = self.selected_date + timedelta(days=days)
        self._load_data()
        self._refresh_view()
    
    def _go_to_today(self):
        """Go to today"""
        self.selected_date = date.today()
        self._load_data()
        self._refresh_view()
    
    def _change_view_mode(self, mode: str):
        """Change view mode"""
        if mode != self.view_mode:
            self.view_mode = mode
            self._refresh_view()
    
    def _refresh_view(self):
        """Refresh the entire view"""
        if self.main_container:
            self.event_display = self._create_event_display()
            # Update the main container content
            self.main_container.content = ft.Column([
                self._create_header(),
                ft.Container(height=16),
                self.event_display
            ], spacing=0, scroll=ft.ScrollMode.AUTO)
            
            # Try to update the page
            try:
                if hasattr(self.app, 'page') and self.app.page:
                    self.app.page.update()
                elif hasattr(self.app, 'main_layout') and self.app.main_layout and hasattr(self.app.main_layout, 'page'):
                    self.app.main_layout.page.update()
            except Exception as e:
                logger.warning(f"Could not update page: {e}")
    
    def _add_event(self):
        """Add new event"""
        # For now, navigate to deadlines view to add new event
        if hasattr(self.app, 'main_layout') and self.app.main_layout:
            self.app.main_layout._navigate_to('deadlines')
    
    def _edit_event(self, event):
        """Edit existing event"""
        # For now, navigate to deadlines view with the event
        if hasattr(self.app, 'main_layout') and self.app.main_layout:
            self.app.main_layout._navigate_to('deadlines')
    
    def _toggle_event_status(self, event):
        """Toggle event completion status"""
        try:
            new_status = DeadlineStatus.COMPLETED if event.status != DeadlineStatus.COMPLETED else DeadlineStatus.PENDING
            event.status = new_status
            if new_status == DeadlineStatus.COMPLETED:
                event.completed_date = date.today()
            else:
                event.completed_date = None
            
            self.db.update_deadline(event)
            self._load_data()
            self._refresh_view()
            
            logger.info(f"Event status changed: {event.title} -> {new_status}")
        except Exception as e:
            logger.error(f"Error changing event status: {e}")
    
    def _view_event_details(self, event):
        """View event details"""
        # For now, navigate to deadlines view
        if hasattr(self.app, 'main_layout') and self.app.main_layout:
            self.app.main_layout._navigate_to('deadlines')
    
    def build(self) -> ft.Container:
        """Build the hybrid calendar view"""
        self.main_container = ft.Container(
            content=ft.Column([
                self._create_header(),
                ft.Container(height=16),
                self.event_display
            ], spacing=0, scroll=ft.ScrollMode.AUTO),
            padding=ft.padding.all(20),
            bgcolor=ft.Colors.GREY_50,
            expand=True
        )
        
        return self.main_container 