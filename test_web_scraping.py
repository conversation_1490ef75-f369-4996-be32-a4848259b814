#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for web scraping service fixes
"""

import sys
import os
import json
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from core.services.web_scraping_service import WebScrapingService
from core.models.incentive_models import IncentiveSource

def test_web_scraping():
    """Test the web scraping service with updated configuration"""
    
    # Load configuration
    config_path = Path("data/incentives_config.json")
    if not config_path.exists():
        print("Configuration file not found!")
        return
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # Create scraper config
    scraper_config = {
        'sources': [IncentiveSource.MIMIT, IncentiveSource.INVITALIA, IncentiveSource.SIMEST],
        'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'request_delay_seconds': 1.0,
        'timeout_seconds': 15,
        'max_items_per_session': 50
    }
    
    # Initialize scraper
    scraper = WebScrapingService(scraper_config)
    
    print("Testing connections to all sources...")
    connection_results = scraper.test_all_connections()
    
    for source, success in connection_results.items():
        status = "✓ SUCCESS" if success else "✗ FAILED"
        print(f"  {source.value}: {status}")
    
    # Test scraping with keywords
    keywords = config.get('keywords', ['incentivi', 'finanziamenti'])
    print(f"\nTesting scraping with keywords: {keywords}")
    
    # Test MIMIT specifically
    print("\nTesting MIMIT scraping...")
    try:
        mimit_items = scraper._scrape_mimit(keywords)
        print(f"Found {len(mimit_items)} items from MIMIT")
        
        if mimit_items:
            print("Sample items:")
            for i, item in enumerate(mimit_items[:3]):
                print(f"  {i+1}. {item.title[:100]}...")
                print(f"     URL: {item.source_url}")
                print(f"     Keywords matched: {item.keywords_matched}")
                print()
    except Exception as e:
        print(f"Error testing MIMIT: {e}")
    
    # Test Invitalia
    print("\nTesting Invitalia scraping...")
    try:
        invitalia_items = scraper._scrape_invitalia(keywords)
        print(f"Found {len(invitalia_items)} items from Invitalia")
        
        if invitalia_items:
            print("Sample items:")
            for i, item in enumerate(invitalia_items[:2]):
                print(f"  {i+1}. {item.title[:100]}...")
                print(f"     URL: {item.source_url}")
                print()
    except Exception as e:
        print(f"Error testing Invitalia: {e}")
    
    # Test SIMEST
    print("\nTesting SIMEST scraping...")
    try:
        simest_items = scraper._scrape_simest(keywords)
        print(f"Found {len(simest_items)} items from SIMEST")
        
        if simest_items:
            print("Sample items:")
            for i, item in enumerate(simest_items[:2]):
                print(f"  {i+1}. {item.title[:100]}...")
                print(f"     URL: {item.source_url}")
                print()
    except Exception as e:
        print(f"Error testing SIMEST: {e}")
    
    # Clean up
    scraper.close()
    print("\nTest completed!")

if __name__ == "__main__":
    test_web_scraping()
