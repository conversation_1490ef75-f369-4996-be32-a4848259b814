#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Reports Settings Section
Modern, self-contained reports configuration component
"""

import flet as ft
from typing import Dict, Any, List
from ..components import SettingsCard, SettingsCardSection, FormField, SwitchField, DropdownField, ButtonGroup, InfoBox, CheckboxGroup
from core import get_logger

logger = get_logger(__name__)

class ReportsSection:
    """
    Self-contained reports settings section with modern UX
    Handles scheduled reports and custom report configuration
    """
    
    def __init__(self, controller):
        self.controller = controller
        self.reports_settings = controller.get_setting('reports')
        
        # Validation state
        self.validation_errors = {}
        
        # Register for setting changes
        controller.register_change_callback('reports', self._on_setting_changed)
    
    def _on_setting_changed(self, key: str, new_value: Any, old_value: Any):
        """Handle setting changes"""
        logger.debug(f"Reports setting changed: {key} = {new_value}")
        self._validate_settings()
    
    def _validate_settings(self):
        """Validate current reports settings"""
        settings = self.controller.get_setting('reports')
        errors = {}
        
        # Validate time formats
        if settings.get('scheduled_enabled'):
            morning_time = settings.get('morning_time', '09:00')
            evening_time = settings.get('evening_time', '17:30')
            
            if not self._validate_time_format(morning_time):
                errors['morning_time'] = "Formato orario non valido (usa HH:MM)"
            
            if not self._validate_time_format(evening_time):
                errors['evening_time'] = "Formato orario non valido (usa HH:MM)"
        
        # Validate recipients
        recipients = settings.get('recipients', [])
        for email in recipients:
            if '@' not in email or '.' not in email.split('@')[1]:
                errors['recipients'] = f"Email non valida: {email}"
                break
        
        # Validate days ahead
        days_ahead = settings.get('days_ahead_filter', 7)
        if not isinstance(days_ahead, int) or days_ahead < 1 or days_ahead > 365:
            errors['days_ahead'] = "Giorni di anticipo deve essere tra 1 e 365"
        
        self.validation_errors = errors
        return len(errors) == 0
    
    def _validate_time_format(self, time_str: str) -> bool:
        """Validate time format (HH:MM)"""
        try:
            parts = time_str.split(':')
            if len(parts) != 2:
                return False
            
            hour = int(parts[0])
            minute = int(parts[1])
            
            return 0 <= hour <= 23 and 0 <= minute <= 59
        except (ValueError, IndexError):
            return False
    
    def _create_scheduled_reports_section(self) -> SettingsCardSection:
        """Create scheduled reports configuration section"""
        
        def on_scheduled_enabled_change(e):
            self.controller.set_setting('reports', 'scheduled_enabled', e.control.value)
        
        def on_morning_enabled_change(e):
            self.controller.set_setting('reports', 'morning_enabled', e.control.value)
        
        def on_evening_enabled_change(e):
            self.controller.set_setting('reports', 'evening_enabled', e.control.value)
        
        def on_workdays_only_change(e):
            self.controller.set_setting('reports', 'workdays_only', e.control.value)
        
        def on_morning_time_change(e):
            if self._validate_time_format(e.control.value):
                self.controller.set_setting('reports', 'morning_time', e.control.value)
            else:
                self._show_error_notification("Formato orario non valido")
        
        def on_evening_time_change(e):
            if self._validate_time_format(e.control.value):
                self.controller.set_setting('reports', 'evening_time', e.control.value)
            else:
                self._show_error_notification("Formato orario non valido")
        
        def send_test_report(e):
            """Send a test scheduled report"""
            self._show_success_notification("Report di test inviato!")
        
        settings = self.controller.get_setting('reports')
        
        # Main scheduled reports switch
        scheduled_enabled_switch = SwitchField.create(
            label="Report Programmati",
            description="Abilita l'invio automatico di report via email",
            value=settings.get('scheduled_enabled', False),
            on_change=on_scheduled_enabled_change,
            color=ft.Colors.GREEN_600
        )
        
        # Schedule configuration
        morning_enabled_switch = SwitchField.create(
            label="Report Mattutino",
            description="Invia report di riepilogo mattutino",
            value=settings.get('morning_enabled', True),
            on_change=on_morning_enabled_change,
            disabled=not settings.get('scheduled_enabled', False)
        )
        
        evening_enabled_switch = SwitchField.create(
            label="Report Serale",
            description="Invia report di riepilogo serale",
            value=settings.get('evening_enabled', True),
            on_change=on_evening_enabled_change,
            disabled=not settings.get('scheduled_enabled', False)
        )
        
        workdays_only_switch = SwitchField.create(
            label="Solo Giorni Lavorativi",
            description="Invia report solo dal lunedì al venerdì",
            value=settings.get('workdays_only', True),
            on_change=on_workdays_only_change,
            disabled=not settings.get('scheduled_enabled', False)
        )
        
        # Time configuration
        morning_time_field = FormField.create_text_field(
            label="Orario Mattutino",
            value=settings.get('morning_time', '09:00'),
            hint_text="09:00",
            width=120,
            validation_message=self.validation_errors.get('morning_time'),
            on_change=on_morning_time_change,
            disabled=not (settings.get('scheduled_enabled', False) and settings.get('morning_enabled', True)),
            prefix_icon=ft.Icons.WB_SUNNY
        )
        
        evening_time_field = FormField.create_text_field(
            label="Orario Serale",
            value=settings.get('evening_time', '17:30'),
            hint_text="17:30",
            width=120,
            validation_message=self.validation_errors.get('evening_time'),
            on_change=on_evening_time_change,
            disabled=not (settings.get('scheduled_enabled', False) and settings.get('evening_enabled', True)),
            prefix_icon=ft.Icons.BEDTIME
        )
        
        schedule_row = ft.Row([
            ft.Column([morning_enabled_switch, morning_time_field], spacing=8),
            ft.Column([evening_enabled_switch, evening_time_field], spacing=8),
            ft.Column([workdays_only_switch], spacing=8, expand=True)
        ], spacing=32)
        
        # Test button
        test_button = ButtonGroup.create_primary_secondary(
            primary_action={
                "text": "Invia Report Test",
                "icon": ft.Icons.SEND,
                "on_click": send_test_report,
                "disabled": not settings.get('scheduled_enabled', False)
            }
        )
        
        return SettingsCardSection(
            title="Report Programmati",
            description="Configurazione dell'invio automatico di report via email",
            controls=[
                scheduled_enabled_switch,
                schedule_row,
                test_button
            ]
        )
    
    def _create_report_content_section(self) -> SettingsCardSection:
        """Create report content configuration section"""
        
        def on_content_change(content_type: str, enabled: bool):
            content_settings = self.controller.get_setting('reports').get('content_types', {})
            content_settings[content_type] = enabled
            self.controller.set_setting('reports', 'content_types', content_settings)
        
        def on_days_ahead_change(value):
            self.controller.set_setting('reports', 'days_ahead_filter', value)
        
        settings = self.controller.get_setting('reports')
        content_types = settings.get('content_types', {
            'project_completion': True,
            'overdue_items': True,
            'upcoming_deadlines': True,
            'incomplete_tasks': True,
            'client_summary': False,
            'statistics': True
        })
        
        # Content type checkboxes
        content_checkboxes = CheckboxGroup.create(
            title="Contenuto Report",
            description="Seleziona le sezioni da includere nei report automatici",
            options=[
                {
                    "label": "Percentuali Completamento Progetti",
                    "value": "project_completion",
                    "checked": content_types.get('project_completion', True),
                    "on_change": lambda e: on_content_change('project_completion', e.control.value)
                },
                {
                    "label": "Elementi in Ritardo",
                    "value": "overdue_items", 
                    "checked": content_types.get('overdue_items', True),
                    "on_change": lambda e: on_content_change('overdue_items', e.control.value)
                },
                {
                    "label": "Scadenze Imminenti",
                    "value": "upcoming_deadlines",
                    "checked": content_types.get('upcoming_deadlines', True),
                    "on_change": lambda e: on_content_change('upcoming_deadlines', e.control.value)
                },
                {
                    "label": "Attività Incomplete",
                    "value": "incomplete_tasks",
                    "checked": content_types.get('incomplete_tasks', True),
                    "on_change": lambda e: on_content_change('incomplete_tasks', e.control.value)
                },
                {
                    "label": "Riepilogo Clienti",
                    "value": "client_summary",
                    "checked": content_types.get('client_summary', False),
                    "on_change": lambda e: on_content_change('client_summary', e.control.value)
                },
                {
                    "label": "Statistiche Generali",
                    "value": "statistics",
                    "checked": content_types.get('statistics', True),
                    "on_change": lambda e: on_content_change('statistics', e.control.value)
                }
            ]
        )
        
        # Days ahead filter
        days_ahead_field = FormField.create_number_field(
            label="Giorni Anticipo Scadenze",
            value=settings.get('days_ahead_filter', 7),
            min_value=1,
            max_value=365,
            width=200,
            on_change=on_days_ahead_change,
            validation_message=self.validation_errors.get('days_ahead')
        )
        
        return SettingsCardSection(
            title="Contenuto Report",
            description="Personalizza le informazioni incluse nei report automatici",
            controls=[
                content_checkboxes,
                days_ahead_field
            ]
        )
    
    def _create_recipients_section(self) -> SettingsCardSection:
        """Create recipients configuration section"""
        
        def on_recipients_change(e):
            try:
                recipients_text = e.control.value.strip()
                if recipients_text:
                    recipients = [email.strip() for email in recipients_text.split(',') if email.strip()]
                    self.controller.set_setting('reports', 'recipients', recipients)
                else:
                    self.controller.set_setting('reports', 'recipients', [])
            except Exception as ex:
                logger.error(f"Error updating recipients: {ex}")
        
        settings = self.controller.get_setting('reports')
        recipients = settings.get('recipients', [])
        recipients_text = ", ".join(recipients)
        
        recipients_field = FormField.create_text_field(
            label="Destinatari Report",
            value=recipients_text,
            hint_text="<EMAIL>, <EMAIL>",
            validation_message=self.validation_errors.get('recipients'),
            on_change=on_recipients_change,
            prefix_icon=ft.Icons.EMAIL
        )
        
        # Recipients status
        recipient_count = len(recipients)
        status_color = ft.Colors.GREEN_600 if recipient_count > 0 else ft.Colors.ORANGE_600
        status_text = f"📧 {recipient_count} destinatar{'i' if recipient_count != 1 else 'o'} configurato/i"
        
        if recipient_count == 0:
            status_text += " (verrà usato il mittente SMTP)"
        
        status_indicator = ft.Container(
            content=ft.Row([
                ft.Icon(ft.Icons.PEOPLE, size=16, color=status_color),
                ft.Text(status_text, size=12, color=status_color)
            ], spacing=8),
            padding=ft.padding.all(8),
            bgcolor=ft.Colors.GREEN_50 if recipient_count > 0 else ft.Colors.ORANGE_50,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.GREEN_200 if recipient_count > 0 else ft.Colors.ORANGE_200)
        )
        
        return SettingsCardSection(
            title="Destinatari Report",
            description="Configura chi riceve i report automatici",
            controls=[
                recipients_field,
                status_indicator,
                InfoBox.create(
                    title="📬 Gestione Destinatari",
                    message="Informazioni sui destinatari dei report",
                    type="info",
                    items=[
                        "Separa più email con virgole",
                        "Se vuoto, invia solo al mittente SMTP configurato",
                        "Verifica che le email siano valide",
                        "Tutti i destinatari ricevono gli stessi report"
                    ]
                )
            ]
        )
    
    def _create_custom_reports_section(self) -> SettingsCardSection:
        """Create custom reports section"""
        
        def send_custom_report(e):
            """Send a custom report now"""
            try:
                self._show_success_notification("Report personalizzato in generazione...")

                # Get recipients from settings
                recipients = self.controller.get_setting('reports').get('recipients', [])
                if not recipients:
                    self._show_error_notification("Nessun destinatario configurato per i report")
                    return

                # Get the statistics service from the app instance
                app_instance = getattr(self.controller, 'app', None)
                if not app_instance:
                    self._show_error_notification("❌ Servizio applicazione non disponibile")
                    return

                # Use existing statistics service from app
                stats_service = getattr(app_instance, 'statistics_service', None)
                if not stats_service:
                    self._show_error_notification("❌ Servizio statistiche non disponibile")
                    return

                # Update email service with current settings from JSON
                email_settings = self.controller.get_setting('email')
                stats_service.email_service.smtp_config.update({
                    'smtp_server': email_settings.get('server', ''),
                    'smtp_port': email_settings.get('port', 587),
                    'smtp_username': email_settings.get('username', ''),
                    'smtp_password': email_settings.get('password', ''),
                    'smtp_use_tls': email_settings.get('use_tls', True),
                    'from_name': email_settings.get('sender_name', 'Agevolami PM'),
                    'from_email': email_settings.get('sender_email', ''),
                    'enabled': bool(email_settings.get('server'))  # Enable if server is set
                })

                logger.info(f"📧 Stats service email configured with: server={email_settings.get('server')}, username={email_settings.get('username')}")

                # Create custom filters based on current settings
                content_types = self.controller.get_setting('reports').get('content_types', {})
                days_ahead = self.controller.get_setting('reports').get('days_ahead_filter', 7)

                filters = {
                    'include_clients': content_types.get('client_summary', True),
                    'include_projects': content_types.get('project_completion', True),
                    'include_tasks': content_types.get('incomplete_tasks', True),
                    'include_deadlines': content_types.get('upcoming_deadlines', True),
                    'include_expired_only': False,
                    'days_ahead': days_ahead
                }

                # First test email configuration
                logger.info(f"📧 Testing email configuration before sending report...")
                email_test_success = stats_service.email_service.test_connection()

                if not email_test_success:
                    self._show_error_notification("❌ Configurazione email non valida. Verifica le impostazioni SMTP.")
                    return

                logger.info(f"✅ Email configuration test passed")

                # Send the custom report
                logger.info(f"📧 Sending custom report to {len(recipients)} recipients: {recipients}")
                success = stats_service.send_custom_report_email(recipients, filters)

                if success:
                    self._show_success_notification(f"✅ Report personalizzato inviato a {len(recipients)} destinatari!")
                    logger.info(f"✅ Custom report sent successfully to {recipients}")
                else:
                    self._show_error_notification("❌ Errore durante l'invio del report personalizzato")
                    logger.error(f"❌ Failed to send custom report to {recipients}")

            except Exception as ex:
                logger.error(f"Errore invio report personalizzato: {ex}")
                self._show_error_notification(f"❌ Errore: {str(ex)}")

        def preview_custom_report(e):
            """Preview custom report"""
            try:
                self._show_success_notification("Anteprima report in preparazione...")

                # Get the statistics service from the app instance for preview
                app_instance = getattr(self.controller, 'app', None)
                if not app_instance:
                    self._show_error_notification("❌ Servizio applicazione non disponibile")
                    return

                # Use existing statistics service from app
                stats_service = getattr(app_instance, 'statistics_service', None)
                if not stats_service:
                    self._show_error_notification("❌ Servizio statistiche non disponibile")
                    return

                # Update email service with current settings from JSON (for preview generation)
                email_settings = self.controller.get_setting('email')
                stats_service.email_service.smtp_config.update({
                    'smtp_server': email_settings.get('server', ''),
                    'smtp_port': email_settings.get('port', 587),
                    'smtp_username': email_settings.get('username', ''),
                    'smtp_password': email_settings.get('password', ''),
                    'smtp_use_tls': email_settings.get('use_tls', True),
                    'from_name': email_settings.get('sender_name', 'Agevolami PM'),
                    'from_email': email_settings.get('sender_email', ''),
                    'enabled': bool(email_settings.get('server'))
                })

                # Generate custom report data
                content_types = self.controller.get_setting('reports').get('content_types', {})
                days_ahead = self.controller.get_setting('reports').get('days_ahead_filter', 7)

                filters = {
                    'include_clients': content_types.get('client_summary', True),
                    'include_projects': content_types.get('project_completion', True),
                    'include_tasks': content_types.get('incomplete_tasks', True),
                    'include_deadlines': content_types.get('upcoming_deadlines', True),
                    'days_ahead': days_ahead
                }

                report = stats_service.generate_custom_report(filters)

                if report:
                    summary = report.get('summary', {})
                    self._show_success_notification(f"📊 Anteprima: {summary.get('total_clients', 0)} clienti, {summary.get('total_projects', 0)} progetti, {summary.get('total_tasks', 0)} task")
                else:
                    self._show_error_notification("❌ Errore nella generazione dell'anteprima")

            except Exception as ex:
                logger.error(f"Errore anteprima report: {ex}")
                self._show_error_notification(f"❌ Errore anteprima: {str(ex)}")

        def export_data(e):
            """Export raw data"""
            self._show_success_notification("Esportazione dati avviata...")

        def test_email_config(e):
            """Test email configuration"""
            try:
                self._show_success_notification("Test configurazione email...")

                # Get app instance and use existing email service
                app_instance = getattr(self.controller, 'app', None)
                if not app_instance:
                    self._show_error_notification("❌ Servizio applicazione non disponibile")
                    return

                # Use existing statistics service which has email service
                stats_service = getattr(app_instance, 'statistics_service', None)
                if not stats_service:
                    self._show_error_notification("❌ Servizio statistiche non disponibile")
                    return

                # Update email service with current settings from JSON
                email_settings = self.controller.get_setting('email')
                stats_service.email_service.smtp_config.update({
                    'smtp_server': email_settings.get('server', ''),
                    'smtp_port': email_settings.get('port', 587),
                    'smtp_username': email_settings.get('username', ''),
                    'smtp_password': email_settings.get('password', ''),
                    'smtp_use_tls': email_settings.get('use_tls', True),
                    'from_name': email_settings.get('sender_name', 'Agevolami PM'),
                    'from_email': email_settings.get('sender_email', ''),
                    'enabled': bool(email_settings.get('server'))  # Enable if server is set
                })

                logger.info(f"📧 Email service configured with: server={email_settings.get('server')}, username={email_settings.get('username')}")

                # Get test recipient from reports settings
                recipients = self.controller.get_setting('reports').get('recipients', [])
                if not recipients:
                    self._show_error_notification("❌ Nessun destinatario configurato per i test")
                    return

                test_recipient = recipients[0]
                logger.info(f"📧 Testing email to: {test_recipient}")

                # Test connection first
                if not stats_service.email_service.test_connection():
                    self._show_error_notification("❌ Test connessione SMTP fallito")
                    return

                # Send test email
                success = stats_service.email_service.send_test_email(test_recipient)

                if success:
                    self._show_success_notification(f"✅ Email di test inviata a {test_recipient}")
                else:
                    self._show_error_notification("❌ Errore invio email di test")

            except Exception as ex:
                logger.error(f"Errore test email: {ex}")
                self._show_error_notification(f"❌ Errore test: {str(ex)}")
        
        # Custom report actions
        custom_actions = ButtonGroup.create_action_group([
            {
                "text": "Invia Report Personalizzato",
                "icon": ft.Icons.SEND,
                "on_click": send_custom_report,
                "style": "primary"
            },
            {
                "text": "Test Email",
                "icon": ft.Icons.EMAIL,
                "on_click": test_email_config,
                "style": "success"
            },
            {
                "text": "Anteprima",
                "icon": ft.Icons.PREVIEW,
                "on_click": preview_custom_report,
                "style": "secondary"
            },
            {
                "text": "Esporta Dati",
                "icon": ft.Icons.DOWNLOAD,
                "on_click": export_data,
                "style": "secondary"
            }
        ])
        
        # Report stats (would be real data in implementation)
        stats_info = InfoBox.create(
            title="📊 Statistiche Rapide",
            message="Panoramica dati disponibili per i report",
            type="success",
            items=[
                "15 progetti attivi con 42 task totali",
                "3 scadenze in arrivo nei prossimi 7 giorni",
                "2 task in ritardo che richiedono attenzione",
                "85% tasso di completamento generale"
            ]
        )
        
        return SettingsCardSection(
            title="Report Personalizzati",
            description="Genera e invia report su richiesta con filtri personalizzati",
            controls=[
                stats_info,
                custom_actions
            ]
        )
    
    def _show_success_notification(self, message: str):
        """Show success notification"""
        logger.info(f"SUCCESS: {message}")
    
    def _show_error_notification(self, message: str):
        """Show error notification"""
        logger.error(f"ERROR: {message}")
    
    def build(self) -> SettingsCard:
        """Build the reports settings card"""
        
        # Validate current settings
        self._validate_settings()
        
        # Create sections
        scheduled_section = self._create_scheduled_reports_section()
        content_section = self._create_report_content_section()
        recipients_section = self._create_recipients_section()
        custom_section = self._create_custom_reports_section()
        
        # Create card content
        card_content = [
            scheduled_section.build(),
            content_section.build(),
            recipients_section.build(),
            custom_section.build()
        ]
        
        # Create save/reset actions
        def save_settings(e):
            if self._validate_settings():
                success = self.controller.save_settings()
                if success:
                    self._show_success_notification("Impostazioni report salvate!")
                else:
                    self._show_error_notification("Errore durante il salvataggio")
            else:
                self._show_error_notification("Correggi gli errori prima di salvare")
        
        def reset_settings(e):
            self.controller.reset_to_defaults('reports')
            self._show_success_notification("Impostazioni report ripristinate")
        
        card_actions = [
            ft.ElevatedButton(
                text="Salva Report",
                icon=ft.Icons.SAVE,
                on_click=save_settings,
                bgcolor=ft.Colors.GREEN_600,
                color=ft.Colors.WHITE,
                disabled=len(self.validation_errors) > 0
            ),
            ft.OutlinedButton(
                text="Ripristina",
                icon=ft.Icons.RESTORE,
                on_click=reset_settings
            )
        ]
        
        # Determine card status
        settings = self.controller.get_setting('reports')
        
        if self.validation_errors:
            card_icon = ft.Icons.ERROR_OUTLINE
            card_icon_color = ft.Colors.RED_600
        elif settings.get('scheduled_enabled'):
            card_icon = ft.Icons.SCHEDULE_SEND
            card_icon_color = ft.Colors.GREEN_600
        else:
            card_icon = ft.Icons.ASSESSMENT
            card_icon_color = ft.Colors.GREY_600
        
        return SettingsCard(
            title="Sistema Report e Analisi",
            description="Configura l'invio automatico di report e genera analisi personalizzate",
            icon=card_icon,
            icon_color=card_icon_color,
            expanded=True,
            content=card_content,
            actions=card_actions,
            accent_color=ft.Colors.GREEN_600
        ) 