#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Google Drive Service per Agevolami PM
"""

import os
import json
import pickle
import sqlite3
import zipfile
import tempfile
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
from io import BytesIO

try:
    from google.oauth2.credentials import Credentials
    from google_auth_oauthlib.flow import InstalledAppFlow
    from google.auth.transport.requests import Request
    from googleapiclient.discovery import build
    from googleapiclient.http import MediaFileUpload, MediaIoBaseDownload
    GOOGLE_AVAILABLE = True
except ImportError:
    GOOGLE_AVAILABLE = False

from ..utils import get_logger

logger = get_logger(__name__)

class GoogleDriveService:
    """Servizio per backup su Google Drive"""
    
    SCOPES = ['https://www.googleapis.com/auth/drive.file']
    APP_FOLDER_NAME = 'Agevolami PM Backups'
    
    # Embedded client credentials for seamless authentication
    EMBEDDED_CREDENTIALS = {
        "installed": **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    }
    
    def __init__(self, config_dir: Path):
        # Ensure consistent config directory structure
        if isinstance(config_dir, str):
            config_dir = Path(config_dir)

        if not str(config_dir).endswith("config"):
            self.config_dir = config_dir / "config"
        else:
            self.config_dir = config_dir
        self.config_dir.mkdir(parents=True, exist_ok=True)

        self.credentials_file = self.config_dir / 'google_credentials.json'  # Keep for backward compatibility
        self.token_file = self.config_dir / 'google_drive_token.pickle'  # Separate token file for Drive
        self.service = None
        self.app_folder_id = None
        self.is_authenticated = False
        
        if not GOOGLE_AVAILABLE:
            logger.warning("Google API libraries non disponibili. Installare: pip install google-api-python-client google-auth-oauthlib")
            return
        
        # Only check existing auth, don't auto-authenticate
        self._check_existing_auth()
    
    def _check_existing_auth(self) -> bool:
        """Check if there's existing valid authentication without triggering new auth"""
        try:
            if not self.token_file.exists():
                return False
                
            with open(self.token_file, 'rb') as token:
                creds = pickle.load(token)
            
            # Check if credentials are valid
            if creds and creds.valid:
                self.service = build('drive', 'v3', credentials=creds)
                self.is_authenticated = True
                self._ensure_app_folder()
                logger.info("Google Drive: Existing authentication found and valid")
                return True
            elif creds and creds.expired and creds.refresh_token:
                try:
                    creds.refresh(Request())
                    with open(self.token_file, 'wb') as token:
                        pickle.dump(creds, token)
                    
                    self.service = build('drive', 'v3', credentials=creds)
                    self.is_authenticated = True
                    self._ensure_app_folder()
                    logger.info("Google Drive: Authentication refreshed successfully")
                    return True
                except Exception as e:
                    logger.warning(f"Google Drive: Token refresh failed: {e}")
                    return False
            else:
                return False
                
        except Exception as e:
            logger.warning(f"Google Drive: Error checking existing auth: {e}")
            return False
    
    def authenticate(self, credentials_content: str = None) -> bool:
        """Autentica con Google Drive usando le credenziali embedded o fornite"""
        if not GOOGLE_AVAILABLE:
            self._show_google_setup_instructions()
            return False
            
        try:
            credentials_data = None
            
            # Use embedded credentials by default
            if not credentials_content:
                credentials_data = self.EMBEDDED_CREDENTIALS
                logger.info("Using embedded Google Drive credentials")
            else:
                # Use provided credentials (backward compatibility)
                with open(self.credentials_file, 'w') as f:
                    f.write(credentials_content)
                credentials_file_path = str(self.credentials_file)
            
            if credentials_data:
                # Create temporary credentials file from embedded data
                with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_file:
                    json.dump(credentials_data, temp_file)
                    credentials_file_path = temp_file.name
            
            try:
                # Esegue il flusso OAuth
                flow = InstalledAppFlow.from_client_secrets_file(
                    credentials_file_path, self.SCOPES)
                creds = flow.run_local_server(port=0)
                
                # Salva il token
                with open(self.token_file, 'wb') as token:
                    pickle.dump(creds, token)
                
                # Inizializza il servizio
                self.service = build('drive', 'v3', credentials=creds)
                self.is_authenticated = True
                
                # Crea cartella dell'app
                self._ensure_app_folder()
                
                logger.info("[SUCCESS] Google Drive autenticato con successo")
                return True
                
            finally:
                # Clean up temporary file if we created one
                if credentials_data and os.path.exists(credentials_file_path):
                    try:
                        os.unlink(credentials_file_path)
                    except:
                        pass
            
        except Exception as e:
            logger.error(f"Errore autenticazione Google Drive: {e}")
            return False
    
    def _show_google_setup_instructions(self):
        """Mostra istruzioni per installare le librerie Google"""
        logger.info("""
        [SETUP] Per utilizzare Google Drive backup, installa le librerie necessarie:

        pip install google-api-python-client google-auth-oauthlib google-auth

        E riavvia l'applicazione.
        """)
    
    def _ensure_app_folder(self):
        """Assicura che esista la cartella dell'app su Google Drive"""
        if not self.service:
            return

        try:
            # Cerca cartella esistente - cerca in tutti gli spazi
            results = self.service.files().list(
                q=f"name='{self.APP_FOLDER_NAME}' and mimeType='application/vnd.google-apps.folder' and trashed=false",
                spaces='drive',
                fields='files(id,name,parents)'
            ).execute()

            folders = results.get('files', [])

            if folders:
                self.app_folder_id = folders[0]['id']
                logger.info(f"[FOLDER] Cartella trovata: {self.APP_FOLDER_NAME} (ID: {self.app_folder_id})")

                # Verifica che la cartella sia visibile
                try:
                    folder_info = self.service.files().get(
                        fileId=self.app_folder_id,
                        fields='id,name,parents,shared,capabilities'
                    ).execute()
                    logger.debug(f"[FOLDER] Info cartella: {folder_info}")
                except Exception as e:
                    logger.warning(f"Impossibile verificare info cartella: {e}")

            else:
                # Crea nuova cartella nella root di Drive (visibile)
                folder_metadata = {
                    'name': self.APP_FOLDER_NAME,
                    'mimeType': 'application/vnd.google-apps.folder',
                    'parents': ['root']  # Esplicitamente nella root
                }

                folder = self.service.files().create(
                    body=folder_metadata,
                    fields='id,name,parents'
                ).execute()
                self.app_folder_id = folder.get('id')
                logger.info(f"[FOLDER] Cartella creata nella root: {self.APP_FOLDER_NAME} (ID: {self.app_folder_id})")

        except Exception as e:
            logger.error(f"Errore creazione cartella Google Drive: {e}")
    
    def create_backup(self, database_path: Path, data_dir: Path, retention_days: int = 30) -> Optional[str]:
        """Crea un backup completo su Google Drive con gestione automatica della retention"""
        if not self.is_authenticated or not self.service:
            logger.error("Google Drive non autenticato")
            return None

        try:
            # Verifica spazio disponibile prima del backup
            storage_info = self.get_storage_info()
            if storage_info and storage_info.get('available_space', 0) < 100 * 1024 * 1024:  # 100MB
                logger.warning("Spazio Google Drive insufficiente (< 100MB)")
                # Prova a pulire vecchi backup
                self._cleanup_old_backups(retention_days)

            # Crea backup locale temporaneo
            backup_path = self._create_local_backup(database_path, data_dir)
            if not backup_path:
                return None

            # Verifica integrità del backup locale
            if not self._verify_backup_integrity(backup_path):
                logger.error("Backup locale corrotto, operazione annullata")
                try:
                    os.unlink(backup_path)
                except Exception as e:
                    logger.warning(f"Impossibile eliminare backup corrotto: {e}")
                return None

            # Carica su Google Drive
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"agevolami_backup_{timestamp}.zip"

            # Verifica dimensione del backup locale
            backup_size = backup_path.stat().st_size
            logger.info(f"[BACKUP] Backup locale: {backup_size} bytes ({backup_size / 1024:.1f} KB)")

            # Aggiungi metadata per identificare i backup dell'app
            file_metadata = {
                'name': backup_name,
                'parents': [self.app_folder_id] if self.app_folder_id else ['root'],
                'description': f'Agevolami PM Backup - Created: {datetime.now().isoformat()}',
                'properties': {
                    'app_name': 'Agevolami PM',
                    'backup_type': 'full',
                    'version': '2.0',
                    'created_timestamp': str(int(datetime.now().timestamp())),
                    'local_size': str(backup_size)
                }
            }

            # Usa upload semplice per file piccoli, resumable per file grandi
            if backup_size < 5 * 1024 * 1024:  # 5MB
                media = MediaFileUpload(str(backup_path), resumable=False)
            else:
                media = MediaFileUpload(str(backup_path), resumable=True)

            logger.info(f"[UPLOAD] Caricamento backup su Google Drive...")
            file = self.service.files().create(
                body=file_metadata,
                media_body=media,
                fields='id,name,size,createdTime,properties,parents'
            ).execute()

            # Verifica che il file sia stato caricato correttamente
            uploaded_size = int(file.get('size', 0))
            logger.info(f"[UPLOAD] File caricato: {uploaded_size} bytes")

            # Rimuovi backup locale temporaneo
            try:
                os.unlink(backup_path)
                logger.debug("Backup locale temporaneo eliminato")
            except Exception as e:
                logger.warning(f"Impossibile eliminare backup temporaneo: {e}")

            # Pulisci automaticamente i backup vecchi
            self._cleanup_old_backups(retention_days)

            logger.info(f"[SUCCESS] Backup caricato su Google Drive: {backup_name}")
            logger.info(f"[BACKUP] Backup ID: {file.get('id')}")

            return file.get('id')

        except Exception as e:
            logger.error(f"Errore creazione backup Google Drive: {e}")
            return None
    
    def _create_local_backup(self, database_path: Path, data_dir: Path) -> Optional[Path]:
        """Crea un backup locale completo (database + file)"""
        try:
            # Crea file zip temporaneo
            temp_dir = Path(tempfile.gettempdir())
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = temp_dir / f"agevolami_backup_{timestamp}.zip"
            
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # Aggiungi database
                logger.debug(f"Percorso database: {database_path}")
                logger.debug(f"Database esiste: {database_path.exists()}")

                if database_path.exists():
                    zipf.write(database_path, 'database.db')
                    logger.debug(f"Database aggiunto al backup: {database_path.name}")
                else:
                    logger.warning(f"Database non trovato: {database_path}")

                # Aggiungi file di configurazione
                config_files = [
                    'settings.json',
                    'app_config.json'
                ]
                
                for config_file in config_files:
                    config_path = data_dir / config_file
                    if config_path.exists():
                        zipf.write(config_path, f'config/{config_file}')
                        logger.debug(f"File di configurazione aggiunto: {config_file}")
                    else:
                        logger.debug(f"File di configurazione non trovato: {config_path}")
                
                # Aggiungi logs recenti (ultimi 7 giorni)
                logs_dir = data_dir.parent / 'logs'
                if logs_dir.exists():
                    for log_file in logs_dir.glob('*.log'):
                        # Controlla data modifica
                        if (datetime.now() - datetime.fromtimestamp(log_file.stat().st_mtime)).days <= 7:
                            zipf.write(log_file, f'logs/{log_file.name}')
                
                # Aggiungi documenti se esistono
                documents_dir = data_dir / 'documents'
                if documents_dir.exists():
                    for doc_file in documents_dir.rglob('*'):
                        if doc_file.is_file():
                            rel_path = doc_file.relative_to(data_dir)
                            zipf.write(doc_file, str(rel_path))
                            logger.debug(f"Documento aggiunto al backup: {rel_path}")

                # Aggiungi assets se esistono
                assets_dir = data_dir.parent / 'assets'
                if assets_dir.exists():
                    for asset_file in assets_dir.rglob('*'):
                        if asset_file.is_file():
                            rel_path = asset_file.relative_to(assets_dir)
                            zipf.write(asset_file, f'assets/{rel_path}')

            # Log riassuntivo del backup creato
            with zipfile.ZipFile(backup_path, 'r') as zipf:
                files_in_backup = zipf.namelist()
                logger.info(f"Backup locale creato con {len(files_in_backup)} file: {files_in_backup}")

            return backup_path
            
        except Exception as e:
            logger.error(f"Errore creazione backup locale: {e}")
            return None
    
    def list_backups(self) -> List[Dict[str, Any]]:
        """Lista tutti i backup disponibili su Google Drive con informazioni dettagliate"""
        if not self.is_authenticated or not self.service:
            return []

        try:
            query = f"parents in '{self.app_folder_id}'" if self.app_folder_id else ""
            query += " and name contains 'agevolami_backup'"

            results = self.service.files().list(
                q=query,
                orderBy='createdTime desc',
                fields='files(id,name,size,createdTime,modifiedTime,description,properties)'
            ).execute()

            backups = []
            for file in results.get('files', []):
                # Calcola età del backup
                created_time = datetime.fromisoformat(file['createdTime'].replace('Z', '+00:00'))
                age_days = (datetime.now(created_time.tzinfo) - created_time).days

                backup_info = {
                    'id': file['id'],
                    'name': file['name'],
                    'size': int(file['size']),
                    'size_mb': round(int(file['size']) / (1024 * 1024), 2),
                    'created': file['createdTime'],
                    'modified': file['modifiedTime'],
                    'age_days': age_days,
                    'description': file.get('description', ''),
                    'properties': file.get('properties', {}),
                    'is_recent': age_days <= 7,
                    'is_old': age_days > 30
                }

                backups.append(backup_info)

            logger.debug(f"Trovati {len(backups)} backup su Google Drive")
            return backups

        except Exception as e:
            logger.error(f"Errore lista backup Google Drive: {e}")
            return []
    
    def restore_backup(self, backup_id: str, target_dir: Path) -> bool:
        """Ripristina un backup da Google Drive"""
        if not self.is_authenticated or not self.service:
            logger.error("[RESTORE] Google Drive non autenticato")
            return False

        try:
            logger.info(f"[RESTORE] Inizio download backup ID: {backup_id}")
            logger.info(f"[RESTORE] Directory target: {target_dir}")

            # Scarica il backup
            request = self.service.files().get_media(fileId=backup_id)

            # Crea file temporaneo
            temp_dir = Path(tempfile.gettempdir())
            temp_backup = temp_dir / f"restore_backup_{backup_id}.zip"
            logger.info(f"[RESTORE] File temporaneo: {temp_backup}")

            # Download con progress tracking
            with open(temp_backup, 'wb') as f:
                downloader = MediaIoBaseDownload(f, request)
                done = False
                while done is False:
                    status, done = downloader.next_chunk()
                    if status:
                        logger.debug(f"[RESTORE] Download progress: {int(status.progress() * 100)}%")

            # Verifica dimensione file scaricato
            download_size = temp_backup.stat().st_size
            logger.info(f"[RESTORE] File scaricato: {download_size} bytes")

            # Estrai il backup
            logger.info(f"[RESTORE] Estrazione backup in: {target_dir}")
            with zipfile.ZipFile(temp_backup, 'r') as zipf:
                file_list = zipf.namelist()
                logger.info(f"[RESTORE] File nel backup: {file_list}")
                zipf.extractall(target_dir)
                logger.info(f"[RESTORE] Estrazione completata")

            # Verifica file estratti
            extracted_files = list(target_dir.rglob("*"))
            logger.info(f"[RESTORE] File estratti: {[f.name for f in extracted_files if f.is_file()]}")

            # Rimuovi file temporaneo
            os.unlink(temp_backup)
            logger.info(f"[RESTORE] File temporaneo eliminato")

            logger.info(f"[SUCCESS] Backup ripristinato da Google Drive")
            return True

        except Exception as e:
            logger.error(f"[RESTORE] Errore ripristino backup Google Drive: {e}")
            import traceback
            logger.error(f"[RESTORE] Traceback: {traceback.format_exc()}")
            return False
    
    def delete_backup(self, backup_id: str) -> bool:
        """Elimina un backup da Google Drive"""
        if not self.is_authenticated or not self.service:
            return False
            
        try:
            self.service.files().delete(fileId=backup_id).execute()
            logger.info(f"[SUCCESS] Backup eliminato da Google Drive: {backup_id}")
            return True
            
        except Exception as e:
            logger.error(f"Errore eliminazione backup Google Drive: {e}")
            return False
    
    def get_storage_info(self) -> Dict[str, Any]:
        """Ottiene informazioni sullo storage di Google Drive"""
        if not self.is_authenticated or not self.service:
            return {}
            
        try:
            about = self.service.about().get(fields='storageQuota,user').execute()
            
            storage = about.get('storageQuota', {})
            user = about.get('user', {})
            
            return {
                'user_email': user.get('emailAddress', 'N/A'),
                'total_space': int(storage.get('limit', 0)),
                'used_space': int(storage.get('usage', 0)),
                'available_space': int(storage.get('limit', 0)) - int(storage.get('usage', 0))
            }
            
        except Exception as e:
            logger.error(f"Errore info storage Google Drive: {e}")
            return {}
    
    def disconnect(self):
        """Disconnette da Google Drive"""
        try:
            if self.token_file.exists():
                os.unlink(self.token_file)
            if self.credentials_file.exists():
                os.unlink(self.credentials_file)
            
            self.service = None
            self.is_authenticated = False
            self.app_folder_id = None
            
            logger.info("🔌 Disconnesso da Google Drive")
            return True
            
        except Exception as e:
            logger.error(f"Errore disconnessione Google Drive: {e}")
            return False
    
    def test_connection(self) -> bool:
        """Testa la connessione a Google Drive"""
        if not self.is_authenticated or not self.service:
            return False
            
        try:
            # Prova a listare i file nella cartella dell'app
            self.service.files().list(
                q=f"parents in '{self.app_folder_id}'" if self.app_folder_id else "",
                pageSize=1
            ).execute()
            
            return True
            
        except Exception as e:
            logger.error(f"Errore test connessione Google Drive: {e}")
            return False

    def _verify_backup_integrity(self, backup_path: Path) -> bool:
        """Verifica l'integrità di un backup locale"""
        try:
            with zipfile.ZipFile(backup_path, 'r') as zipf:
                # Testa l'integrità del file ZIP
                bad_file = zipf.testzip()
                if bad_file:
                    logger.error(f"File corrotto nel backup: {bad_file}")
                    return False

                # Verifica che contenga almeno il database
                files_in_zip = zipf.namelist()
                logger.debug(f"File nel backup: {files_in_zip}")

                # Cerca il database con diversi nomi possibili
                database_files = [f for f in files_in_zip if f.endswith('.db') or f == 'database.db']

                if not database_files:
                    # Se non c'è database, verifica almeno che ci siano file di configurazione
                    config_files = [f for f in files_in_zip if f.startswith('config/') and f.endswith('.json')]
                    if not config_files:
                        logger.error(f"Backup vuoto o corrotto. File presenti: {files_in_zip}")
                        return False
                    else:
                        logger.warning(f"Database non presente nel backup, ma trovati file di configurazione: {config_files}")
                        return True  # Accetta backup senza database se ha configurazioni

                # Usa il primo database trovato
                db_file = database_files[0]
                logger.debug(f"Database trovato: {db_file}")

                # Verifica dimensione minima del database
                db_info = zipf.getinfo(db_file)
                if db_info.file_size < 1024:  # Almeno 1KB
                    logger.error(f"Database troppo piccolo nel backup: {db_info.file_size} bytes")
                    return False

                logger.debug(f"Backup verificato: {len(files_in_zip)} file, database {db_file} ({db_info.file_size} bytes)")
                return True

        except Exception as e:
            logger.error(f"Errore verifica integrità backup: {e}")
            return False

    def _cleanup_old_backups(self, retention_days: int = 30) -> Dict[str, Any]:
        """Pulisce automaticamente i backup più vecchi del periodo di retention"""
        if not self.is_authenticated or not self.service:
            return {"success": False, "error": "Not authenticated"}

        results = {
            "success": True,
            "deleted_count": 0,
            "freed_space": 0,
            "errors": [],
            "total_checked": 0
        }

        try:
            # Calcola data limite
            cutoff_date = datetime.now() - timedelta(days=retention_days)
            cutoff_timestamp = cutoff_date.timestamp()

            # Lista tutti i backup
            backups = self.list_backups()
            results["total_checked"] = len(backups)

            for backup in backups:
                try:
                    # Converte la data di creazione
                    created_time = datetime.fromisoformat(backup['created'].replace('Z', '+00:00'))

                    # Se il backup è più vecchio del periodo di retention
                    if created_time.timestamp() < cutoff_timestamp:
                        # Elimina il backup
                        if self.delete_backup(backup['id']):
                            results["deleted_count"] += 1
                            results["freed_space"] += backup['size']
                            logger.info(f"Eliminato backup vecchio: {backup['name']}")
                        else:
                            results["errors"].append(f"Errore eliminazione: {backup['name']}")

                except Exception as e:
                    results["errors"].append(f"Errore processamento backup {backup.get('name', 'unknown')}: {str(e)}")

            if results["deleted_count"] > 0:
                logger.info(f"Cleanup completato: {results['deleted_count']} backup eliminati, "
                           f"{results['freed_space'] / (1024*1024):.1f}MB liberati")
            else:
                logger.debug("Nessun backup da eliminare")

            return results

        except Exception as e:
            logger.error(f"Errore cleanup backup: {e}")
            return {"success": False, "error": str(e)}

    def get_backup_statistics(self) -> Dict[str, Any]:
        """Ottiene statistiche dettagliate sui backup"""
        if not self.is_authenticated or not self.service:
            return {"success": False, "error": "Not authenticated"}

        try:
            backups = self.list_backups()

            if not backups:
                return {
                    "success": True,
                    "total_backups": 0,
                    "total_size_mb": 0,
                    "oldest_backup": None,
                    "newest_backup": None,
                    "recent_backups": 0,
                    "old_backups": 0,
                    "average_size_mb": 0
                }

            total_size = sum(backup['size'] for backup in backups)
            recent_count = sum(1 for backup in backups if backup['is_recent'])
            old_count = sum(1 for backup in backups if backup['is_old'])

            stats = {
                "success": True,
                "total_backups": len(backups),
                "total_size_mb": round(total_size / (1024 * 1024), 2),
                "oldest_backup": backups[-1] if backups else None,
                "newest_backup": backups[0] if backups else None,
                "recent_backups": recent_count,
                "old_backups": old_count,
                "average_size_mb": round((total_size / len(backups)) / (1024 * 1024), 2)
            }

            return stats

        except Exception as e:
            logger.error(f"Errore statistiche backup: {e}")
            return {"success": False, "error": str(e)}

    def manual_cleanup_old_backups(self, retention_days: int = 30) -> Dict[str, Any]:
        """Metodo pubblico per pulizia manuale dei backup vecchi"""
        logger.info(f"Avvio pulizia manuale backup più vecchi di {retention_days} giorni")
        return self._cleanup_old_backups(retention_days)

    def fix_backup_visibility(self) -> Dict[str, Any]:
        """Corregge la visibilità dei backup esistenti"""
        if not self.is_authenticated or not self.service:
            return {"success": False, "error": "Not authenticated"}

        results = {
            "success": True,
            "checked": 0,
            "moved": 0,
            "errors": []
        }

        try:
            # Cerca tutti i backup dell'app ovunque in Drive
            all_backups_query = "name contains 'agevolami_backup' and trashed=false"

            all_results = self.service.files().list(
                q=all_backups_query,
                fields='files(id,name,parents,size,createdTime)'
            ).execute()

            all_backups = all_results.get('files', [])
            results["checked"] = len(all_backups)

            logger.info(f"[SEARCH] Trovati {len(all_backups)} backup totali in Drive")

            # Assicurati che la cartella app esista
            self._ensure_app_folder()

            for backup in all_backups:
                try:
                    current_parents = backup.get('parents', [])

                    # Se il backup non è nella cartella corretta
                    if self.app_folder_id not in current_parents:
                        logger.info(f"[MOVE] Spostamento backup: {backup['name']}")

                        # Sposta il backup nella cartella corretta
                        self.service.files().update(
                            fileId=backup['id'],
                            addParents=self.app_folder_id,
                            removeParents=','.join(current_parents),
                            fields='id,parents'
                        ).execute()

                        results["moved"] += 1
                        logger.info(f"[SUCCESS] Backup spostato: {backup['name']}")

                except Exception as e:
                    error_msg = f"Errore spostamento {backup.get('name', 'unknown')}: {str(e)}"
                    results["errors"].append(error_msg)
                    logger.error(error_msg)

            logger.info(f"[FIX] Fix visibilità completato: {results['moved']}/{results['checked']} backup spostati")
            return results

        except Exception as e:
            logger.error(f"Errore fix visibilità backup: {e}")
            return {"success": False, "error": str(e)}

    def diagnose_folder_visibility(self) -> Dict[str, Any]:
        """Diagnostica problemi di visibilità della cartella"""
        if not self.is_authenticated or not self.service:
            return {"success": False, "error": "Not authenticated"}

        results = {
            "success": True,
            "folder_info": {},
            "backups_in_folder": [],
            "all_backups": [],
            "issues": []
        }

        try:
            # 1. Info cartella app
            if self.app_folder_id:
                try:
                    folder_info = self.service.files().get(
                        fileId=self.app_folder_id,
                        fields='id,name,parents,webViewLink,shared,capabilities,trashed'
                    ).execute()

                    results["folder_info"] = {
                        "id": folder_info.get('id'),
                        "name": folder_info.get('name'),
                        "parents": folder_info.get('parents', []),
                        "webViewLink": folder_info.get('webViewLink'),
                        "shared": folder_info.get('shared', False),
                        "trashed": folder_info.get('trashed', False),
                        "capabilities": folder_info.get('capabilities', {})
                    }

                    # Controlla se la cartella è nel cestino
                    if folder_info.get('trashed'):
                        results["issues"].append("Cartella nel cestino")

                    # Controlla se la cartella è nella root
                    parents = folder_info.get('parents', [])
                    if 'root' not in parents:
                        results["issues"].append(f"Cartella non nella root: {parents}")

                except Exception as e:
                    results["issues"].append(f"Errore info cartella: {e}")

            # 2. Backup nella cartella app
            try:
                app_results = self.service.files().list(
                    q=f"parents in '{self.app_folder_id}' and name contains 'agevolami_backup' and trashed=false",
                    fields='files(id,name,size,createdTime,webViewLink)'
                ).execute()

                results["backups_in_folder"] = app_results.get('files', [])

            except Exception as e:
                results["issues"].append(f"Errore lista backup cartella: {e}")

            # 3. Tutti i backup ovunque
            try:
                all_results = self.service.files().list(
                    q="name contains 'agevolami_backup' and trashed=false",
                    fields='files(id,name,parents,size,createdTime,webViewLink)'
                ).execute()

                results["all_backups"] = all_results.get('files', [])

            except Exception as e:
                results["issues"].append(f"Errore lista tutti backup: {e}")

            # 4. Analisi discrepanze
            folder_backup_ids = {b['id'] for b in results["backups_in_folder"]}
            all_backup_ids = {b['id'] for b in results["all_backups"]}

            missing_from_folder = all_backup_ids - folder_backup_ids
            if missing_from_folder:
                results["issues"].append(f"Backup non nella cartella: {len(missing_from_folder)}")

            # 5. Controlla dimensioni 0
            zero_size_backups = [b for b in results["all_backups"] if int(b.get('size', 0)) == 0]
            if zero_size_backups:
                results["issues"].append(f"Backup con dimensione 0: {len(zero_size_backups)}")

            logger.info(f"[DIAG] Diagnosi cartella completata: {len(results['issues'])} problemi trovati")
            return results

        except Exception as e:
            logger.error(f"Errore diagnosi cartella: {e}")
            return {"success": False, "error": str(e)}