name: Build and Release

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to release (e.g., v1.0.1)'
        required: true
        default: 'v1.0.1'

permissions:
  contents: write
  packages: write
  deployments: write

jobs:
  build-windows:
    runs-on: windows-latest

    env:
      PYTHONIOENCODING: utf-8
      PYTHONUTF8: 1
      PYTHONLEGACYWINDOWSSTDIO: 1

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~\AppData\Local\pip\Cache
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements*.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    - name: Clear pip cache and install dependencies
      run: |
        python -m pip install --upgrade pip
        python -m pip cache purge
        pip install -r requirements-build.txt --verbose

    - name: Install additional build tools
      run: |
        pip install cookiecutter wheel setuptools --upgrade

    - name: Verify installation
      run: |
        python -c "import flet; print('Flet module loaded successfully')"
        python -c "import sys; print(f'Python version: {sys.version}')"
        flet --version

    - name: Build Windows executable
      run: |
        python scripts/build_windows.py
        
    - name: Create version info
      run: |
        echo "VERSION=${{ github.ref_name }}" >> version.txt
        echo "BUILD_DATE=$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" >> version.txt
        echo "COMMIT_SHA=${{ github.sha }}" >> version.txt
        
    - name: Package release
      run: |
        mkdir release
        if (Test-Path "build\windows") {
          Copy-Item -Path "build\windows\*" -Destination "release\" -Recurse
        } elseif (Test-Path "dist") {
          Copy-Item -Path "dist\*" -Destination "release\" -Recurse
        } else {
          Write-Host "❌ No build output found in 'build\windows' or 'dist' directories"
          Get-ChildItem -Directory | Where-Object { $_.Name -match "build|dist|output" }
          exit 1
        }
        Copy-Item -Path "version.txt" -Destination "release\"
        Copy-Item -Path "README.md" -Destination "release\"
        Compress-Archive -Path "release\*" -DestinationPath "AgevolamiPM-${{ github.ref_name }}-Windows.zip"
        
    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: windows-build
        path: |
          AgevolamiPM-*.zip
          build/
          dist/
          
    - name: Create Release
      if: startsWith(github.ref, 'refs/tags/')
      uses: softprops/action-gh-release@v1
      with:
        files: |
          AgevolamiPM-*.zip
        body: |
          ## Agevolami PM ${{ github.ref_name }}
          
          ### 🚀 Novità in questa versione
          - Miglioramenti delle prestazioni
          - Correzioni di bug
          - Aggiornamenti di sicurezza
          
          ### 📥 Download
          - **Windows**: Scarica `AgevolamiPM-${{ github.ref_name }}-Windows.zip`
          
          ### 🔧 Installazione
          1. Scarica il file ZIP
          2. Estrai in una cartella
          3. Esegui `AgevolamiPM.exe`
          
          ### 📋 Requisiti di Sistema
          - Windows 10/11
          - 4GB RAM minimo
          - 500MB spazio libero
          
        draft: false
        prerelease: false
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  auto-update-check:
    runs-on: ubuntu-latest
    needs: build-windows
    if: startsWith(github.ref, 'refs/tags/')
    
    steps:
    - name: Trigger update notification
      run: |
        echo "New version ${{ github.ref_name }} has been released!"
        echo "Users will be notified on next app startup."
