# Enhanced Web Scraping System

## Overview

The enhanced web scraping system provides robust, intelligent scraping capabilities for monitoring Italian financial incentives. It includes advanced features for reliability, quality control, and monitoring.

## Key Features

### 🤖 Selenium Support for JavaScript-Heavy Sites
- **Hybrid Scraping**: Automatically chooses between requests and Selenium based on site requirements
- **Headless Chrome**: Runs Chrome in headless mode for JavaScript execution
- **Fallback Strategy**: Falls back to requests-only if Selenium fails
- **Auto-Setup**: Automatically downloads and configures ChromeDriver

### ✅ Content Validation with Quality Thresholds
- **Title Validation**: Minimum/maximum length requirements
- **Description Validation**: Quality and length checks
- **Keyword Filtering**: Required and forbidden keyword lists
- **Relevance Scoring**: Automatic relevance assessment
- **Generic Content Filtering**: Removes navigation, footer, and other non-content

### 🏥 Website Health Monitoring
- **Real-time Health Checks**: Monitors response times and status codes
- **Failure Tracking**: Tracks consecutive failures per site
- **Automatic Skipping**: Skips unhealthy sites during scraping
- **Health Reports**: Comprehensive health status reporting
- **Persistent Storage**: Saves health data between sessions

### 🎯 CSS Selector Validation
- **Selector Testing**: Validates CSS selectors before scraping
- **Adaptive Selection**: Uses working selectors when others fail
- **Validation Reports**: Detailed selector performance reports
- **Fallback Selectors**: Multiple selector strategies per site

### 🔄 Advanced Content Deduplication
- **Hash-based Detection**: Fast exact duplicate detection
- **Similarity Analysis**: Detects near-duplicate content using difflib
- **Configurable Thresholds**: Adjustable similarity thresholds
- **Source-specific Caching**: Separate deduplication per source

### 🔁 Intelligent Retry Logic
- **Exponential Backoff**: Smart delay calculation between retries
- **Jitter Support**: Randomized delays to avoid thundering herd
- **Configurable Limits**: Customizable retry counts and delays
- **Error-specific Handling**: Different strategies for different error types

## Configuration

### Basic Configuration
```json
{
  "scraping_method": "hybrid",
  "validation": {
    "min_title_length": 10,
    "max_title_length": 200,
    "min_description_length": 20,
    "max_description_length": 1000,
    "forbidden_keywords": ["cookie", "privacy", "login"],
    "max_duplicate_similarity": 0.85
  },
  "retry": {
    "max_retries": 3,
    "base_delay": 1.0,
    "max_delay": 60.0,
    "exponential_base": 2.0,
    "jitter": true
  }
}
```

### Scraping Methods
- **`requests`**: Fast, lightweight scraping for static content
- **`selenium`**: Full browser automation for JavaScript-heavy sites
- **`hybrid`**: Automatically chooses the best method per site

### Website Configuration
```json
{
  "name": "Example Site",
  "url": "https://example.com",
  "enabled": true,
  "requires_javascript": true,
  "health_check_enabled": true,
  "search_paths": ["/news", "/incentives"]
}
```

## Usage

### Basic Usage
```python
from core.services.enhanced_web_scraping_service import EnhancedWebScrapingService

config = {
    "scraping_method": "hybrid",
    "validation": {...},
    "retry": {...},
    "websites": [...]
}

scraper = EnhancedWebScrapingService(config)
items = scraper.scrape_all_sources(keywords=["incentivi", "finanziamenti"])
```

### Health Monitoring
```python
# Check individual website health
health = scraper.check_website_health("https://example.com")
print(f"Healthy: {health.is_healthy}, Response time: {health.response_time}s")

# Get comprehensive health report
report = scraper.get_health_report()
print(f"Healthy sites: {report['healthy_sites']}/{report['total_sites']}")
```

### Selector Validation
```python
# Validate selectors on a webpage
selectors = ['.news-item', 'article', 'h2']
validations = scraper.validate_selectors("https://example.com", selectors)

for validation in validations:
    print(f"Selector '{validation.selector}': {validation.elements_found} elements")
```

## Installation

### Automatic Setup
Run the setup script to automatically install and configure Selenium:
```bash
python scripts/setup_selenium.py
```

### Manual Installation
1. Install dependencies:
   ```bash
   pip install selenium>=4.15.0 webdriver-manager>=4.0.0
   ```

2. Ensure Chrome browser is installed

3. The system will automatically download ChromeDriver when first used

## Monitoring and Reports

### System Health Dialog
The enhanced UI includes a "Stato Sistema" button that shows:
- **Website Health**: Real-time status of all monitored sites
- **Selector Validation**: CSS selector performance per site
- **Overall Statistics**: System-wide health metrics

### Health Report Structure
```json
{
  "healthy_sites": 3,
  "total_sites": 4,
  "health_percentage": 75.0,
  "average_response_time": 1.23,
  "unhealthy_sites": [
    {
      "url": "https://problematic-site.com",
      "error": "Connection timeout",
      "failures": 3,
      "last_check": "2024-01-15T10:30:00"
    }
  ]
}
```

## Performance Optimizations

### Parallel Processing
- **Concurrent Scraping**: Multiple sites scraped simultaneously
- **Thread Pool**: Configurable worker threads
- **Resource Management**: Automatic cleanup and resource limits

### Caching and Storage
- **Health Data Persistence**: Health status saved between sessions
- **Selector Cache**: Validated selectors cached for reuse
- **Content Fingerprinting**: Efficient duplicate detection

### Adaptive Behavior
- **Smart Method Selection**: Chooses optimal scraping method per site
- **Dynamic Selector Fallback**: Adapts when selectors break
- **Rate Limiting**: Respects server limits and adjusts delays

## Troubleshooting

### Common Issues

#### Selenium Not Working
- Ensure Chrome browser is installed
- Run `python scripts/setup_selenium.py`
- Check firewall/antivirus settings

#### Poor Content Quality
- Adjust validation thresholds in configuration
- Add more forbidden keywords
- Increase minimum content lengths

#### High Duplicate Rate
- Lower `max_duplicate_similarity` threshold
- Check if sites have changed structure
- Verify keyword relevance

#### Slow Performance
- Reduce `request_delay_seconds`
- Increase parallel worker count
- Use `requests` method for simple sites

### Debug Mode
Enable detailed logging by setting log level to DEBUG:
```python
import logging
logging.getLogger('core.services.enhanced_web_scraping_service').setLevel(logging.DEBUG)
```

## Best Practices

1. **Start with Hybrid Mode**: Let the system choose the best scraping method
2. **Monitor Health Regularly**: Check the system health dialog weekly
3. **Adjust Validation Rules**: Fine-tune based on content quality
4. **Use Appropriate Delays**: Respect server resources with reasonable delays
5. **Regular Selector Validation**: Run selector validation monthly
6. **Clean Up Resources**: Always call `cleanup()` when done

## Future Enhancements

- **Machine Learning**: Content quality prediction
- **Proxy Support**: Rotating proxy integration
- **Advanced Scheduling**: Site-specific scraping schedules
- **API Integration**: Direct API connections where available
- **Real-time Monitoring**: Live health monitoring dashboard
