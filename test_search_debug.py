#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug script to test search functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import flet as ft
from src.ui.components.header import Header, ProfessionalSearchEngine
from src.core.database.database_manager import DatabaseManager

class TestApp:
    def __init__(self):
        self.db = DatabaseManager()
        self.page = None

def test_search_functionality(page: ft.Page):
    """Test search functionality with debug output"""
    page.title = "Search Debug Test"
    page.window_width = 1200
    page.window_height = 800
    
    # Create test app
    app = TestApp()
    app.page = page
    
    # Test search engine directly
    print("🔍 Testing Search Engine...")
    
    try:
        search_engine = ProfessionalSearchEngine(app)
        results = search_engine.search("test", max_results=5)
        print(f"✅ Search engine works! Found {len(results)} results")
        
        # Test simple search
        header = Header(
            on_search=lambda q: print(f"Global search: {q}"),
            on_notification_click=lambda: print("Notifications"),
            on_settings_click=lambda: print("Settings"),
            app_instance=app
        )
        
        simple_results = header._simple_search("test")
        print(f"✅ Simple search works! Found {len(simple_results)} results")
        
        # Create UI
        content = ft.Column([
            ft.Text("🔍 Search Debug Test", size=24, weight=ft.FontWeight.BOLD),
            ft.Text(f"Search engine results: {len(results)}", size=16),
            ft.Text(f"Simple search results: {len(simple_results)}", size=16),
            ft.Divider(),
            ft.Text("Try the search field below:", size=16),
            header.build()
        ])
        
        page.add(content)
        
    except Exception as e:
        print(f"❌ Search test failed: {e}")
        import traceback
        traceback.print_exc()
        
        error_content = ft.Column([
            ft.Text("❌ Search Test Failed", size=24, color=ft.Colors.RED),
            ft.Text(f"Error: {str(e)}", size=16),
            ft.Text("Check console for details", size=14)
        ])
        page.add(error_content)

if __name__ == "__main__":
    print("🧪 Starting Search Debug Test...")
    ft.app(target=test_search_functionality)
