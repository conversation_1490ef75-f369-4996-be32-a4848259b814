#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Settings Migration Manager
Safely transitions from old monolithic settings.py to new modern system
"""

import os
import shutil
import json
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import traceback

from core import get_logger
from .complete_settings_system import CompleteSettingsSystem, run_settings_system_tests

logger = get_logger(__name__)

class MigrationStep:
    """Represents a single migration step"""
    
    def __init__(self, name: str, description: str, execute_func, rollback_func=None):
        self.name = name
        self.description = description
        self.execute_func = execute_func
        self.rollback_func = rollback_func
        self.executed = False
        self.success = False
        self.error_message = None
        self.execution_time = None
    
    def execute(self) -> bool:
        """Execute this migration step"""
        try:
            start_time = datetime.now()
            logger.info(f"🔄 Executing migration step: {self.name}")
            
            result = self.execute_func()
            
            self.execution_time = (datetime.now() - start_time).total_seconds()
            self.executed = True
            self.success = bool(result)
            
            if self.success:
                logger.info(f"✅ Migration step completed: {self.name} ({self.execution_time:.2f}s)")
            else:
                logger.error(f"❌ Migration step failed: {self.name}")
            
            return self.success
            
        except Exception as e:
            self.execution_time = (datetime.now() - start_time).total_seconds()
            self.executed = True
            self.success = False
            self.error_message = str(e)
            
            logger.error(f"❌ Migration step error: {self.name} - {e}")
            logger.debug(traceback.format_exc())
            
            return False
    
    def rollback(self) -> bool:
        """Rollback this migration step"""
        if not self.executed or not self.rollback_func:
            return True
        
        try:
            logger.info(f"🔄 Rolling back migration step: {self.name}")
            result = self.rollback_func()
            
            if result:
                logger.info(f"✅ Migration step rolled back: {self.name}")
            else:
                logger.error(f"❌ Migration step rollback failed: {self.name}")
            
            return bool(result)
            
        except Exception as e:
            logger.error(f"❌ Migration step rollback error: {self.name} - {e}")
            return False

class SettingsMigrationManager:
    """
    Manages the migration from old to new settings system
    """
    
    def __init__(self, app_instance):
        self.app = app_instance
        self.backup_dir = Path("migration_backups") / datetime.now().strftime("%Y%m%d_%H%M%S")
        self.migration_steps: List[MigrationStep] = []
        self.migration_started = False
        self.migration_completed = False
        self.rollback_available = False
        
        # Paths
        self.old_settings_path = Path("src/ui/views/settings.py")
        self.old_settings_backup = self.backup_dir / "settings_old.py"
        self.settings_data_backup = self.backup_dir / "settings_data.json"
        
        # Migration state
        self.migration_state = {
            'version': '2.0.0',
            'started_at': None,
            'completed_at': None,
            'steps_completed': [],
            'rollback_data': {},
            'success': False
        }
        
        logger.info("Settings Migration Manager initialized")
        self._setup_migration_steps()
    
    def _setup_migration_steps(self):
        """Setup all migration steps"""
        
        # Step 1: Create backup
        self.migration_steps.append(MigrationStep(
            name="create_backup",
            description="Create backup of current settings system",
            execute_func=self._create_backup,
            rollback_func=self._restore_backup
        ))
        
        # Step 2: Export current settings data
        self.migration_steps.append(MigrationStep(
            name="export_settings_data",
            description="Export current settings data for migration",
            execute_func=self._export_current_settings,
            rollback_func=self._restore_settings_data
        ))
        
        # Step 3: Initialize new system
        self.migration_steps.append(MigrationStep(
            name="initialize_new_system",
            description="Initialize new modern settings system",
            execute_func=self._initialize_new_system,
            rollback_func=self._cleanup_new_system
        ))
        
        # Step 4: Migrate settings data
        self.migration_steps.append(MigrationStep(
            name="migrate_settings_data",
            description="Migrate settings data to new system",
            execute_func=self._migrate_settings_data,
            rollback_func=self._revert_settings_data
        ))
        
        # Step 5: Test new system
        self.migration_steps.append(MigrationStep(
            name="test_new_system",
            description="Run comprehensive tests on new system",
            execute_func=self._test_new_system,
            rollback_func=None  # No rollback needed for tests
        ))
        
        # Step 6: Update application integration
        self.migration_steps.append(MigrationStep(
            name="update_app_integration",
            description="Update application to use new settings system",
            execute_func=self._update_app_integration,
            rollback_func=self._revert_app_integration
        ))
        
        # Step 7: Finalize migration
        self.migration_steps.append(MigrationStep(
            name="finalize_migration",
            description="Finalize migration and cleanup",
            execute_func=self._finalize_migration,
            rollback_func=None
        ))
    
    def _create_backup(self) -> bool:
        """Create backup of current system"""
        try:
            # Create backup directory
            self.backup_dir.mkdir(parents=True, exist_ok=True)
            
            # Backup old settings.py
            if self.old_settings_path.exists():
                shutil.copy2(self.old_settings_path, self.old_settings_backup)
                logger.info(f"📁 Backed up old settings.py to {self.old_settings_backup}")
            
            # Backup settings components directory if exists
            old_components_dir = Path("src/ui/views/settings")
            backup_components_dir = self.backup_dir / "settings_components"
            
            if old_components_dir.exists():
                shutil.copytree(old_components_dir, backup_components_dir, dirs_exist_ok=True)
                logger.info(f"📁 Backed up settings components to {backup_components_dir}")
            
            # Save migration state
            self._save_migration_state()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Backup creation failed: {e}")
            return False
    
    def _restore_backup(self) -> bool:
        """Restore from backup"""
        try:
            if self.old_settings_backup.exists():
                shutil.copy2(self.old_settings_backup, self.old_settings_path)
                logger.info(f"📁 Restored old settings.py from backup")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Backup restoration failed: {e}")
            return False
    
    def _export_current_settings(self) -> bool:
        """Export current settings data"""
        try:
            # Try to extract settings from old system
            settings_data = {}
            
            if hasattr(self.app, 'config'):
                config = self.app.config
                
                # Extract email settings
                if hasattr(config, 'email_config'):
                    settings_data['email'] = {
                        'smtp_server': config.email_config.get('smtp_server', ''),
                        'smtp_port': config.email_config.get('smtp_port', 587),
                        'smtp_username': config.email_config.get('smtp_username', ''),
                        'smtp_password': config.email_config.get('smtp_password', ''),
                        'smtp_use_tls': config.email_config.get('smtp_use_tls', True),
                        'sender_name': config.email_config.get('from_name', 'Agevolami PM'),
                        'sender_email': config.email_config.get('from_email', '')
                    }
                
                # Extract alert settings
                if hasattr(config, 'alert_config'):
                    settings_data['notifications'] = {
                        'enabled': config.alert_config.get('email_notifications_enabled', True),
                        'email_enabled': config.alert_config.get('email_notifications_enabled', False),
                        'check_interval': config.alert_config.get('check_interval_hours', 24),
                        'advance_days': [config.alert_config.get('days_before_deadline', 7)],
                        'working_hours_only': True,
                        'weekend_alerts': False,
                        'reminder_recipients': config.alert_config.get('reminder_recipients', [])
                    }
                
                # Set defaults for other sections
                settings_data['google_services'] = {
                    'drive_enabled': False,
                    'drive_auto_backup': False,
                    'drive_backup_frequency': 'daily',
                    'calendar_enabled': False,
                    'calendar_auto_sync': True,
                    'tasks_enabled': False,
                    'tasks_auto_sync': True
                }
                
                settings_data['windows'] = {
                    'startup_enabled': False,
                    'startup_minimized': True,
                    'notifications_enabled': True,
                    'notification_sound': True,
                    'notification_quiet_hours_enabled': False
                }
                
                settings_data['reports'] = {
                    'scheduled_enabled': False,
                    'morning_enabled': True,
                    'morning_time': '09:00',
                    'evening_enabled': True,
                    'evening_time': '17:30',
                    'workdays_only': True,
                    'recipients': []
                }
            
            # Save exported data
            with open(self.settings_data_backup, 'w', encoding='utf-8') as f:
                json.dump(settings_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"📊 Exported settings data to {self.settings_data_backup}")
            self.migration_state['rollback_data']['exported_settings'] = settings_data
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Settings export failed: {e}")
            return False
    
    def _restore_settings_data(self) -> bool:
        """Restore settings data from backup"""
        try:
            if self.settings_data_backup.exists():
                with open(self.settings_data_backup, 'r', encoding='utf-8') as f:
                    settings_data = json.load(f)
                
                # Restore to app config if possible
                if hasattr(self.app, 'config') and 'email' in settings_data:
                    self.app.config.email_config.update(settings_data['email'])
                    logger.info("📊 Restored settings data from backup")
                
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ Settings data restoration failed: {e}")
            return False
    
    def _initialize_new_system(self) -> bool:
        """Initialize new modern settings system"""
        try:
            # Create new settings system
            self.new_settings_system = CompleteSettingsSystem(self.app)
            
            logger.info("🚀 New settings system initialized")
            self.migration_state['rollback_data']['new_system_created'] = True
            
            return True
            
        except Exception as e:
            logger.error(f"❌ New system initialization failed: {e}")
            return False
    
    def _cleanup_new_system(self) -> bool:
        """Cleanup new system during rollback"""
        try:
            if hasattr(self, 'new_settings_system'):
                self.new_settings_system.cleanup()
                delattr(self, 'new_settings_system')
                logger.info("🧹 New settings system cleaned up")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ New system cleanup failed: {e}")
            return False
    
    def _migrate_settings_data(self) -> bool:
        """Migrate settings data to new system"""
        try:
            if not hasattr(self, 'new_settings_system'):
                logger.error("❌ New settings system not initialized")
                return False
            
            # Load exported settings
            if self.settings_data_backup.exists():
                with open(self.settings_data_backup, 'r', encoding='utf-8') as f:
                    settings_data = json.load(f)
                
                # Import settings to new system
                success = self.new_settings_system.import_settings(settings_data)
                
                if success:
                    logger.info("📊 Settings data migrated to new system")
                    return True
                else:
                    logger.error("❌ Settings data migration failed")
                    return False
            
            logger.warning("⚠️ No settings data to migrate")
            return True
            
        except Exception as e:
            logger.error(f"❌ Settings data migration failed: {e}")
            return False
    
    def _revert_settings_data(self) -> bool:
        """Revert settings data migration"""
        try:
            # Reset new system to defaults
            if hasattr(self, 'new_settings_system'):
                self.new_settings_system.reset_to_defaults()
                logger.info("📊 Settings data migration reverted")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Settings data revert failed: {e}")
            return False
    
    def _test_new_system(self) -> bool:
        """Test new system thoroughly"""
        try:
            if not hasattr(self, 'new_settings_system'):
                logger.error("❌ New settings system not available for testing")
                return False
            
            # Run comprehensive tests
            test_results = run_settings_system_tests(self.new_settings_system)
            
            # Save test results
            test_results_file = self.backup_dir / "test_results.json"
            with open(test_results_file, 'w', encoding='utf-8') as f:
                json.dump(test_results, f, indent=2, ensure_ascii=False)
            
            # Check test results
            passed = test_results['summary']['passed']
            total = test_results['summary']['total']
            
            logger.info(f"🧪 System tests completed: {passed}/{total} passed")
            
            # Require at least 80% pass rate
            success_rate = passed / total if total > 0 else 0
            success = success_rate >= 0.8
            
            if success:
                logger.info(f"✅ System tests passed ({success_rate:.1%} success rate)")
            else:
                logger.error(f"❌ System tests failed ({success_rate:.1%} success rate)")
            
            self.migration_state['rollback_data']['test_results'] = test_results
            
            return success
            
        except Exception as e:
            logger.error(f"❌ System testing failed: {e}")
            return False
    
    def _update_app_integration(self) -> bool:
        """Update application to use new settings system"""
        try:
            # Store original settings view reference
            original_settings_view = None
            if hasattr(self.app, 'settings_view'):
                original_settings_view = self.app.settings_view
                self.migration_state['rollback_data']['original_settings_view'] = True
            
            # Replace with new settings system
            if hasattr(self, 'new_settings_system'):
                self.app.settings_view = self.new_settings_system
                self.app.modern_settings_system = self.new_settings_system
                
                logger.info("🔄 Application integration updated")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ App integration update failed: {e}")
            return False
    
    def _revert_app_integration(self) -> bool:
        """Revert application integration"""
        try:
            # Restore original settings view if we had one
            if self.migration_state['rollback_data'].get('original_settings_view'):
                # We would restore the original view here
                # For now, just remove the new system reference
                if hasattr(self.app, 'modern_settings_system'):
                    delattr(self.app, 'modern_settings_system')
                
                logger.info("🔄 Application integration reverted")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ App integration revert failed: {e}")
            return False
    
    def _finalize_migration(self) -> bool:
        """Finalize migration"""
        try:
            # Mark migration as completed
            self.migration_completed = True
            self.migration_state['completed_at'] = datetime.now().isoformat()
            self.migration_state['success'] = True
            
            # Save final migration state
            self._save_migration_state()
            
            # Create success marker file
            success_file = self.backup_dir / "migration_success.txt"
            with open(success_file, 'w') as f:
                f.write(f"Migration completed successfully at {self.migration_state['completed_at']}\n")
                f.write(f"Backup location: {self.backup_dir}\n")
                f.write("To rollback, use: python -m migration_manager --rollback\n")
            
            logger.info(f"🎉 Migration finalized successfully!")
            logger.info(f"📁 Backup available at: {self.backup_dir}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Migration finalization failed: {e}")
            return False
    
    def _save_migration_state(self):
        """Save current migration state"""
        try:
            self.backup_dir.mkdir(parents=True, exist_ok=True)
            state_file = self.backup_dir / "migration_state.json"
            
            with open(state_file, 'w', encoding='utf-8') as f:
                json.dump(self.migration_state, f, indent=2, ensure_ascii=False, default=str)
            
        except Exception as e:
            logger.error(f"❌ Failed to save migration state: {e}")
    
    def run_migration(self) -> bool:
        """Run the complete migration process"""
        if self.migration_started:
            logger.warning("⚠️ Migration already in progress")
            return False
        
        logger.info("🚀 Starting Settings System Migration")
        logger.info(f"📁 Backup directory: {self.backup_dir}")
        
        self.migration_started = True
        self.migration_state['started_at'] = datetime.now().isoformat()
        
        try:
            # Execute all migration steps
            for i, step in enumerate(self.migration_steps, 1):
                logger.info(f"📋 Step {i}/{len(self.migration_steps)}: {step.description}")
                
                if not step.execute():
                    logger.error(f"❌ Migration failed at step: {step.name}")
                    
                    # Attempt rollback
                    self._rollback_migration()
                    return False
                
                self.migration_state['steps_completed'].append(step.name)
                self._save_migration_state()
            
            logger.info("🎉 Migration completed successfully!")
            self.rollback_available = True
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Migration failed with exception: {e}")
            logger.debug(traceback.format_exc())
            
            # Attempt rollback
            self._rollback_migration()
            return False
    
    def _rollback_migration(self):
        """Rollback the migration"""
        logger.info("🔄 Starting migration rollback")
        
        # Rollback steps in reverse order
        completed_steps = [step for step in self.migration_steps if step.executed]
        
        for step in reversed(completed_steps):
            if step.rollback_func:
                logger.info(f"🔄 Rolling back: {step.name}")
                step.rollback()
        
        logger.info("🔄 Migration rollback completed")
    
    def get_migration_status(self) -> Dict[str, Any]:
        """Get current migration status"""
        return {
            'started': self.migration_started,
            'completed': self.migration_completed,
            'rollback_available': self.rollback_available,
            'backup_dir': str(self.backup_dir),
            'steps_total': len(self.migration_steps),
            'steps_completed': len([s for s in self.migration_steps if s.executed and s.success]),
            'steps_failed': len([s for s in self.migration_steps if s.executed and not s.success]),
            'current_step': next((s.name for s in self.migration_steps if not s.executed), None),
            'migration_state': self.migration_state
        }
    
    def can_rollback(self) -> bool:
        """Check if rollback is possible"""
        return self.rollback_available and self.backup_dir.exists()
    
    def execute_rollback(self) -> bool:
        """Execute complete rollback"""
        if not self.can_rollback():
            logger.error("❌ Rollback not available")
            return False
        
        logger.info("🔄 Executing complete rollback")
        
        try:
            # Load migration state
            state_file = self.backup_dir / "migration_state.json"
            if state_file.exists():
                with open(state_file, 'r', encoding='utf-8') as f:
                    self.migration_state = json.load(f)
            
            # Rollback all steps
            self._rollback_migration()
            
            logger.info("✅ Rollback completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Rollback failed: {e}")
            return False

def run_migration(app_instance) -> bool:
    """
    Main migration function
    """
    logger.info("🚀 Initializing Settings System Migration")
    
    migration_manager = SettingsMigrationManager(app_instance)
    success = migration_manager.run_migration()
    
    if success:
        logger.info("🎉 Settings system migration completed successfully!")
        logger.info("🔧 Your application now uses the modern settings system")
    else:
        logger.error("❌ Settings system migration failed")
        logger.info("🔄 System has been rolled back to previous state")
    
    return success

def check_migration_status() -> Dict[str, Any]:
    """
    Check if migration has been run and get status
    """
    # Look for recent migration backups
    backup_base = Path("migration_backups")
    
    if not backup_base.exists():
        return {'migrated': False, 'message': 'No migration found'}
    
    # Find most recent migration
    migration_dirs = [d for d in backup_base.iterdir() if d.is_dir()]
    
    if not migration_dirs:
        return {'migrated': False, 'message': 'No migration backups found'}
    
    latest_migration = max(migration_dirs, key=lambda d: d.stat().st_mtime)
    success_file = latest_migration / "migration_success.txt"
    
    if success_file.exists():
        return {
            'migrated': True,
            'migration_date': latest_migration.name,
            'backup_location': str(latest_migration),
            'message': 'Migration completed successfully'
        }
    else:
        return {
            'migrated': False,
            'migration_attempted': True,
            'migration_date': latest_migration.name,
            'backup_location': str(latest_migration),
            'message': 'Migration was attempted but may have failed'
        }

if __name__ == "__main__":
    # CLI interface for migration
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--rollback":
        # Execute rollback
        print("🔄 Executing migration rollback...")
        # Implementation would depend on how app_instance is available
        print("⚠️ Rollback must be executed from within the application")
    else:
        # Check migration status
        status = check_migration_status()
        print(f"Migration Status: {status}") 