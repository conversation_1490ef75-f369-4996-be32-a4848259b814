"""
Update Manager for Agevolami PM
Coordinates update checking and UI interactions
"""

import flet as ft
from typing import Optional, Callable
import threading
import time
from .updater import AutoUpdater

# Use absolute import to avoid relative import issues in built applications
try:
    from ui.update_dialog import UpdateDialog
except ImportError:
    # Fallback for different module structures
    UpdateDialog = None

class UpdateManager:
    """Manages the update process and UI interactions"""
    
    def __init__(self, page: ft.Page, current_version: str = "2.3.4"):
        self.page = page
        self.current_version = current_version
        self.updater = AutoUpdater(current_version=current_version)
        self.update_dialog = None
        self.checking_for_updates = False
        
    def check_for_updates_on_startup(self, silent: bool = True):
        """Check for updates when the application starts"""
        if self.checking_for_updates:
            return
            
        def check_thread():
            self.checking_for_updates = True
            try:
                update_info = self.updater.check_for_updates(force_check=False)
                
                if update_info and update_info.get('available', False):
                    # Show update dialog on main thread
                    self.page.run_thread(
                        lambda: self._show_update_available(update_info)
                    )
                elif not silent:
                    # Show "no updates" message if not silent
                    self.page.run_thread(self._show_no_updates_available)
                    
            except Exception as e:
                if not silent:
                    self.page.run_thread(
                        lambda: self._show_update_error(f"Errore controllo aggiornamenti: {e}")
                    )
            finally:
                self.checking_for_updates = False
        
        threading.Thread(target=check_thread, daemon=True).start()
    
    def check_for_updates_manual(self):
        """Manually check for updates (triggered by user)"""
        if self.checking_for_updates:
            self._show_already_checking()
            return
            
        # Show checking dialog
        self._show_checking_dialog()
        
        def check_thread():
            self.checking_for_updates = True
            try:
                time.sleep(1)  # Brief delay for UX
                update_info = self.updater.check_for_updates(force_check=True)
                
                if update_info and update_info.get('available', False):
                    self.page.run_thread(
                        lambda: self._show_update_available(update_info)
                    )
                else:
                    self.page.run_thread(self._show_no_updates_available)
                    
            except Exception as e:
                self.page.run_thread(
                    lambda: self._show_update_error(f"Errore controllo aggiornamenti: {e}")
                )
            finally:
                self.checking_for_updates = False
        
        threading.Thread(target=check_thread, daemon=True).start()
    
    def _show_update_available(self, update_info):
        """Show update available dialog"""
        if UpdateDialog is None:
            # Fallback to simple dialog if UpdateDialog is not available
            self._show_simple_update_available(update_info)
            return

        try:
            self.update_dialog = UpdateDialog(self.page, update_info, self.updater)
            self.update_dialog.show_update_available_dialog(
                on_update_callback=self._on_update_completed
            )
        except Exception as e:
            # Fallback to simple dialog if UpdateDialog fails
            self._show_simple_update_available(update_info)
    
    def _show_simple_update_available(self, update_info):
        """Show simple update available dialog (fallback)"""
        try:
            version = update_info.get('latest_version', update_info.get('version', 'Unknown'))
            dialog = ft.AlertDialog(
                modal=True,
                title=ft.Text("Aggiornamento Disponibile", weight=ft.FontWeight.BOLD),
                content=ft.Column([
                    ft.Row([
                        ft.Icon(ft.Icons.SYSTEM_UPDATE, color=ft.Colors.BLUE, size=30),
                        ft.Column([
                            ft.Text(f"Nuova versione disponibile: {version}", size=16),
                            ft.Text(f"Versione corrente: {self.current_version}", size=12),
                            ft.Text("Visita il repository GitHub per scaricare l'aggiornamento.", size=12)
                        ], expand=True)
                    ])
                ], spacing=10, width=400),
                actions=[
                    ft.ElevatedButton("OK", on_click=lambda _: self.page.close(dialog),
                                    bgcolor=ft.Colors.BLUE_600, color=ft.Colors.WHITE)
                ],
                actions_alignment=ft.MainAxisAlignment.CENTER
            )

            self.page.open(dialog)
        except Exception as e:
            print(f"Error showing simple update dialog: {e}")

    def _show_no_updates_available(self):
        """Show no updates available dialog"""
        try:
            dialog = ft.AlertDialog(
                modal=True,
                title=ft.Text("Nessun Aggiornamento", weight=ft.FontWeight.BOLD),
                content=ft.Column([
                    ft.Row([
                        ft.Icon(ft.Icons.CHECK_CIRCLE, color=ft.Colors.GREEN, size=30),
                        ft.Text(f"Stai utilizzando la versione più recente ({self.current_version})",
                               size=16)
                    ])
                ], spacing=10, width=350),
                actions=[
                    ft.ElevatedButton("OK", on_click=lambda _: self.page.close(dialog),
                                    bgcolor=ft.Colors.BLUE_600, color=ft.Colors.WHITE)
                ],
                actions_alignment=ft.MainAxisAlignment.CENTER
            )

            self.page.open(dialog)
        except Exception as e:
            print(f"Error showing no updates dialog: {e}")
            # Fallback: log message
            print("No updates available")
    
    def _show_checking_dialog(self):
        """Show checking for updates dialog"""
        try:
            dialog = ft.AlertDialog(
                title=ft.Text("Controllo Aggiornamenti"),
                content=ft.Column([
                    ft.Row([
                        ft.ProgressRing(width=30, height=30),
                        ft.Text("Controllo aggiornamenti in corso...", size=16)
                    ], spacing=15)
                ], spacing=10, width=300),
                modal=True
            )

            self.page.open(dialog)

            # Auto-close after 10 seconds if still open
            def auto_close():
                time.sleep(10)
                if dialog.open:
                    self.page.run_thread(lambda: self.page.close(dialog))

            threading.Thread(target=auto_close, daemon=True).start()
        except Exception as e:
            print(f"Error showing checking dialog: {e}")
    
    def _show_update_error(self, error_message: str):
        """Show update error dialog"""
        try:
            dialog = ft.AlertDialog(
                modal=True,
                title=ft.Text("Errore Aggiornamento", weight=ft.FontWeight.BOLD),
                content=ft.Column([
                    ft.Row([
                        ft.Icon(ft.Icons.ERROR, color=ft.Colors.RED, size=30),
                        ft.Column([
                            ft.Text("Impossibile controllare gli aggiornamenti", size=16),
                            ft.Text(error_message, size=12, color=ft.Colors.GREY_600)
                        ], expand=True)
                    ])
                ], spacing=10, width=400),
                actions=[
                    ft.ElevatedButton("OK", on_click=lambda _: self.page.close(dialog),
                                    bgcolor=ft.Colors.RED, color=ft.Colors.WHITE)
                ],
                actions_alignment=ft.MainAxisAlignment.CENTER
            )

            self.page.open(dialog)
        except Exception as e:
            print(f"Error showing update error dialog: {e}")
            print(f"Update error: {error_message}")
    
    def _show_already_checking(self):
        """Show already checking message"""
        try:
            dialog = ft.AlertDialog(
                modal=True,
                title=ft.Text("Controllo in Corso", weight=ft.FontWeight.BOLD),
                content=ft.Text("Controllo aggiornamenti già in corso. Attendere..."),
                actions=[
                    ft.ElevatedButton("OK", on_click=lambda _: self.page.close(dialog),
                                    bgcolor=ft.Colors.ORANGE, color=ft.Colors.WHITE)
                ],
                actions_alignment=ft.MainAxisAlignment.CENTER
            )

            self.page.open(dialog)
        except Exception as e:
            print(f"Error showing already checking dialog: {e}")
    
    def _close_dialog(self, dialog):
        """Close a dialog"""
        self.page.close(dialog)
    
    def _close_dialog_overlay(self, dialog):
        """Close a dialog - FIXED VERSION using proper Flet API"""
        try:
            self.page.close(dialog)
        except Exception as e:
            print(f"Error closing dialog: {e}")
            # Fallback: try the old way
            try:
                dialog.open = False
                self.page.update()
            except:
                pass
    
    def _on_update_completed(self):
        """Called when update is completed"""
        # This can be used for cleanup or additional actions
        pass
    
    def create_update_menu_item(self) -> ft.MenuItemButton:
        """Create menu item for manual update check"""
        return ft.MenuItemButton(
            content=ft.Text("Controlla Aggiornamenti"),
            leading=ft.Icon(ft.Icons.SYSTEM_UPDATE),
            on_click=lambda _: self.check_for_updates_manual()
        )
    
    def create_update_button(self) -> ft.ElevatedButton:
        """Create button for manual update check"""
        return ft.ElevatedButton(
            "Controlla Aggiornamenti",
            icon=ft.Icons.SYSTEM_UPDATE,
            on_click=lambda _: self.check_for_updates_manual()
        )
    
    def show_github_token_dialog(self):
        """Show dialog to configure GitHub token for private repository access"""
        token_field = ft.TextField(
            label="GitHub Personal Access Token",
            password=True,
            can_reveal_password=True,
            hint_text="ghp_xxxxxxxxxxxx",
            expand=True,
            helper_text="Required for private repository access"
        )
        
        # Load existing token if available
        existing_token = self.updater._load_github_token()
        if existing_token:
            token_field.value = existing_token
        
        def save_token(e):
            token = token_field.value.strip()
            if not token:
                self._show_token_error("Token cannot be empty")
                return
            
            # Test the token
            self.updater.github_token = token
            if self.updater.test_authentication():
                # Save token if test succeeds
                if self.updater.save_github_token(token):
                    self.page.close(dialog)
                    self._show_token_success("GitHub token saved successfully!")
                else:
                    self._show_token_error("Failed to save token")
            else:
                self._show_token_error("Invalid token or no access to repository")
        
        def test_token(e):
            token = token_field.value.strip()
            if not token:
                self._show_token_error("Enter a token to test")
                return
            
            self.updater.github_token = token
            if self.updater.test_authentication():
                self._show_token_success("Token is valid!")
            else:
                self._show_token_error("Invalid token or no access to repository")
        
        dialog = ft.AlertDialog(
            title=ft.Text("Configure GitHub Token"),
            content=ft.Container(
                content=ft.Column([
                    ft.Text("For private repositories, a GitHub Personal Access Token is required:", 
                           size=14),
                    ft.Container(height=10),
                    token_field,
                    ft.Container(height=10),
                    ft.Text("How to create a token:", size=12, weight=ft.FontWeight.BOLD),
                    ft.Text("1. Go to GitHub → Settings → Developer settings", size=11),
                    ft.Text("2. Personal access tokens → Tokens (classic)", size=11),
                    ft.Text("3. Generate new token with 'repo' scope", size=11),
                    ft.Container(height=5),
                    ft.Row([
                        ft.TextButton("Test Token", on_click=test_token),
                        ft.TextButton("Repository Link", 
                                    url="https://github.com/serhabdel/agevolami_pm_v2",
                                    icon=ft.Icons.OPEN_IN_NEW)
                    ])
                ], spacing=5),
                width=500
            ),
            actions=[
                ft.TextButton("Cancel", on_click=lambda _: self.page.close(dialog)),
                ft.ElevatedButton("Save Token", on_click=save_token, 
                                bgcolor=ft.Colors.BLUE, color=ft.Colors.WHITE)
            ]
        )
        
        self.page.open(dialog)
    
    def _show_token_success(self, message: str):
        """Show token success message"""
        try:
            dialog = ft.AlertDialog(
                title=ft.Text("✅ Successo", color=ft.Colors.GREEN, weight=ft.FontWeight.BOLD),
                content=ft.Text(message, size=14),
                actions=[
                    ft.ElevatedButton("OK", on_click=lambda e: self.page.close(dialog), 
                                    bgcolor=ft.Colors.GREEN, color=ft.Colors.WHITE)
                ],
                actions_alignment=ft.MainAxisAlignment.CENTER
            )

            self.page.open(dialog)
            
        except Exception as e:
            print(f"Error showing success: {e}")
    
    def _show_token_error(self, message: str):
        """Show token error message"""
        try:
            dialog = ft.AlertDialog(
                title=ft.Text("❌ Errore", color=ft.Colors.RED, weight=ft.FontWeight.BOLD),
                content=ft.Text(message, size=14),
                actions=[
                    ft.ElevatedButton("OK", on_click=lambda e: self.page.close(dialog), 
                                    bgcolor=ft.Colors.RED, color=ft.Colors.WHITE)
                ],
                actions_alignment=ft.MainAxisAlignment.CENTER
            )

            self.page.open(dialog)
            
        except Exception as e:
            print(f"Error showing error: {e}")
