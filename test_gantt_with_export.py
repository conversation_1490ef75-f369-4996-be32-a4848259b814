#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Enhanced Native Gantt Chart with Scrolling & Export
"""

import sys
from pathlib import Path
import os

# Add src to path
current_dir = Path(__file__).parent
src_path = current_dir / "src"
sys.path.insert(0, str(src_path))

import flet as ft
from datetime import datetime, timedelta
from dataclasses import dataclass
from typing import List
from ui.views.native_gantt import NativeGanttChart, GanttTask

print("🚀 Enhanced Native Gantt with Scrolling & Export Test...")

# Mock app class with database simulation
class MockApp:
    def __init__(self):
        self.page = None
        self.db_manager = MockDBManager()

class MockDBManager:
    def get_all_projects(self):
        """Return mock projects"""
        from datetime import datetime, timedelta
        base_date = datetime.now()
        
        return [
            MockProject("PROJ-001", "Website Redesign", base_date.date(), (base_date + timedelta(days=30)).date(), 65, "in_corso"),
            MockProject("PROJ-002", "Mobile App Development", (base_date + timedelta(days=5)).date(), (base_date + timedelta(days=60)).date(), 25, "in_attesa"),
            MockProject("PROJ-003", "Database Migration", (base_date + timedelta(days=10)).date(), (base_date + timedelta(days=20)).date(), 100, "completato"),
            MockProject("PROJ-004", "Security Audit", (base_date + timedelta(days=15)).date(), (base_date + timedelta(days=45)).date(), 40, "in_corso"),
            MockProject("PROJ-005", "API Documentation", (base_date + timedelta(days=20)).date(), (base_date + timedelta(days=35)).date(), 80, "presentato"),
            MockProject("PROJ-006", "User Training", (base_date + timedelta(days=25)).date(), (base_date + timedelta(days=40)).date(), 10, "in_attesa"),
            MockProject("PROJ-007", "Performance Testing", (base_date + timedelta(days=30)).date(), (base_date + timedelta(days=50)).date(), 90, "approvato"),
            MockProject("PROJ-008", "Quality Assurance", (base_date + timedelta(days=35)).date(), (base_date + timedelta(days=55)).date(), 55, "in_corso"),
        ]
    
    def get_all_deadlines(self):
        """Return mock deadlines"""
        from datetime import datetime, timedelta
        base_date = datetime.now()
        
        return [
            MockDeadline("DL-001", "Alpha Release", (base_date + timedelta(days=15)).date(), "critica", "in_corso"),
            MockDeadline("DL-002", "Beta Testing", (base_date + timedelta(days=30)).date(), "alta", "in_attesa"),
            MockDeadline("DL-003", "Production Deploy", (base_date + timedelta(days=60)).date(), "critica", "in_attesa"),
            MockDeadline("DL-004", "Documentation Complete", (base_date + timedelta(days=35)).date(), "media", "in_corso"),
            MockDeadline("DL-005", "Security Review", (base_date + timedelta(days=45)).date(), "alta", "in_corso"),
            MockDeadline("DL-006", "User Acceptance", (base_date + timedelta(days=50)).date(), "media", "in_attesa"),
        ]

@dataclass 
class MockProject:
    id: str
    name: str
    start_date: datetime
    end_date: datetime
    progress: int
    status: str

@dataclass
class MockDeadline:
    id: str
    title: str  # Changed from name to title
    due_date: datetime  # Changed from deadline_date to due_date
    priority: str
    status: str

def main(page: ft.Page):
    """Main app function"""
    
    page.title = "🚀 Enhanced Native Gantt Chart Test"
    page.theme_mode = ft.ThemeMode.LIGHT
    page.padding = 0
    page.window_width = 1400
    page.window_height = 900
    
    # Create mock app
    app = MockApp()
    app.page = page
    
    # Create enhanced Gantt chart
    gantt_chart = NativeGanttChart(app)
    
    # Create main view with enhanced features notice
    main_view = ft.Column([
        ft.Container(
            content=ft.Row([
                ft.Icon(ft.Icons.TIMELINE, size=32, color=ft.Colors.BLUE_600),
                ft.Text("🚀 Enhanced Native Gantt Chart", size=28, weight=ft.FontWeight.BOLD),
                ft.Container(expand=True),
                ft.Container(
                    content=ft.Column([
                        ft.Text("✨ NEW FEATURES", size=12, weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN_600),
                        ft.Text("🔄 Horizontal Scrolling", size=10, color=ft.Colors.GREY_600),
                        ft.Text("📊 PNG/PDF Export", size=10, color=ft.Colors.GREY_600),
                        ft.Text("📅 Period Filter (1M/3M/6M/1Y)", size=10, color=ft.Colors.GREY_600),
                    ], spacing=2),
                    padding=ft.padding.all(10),
                    bgcolor=ft.Colors.GREEN_50,
                    border_radius=8
                )
            ]),
            padding=ft.padding.all(20),
            bgcolor=ft.Colors.WHITE,
            border_radius=8,
            margin=ft.margin.only(bottom=10)
        ),
        gantt_chart.build()
    ], expand=True, spacing=0)
    
    page.add(main_view)
    
    print("✅ Enhanced Native Gantt Chart loaded successfully!")
    print("🎯 Test NEW features:")
    print("  🔄 Horizontal Scrolling")
    print("  📊 Export: PNG & PDF (High Quality)")
    print("  📅 Period Filter: 1M (default), 3M, 6M, 1Y, All")

if __name__ == "__main__":
    # Use a different port to avoid conflicts
    ft.app(target=main, port=8082, view=ft.AppView.WEB_BROWSER) 