#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Enhanced Calendar View with Google Calendar Integration for Agevolami PM
"""

import flet as ft
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Any
from uuid import UUID
import calendar
import webbrowser

from core import get_logger
from core.models.base_models import Deadline, Project, Client, DeadlineStatus, Priority
from core.services.google_calendar_service import GoogleCalendarService

logger = get_logger(__name__)

class EnhancedCalendarView:
    """Enhanced Calendar View with Google Calendar integration"""
    
    def __init__(self, app_instance):
        self.app = app_instance
        self.db_manager = app_instance.db_manager

        # Use background service manager for Google services
        from core.services.background_service_manager import get_google_calendar_service
        self._get_google_service = get_google_calendar_service
        
        # Stato del calendario
        self.current_date = datetime.now().date()
        self.view_mode = "month"  # month, week, day, google
        self.calendar_source = "local"  # local, google, both
        self.selected_date = None
        self.filter_client = None
        self.filter_project = None
        self.filter_priority = None
        self.filter_status = None
        
        # Flag per prevenire loop infiniti
        self._rebuilding = False
        
        # Dati
        self.deadlines = []
        self.google_events = []
        self.projects = []
        self.clients = []
        
        # Componenti UI
        self.main_container = None
        self.header_container = None
        self.calendar_grid_container = None
        self.month_year_text = None
        self.view_mode_dropdown = None
        self.source_dropdown = None
        
        # Colori per priorità
        self.priority_colors = {
            Priority.LOW: ft.Colors.GREEN,
            Priority.MEDIUM: ft.Colors.BLUE,
            Priority.HIGH: ft.Colors.ORANGE,
            Priority.CRITICAL: ft.Colors.RED
        }
        
        # Colori per stati
        self.status_colors = {
            DeadlineStatus.PENDING: ft.Colors.BLUE,
            DeadlineStatus.COMPLETED: ft.Colors.GREEN,
            DeadlineStatus.OVERDUE: ft.Colors.RED,
            DeadlineStatus.CANCELLED: ft.Colors.GREY
        }
        
        self._load_data()
        self._init_components()
    
    def _init_components(self):
        """Inizializza i componenti principali"""
        # Inizializza i container principali
        self.calendar_grid_container = ft.Container(
            content=self._create_calendar_grid(),
            expand=True
        )
        
        self.header_container = ft.Container(
            content=self._create_header_content(),
            padding=ft.padding.all(20),
            bgcolor=ft.Colors.WHITE,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )
    
    def _load_data(self):
        """Carica i dati necessari con caching"""
        try:
            # Carica scadenze del mese corrente (± 2 mesi per buffer)
            start_date = (self.current_date.replace(day=1) - timedelta(days=60))
            end_date = (self.current_date.replace(day=28) + timedelta(days=60))

            # Load data using cache for better performance
            from core.services.data_cache_manager import get_data_cache
            data_cache = get_data_cache(self.db_manager)

            self.deadlines = data_cache.get_deadlines_by_date_range(start_date, end_date)
            self.projects = data_cache.get_all_projects()
            self.clients = data_cache.get_all_clients()
            
            # Carica eventi Google Calendar se abilitato
            if self.google_service.is_enabled() and self.calendar_source in ["google", "both"]:
                self.google_events = self.google_service.get_google_events(start_date, end_date)
            else:
                self.google_events = []
            
            logger.info(f"Caricati {len(self.deadlines)} scadenze e {len(self.google_events)} eventi Google")
            
        except Exception as e:
            logger.error(f"Errore caricamento dati calendario: {e}")
            self.deadlines = []
            self.google_events = []
            self.projects = []
            self.clients = []
    
    def _create_header_content(self) -> ft.Column:
        """Crea il contenuto dell'header del calendario"""
        # Navigazione mese/anno
        self.month_year_text = ft.Text(
            self._get_view_title(),
            size=24,
            weight=ft.FontWeight.BOLD,
            color=ft.Colors.GREY_800
        )
        
        # Pulsanti navigazione
        nav_tooltip_prev, nav_tooltip_next = self._get_navigation_tooltips()
        
        prev_btn = ft.IconButton(
            icon=ft.Icons.CHEVRON_LEFT,
            on_click=lambda _: self._navigate_month(-1),
            tooltip=nav_tooltip_prev
        )
        
        next_btn = ft.IconButton(
            icon=ft.Icons.CHEVRON_RIGHT,
            on_click=lambda _: self._navigate_month(1),
            tooltip=nav_tooltip_next
        )
        
        today_btn = ft.OutlinedButton(
            text="Oggi",
            icon=ft.Icons.TODAY,
            on_click=lambda _: self._navigate_to_today()
        )
        
        # View mode selector
        view_options = [
            ft.dropdown.Option(text="Mese", key="month"),
            ft.dropdown.Option(text="Settimana", key="week"),
            ft.dropdown.Option(text="Giorno", key="day")
        ]
        
        # Add Google Calendar option if available
        if self.google_service.is_enabled():
            view_options.append(ft.dropdown.Option(text="Google Calendar", key="google"))
        
        self.view_mode_dropdown = ft.Dropdown(
            label="Vista",
            options=view_options,
            value=self.view_mode,
            width=140,
            on_change=lambda e: self._change_view_mode(e.control.value)
        )
        
        # Calendar source selector
        source_options = [ft.dropdown.Option(text="Solo Locali", key="local")]
        if self.google_service.is_enabled():
            source_options.extend([
                ft.dropdown.Option(text="Solo Google", key="google"),
                ft.dropdown.Option(text="Entrambi", key="both")
            ])
        
        self.source_dropdown = ft.Dropdown(
            label="Origine",
            options=source_options,
            value=self.calendar_source,
            width=120,
            on_change=lambda e: self._change_calendar_source(e.control.value)
        )
        
        # Google Calendar status indicator
        google_status = self._create_google_status_indicator()
        
        # Filtri (stessi del calendario originale)
        filter_client = ft.Dropdown(
            label="Cliente",
            options=[ft.dropdown.Option(text="Tutti", key=None)] + [
                ft.dropdown.Option(text=client.name, key=str(client.id))
                for client in self.clients
            ],
            value=str(self.filter_client) if self.filter_client else None,
            width=200,
            on_change=lambda e: self._update_filter('client', e.control.value)
        )
        
        filter_priority = ft.Dropdown(
            label="Priorità",
            options=[
                ft.dropdown.Option(text="Tutte", key=None),
                ft.dropdown.Option(text="Bassa", key="bassa"),
                ft.dropdown.Option(text="Media", key="media"),
                ft.dropdown.Option(text="Alta", key="alta"),
                ft.dropdown.Option(text="Critica", key="critica")
            ],
            value=self.filter_priority,
            width=120,
            on_change=lambda e: self._update_filter('priority', e.control.value)
        )
        
        # Azioni
        sync_btn = ft.ElevatedButton(
            text="Sincronizza",
            icon=ft.Icons.SYNC,
            bgcolor=ft.Colors.GREEN_600,
            color=ft.Colors.WHITE,
            on_click=lambda _: self._sync_to_google(),
            disabled=not self.google_service.is_enabled()
        )
        
        add_deadline_btn = ft.ElevatedButton(
            text="Nuova Scadenza",
            icon=ft.Icons.ADD,
            bgcolor=ft.Colors.BLUE_600,
            color=ft.Colors.WHITE,
            on_click=lambda _: self._create_new_deadline()
        )
        
        # Setup Google Calendar button
        setup_btn = ft.TextButton(
            text="Configura Google Calendar",
            icon=ft.Icons.SETTINGS,
            on_click=lambda _: self._show_google_setup(),
            visible=not self.google_service.is_enabled()
        )
        
        return ft.Column([
            # Prima riga - Titolo e navigazione
            ft.Row([
                ft.Row([
                    prev_btn,
                    self.month_year_text,
                    next_btn,
                    today_btn
                ], spacing=8, alignment=ft.MainAxisAlignment.START),
                
                ft.Container(expand=True),
                
                ft.Row([
                    google_status,
                    self.view_mode_dropdown,
                    self.source_dropdown,
                    sync_btn,
                    add_deadline_btn
                ], spacing=8)
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            
            # Seconda riga - Filtri e setup
            ft.Row([
                filter_client,
                filter_priority,
                
                ft.Container(expand=True),
                
                setup_btn,
                
                # Legenda priorità (stessa del calendario originale)
                ft.Row([
                    ft.Text("Priorità:", size=12, color=ft.Colors.GREY_600),
                    ft.Container(
                        content=ft.Text("B", size=10, color=ft.Colors.WHITE, weight=ft.FontWeight.BOLD),
                        bgcolor=ft.Colors.GREEN,
                        width=20, height=20,
                        border_radius=10,
                        alignment=ft.alignment.center,
                        tooltip="Bassa"
                    ),
                    ft.Container(
                        content=ft.Text("M", size=10, color=ft.Colors.WHITE, weight=ft.FontWeight.BOLD),
                        bgcolor=ft.Colors.BLUE,
                        width=20, height=20,
                        border_radius=10,
                        alignment=ft.alignment.center,
                        tooltip="Media"
                    ),
                    ft.Container(
                        content=ft.Text("A", size=10, color=ft.Colors.WHITE, weight=ft.FontWeight.BOLD),
                        bgcolor=ft.Colors.ORANGE,
                        width=20, height=20,
                        border_radius=10,
                        alignment=ft.alignment.center,
                        tooltip="Alta"
                    ),
                    ft.Container(
                        content=ft.Text("C", size=10, color=ft.Colors.WHITE, weight=ft.FontWeight.BOLD),
                        bgcolor=ft.Colors.RED,
                        width=20, height=20,
                        border_radius=10,
                        alignment=ft.alignment.center,
                        tooltip="Critica"
                    )
                ], spacing=4)
            ], spacing=16, alignment=ft.MainAxisAlignment.SPACE_BETWEEN)
        ], spacing=12)
    
    def _create_google_status_indicator(self) -> ft.Container:
        """Crea l'indicatore di stato di Google Calendar"""
        if self.google_service.is_enabled():
            return ft.Container(
                content=ft.Row([
                    ft.Icon(ft.Icons.CHECK_CIRCLE, size=16, color=ft.Colors.GREEN),
                    ft.Text("Google OK", size=12, color=ft.Colors.GREEN, weight=ft.FontWeight.BOLD)
                ], spacing=4),
                padding=ft.padding.symmetric(horizontal=8, vertical=4),
                bgcolor=ft.Colors.GREEN_50,
                border_radius=8,
                border=ft.border.all(1, ft.Colors.GREEN_200),
                tooltip="Google Calendar connesso"
            )
        else:
            return ft.Container(
                content=ft.Row([
                    ft.Icon(ft.Icons.ERROR, size=16, color=ft.Colors.ORANGE),
                    ft.Text("Google Non Configurato", size=12, color=ft.Colors.ORANGE, weight=ft.FontWeight.BOLD)
                ], spacing=4),
                padding=ft.padding.symmetric(horizontal=8, vertical=4),
                bgcolor=ft.Colors.ORANGE_50,
                border_radius=8,
                border=ft.border.all(1, ft.Colors.ORANGE_200),
                tooltip="Configura Google Calendar per la sincronizzazione"
            )
    
    def _create_calendar_grid(self) -> ft.Container:
        """Crea la griglia del calendario"""
        if self.view_mode == "google" and self.google_service.is_enabled():
            return self._create_google_calendar_view()
        elif self.view_mode == "month":
            return self._create_month_view()
        elif self.view_mode == "week":
            return self._create_week_view()
        elif self.view_mode == "day":
            return self._create_day_view()
        else:
            return self._create_month_view()
    
    def _create_google_calendar_view(self) -> ft.Container:
        """Crea la vista Google Calendar embedded"""
        embed_url = self.google_service.get_calendar_embed_url()
        
        if not embed_url:
            return ft.Container(
                content=ft.Column([
                    ft.Icon(ft.Icons.CALENDAR_TODAY, size=64, color=ft.Colors.GREY_400),
                    ft.Text(
                        "Google Calendar non disponibile",
                        size=16,
                        color=ft.Colors.GREY_600,
                        text_align=ft.TextAlign.CENTER
                    ),
                    ft.ElevatedButton(
                        text="Apri Google Calendar",
                        icon=ft.Icons.OPEN_IN_NEW,
                        on_click=lambda _: webbrowser.open("https://calendar.google.com")
                    )
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=16),
                padding=ft.padding.all(40),
                alignment=ft.alignment.center,
                expand=True
            )
        
        # Create an iframe-like container with instructions
        return ft.Container(
            content=ft.Column([
                ft.Container(
                    content=ft.Column([
                        ft.Row([
                            ft.Icon(ft.Icons.INFO, color=ft.Colors.BLUE),
                            ft.Text(
                                "Google Calendar - Calendario Agevolami PM",
                                size=18,
                                weight=ft.FontWeight.BOLD,
                                color=ft.Colors.BLUE_800
                            )
                        ], spacing=8),
                        ft.Text(
                            "Visualizza e gestisci le tue scadenze direttamente in Google Calendar",
                            size=14,
                            color=ft.Colors.GREY_700
                        )
                    ], spacing=8),
                    padding=ft.padding.all(16),
                    bgcolor=ft.Colors.BLUE_50,
                    border_radius=8,
                    margin=ft.margin.only(bottom=16)
                ),
                
                ft.Row([
                    ft.ElevatedButton(
                        text="Apri in Google Calendar",
                        icon=ft.Icons.OPEN_IN_NEW,
                        bgcolor=ft.Colors.BLUE_600,
                        color=ft.Colors.WHITE,
                        on_click=lambda _: webbrowser.open(embed_url)
                    ),
                    ft.OutlinedButton(
                        text="Sincronizza Ora",
                        icon=ft.Icons.SYNC,
                        on_click=lambda _: self._sync_to_google()
                    ),
                    ft.TextButton(
                        text="Torna al Calendario Locale",
                        on_click=lambda _: self._change_view_mode("month")
                    )
                ], spacing=12, alignment=ft.MainAxisAlignment.CENTER),
                
                # Show recent Google Calendar events
                self._create_google_events_summary()
            ], spacing=0),
            padding=ft.padding.all(20)
        )
    
    def _create_google_events_summary(self) -> ft.Container:
        """Crea un riassunto degli eventi Google Calendar"""
        if not self.google_events:
            return ft.Container(
                content=ft.Text(
                    "Nessun evento sincronizzato da Google Calendar",
                    size=14,
                    color=ft.Colors.GREY_500,
                    text_align=ft.TextAlign.CENTER
                ),
                padding=ft.padding.all(20),
                alignment=ft.alignment.center
            )
        
        event_widgets = []
        for event in self.google_events[:10]:  # Show max 10 events
            start = event.get('start', {})
            start_date = start.get('date') or start.get('dateTime', '')
            
            event_widgets.append(
                ft.Container(
                    content=ft.Row([
                        ft.Icon(ft.Icons.EVENT, size=16, color=ft.Colors.BLUE),
                        ft.Column([
                            ft.Text(
                                event.get('summary', 'Evento senza titolo'),
                                size=14,
                                weight=ft.FontWeight.W_500,
                                color=ft.Colors.GREY_800
                            ),
                            ft.Text(
                                start_date[:10] if start_date else 'Data non disponibile',
                                size=12,
                                color=ft.Colors.GREY_600
                            )
                        ], spacing=2, expand=True)
                    ], spacing=8),
                    padding=ft.padding.all(12),
                    margin=ft.margin.only(bottom=4),
                    bgcolor=ft.Colors.WHITE,
                    border_radius=8,
                    border=ft.border.all(1, ft.Colors.GREY_200)
                )
            )
        
        return ft.Container(
            content=ft.Column([
                ft.Text(
                    f"Eventi recenti da Google Calendar ({len(self.google_events)} totali)",
                    size=16,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.GREY_800
                ),
                ft.Column(event_widgets, spacing=4, scroll=ft.ScrollMode.AUTO)
            ], spacing=12),
            padding=ft.padding.all(16),
            bgcolor=ft.Colors.GREY_50,
            border_radius=8,
            margin=ft.margin.only(top=16),
            height=300
        )
    
    # Include all the original calendar methods (_create_month_view, _create_week_view, etc.)
    # For brevity, I'll include just the key ones. The rest would be the same as the original calendar.
    
    def _create_month_view(self) -> ft.Container:
        """Crea la vista mensile (same as original calendar)"""
        # Header giorni della settimana
        weekday_headers = ft.Row([
            ft.Container(
                content=ft.Text(
                    day,
                    size=12,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.GREY_600,
                    text_align=ft.TextAlign.CENTER
                ),
                width=140,
                height=30,
                alignment=ft.alignment.center
            )
            for day in ["Lun", "Mar", "Mer", "Gio", "Ven", "Sab", "Dom"]
        ], spacing=1)
        
        # Griglia dei giorni
        cal = calendar.Calendar(firstweekday=0)  # Lunedì come primo giorno
        month_days = cal.monthdayscalendar(self.current_date.year, self.current_date.month)
        
        week_rows = []
        for week in month_days:
            day_cells = []
            for day_num in week:
                if day_num == 0:
                    # Giorno vuoto
                    day_cells.append(
                        ft.Container(
                            width=140,
                            height=120,
                            bgcolor=ft.Colors.GREY_50,
                            border=ft.border.all(1, ft.Colors.GREY_200)
                        )
                    )
                else:
                    day_date = date(self.current_date.year, self.current_date.month, day_num)
                    day_cells.append(self._create_day_cell(day_date))
            
            week_rows.append(ft.Row(day_cells, spacing=1))
        
        return ft.Container(
            content=ft.Column([
                weekday_headers,
                ft.Column(week_rows, spacing=1)
            ], spacing=1),
            bgcolor=ft.Colors.WHITE,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_200),
            padding=ft.padding.all(8)
        )
    
    def _create_day_cell(self, day_date: date) -> ft.Container:
        """Crea una cella per un giorno specifico con supporto per eventi Google"""
        # Filtra scadenze per questo giorno
        day_deadlines = [
            d for d in self.deadlines 
            if d.due_date == day_date and self._passes_filters(d)
        ]
        
        # Filtra eventi Google per questo giorno se abilitato
        day_google_events = []
        if self.calendar_source in ["google", "both"] and self.google_events:
            for event in self.google_events:
                start = event.get('start', {})
                event_date_str = start.get('date') or start.get('dateTime', '')
                if event_date_str:
                    try:
                        event_date = datetime.fromisoformat(event_date_str.replace('Z', '+00:00')).date()
                        if event_date == day_date:
                            day_google_events.append(event)
                    except:
                        pass
        
        # Determina il colore di sfondo
        is_today = day_date == date.today()
        is_weekend = day_date.weekday() >= 5
        is_current_month = day_date.month == self.current_date.month
        
        if is_today:
            bg_color = ft.Colors.BLUE_50
            border_color = ft.Colors.BLUE_400
        elif not is_current_month:
            bg_color = ft.Colors.GREY_50
            border_color = ft.Colors.GREY_200
        elif is_weekend:
            bg_color = ft.Colors.GREY_100
            border_color = ft.Colors.GREY_200
        else:
            bg_color = ft.Colors.WHITE
            border_color = ft.Colors.GREY_200
        
        # Numero del giorno
        day_number = ft.Text(
            str(day_date.day),
            size=14,
            weight=ft.FontWeight.BOLD if is_today else ft.FontWeight.NORMAL,
            color=ft.Colors.BLUE_600 if is_today else (
                ft.Colors.GREY_400 if not is_current_month else ft.Colors.GREY_800
            )
        )
        
        # Combina scadenze locali e eventi Google
        all_items = []
        
        # Aggiungi scadenze locali se abilitato
        if self.calendar_source in ["local", "both"]:
            for deadline in day_deadlines[:3]:  # Max 3 per non sovraffollare
                deadline_color = self.priority_colors.get(deadline.priority, ft.Colors.BLUE)
                all_items.append(
                    ft.Container(
                        content=ft.Text(
                            deadline.title[:15] + ("..." if len(deadline.title) > 15 else ""),
                            size=8,
                            color=ft.Colors.WHITE,
                            weight=ft.FontWeight.BOLD
                        ),
                        padding=ft.padding.symmetric(horizontal=2, vertical=1),
                        bgcolor=deadline_color,
                        border_radius=4,
                        margin=ft.margin.only(bottom=1),
                        tooltip=f"[Local] {deadline.title}\n{deadline.description or ''}\nPriorità: {deadline.priority}",
                        on_click=lambda e, d=deadline: self._show_deadline_detail(d)
                    )
                )
        
        # Aggiungi eventi Google se abilitato
        if self.calendar_source in ["google", "both"]:
            for event in day_google_events[:2]:  # Max 2 per lasciare spazio
                all_items.append(
                    ft.Container(
                        content=ft.Text(
                            event.get('summary', 'Evento')[:15] + ("..." if len(event.get('summary', '')) > 15 else ""),
                            size=8,
                            color=ft.Colors.WHITE,
                            weight=ft.FontWeight.BOLD
                        ),
                        padding=ft.padding.symmetric(horizontal=2, vertical=1),
                        bgcolor=ft.Colors.PURPLE_600,  # Colore distintivo per eventi Google
                        border_radius=4,
                        margin=ft.margin.only(bottom=1),
                        tooltip=f"[Google] {event.get('summary', 'Evento')}\n{event.get('description', '')}",
                        on_click=lambda e, ev=event: self._open_google_event(ev)
                    )
                )
        
        # Mostra indicatore se ci sono più elementi
        total_items = len(day_deadlines) + len(day_google_events)
        if total_items > len(all_items):
            all_items.append(
                ft.Container(
                    content=ft.Text(
                        f"e altri {total_items - len(all_items)}...",
                        size=8,
                        color=ft.Colors.GREY_600
                    ),
                    padding=ft.padding.all(2),
                    bgcolor=ft.Colors.GREY_200,
                    border_radius=4,
                    margin=ft.margin.only(bottom=1)
                )
            )
        
        cell_content = ft.Column([
            ft.Row([
                day_number,
                ft.Container(expand=True)
            ]),
            ft.Column(
                all_items,
                spacing=1,
                scroll=ft.ScrollMode.AUTO
            )
        ], spacing=4, expand=True)
        
        return ft.Container(
            content=cell_content,
            width=140,
            height=120,
            bgcolor=bg_color,
            border=ft.border.all(2 if is_today else 1, border_color),
            border_radius=8,
            padding=ft.padding.all(4),
            on_click=lambda e: self._on_day_click(day_date)
        )
    
    # Utility methods
    def _get_view_title(self) -> str:
        """Ottiene il titolo appropriato per la vista corrente"""
        if self.view_mode == "google":
            return "Google Calendar"
        elif self.view_mode == "month":
            return self.current_date.strftime("%B %Y").title()
        elif self.view_mode == "week":
            start_of_week = self.current_date - timedelta(days=self.current_date.weekday())
            end_of_week = start_of_week + timedelta(days=6)
            return f"Settimana {start_of_week.strftime('%d %B')} - {end_of_week.strftime('%d %B %Y')}"
        elif self.view_mode == "day":
            return self.current_date.strftime("%A, %d %B %Y")
        return self.current_date.strftime("%B %Y")
    
    def _get_navigation_tooltips(self) -> tuple:
        """Ottiene i tooltip per i pulsanti di navigazione"""
        if self.view_mode == "month":
            return ("Mese precedente", "Mese successivo")
        elif self.view_mode == "week":
            return ("Settimana precedente", "Settimana successiva")
        elif self.view_mode == "day":
            return ("Giorno precedente", "Giorno successivo")
        return ("Precedente", "Successivo")
    
    def _passes_filters(self, deadline: Any) -> bool:
        """Verifica se una scadenza passa i filtri attivi"""
        # Hide completed deadlines by default (following the same pattern as deadlines view)
        if hasattr(deadline, 'status') and deadline.status == DeadlineStatus.COMPLETED:
            return False

        if self.filter_client and str(deadline.client_id) != str(self.filter_client):
            return False

        if self.filter_priority and deadline.priority != self.filter_priority:
            return False

        if self.filter_status and deadline.status != self.filter_status:
            return False

        return True
    
    # Event handlers
    def _change_view_mode(self, mode: str):
        """Cambia modalità di visualizzazione"""
        if mode != self.view_mode and not self._rebuilding:
            logger.info(f"Cambio modalità vista da {self.view_mode} a {mode}")
            self.view_mode = mode
            self._rebuild_view()
    
    def _change_calendar_source(self, source: str):
        """Cambia la fonte del calendario"""
        if source != self.calendar_source:
            logger.info(f"Cambio fonte calendario da {self.calendar_source} a {source}")
            self.calendar_source = source
            self._load_data()  # Ricarica i dati per la nuova fonte
            self._rebuild_view()
    
    def _update_filter(self, filter_type: str, value: Any):
        """Aggiorna un filtro"""
        logger.debug(f"Updating filter: {filter_type} = {value}")
        if filter_type == "client":
            try:
                self.filter_client = UUID(value) if value and value != "None" else None
            except ValueError:
                logger.warning(f"Invalid UUID string for client filter: {value}")
                self.filter_client = None
        elif filter_type == "priority":
            self.filter_priority = value if value != "None" else None
        elif filter_type == "status":
            self.filter_status = value if value != "None" else None
        
        self._rebuild_view()
    
    def _sync_to_google(self):
        """Sincronizza le scadenze con Google Calendar"""
        if not self.google_service:
            self._show_error_dialog("Errore", "Google Calendar non configurato")
            return
        
        try:
            # Sync all deadlines that have sync_to_google = True
            deadlines_to_sync = [d for d in self.deadlines if getattr(d, 'sync_to_google', True)]
            
            results = self.google_service.sync_all_deadlines(deadlines_to_sync)
            
            if results["success"]:
                message = f"Sincronizzate {results['synced']}/{results['total']} scadenze"
                if results["errors"]:
                    message += f"\n{len(results['errors'])} errori"
                
                self._show_success_dialog("Sincronizzazione completata", message)
                self._load_data()  # Ricarica per vedere i cambiamenti
                self._rebuild_view()
            else:
                self._show_error_dialog("Errore sincronizzazione", results.get("error", "Errore sconosciuto"))
                
        except Exception as e:
            logger.error(f"Errore sincronizzazione Google Calendar: {e}")
            self._show_error_dialog("Errore", f"Errore durante la sincronizzazione: {str(e)}")
    
    def _show_google_setup(self):
        """Mostra le istruzioni per configurare Google Calendar"""
        instructions = self.google_service.create_setup_instructions()
        
        def close_dialog(e):
            self.app.page.close(setup_dialog)
        
        setup_dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text("Configurazione Google Calendar", size=18, weight=ft.FontWeight.BOLD),
            content=ft.Container(
                content=ft.Column([
                    ft.Text(instructions, size=12, selectable=True),
                    ft.Container(height=20),
                    ft.Row([
                        ft.ElevatedButton(
                            "Apri Google Console",
                            icon=ft.Icons.OPEN_IN_NEW,
                            on_click=lambda _: webbrowser.open("https://console.developers.google.com/")
                        ),
                        ft.TextButton(
                            "Ricarica dopo configurazione",
                            on_click=lambda _: self._reload_google_service()
                        )
                    ], spacing=10)
                ], scroll=ft.ScrollMode.AUTO),
                width=600,
                height=400
            ),
            actions=[
                ft.TextButton("Chiudi", on_click=close_dialog)
            ]
        )
        
        self.app.page.open(setup_dialog)
    
    def _reload_google_service(self):
        """Ricarica il servizio Google Calendar"""
        self.google_service = GoogleCalendarService()
        self._rebuild_view()
        
        if self.google_service:
            self._show_success_dialog("Successo", "Google Calendar configurato correttamente!")
        else:
            self._show_error_dialog("Errore", "Configurazione Google Calendar non riuscita")
    
    # Standard calendar methods (same as original)
    def _navigate_month(self, direction: int):
        """Naviga di mesi (±1)"""
        if self.view_mode == "month":
            if direction > 0:
                if self.current_date.month == 12:
                    self.current_date = self.current_date.replace(year=self.current_date.year + 1, month=1)
                else:
                    self.current_date = self.current_date.replace(month=self.current_date.month + 1)
            else:
                if self.current_date.month == 1:
                    self.current_date = self.current_date.replace(year=self.current_date.year - 1, month=12)
                else:
                    self.current_date = self.current_date.replace(month=self.current_date.month - 1)
        elif self.view_mode == "week":
            self.current_date = self.current_date + timedelta(weeks=direction)
        elif self.view_mode == "day":
            self.current_date = self.current_date + timedelta(days=direction)
        
        self._load_data()
        self._rebuild_view()
    
    def _navigate_to_today(self):
        """Naviga alla data odierna"""
        self.current_date = date.today()
        self._load_data()
        self._rebuild_view()
    
    def _rebuild_view(self):
        """Ricostruisce completamente la vista"""
        if self._rebuilding:
            logger.warning("_rebuild_view chiamato durante ricostruzione, ignorato per prevenire loop")
            return
            
        self._rebuilding = True
        try:
            if self.calendar_grid_container:
                self.calendar_grid_container.content = self._create_calendar_grid()
            
            if self.header_container:
                self.header_container.content = self._create_header_content()
            
            # Update with improved error handling
            updated = False
            
            try:
                if self.calendar_grid_container and hasattr(self.calendar_grid_container, 'update'):
                    self.calendar_grid_container.update()
                    updated = True
            except Exception as e:
                logger.debug(f"Errore aggiornamento calendar_grid_container: {e}")
            
            try:
                if self.header_container and hasattr(self.header_container, 'update'):
                    self.header_container.update()
                    updated = True
            except Exception as e:
                logger.debug(f"Errore aggiornamento header_container: {e}")
            
            if not updated:
                try:
                    if (hasattr(self.app, 'main_layout') and 
                        self.app.main_layout and 
                        hasattr(self.app.main_layout, 'page') and 
                        self.app.main_layout.page and
                        hasattr(self.app.main_layout.page, 'update')):
                        self.app.main_layout.page.update()
                except Exception as e:
                    logger.debug(f"Errore aggiornamento page: {e}")
                
        except Exception as e:
            logger.error(f"Errore ricostruzione vista calendario: {e}")
        finally:
            self._rebuilding = False
    
    # Dialog helpers
    def _show_success_dialog(self, title: str, message: str):
        """Mostra un dialog di successo"""
        def close_dialog(e):
            self.app.page.close(success_dialog)
        
        success_dialog = ft.AlertDialog(
            title=ft.Text(title, color=ft.Colors.GREEN),
            content=ft.Text(message),
            actions=[ft.TextButton("OK", on_click=close_dialog)]
        )
        self.app.page.open(success_dialog)
    
    def _show_error_dialog(self, title: str, message: str):
        """Mostra un dialog di errore"""
        def close_dialog(e):
            self.app.page.close(error_dialog)
        
        error_dialog = ft.AlertDialog(
            title=ft.Text(title, color=ft.Colors.RED),
            content=ft.Text(message),
            actions=[ft.TextButton("OK", on_click=close_dialog)]
        )
        self.app.page.open(error_dialog)
    
    # Placeholder methods (implement as needed)
    def _create_week_view(self): return ft.Container(content=ft.Text("Week view - Coming soon"))
    def _create_day_view(self): return ft.Container(content=ft.Text("Day view - Coming soon"))
    def _on_day_click(self, day_date): pass
    def _show_deadline_detail(self, deadline): pass
    def _create_new_deadline(self): pass
    def _open_google_event(self, event): 
        """Apre l'evento Google Calendar nel browser"""
        if 'htmlLink' in event:
            webbrowser.open(event['htmlLink'])
    
    def refresh(self):
        """Metodo per refresh della vista"""
        self._load_data()
        self._rebuild_view()
    
    def build(self) -> ft.Container:
        """Costruisce la vista calendario"""
        self.main_container = ft.Container(
            content=ft.Column([
                self.header_container,
                ft.Container(height=16),
                self.calendar_grid_container
            ], spacing=0, scroll=ft.ScrollMode.AUTO),
            padding=ft.padding.all(20),
            expand=True
        )
        return self.main_container

    @property
    def google_service(self):
        """Get Google Calendar service from background service manager"""
        service = self._get_google_service()
        if service is None:
            # Return a dummy service that reports as disabled
            class DummyService:
                def is_enabled(self):
                    return False
                def get_google_events(self, start_date, end_date):
                    return []
                def get_calendar_embed_url(self):
                    return None
            return DummyService()
        return service