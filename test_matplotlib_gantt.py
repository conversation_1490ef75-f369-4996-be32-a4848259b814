#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Script for Matplotlib Professional Gantt Chart
Run this to see the new Matplotlib-powered Gantt visualization
"""

import flet as ft
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle
from datetime import datetime, date, timedelta
from uuid import uuid4, UUID
from dataclasses import dataclass
from enum import Enum
from typing import List, Optional
import io
import base64

# Mock data models for testing
class ProjectStatus(Enum):
    DRAFT = "bozza"
    SUBMITTED = "presentato"
    APPROVED = "approvato"
    IN_PROGRESS = "in_corso"
    COMPLETED = "completato"
    SUSPENDED = "sospeso"
    CANCELLED = "cancellato"

class Priority(Enum):
    LOW = "bassa"
    MEDIUM = "media"
    HIGH = "alta"
    CRITICAL = "critica"

class DeadlineStatus(Enum):
    PENDING = "in_attesa"
    COMPLETED = "completato"
    OVERDUE = "scaduto"
    CANCELLED = "cancellato"

@dataclass
class MockClient:
    id: UUID
    name: str

@dataclass
class MockProject:
    id: UUID
    name: str
    description: str
    client_id: UUID
    status: ProjectStatus
    start_date: Optional[date]
    end_date: Optional[date]

@dataclass
class MockDeadline:
    id: UUID
    title: str
    description: str
    client_id: UUID
    due_date: date
    priority: Priority
    status: DeadlineStatus

# Mock database manager
class MockDatabaseManager:
    def __init__(self):
        self.clients = [
            MockClient(id=uuid4(), name="Acme Corporation"),
            MockClient(id=uuid4(), name="TechStart SRL"),
            MockClient(id=uuid4(), name="Green Energy SpA"),
            MockClient(id=uuid4(), name="Digital Solutions"),
            MockClient(id=uuid4(), name="Consulting Plus")
        ]
        
        # Create projects with realistic timelines
        today = date.today()
        self.projects = [
            MockProject(
                id=uuid4(),
                name="Sviluppo App Mobile",
                description="Applicazione iOS/Android per gestione ordini",
                client_id=self.clients[0].id,
                status=ProjectStatus.IN_PROGRESS,
                start_date=today - timedelta(days=20),
                end_date=today + timedelta(days=40)
            ),
            MockProject(
                id=uuid4(),
                name="Sito Web E-commerce",
                description="Piattaforma vendita online con integrazione pagamenti",
                client_id=self.clients[1].id,
                status=ProjectStatus.APPROVED,
                start_date=today - timedelta(days=5),
                end_date=today + timedelta(days=60)
            ),
            MockProject(
                id=uuid4(),
                name="Sistema CRM",
                description="Customer Relationship Management personalizzato",
                client_id=self.clients[2].id,
                status=ProjectStatus.SUBMITTED,
                start_date=today + timedelta(days=10),
                end_date=today + timedelta(days=90)
            ),
            MockProject(
                id=uuid4(),
                name="Migrazione Database",
                description="Migrazione da SQL Server a PostgreSQL",
                client_id=self.clients[3].id,
                status=ProjectStatus.COMPLETED,
                start_date=today - timedelta(days=45),
                end_date=today - timedelta(days=5)
            ),
            MockProject(
                id=uuid4(),
                name="Dashboard Analytics",
                description="Cruscotto business intelligence",
                client_id=self.clients[4].id,
                status=ProjectStatus.DRAFT,
                start_date=today + timedelta(days=30),
                end_date=today + timedelta(days=100)
            ),
            MockProject(
                id=uuid4(),
                name="API Integration",
                description="Integrazione servizi terze parti",
                client_id=self.clients[0].id,
                status=ProjectStatus.SUSPENDED,
                start_date=today - timedelta(days=10),
                end_date=today + timedelta(days=20)
            )
        ]
        
        # Create deadlines
        self.deadlines = [
            MockDeadline(
                id=uuid4(),
                title="Consegna Prototipo",
                description="Primo prototipo app mobile",
                client_id=self.clients[0].id,
                due_date=today + timedelta(days=7),
                priority=Priority.HIGH,
                status=DeadlineStatus.PENDING
            ),
            MockDeadline(
                id=uuid4(),
                title="Testing UAT",
                description="User Acceptance Testing e-commerce",
                client_id=self.clients[1].id,
                due_date=today + timedelta(days=15),
                priority=Priority.CRITICAL,
                status=DeadlineStatus.PENDING
            ),
            MockDeadline(
                id=uuid4(),
                title="Presentazione Progetto",
                description="Presentazione finale CRM",
                client_id=self.clients[2].id,
                due_date=today + timedelta(days=25),
                priority=Priority.MEDIUM,
                status=DeadlineStatus.PENDING
            ),
            MockDeadline(
                id=uuid4(),
                title="Go Live",
                description="Messa in produzione dashboard",
                client_id=self.clients[4].id,
                due_date=today + timedelta(days=80),
                priority=Priority.HIGH,
                status=DeadlineStatus.PENDING
            ),
            MockDeadline(
                id=uuid4(),
                title="Documentazione",
                description="Consegna documentazione tecnica",
                client_id=self.clients[3].id,
                due_date=today - timedelta(days=2),
                priority=Priority.LOW,
                status=DeadlineStatus.COMPLETED
            )
        ]
    
    def get_all_projects(self) -> List[MockProject]:
        return self.projects
    
    def get_deadlines_by_date_range(self, start_date: date, end_date: date) -> List[MockDeadline]:
        return [d for d in self.deadlines if start_date <= d.due_date <= end_date]
    
    def get_all_clients(self) -> List[MockClient]:
        return self.clients

# Mock app instance
class MockApp:
    def __init__(self):
        self.db_manager = MockDatabaseManager()

# Create professional Gantt chart function
def create_matplotlib_gantt_chart(app_instance):
    """Create a professional Gantt chart using Matplotlib"""
    
    # Get data
    projects = app_instance.db_manager.get_all_projects()
    today = date.today()
    start_date = today - timedelta(days=60)
    end_date = today + timedelta(days=120)
    deadlines = app_instance.db_manager.get_deadlines_by_date_range(start_date, end_date)
    clients = app_instance.db_manager.get_all_clients()
    
    # Colors
    project_colors = {
        ProjectStatus.DRAFT: "#9E9E9E",           # Grey
        ProjectStatus.SUBMITTED: "#FF9800",       # Orange
        ProjectStatus.APPROVED: "#2196F3",        # Blue
        ProjectStatus.IN_PROGRESS: "#4CAF50",     # Green
        ProjectStatus.COMPLETED: "#607D8B",       # Blue Grey
        ProjectStatus.SUSPENDED: "#FF5722",       # Deep Orange
        ProjectStatus.CANCELLED: "#F44336"        # Red
    }
    
    priority_colors = {
        Priority.LOW: "#4CAF50",       # Green
        Priority.MEDIUM: "#2196F3",    # Blue
        Priority.HIGH: "#FF9800",      # Orange
        Priority.CRITICAL: "#F44336"   # Red
    }
    
    # Create figure
    fig, ax = plt.subplots(figsize=(16, 10))
    fig.patch.set_facecolor('white')
    
    # Prepare data
    all_items = []
    
    # Add projects
    for project in projects:
        if not project.start_date:
            continue
            
        # Calculate end date
        end_date_proj = project.end_date or (project.start_date + timedelta(days=30))
        
        # Get client name
        client_name = "Sconosciuto"
        for client in clients:
            if client.id == project.client_id:
                client_name = client.name
                break
        
        all_items.append({
            'name': f"📋 {project.name}",
            'start': project.start_date,
            'end': end_date_proj,
            'type': 'project',
            'status': project.status,
            'client': client_name,
            'color': project_colors.get(project.status, "#9E9E9E")
        })
    
    # Add deadlines
    for deadline in deadlines:
        # Get client name
        client_name = "Sconosciuto"
        for client in clients:
            if client.id == deadline.client_id:
                client_name = client.name
                break
        
        all_items.append({
            'name': f"⏰ {deadline.title}",
            'start': deadline.due_date,
            'end': deadline.due_date,
            'type': 'deadline',
            'priority': deadline.priority,
            'client': client_name,
            'color': priority_colors.get(deadline.priority, "#9E9E9E")
        })
    
    # Sort items by start date
    all_items.sort(key=lambda x: x['start'])
    
    # Create Gantt chart
    y_pos = range(len(all_items))
    today = date.today()
    
    for i, item in enumerate(all_items):
        start_date = item['start']
        
        if item['type'] == 'project':
            # Project bar
            end_date = item['end']
            duration = (end_date - start_date).days
            
            bar = Rectangle((mdates.date2num(start_date), i - 0.3),
                          duration, 0.6,
                          facecolor=item['color'],
                          edgecolor='white',
                          alpha=0.8,
                          linewidth=2)
            ax.add_patch(bar)
            
        else:
            # Deadline marker
            ax.scatter(mdates.date2num(start_date), i, 
                     color=item['color'], s=150, marker='D',
                     edgecolor='white', linewidth=2, zorder=3)
    
    # Add today line
    ax.axvline(mdates.date2num(today), color='red', linestyle='--', 
              alpha=0.8, linewidth=3, label='OGGI')
    
    # Format axes
    ax.set_yticks(y_pos)
    ax.set_yticklabels([item['name'] for item in all_items], fontsize=11)
    ax.invert_yaxis()
    
    # Format x-axis (dates)
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%d/%m'))
    ax.xaxis.set_major_locator(mdates.WeekdayLocator(interval=1))
    plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, fontsize=10)
    
    # Set date limits
    if all_items:
        min_date = min(item['start'] for item in all_items) - timedelta(days=5)
        max_date = max(item['end'] if item['type'] == 'project' else item['start'] 
                      for item in all_items) + timedelta(days=5)
        ax.set_xlim(mdates.date2num(min_date), mdates.date2num(max_date))
    
    # Styling
    ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
    ax.set_xlabel('Timeline', fontsize=14, fontweight='bold')
    ax.set_ylabel('Progetti e Scadenze', fontsize=14, fontweight='bold')
    
    # Legend for today line
    ax.legend(loc='upper right', fontsize=12)
    
    # Title
    fig.suptitle('📊 Gantt Chart Professionale - Demo Matplotlib', 
                fontsize=18, fontweight='bold', color='#2E3440')
    
    # Tight layout
    plt.tight_layout()
    
    return fig

def main(page: ft.Page):
    """Main function for Flet app"""
    page.title = "Matplotlib Gantt Chart - Demo"
    page.theme_mode = ft.ThemeMode.LIGHT
    page.window.width = 1400
    page.window.height = 900
    page.padding = 20
    
    # Mock app instance
    app = MockApp()
    
    # Create stats info
    projects_count = len(app.db_manager.get_all_projects())
    deadlines_count = len(app.db_manager.get_deadlines_by_date_range(
        date.today() - timedelta(days=60),
        date.today() + timedelta(days=120)
    ))
    
    # Header
    header = ft.Container(
        content=ft.Column([
            ft.Row([
                ft.Text(
                    "📊 Professional Gantt Chart Demo (Matplotlib)",
                    size=28,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.INDIGO_700
                ),
                ft.Container(expand=True),
                ft.Text(
                    f"📋 {projects_count} progetti • ⏰ {deadlines_count} scadenze",
                    size=16,
                    color=ft.Colors.GREY_600,
                    weight=ft.FontWeight.W_500
                )
            ]),
            ft.Text(
                "Powered by Matplotlib - Professional timeline visualization",
                size=14,
                color=ft.Colors.GREY_500,
                italic=True
            ),
            ft.Divider()
        ], spacing=10),
        padding=ft.padding.all(20),
        bgcolor=ft.Colors.WHITE,
        border_radius=16,
        border=ft.border.all(1, ft.Colors.GREY_200),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=8,
            color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
            offset=ft.Offset(0, 2)
        )
    )
    
    # Create Matplotlib chart
    fig = create_matplotlib_gantt_chart(app)
    
    # Convert to image for Flet
    img_buffer = io.BytesIO()
    fig.savefig(img_buffer, format='png', dpi=150, bbox_inches='tight',
               facecolor='white', edgecolor='none')
    img_buffer.seek(0)
    
    # Encode to base64
    img_base64 = base64.b64encode(img_buffer.getvalue()).decode()
    
    plt.close(fig)
    
    matplotlib_chart = ft.Image(
        src_base64=img_base64,
        fit=ft.ImageFit.CONTAIN,
        expand=True
    )
    
    # Features info
    features = ft.Container(
        content=ft.Column([
            ft.Text("✨ Caratteristiche Matplotlib Gantt:", size=18, weight=ft.FontWeight.BOLD),
            ft.Column([
                ft.Row([ft.Icon(ft.Icons.CHECK_CIRCLE, color=ft.Colors.GREEN, size=16), ft.Text("Timeline professionale con barre colorate per progetti")]),
                ft.Row([ft.Icon(ft.Icons.CHECK_CIRCLE, color=ft.Colors.GREEN, size=16), ft.Text("Marcatori diamante per scadenze con priorità")]),
                ft.Row([ft.Icon(ft.Icons.CHECK_CIRCLE, color=ft.Colors.GREEN, size=16), ft.Text("Linea 'Oggi' per orientamento temporale")]),
                ft.Row([ft.Icon(ft.Icons.CHECK_CIRCLE, color=ft.Colors.GREEN, size=16), ft.Text("Layout pulito e professionale")]),
                ft.Row([ft.Icon(ft.Icons.CHECK_CIRCLE, color=ft.Colors.GREEN, size=16), ft.Text("Compatibilità garantita con Flet")]),
                ft.Row([ft.Icon(ft.Icons.CHECK_CIRCLE, color=ft.Colors.GREEN, size=16), ft.Text("Alta qualità di rendering")]),
            ], spacing=8)
        ], spacing=12),
        padding=ft.padding.all(20),
        bgcolor=ft.Colors.BLUE_50,
        border_radius=12,
        border=ft.border.all(1, ft.Colors.BLUE_200)
    )
    
    # Layout
    page.add(
        ft.Column([
            header,
            matplotlib_chart,
            features
        ], spacing=20, scroll=ft.ScrollMode.AUTO)
    )

if __name__ == "__main__":
    ft.app(target=main)