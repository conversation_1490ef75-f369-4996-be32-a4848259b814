#!/usr/bin/env python3
"""
Debug script to check database status and paths
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from core.config.app_config import AppConfig
from core.database.database import Database

def check_database_status():
    """Check current database status"""
    print("🔍 Database Status Check")
    print("=" * 50)
    
    # Initialize config
    config = AppConfig()
    print(f"📁 Data directory: {config.data_dir}")
    print(f"📁 Data directory exists: {config.data_dir.exists()}")
    print(f"🗄️ Expected database path: {config.database_path}")
    print(f"🗄️ Expected database exists: {config.database_path.exists()}")
    
    if config.database_path.exists():
        size = config.database_path.stat().st_size
        print(f"📏 Database size: {size} bytes ({size/1024:.1f} KB)")
    
    # Check for other database files
    print(f"\n🔍 Searching for .db files in data directory:")
    db_files = list(config.data_dir.glob("*.db"))
    for db_file in db_files:
        size = db_file.stat().st_size
        print(f"   • {db_file.name}: {size} bytes ({size/1024:.1f} KB)")
    
    # Check current working directory
    print(f"\n🔍 Searching for .db files in current directory:")
    cwd_db_files = list(Path.cwd().glob("*.db"))
    for db_file in cwd_db_files:
        size = db_file.stat().st_size
        print(f"   • {db_file.name}: {size} bytes ({size/1024:.1f} KB)")
    
    # Try to connect to database and check content
    print(f"\n🗄️ Testing database connection:")
    try:
        db = Database(config.database_path)
        
        # Check if we can query the database
        with db.get_connection() as conn:
            cursor = conn.cursor()
            
            # Check tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            print(f"   • Tables found: {[table[0] for table in tables]}")
            
            # Check record counts
            if tables:
                for table in tables:
                    table_name = table[0]
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                        count = cursor.fetchone()[0]
                        print(f"   • {table_name}: {count} records")
                    except Exception as e:
                        print(f"   • {table_name}: Error counting - {e}")
            
    except Exception as e:
        print(f"   ❌ Database connection failed: {e}")
    
    print(f"\n🎯 Summary:")
    print(f"   • Config expects: {config.database_path}")
    print(f"   • File exists: {config.database_path.exists()}")
    if config.database_path.exists():
        print(f"   • File size: {config.database_path.stat().st_size} bytes")
    
    # Check what the _find_database_file method would return
    print(f"\n🔍 Testing _find_database_file logic:")
    possible_paths = [
        config.data_dir / "agevolami.db",
        Path("D:/pro projects/flet/AGevolami_PM/agevolami_pm.db"),
        Path.cwd() / "agevolami_pm.db",
        Path.cwd() / "agevolami.db",
        *list(config.data_dir.glob("*.db")),
        *list(Path.cwd().glob("*.db"))
    ]
    
    print(f"   Checking {len(possible_paths)} possible paths:")
    found_db = None
    for db_path in possible_paths:
        if db_path and db_path.exists() and db_path.is_file():
            size = db_path.stat().st_size
            if size > 1024:  # At least 1KB
                print(f"   ✅ {db_path}: {size} bytes")
                if not found_db:
                    found_db = db_path
            else:
                print(f"   ⚠️ {db_path}: {size} bytes (too small)")
        else:
            print(f"   ❌ {db_path}: not found")
    
    if found_db:
        print(f"\n🎯 _find_database_file would return: {found_db}")
    else:
        print(f"\n❌ _find_database_file would not find any suitable database")

if __name__ == "__main__":
    check_database_status()
