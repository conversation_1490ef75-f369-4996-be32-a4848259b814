#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vista Impostazioni per Agevolami PM
"""

import flet as ft
from typing import Dict, Any, Optional, List
import os
from pathlib import Path
import threading
import time

from core import get_logger, AppConfig
from core.services import EmailService, GoogleDriveService, GoogleTasksService
from core.services.google_calendar_service import GoogleCalendarService
from core.utils.windows_utils import WindowsSystemIntegration
from core.database import DatabaseManagerExtended

logger = get_logger(__name__)

class SettingsView:
    """Vista per la gestione delle impostazioni"""
    
    def __init__(self, app_instance):
        self.app = app_instance
        self.config = app_instance.config
        self.email_service = EmailService(self.config)
        
        # Inizializza integrazione Windows
        self.windows_integration = WindowsSystemIntegration()
        
        # Inizializza Google services
        self.google_drive_service = GoogleDriveService(self.config.data_dir)
        self.google_calendar_service = GoogleCalendarService(os.path.join(self.config.data_dir, "config"))
        self.google_tasks_service = GoogleTasksService(os.path.join(self.config.data_dir, "config"))
        
        logger.info(f"[SETTINGS] SettingsView initialized with app: {type(app_instance)}")
        logger.info(f"[SETTINGS] Has scheduler_service: {hasattr(app_instance, 'scheduler_service')}")
        if hasattr(app_instance, 'scheduler_service'):
            logger.info(f"[SETTINGS] Scheduler service type: {type(app_instance.scheduler_service)}")
        
        # Section visibility state
        self.section_expanded = {
            'email': True,
            'alerts': False,
            'reports': False,
            'custom_reports': False,
            'app': False,
            'windows': False,
            'google': True,
            'info': False
        }
        
        # Stato del form
        self.smtp_settings = {
            'server': '',
            'port': 587,
            'username': '',
            'password': '',
            'use_tls': True,
            'sender_name': 'Agevolami PM',
            'sender_email': ''
        }
        
        self.alert_settings = {
            'enabled': True,
            'email_enabled': False,
            'check_interval': 24,
            'advance_days': [1, 3, 7, 15],
            'working_hours_only': True,
            'weekend_alerts': False
        }
        
        self.app_settings = {
            'theme': 'light',
            'language': 'it',
            'auto_backup': True,
            'backup_interval': 7,
            'max_backups': 10
        }
        
        # Windows system settings
        self.windows_settings = {
            'startup_enabled': False,
            'startup_minimized': True,
            'notifications_enabled': True,
            'notification_sound': True,
            'notification_priority_filter': 'normal',  # all, normal, high, urgent
            'notification_quiet_hours_enabled': False,
            'notification_quiet_start': '22:00',
            'notification_quiet_end': '08:00',
            'notification_desktop_enabled': True,
            'notification_email_enabled': True
        }
        
        # Google Services settings - Combined Drive, Calendar and Tasks
        self.google_services_settings = {
            # Google Drive
            'drive_enabled': False,
            'drive_auto_backup': False,
            'drive_backup_frequency': 'daily',  # daily, weekly, monthly
            'drive_retention_days': 30,
            'drive_include_logs': True,
            'drive_include_assets': True,
            'drive_authenticated': False,
            
            # Google Calendar
            'calendar_enabled': False,
            'calendar_auto_sync': True,
            'calendar_sync_completed': False,
            'calendar_delete_completed': True,  # New setting for deleting completed deadlines
            'calendar_color_priority': True,
            'calendar_reminders': True,
            'calendar_authenticated': False,
            
            # Google Tasks
            'tasks_enabled': False,
            'tasks_auto_sync': True,
            'tasks_sync_on_deadline_change': True,
            'tasks_create_list_per_deadline': True,
            'tasks_sync_completed': True,
            'tasks_authenticated': False
        }
        
        # Scheduled reporting settings
        self.scheduled_reports_settings = {
            'enabled': False,
            'morning_enabled': True,
            'morning_time': '09:00',
            'evening_enabled': True,
            'evening_time': '17:30',
            'workdays_only': True,
            'report_types': {
                'full_report': True,
                'deadlines_only': False,
                'project_completion': True,
                'incomplete_tasks': True
            },
            'recipients': [],
            'include_project_percentages': True,
            'include_overdue_items': True,
            'include_upcoming_deadlines': True,
            'days_ahead_filter': 7
        }

        # Custom report settings
        self.custom_report_settings = {
            'include_clients': True,
            'include_projects': True,
            'include_tasks': True,
            'include_deadlines': True,
            'include_expired_only': False,
            'days_ahead': 30,
            'recipients': []
        }
        
        self._load_settings()
        self._init_components()
    
    def _init_components(self):
        """Inizializza i componenti della vista"""
        pass
    
    def _load_settings(self):
        """Carica le impostazioni salvate"""
        try:
            # Carica configurazioni da file se esistono
            self._load_config_from_file()
            
            # Carica impostazioni SMTP
            self.smtp_settings.update({
                'server': self.config.email_config['smtp_server'],
                'port': self.config.email_config['smtp_port'],
                'username': self.config.email_config['smtp_username'],
                'password': self.config.email_config['smtp_password'],
                'use_tls': self.config.email_config['smtp_use_tls'],
                'sender_name': self.config.email_config['from_name'],
                'sender_email': self.config.email_config['from_email']
            })
            
            # Carica impostazioni alert
            self.alert_settings.update({
                'enabled': self.config.alert_config['email_notifications_enabled'],
                'email_enabled': self.config.alert_config['email_notifications_enabled'],
                'check_interval': self.config.alert_config['check_interval_hours'],
                'advance_days': [self.config.alert_config['days_before_deadline']],  # Convert to list
                'working_hours_only': True,  # Default value
                'weekend_alerts': False,  # Default value
                'reminder_recipients': self.config.alert_config.get('reminder_recipients', [])
            })
            
            # Carica impostazioni Windows
            self._load_windows_settings()
            
            # Carica impostazioni Google Services
            self._load_google_services_settings()
            
            logger.info("Impostazioni caricate")
            
        except Exception as e:
            logger.error(f"Errore caricamento impostazioni: {e}")
    
    def _load_windows_settings(self):
        """Carica le impostazioni specifiche Windows"""
        try:
            # Verifica stato startup attuale
            self.windows_settings['startup_enabled'] = self.windows_integration.is_startup_enabled()
            
            # Carica altre impostazioni Windows da config se esistono
            windows_config = getattr(self.config, 'windows_config', {})
            if windows_config:
                self.windows_settings.update(windows_config)
                
        except Exception as e:
            logger.error(f"Errore caricamento impostazioni Windows: {e}")
    
    def _load_google_services_settings(self):
        """Carica le impostazioni specifiche dei servizi Google"""
        try:
            # Google Drive settings
            self.google_services_settings['drive_enabled'] = self.google_drive_service.is_authenticated
            drive_config = getattr(self.config, 'google_drive_config', {})
            if drive_config:
                for key, value in drive_config.items():
                    if f'drive_{key}' in self.google_services_settings:
                        self.google_services_settings[f'drive_{key}'] = value

            # Google Calendar settings
            self.google_services_settings['calendar_enabled'] = self.google_calendar_service.is_enabled()
            self.google_services_settings['calendar_authenticated'] = self.google_calendar_service.is_enabled()  # Assume authenticated if enabled
            calendar_config = getattr(self.config, 'google_calendar_config', {})
            if calendar_config:
                for key, value in calendar_config.items():
                    if f'calendar_{key}' in self.google_services_settings:
                        self.google_services_settings[f'calendar_{key}'] = value

            # Google Tasks settings - Fix authentication status detection
            self.google_services_settings['tasks_enabled'] = self.google_tasks_service.is_enabled()
            self.google_services_settings['tasks_authenticated'] = self.google_tasks_service.is_authenticated()

            tasks_config = getattr(self.config, 'google_tasks_config', {})
            if tasks_config:
                for key, value in tasks_config.items():
                    if f'tasks_{key}' in self.google_services_settings:
                        self.google_services_settings[f'tasks_{key}'] = value

            # Log the current Google Tasks status for debugging
            logger.info(f"Google Tasks status - Enabled: {self.google_services_settings['tasks_enabled']}, "
                       f"Authenticated: {self.google_services_settings['tasks_authenticated']}")

        except Exception as e:
            logger.error(f"Errore caricamento impostazioni Google Services: {e}")

    def _create_section_header(self, title: str, icon: str, section_key: str, expanded: bool = True) -> ft.Container:
        """Crea un header per sezione collassabile"""
        def toggle_section(e):
            self.section_expanded[section_key] = not self.section_expanded[section_key]
            self._refresh_view()
        
        return ft.Container(
            content=ft.Row([
                ft.Icon(icon, size=20, color=ft.Colors.BLUE_600),
                ft.Text(title, size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_800),
                ft.Container(expand=True),
                ft.IconButton(
                    icon=ft.Icons.EXPAND_LESS if expanded else ft.Icons.EXPAND_MORE,
                    icon_size=20,
                    on_click=toggle_section,
                    tooltip="Espandi/Richiudi"
                )
            ], alignment=ft.MainAxisAlignment.START),
            bgcolor=ft.Colors.GREY_100,
            padding=ft.padding.symmetric(horizontal=16, vertical=8),
            border_radius=ft.border_radius.only(top_left=8, top_right=8),
            border=ft.border.all(1, ft.Colors.GREY_300)
        )

    def _create_collapsible_section(self, header: ft.Container, content: ft.Container, section_key: str) -> ft.Container:
        """Crea una sezione collassabile"""
        expanded = self.section_expanded.get(section_key, True)
        
        if expanded:
            return ft.Container(
                content=ft.Column([header, content], spacing=0),
                border_radius=8,
                margin=ft.margin.only(bottom=16)
            )
        else:
            return ft.Container(
                content=header,
                border_radius=8, 
                margin=ft.margin.only(bottom=16)
            )

    def _refresh_view(self):
        """Aggiorna la vista quando le sezioni vengono espanse/collassate"""
        if hasattr(self.app, 'page') and self.app.page:
            # Rebuild the main layout to reflect changes
            main_layout = getattr(self.app, 'main_layout', None)
            if main_layout:
                main_layout.refresh_current_view()
    
    def _create_header(self) -> ft.Container:
        """Crea l'header della vista"""
        return ft.Container(
            content=ft.Row([
                ft.Column([
                    ft.Text(
                        "Impostazioni",
                        size=24,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.GREY_800
                    ),
                    ft.Text(
                        "Configura l'applicazione e i servizi",
                        size=12,
                        color=ft.Colors.GREY_500
                    )
                ], spacing=4),
                
                ft.Container(expand=True),
                
                ft.Row([
                    ft.OutlinedButton(
                        text="Ripristina Default",
                        icon=ft.Icons.RESTORE,
                        on_click=lambda _: self._reset_to_defaults()
                    ),
                    
                    ft.ElevatedButton(
                        text="Salva Impostazioni",
                        icon=ft.Icons.SAVE,
                        bgcolor=ft.Colors.BLUE_600,
                        color=ft.Colors.WHITE,
                        on_click=lambda _: self._save_settings()
                    )
                ], spacing=8)
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            padding=ft.padding.only(bottom=20)
        )
    
    def _create_smtp_section(self) -> ft.Container:
        """Crea la sezione impostazioni SMTP"""
        # Campi SMTP
        server_field = ft.TextField(
            label="Server SMTP *",
            value=self.smtp_settings['server'],
            hint_text="es. smtp.gmail.com",
            on_change=lambda e: self._update_smtp_setting('server', e.control.value)
        )
        
        port_field = ft.TextField(
            label="Porta *",
            value=str(self.smtp_settings['port']),
            hint_text="587",
            width=100,
            on_change=lambda e: self._update_smtp_setting('port', int(e.control.value) if e.control.value.isdigit() else 587)
        )
        
        username_field = ft.TextField(
            label="Username *",
            value=self.smtp_settings['username'],
            hint_text="<EMAIL>",
            on_change=lambda e: self._update_smtp_setting('username', e.control.value)
        )
        
        password_field = ft.TextField(
            label="Password *",
            value=self.smtp_settings['password'],
            password=True,
            can_reveal_password=True,
            on_change=lambda e: self._update_smtp_setting('password', e.control.value)
        )
        
        sender_name_field = ft.TextField(
            label="Nome Mittente",
            value=self.smtp_settings['sender_name'],
            hint_text="Agevolami PM",
            on_change=lambda e: self._update_smtp_setting('sender_name', e.control.value)
        )
        
        sender_email_field = ft.TextField(
            label="Email Mittente",
            value=self.smtp_settings['sender_email'],
            hint_text="<EMAIL>",
            on_change=lambda e: self._update_smtp_setting('sender_email', e.control.value)
        )
        
        use_tls_switch = ft.Switch(
            label="Usa TLS/SSL",
            value=self.smtp_settings['use_tls'],
            on_change=lambda e: self._update_smtp_setting('use_tls', e.control.value)
        )
        
        return ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Icon(
                        ft.Icons.EMAIL,
                        size=20,
                        color=ft.Colors.BLUE_600
                    ),
                    ft.Text(
                        "Configurazione Email (SMTP)",
                        size=16,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.GREY_800
                    )
                ], spacing=8),
                
                ft.Text(
                    "Configura il server SMTP per l'invio delle notifiche email",
                    size=12,
                    color=ft.Colors.GREY_500
                ),
                
                ft.Divider(height=1, color=ft.Colors.GREY_300),
                
                # Campi configurazione
                ft.Row([
                    server_field,
                    port_field
                ], spacing=16),
                
                ft.Row([
                    username_field,
                    password_field
                ], spacing=16),
                
                ft.Row([
                    sender_name_field,
                    sender_email_field
                ], spacing=16),
                
                use_tls_switch,
                
                # Test connessione
                ft.Container(
                    content=ft.Row([
                        ft.OutlinedButton(
                            text="Testa Connessione",
                            icon=ft.Icons.WIFI_PROTECTED_SETUP,
                            on_click=lambda _: self._test_smtp_connection()
                        ),
                        
                        ft.OutlinedButton(
                            text="Invia Email di Test",
                            icon=ft.Icons.SEND,
                            on_click=lambda _: self._send_test_email()
                        )
                    ], spacing=8),
                    padding=ft.padding.only(top=8)
                )
            ], spacing=12),
            padding=ft.padding.all(20),
            bgcolor=ft.Colors.WHITE,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )
    
    def _create_alerts_section(self) -> ft.Container:
        """Crea la sezione impostazioni alert"""
        # Switch principali
        alerts_enabled_switch = ft.Switch(
            label="Abilita Sistema Alert",
            value=self.alert_settings['enabled'],
            on_change=lambda e: self._update_alert_setting('enabled', e.control.value)
        )
        
        email_alerts_switch = ft.Switch(
            label="Notifiche Email",
            value=self.alert_settings['email_enabled'],
            on_change=lambda e: self._update_alert_setting('email_enabled', e.control.value)
        )
        
        working_hours_switch = ft.Switch(
            label="Solo Orari Lavorativi (9-18)",
            value=self.alert_settings['working_hours_only'],
            on_change=lambda e: self._update_alert_setting('working_hours_only', e.control.value)
        )
        
        weekend_alerts_switch = ft.Switch(
            label="Alert nei Weekend",
            value=self.alert_settings['weekend_alerts'],
            on_change=lambda e: self._update_alert_setting('weekend_alerts', e.control.value)
        )
        
        # Intervallo controllo
        check_interval_field = ft.TextField(
            label="Intervallo Controllo (ore)",
            value=str(self.alert_settings['check_interval']),
            width=150,
            hint_text="24",
            on_change=lambda e: self._update_alert_setting('check_interval', int(e.control.value) if e.control.value.isdigit() else 24)
        )
        
        # Giorni di anticipo
        advance_days_text = ", ".join(map(str, self.alert_settings['advance_days']))
        advance_days_field = ft.TextField(
            label="Giorni di Anticipo (separati da virgola)",
            value=advance_days_text,
            hint_text="1, 3, 7, 15",
            expand=True,
            on_change=lambda e: self._update_advance_days(e.control.value)
        )
        
        # Destinatari promemoria
        recipients_text = ", ".join(self.alert_settings['reminder_recipients'])
        recipients_field = ft.TextField(
            label="Destinatari Promemoria Email (separati da virgola)",
            value=recipients_text,
            hint_text="<EMAIL>, <EMAIL>",
            expand=True,
            on_change=lambda e: self._update_reminder_recipients(e.control.value),
            helper_text="Lascia vuoto per inviare solo al mittente configurato"
        )
        
        return ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Icon(
                        ft.Icons.NOTIFICATIONS,
                        size=20,
                        color=ft.Colors.ORANGE_600
                    ),
                    ft.Text(
                        "Sistema di Alert",
                        size=16,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.GREY_800
                    )
                ], spacing=8),
                
                ft.Text(
                    "Configura quando e come ricevere le notifiche per le scadenze",
                    size=12,
                    color=ft.Colors.GREY_500
                ),
                
                ft.Divider(height=1, color=ft.Colors.GREY_300),
                
                # Switch principali
                alerts_enabled_switch,
                email_alerts_switch,
                
                ft.Row([
                    working_hours_switch,
                    weekend_alerts_switch
                ], spacing=32),
                
                # Configurazioni avanzate
                ft.Row([
                    check_interval_field,
                    advance_days_field
                ], spacing=16),
                
                # Destinatari email
                recipients_field,
                
                # Info aggiuntive
                ft.Container(
                    content=ft.Column([
                                            ft.Text(
                        "[INFO] Informazioni:",
                        size=12,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.BLUE_600
                    ),
                        ft.Text(
                            "• Gli alert vengono controllati automaticamente ogni X ore",
                            size=11,
                            color=ft.Colors.GREY_600
                        ),
                        ft.Text(
                            "• I giorni di anticipo determinano quando creare gli alert",
                            size=11,
                            color=ft.Colors.GREY_600
                        ),
                        ft.Text(
                            "• Gli alert email richiedono la configurazione SMTP",
                            size=11,
                            color=ft.Colors.GREY_600
                        ),
                        ft.Text(
                            "• I promemoria vengono inviati al mittente + destinatari aggiuntivi",
                            size=11,
                            color=ft.Colors.GREY_600
                        )
                    ], spacing=4),
                    padding=ft.padding.all(12),
                    bgcolor=ft.Colors.BLUE_50,
                    border_radius=8,
                    border=ft.border.all(1, ft.Colors.BLUE_200)
                )
            ], spacing=12),
            padding=ft.padding.all(20),
            bgcolor=ft.Colors.WHITE,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )
    
    def _create_scheduled_reports_section(self) -> ft.Container:
        """Crea la sezione report programmati"""
        # Switch principale per abilitare i report programmati
        reports_enabled_switch = ft.Switch(
            label="Abilita Report Programmati",
            value=self.scheduled_reports_settings['enabled'],
            on_change=lambda e: self._update_scheduled_reports_setting('enabled', e.control.value)
        )
        
        # Switch per report mattutino
        morning_enabled_switch = ft.Switch(
            label="Report Mattutino",
            value=self.scheduled_reports_settings['morning_enabled'],
            on_change=lambda e: self._update_scheduled_reports_setting('morning_enabled', e.control.value)
        )
        
        # Orario report mattutino
        morning_time_field = ft.TextField(
            label="Orario Mattutino",
            value=self.scheduled_reports_settings['morning_time'],
            hint_text="09:00",
            width=120,
            on_change=lambda e: self._update_scheduled_reports_setting('morning_time', e.control.value)
        )
        
        # Switch per report serale
        evening_enabled_switch = ft.Switch(
            label="Report Serale",
            value=self.scheduled_reports_settings['evening_enabled'],
            on_change=lambda e: self._update_scheduled_reports_setting('evening_enabled', e.control.value)
        )
        
        # Orario report serale
        evening_time_field = ft.TextField(
            label="Orario Serale",
            value=self.scheduled_reports_settings['evening_time'],
            hint_text="17:30",
            width=120,
            on_change=lambda e: self._update_scheduled_reports_setting('evening_time', e.control.value)
        )
        
        # Switch solo giorni lavorativi
        workdays_only_switch = ft.Switch(
            label="Solo Giorni Lavorativi",
            value=self.scheduled_reports_settings['workdays_only'],
            on_change=lambda e: self._update_scheduled_reports_setting('workdays_only', e.control.value)
        )
        
        # Destinatari report
        recipients_text = ", ".join(self.scheduled_reports_settings['recipients'])
        recipients_field = ft.TextField(
            label="Destinatari Report (separati da virgola)",
            value=recipients_text,
            hint_text="<EMAIL>, <EMAIL>",
            expand=True,
            on_change=lambda e: self._update_scheduled_reports_recipients(e.control.value),
            helper_text="Lascia vuoto per inviare solo al mittente configurato"
        )
        
        # Giorni di anticipo per scadenze
        days_ahead_field = ft.TextField(
            label="Giorni Anticipo Scadenze",
            value=str(self.scheduled_reports_settings['days_ahead_filter']),
            hint_text="7",
            width=120,
            on_change=lambda e: self._update_scheduled_reports_setting('days_ahead_filter', int(e.control.value) if e.control.value.isdigit() else 7)
        )
        
        # Checkboxes per tipi di contenuto
        project_completion_checkbox = ft.Checkbox(
            label="Percentuali Completamento Progetti",
            value=self.scheduled_reports_settings['include_project_percentages'],
            on_change=lambda e: self._update_scheduled_reports_setting('include_project_percentages', e.control.value)
        )
        
        overdue_items_checkbox = ft.Checkbox(
            label="Elementi in Ritardo",
            value=self.scheduled_reports_settings['include_overdue_items'],
            on_change=lambda e: self._update_scheduled_reports_setting('include_overdue_items', e.control.value)
        )
        
        upcoming_deadlines_checkbox = ft.Checkbox(
            label="Scadenze Imminenti",
            value=self.scheduled_reports_settings['include_upcoming_deadlines'],
            on_change=lambda e: self._update_scheduled_reports_setting('include_upcoming_deadlines', e.control.value)
        )
        
        incomplete_tasks_checkbox = ft.Checkbox(
            label="Attività Incomplete",
            value=self.scheduled_reports_settings['report_types']['incomplete_tasks'],
            on_change=lambda e: self._update_scheduled_reports_report_type('incomplete_tasks', e.control.value)
        )
        
        return ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Icon(
                        ft.Icons.SCHEDULE_SEND,
                        size=20,
                        color=ft.Colors.GREEN_600
                    ),
                    ft.Text(
                        "Report Programmati",
                        size=16,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.GREY_800
                    )
                ], spacing=8),
                
                ft.Text(
                    "Configura l'invio automatico di report via email",
                    size=12,
                    color=ft.Colors.GREY_500
                ),
                
                ft.Divider(height=1, color=ft.Colors.GREY_300),
                
                # Switch principale
                reports_enabled_switch,
                
                # Configurazione orari
                ft.Row([
                    ft.Column([
                        morning_enabled_switch,
                        morning_time_field
                    ], spacing=8),
                    ft.Column([
                        evening_enabled_switch,
                        evening_time_field
                    ], spacing=8),
                    ft.Column([
                        workdays_only_switch,
                        days_ahead_field
                    ], spacing=8)
                ], spacing=32),
                
                # Destinatari
                recipients_field,
                
                # Contenuto report
                ft.Text(
                    "Contenuto Report:",
                    size=14,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.GREY_700
                ),
                
                ft.Row([
                    ft.Column([
                        project_completion_checkbox,
                        overdue_items_checkbox
                    ], spacing=8),
                    ft.Column([
                        upcoming_deadlines_checkbox,
                        incomplete_tasks_checkbox
                    ], spacing=8)
                ], spacing=32),
                
                # Pulsante invio manuale
                ft.Container(
                    content=ft.Row([
                        ft.ElevatedButton(
                            text="Invia Report Ora",
                            icon=ft.Icons.SEND,
                            on_click=self._send_manual_report,
                            bgcolor=ft.Colors.BLUE_600,
                            color=ft.Colors.WHITE,
                            style=ft.ButtonStyle(
                                shape=ft.RoundedRectangleBorder(radius=8)
                            )
                        ),
                        ft.Text(
                            "Invia immediatamente un report completo ai destinatari configurati",
                            size=12,
                            color=ft.Colors.GREY_600,
                            expand=True
                        )
                    ], spacing=12, alignment=ft.MainAxisAlignment.START),
                    padding=ft.padding.all(12),
                    bgcolor=ft.Colors.BLUE_50,
                    border_radius=8,
                    border=ft.border.all(1, ft.Colors.BLUE_200)
                ),
                
                # Info aggiuntive
                ft.Container(
                    content=ft.Column([
                        ft.Text(
                            "[INFO] Informazioni:",
                            size=12,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.GREEN_600
                        ),
                        ft.Text(
                            "• I report vengono inviati automaticamente agli orari configurati",
                            size=11,
                            color=ft.Colors.GREY_600
                        ),
                        ft.Text(
                            "• L'opzione 'Solo Giorni Lavorativi' esclude sabato e domenica",
                            size=11,
                            color=ft.Colors.GREY_600
                        ),
                        ft.Text(
                            "• I report richiedono la configurazione SMTP per l'invio email",
                            size=11,
                            color=ft.Colors.GREY_600
                        ),
                        ft.Text(
                            "• Il contenuto del report può essere personalizzato",
                            size=11,
                            color=ft.Colors.GREY_600
                        )
                    ], spacing=4),
                    padding=ft.padding.all(12),
                    bgcolor=ft.Colors.GREEN_50,
                    border_radius=8,
                    border=ft.border.all(1, ft.Colors.GREEN_200)
                )
            ], spacing=12),
            padding=ft.padding.all(20),
            bgcolor=ft.Colors.WHITE,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )

    def _create_custom_reports_section(self) -> ft.Container:
        """Crea la sezione report personalizzati"""

        # Checkboxes per includere sezioni
        include_clients_checkbox = ft.Checkbox(
            label="Includi Clienti",
            value=self.custom_report_settings['include_clients'],
            on_change=lambda e: self._update_custom_report_setting('include_clients', e.control.value)
        )

        include_projects_checkbox = ft.Checkbox(
            label="Includi Progetti",
            value=self.custom_report_settings['include_projects'],
            on_change=lambda e: self._update_custom_report_setting('include_projects', e.control.value)
        )

        include_tasks_checkbox = ft.Checkbox(
            label="Includi Task",
            value=self.custom_report_settings['include_tasks'],
            on_change=lambda e: self._update_custom_report_setting('include_tasks', e.control.value)
        )

        include_deadlines_checkbox = ft.Checkbox(
            label="Includi Scadenze",
            value=self.custom_report_settings['include_deadlines'],
            on_change=lambda e: self._update_custom_report_setting('include_deadlines', e.control.value)
        )

        # Switch per solo task scadute
        expired_only_switch = ft.Switch(
            label="Solo Task Scadute/In Scadenza",
            value=self.custom_report_settings['include_expired_only'],
            on_change=lambda e: self._update_custom_report_setting('include_expired_only', e.control.value)
        )

        # Giorni di anticipo
        days_ahead_field = ft.TextField(
            label="Giorni di Anticipo",
            value=str(self.custom_report_settings['days_ahead']),
            hint_text="30",
            width=120,
            on_change=lambda e: self._update_custom_report_setting('days_ahead', int(e.control.value) if e.control.value.isdigit() else 30)
        )

        # Destinatari
        recipients_text = ", ".join(self.custom_report_settings['recipients'])
        recipients_field = ft.TextField(
            label="Destinatari (separati da virgola)",
            value=recipients_text,
            hint_text="<EMAIL>, <EMAIL>",
            expand=True,
            on_change=lambda e: self._update_custom_report_recipients(e.control.value),
            helper_text="Lascia vuoto per inviare solo al mittente configurato"
        )

        return ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Icon(
                        ft.Icons.ANALYTICS,
                        size=20,
                        color=ft.Colors.PURPLE_600
                    ),
                    ft.Text(
                        "Report Personalizzati",
                        size=16,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.GREY_800
                    )
                ], spacing=8),

                ft.Text(
                    "Crea e invia report personalizzati con filtri specifici",
                    size=12,
                    color=ft.Colors.GREY_500
                ),

                ft.Divider(height=1, color=ft.Colors.GREY_300),

                # Contenuto da includere
                ft.Text(
                    "Contenuto Report:",
                    size=14,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.GREY_700
                ),

                ft.Row([
                    ft.Column([
                        include_clients_checkbox,
                        include_projects_checkbox
                    ], spacing=8),
                    ft.Column([
                        include_tasks_checkbox,
                        include_deadlines_checkbox
                    ], spacing=8)
                ], spacing=32),

                # Filtri
                ft.Text(
                    "Filtri:",
                    size=14,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.GREY_700
                ),

                ft.Row([
                    expired_only_switch,
                    days_ahead_field
                ], spacing=32),

                # Destinatari
                recipients_field,

                # Pulsanti azione
                ft.Container(
                    content=ft.Row([
                        ft.ElevatedButton(
                            text="Invia Report Personalizzato",
                            icon=ft.Icons.SEND,
                            on_click=self._send_custom_report,
                            bgcolor=ft.Colors.PURPLE_600,
                            color=ft.Colors.WHITE,
                            style=ft.ButtonStyle(
                                shape=ft.RoundedRectangleBorder(radius=8)
                            )
                        ),
                        ft.OutlinedButton(
                            text="Anteprima",
                            icon=ft.Icons.PREVIEW,
                            on_click=self._preview_custom_report,
                            style=ft.ButtonStyle(
                                shape=ft.RoundedRectangleBorder(radius=8)
                            )
                        )
                    ], spacing=12),
                    padding=ft.padding.all(12),
                    bgcolor=ft.Colors.PURPLE_50,
                    border_radius=8,
                    border=ft.border.all(1, ft.Colors.PURPLE_200)
                ),

                # Info aggiuntive
                ft.Container(
                    content=ft.Column([
                        ft.Text(
                            "💡 Suggerimenti:",
                            size=12,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.PURPLE_600
                        ),
                        ft.Text(
                            "• Usa 'Solo Task Scadute' per report di emergenza",
                            size=11,
                            color=ft.Colors.GREY_600
                        ),
                        ft.Text(
                            "• Personalizza i giorni di anticipo per scadenze future",
                            size=11,
                            color=ft.Colors.GREY_600
                        ),
                        ft.Text(
                            "• L'anteprima mostra il contenuto senza inviare email",
                            size=11,
                            color=ft.Colors.GREY_600
                        ),
                        ft.Text(
                            "• I report personalizzati usano template HTML avanzati",
                            size=11,
                            color=ft.Colors.GREY_600
                        )
                    ], spacing=4),
                    padding=ft.padding.all(12),
                    bgcolor=ft.Colors.PURPLE_50,
                    border_radius=8,
                    border=ft.border.all(1, ft.Colors.PURPLE_200)
                )
            ], spacing=12),
            padding=ft.padding.all(20),
            bgcolor=ft.Colors.WHITE,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )

    def _create_app_section(self) -> ft.Container:
        """Crea la sezione impostazioni applicazione"""
        # Theme selector
        theme_dropdown = ft.Dropdown(
            label="Tema",
            options=[
                ft.dropdown.Option(text="Chiaro", key="light"),
                ft.dropdown.Option(text="Scuro", key="dark"),
                ft.dropdown.Option(text="Sistema", key="system")
            ],
            value=self.app_settings['theme'],
            width=150,
            on_change=lambda e: self._update_app_setting('theme', e.control.value)
        )
        
        # Language selector
        language_dropdown = ft.Dropdown(
            label="Lingua",
            options=[
                ft.dropdown.Option(text="Italiano", key="it"),
                ft.dropdown.Option(text="English", key="en")
            ],
            value=self.app_settings['language'],
            width=150,
            on_change=lambda e: self._update_app_setting('language', e.control.value)
        )
        
        # Auto backup
        auto_backup_switch = ft.Switch(
            label="Backup Automatico",
            value=self.app_settings['auto_backup'],
            on_change=lambda e: self._update_app_setting('auto_backup', e.control.value)
        )
        
        # Backup interval
        backup_interval_field = ft.TextField(
            label="Intervallo Backup (giorni)",
            value=str(self.app_settings['backup_interval']),
            width=150,
            hint_text="7",
            on_change=lambda e: self._update_app_setting('backup_interval', int(e.control.value) if e.control.value.isdigit() else 7)
        )
        
        # Max backups
        max_backups_field = ft.TextField(
            label="Backup Massimi",
            value=str(self.app_settings['max_backups']),
            width=150,
            hint_text="10",
            on_change=lambda e: self._update_app_setting('max_backups', int(e.control.value) if e.control.value.isdigit() else 10)
        )
        
        return ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Icon(
                        ft.Icons.SETTINGS,
                        size=20,
                        color=ft.Colors.GREEN_600
                    ),
                    ft.Text(
                        "Impostazioni Applicazione",
                        size=16,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.GREY_800
                    )
                ], spacing=8),
                
                ft.Text(
                    "Personalizza l'aspetto e il comportamento dell'applicazione",
                    size=12,
                    color=ft.Colors.GREY_500
                ),
                
                ft.Divider(height=1, color=ft.Colors.GREY_300),
                
                # Aspetto
                ft.Row([
                    theme_dropdown,
                    language_dropdown
                ], spacing=16),
                
                # Backup
                auto_backup_switch,
                
                ft.Row([
                    backup_interval_field,
                    max_backups_field
                ], spacing=16),
                
                # Azioni database
                ft.Container(
                    content=ft.Column([
                        ft.Text(
                            "Gestione Database",
                            size=14,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.GREY_700
                        ),
                        
                        ft.Row([
                            ft.OutlinedButton(
                                text="Backup Manuale",
                                icon=ft.Icons.BACKUP,
                                on_click=self._manual_google_drive_backup,
                                disabled=not self.google_drive_service.is_authenticated
                            ),

                            ft.OutlinedButton(
                                text="Ripristina Backup",
                                icon=ft.Icons.RESTORE,
                                on_click=self._restore_google_drive_backup,
                                disabled=not self.google_drive_service.is_authenticated
                            ),
                            
                                                    ft.OutlinedButton(
                            text="Esporta Dati",
                            icon=ft.Icons.DOWNLOAD,
                            on_click=lambda _: self._export_data()
                        ),
                        
                        ft.OutlinedButton(
                            text="Controlla Aggiornamenti",
                            icon=ft.Icons.SYSTEM_UPDATE,
                            on_click=lambda _: self._check_for_updates()
                        ),
                        
                        ft.OutlinedButton(
                            text="Configura GitHub Token",
                            icon=ft.Icons.KEY,
                            on_click=lambda _: self._configure_github_token(),
                            tooltip="Richiesto per repository privati"
                        )
                        ], spacing=8)
                    ], spacing=8),
                    padding=ft.padding.only(top=12)
                )
            ], spacing=12),
            padding=ft.padding.all(20),
            bgcolor=ft.Colors.WHITE,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )
    
    def _create_windows_section(self) -> ft.Container:
        """Crea la sezione impostazioni Windows"""
        # Switch avvio automatico
        startup_enabled_switch = ft.Switch(
            label="Avvia con Windows",
            value=self.windows_settings['startup_enabled'],
            on_change=lambda e: self._update_windows_setting('startup_enabled', e.control.value)
        )
        
        startup_minimized_switch = ft.Switch(
            label="Avvia Minimizzato",
            value=self.windows_settings['startup_minimized'],
            on_change=lambda e: self._update_windows_setting('startup_minimized', e.control.value)
        )
        
        # Notifiche
        notifications_enabled_switch = ft.Switch(
            label="Notifiche Desktop",
            value=self.windows_settings['notifications_enabled'],
            on_change=lambda e: self._update_windows_setting('notifications_enabled', e.control.value)
        )
        
        notification_sound_switch = ft.Switch(
            label="Suoni Notifiche",
            value=self.windows_settings['notification_sound'],
            on_change=lambda e: self._update_windows_setting('notification_sound', e.control.value)
        )
        
        # Filtro priorità notifiche
        priority_filter_dropdown = ft.Dropdown(
            label="Mostra Notifiche",
            options=[
                ft.dropdown.Option(text="Tutte", key="all"),
                ft.dropdown.Option(text="Normali e superiori", key="normal"),
                ft.dropdown.Option(text="Solo importanti", key="high"),
                ft.dropdown.Option(text="Solo urgenti", key="urgent")
            ],
            value=self.windows_settings['notification_priority_filter'],
            width=200,
            on_change=lambda e: self._update_windows_setting('notification_priority_filter', e.control.value)
        )
        
        # Orari silenziosi
        quiet_hours_switch = ft.Switch(
            label="Orari Silenziosi",
            value=self.windows_settings['notification_quiet_hours_enabled'],
            on_change=lambda e: self._update_windows_setting('notification_quiet_hours_enabled', e.control.value)
        )
        
        quiet_start_field = ft.TextField(
            label="Inizio Silenzioso",
            value=self.windows_settings['notification_quiet_start'],
            hint_text="22:00",
            width=120,
            on_change=lambda e: self._update_windows_setting('notification_quiet_start', e.control.value)
        )
        
        quiet_end_field = ft.TextField(
            label="Fine Silenzioso",
            value=self.windows_settings['notification_quiet_end'],
            hint_text="08:00",
            width=120,
            on_change=lambda e: self._update_windows_setting('notification_quiet_end', e.control.value)
        )
        
        return ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Icon(
                        ft.Icons.COMPUTER,
                        size=20,
                        color=ft.Colors.PURPLE_600
                    ),
                    ft.Text(
                        "Impostazioni Windows",
                        size=16,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.GREY_800
                    )
                ], spacing=8),
                
                ft.Text(
                    "Configura l'integrazione con il sistema Windows",
                    size=12,
                    color=ft.Colors.GREY_500
                ),
                
                ft.Divider(height=1, color=ft.Colors.GREY_300),
                
                # Startup
                ft.Text(
                    "Avvio Automatico:",
                    size=14,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.GREY_700
                ),
                
                ft.Row([
                    startup_enabled_switch,
                    startup_minimized_switch
                ], spacing=32),
                
                # Notifiche
                ft.Text(
                    "Notifiche Desktop:",
                    size=14,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.GREY_700
                ),
                
                ft.Row([
                    notifications_enabled_switch,
                    notification_sound_switch
                ], spacing=32),
                
                ft.Row([
                    priority_filter_dropdown
                ], spacing=16),
                
                # Orari silenziosi
                quiet_hours_switch,
                
                ft.Row([
                    quiet_start_field,
                    quiet_end_field
                ], spacing=16),
                
                # Test notifiche
                ft.Container(
                    content=ft.Column([
                        ft.Text(
                            "Test Sistema:",
                            size=14,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.GREY_700
                        ),
                        
                        ft.Row([
                            ft.ElevatedButton(
                                text="Test Notifica",
                                icon=ft.Icons.NOTIFICATIONS_ACTIVE,
                                on_click=self._test_notification,
                                bgcolor=ft.Colors.BLUE_600,
                                color=ft.Colors.WHITE
                            ),
                            
                            ft.ElevatedButton(
                                text="Test Scadenza",
                                icon=ft.Icons.SCHEDULE,
                                on_click=self._test_deadline_notification,
                                bgcolor=ft.Colors.ORANGE_600,
                                color=ft.Colors.WHITE
                            )
                        ], spacing=8)
                    ], spacing=8),
                    padding=ft.padding.only(top=12)
                ),
                
                # Info aggiuntive
                ft.Container(
                    content=ft.Column([
                        ft.Text(
                            "[INFO] Informazioni:",
                            size=12,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.PURPLE_600
                        ),
                        ft.Text(
                            "• L'avvio automatico registra l'app nel sistema Windows",
                            size=11,
                            color=ft.Colors.GREY_600
                        ),
                        ft.Text(
                            "• Le notifiche desktop appaiono nell'area notifiche",
                            size=11,
                            color=ft.Colors.GREY_600
                        ),
                        ft.Text(
                            "• Gli orari silenziosi sopprimono le notifiche non urgenti",
                            size=11,
                            color=ft.Colors.GREY_600
                        ),
                        ft.Text(
                            "• Richiede privilegi utente per modificare il registro",
                            size=11,
                            color=ft.Colors.GREY_600
                        )
                    ], spacing=4),
                    padding=ft.padding.all(12),
                    bgcolor=ft.Colors.PURPLE_50,
                    border_radius=8,
                    border=ft.border.all(1, ft.Colors.PURPLE_200)
                )
            ], spacing=12),
            padding=ft.padding.all(20),
            bgcolor=ft.Colors.WHITE,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )
    
    def _create_google_drive_section(self) -> ft.Container:
        """Crea la sezione configurazione Google Drive"""
        # This method is now replaced by _create_google_services_section
        return self._create_google_services_section()
    
    def _create_google_services_section(self) -> ft.Container:
        """Crea la sezione configurazione servizi Google (Drive + Calendar)"""
        return ft.Container(
            content=ft.Column([
                # Google Drive Section
                ft.Container(
                    content=ft.Column([
                        ft.Row([
                            ft.Icon(ft.Icons.CLOUD, size=20, color=ft.Colors.BLUE_600),
                            ft.Text("Google Drive Backup", size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_800)
                        ], spacing=8),
                        
                        ft.Text("Configura il backup automatico su Google Drive", size=12, color=ft.Colors.GREY_500),
                        ft.Divider(height=1, color=ft.Colors.GREY_300),
                        
                        # Status connessione Drive
                        ft.Row([
                            ft.Text(
                                f"Stato: {'Connesso' if self.google_drive_service.is_authenticated else 'Non connesso'}",
                                size=12,
                                color=ft.Colors.GREEN if self.google_drive_service.is_authenticated else ft.Colors.RED,
                                weight=ft.FontWeight.BOLD
                            )
                        ]),
                        
                        # Switch abilitazione Drive
                        ft.Switch(
                            label="Abilita Backup Google Drive",
                            value=self.google_services_settings['drive_enabled'] and self.google_drive_service.is_authenticated,
                            on_change=lambda e: self._update_google_drive_setting('drive_enabled', e.control.value)
                        ),
                        
                        # Configurazioni Drive
                        ft.Row([
                            ft.Switch(
                                label="Backup Automatico",
                                value=self.google_services_settings['drive_auto_backup'],
                                on_change=lambda e: self._update_google_drive_setting('drive_auto_backup', e.control.value),
                                disabled=not self.google_drive_service.is_authenticated
                            ),
                            ft.Dropdown(
                                label="Frequenza",
                                options=[
                                    ft.dropdown.Option(text="Giornaliero", key="daily"),
                                    ft.dropdown.Option(text="Settimanale", key="weekly"),
                                    ft.dropdown.Option(text="Mensile", key="monthly")
                                ],
                                value=self.google_services_settings['drive_backup_frequency'],
                                width=150,
                                on_change=lambda e: self._update_google_drive_setting('drive_backup_frequency', e.control.value),
                                disabled=not self.google_drive_service.is_authenticated
                            )
                        ], spacing=32),
                        
                        # Retention settings
                        ft.Row([
                            ft.TextField(
                                label="Giorni di Retention",
                                value=str(self.google_services_settings['drive_retention_days']),
                                width=150,
                                hint_text="30",
                                on_change=lambda e: self._update_google_drive_setting('drive_retention_days', int(e.control.value) if e.control.value.isdigit() else 30),
                                disabled=not self.google_drive_service.is_authenticated
                            ),
                            ft.Text("Backup più vecchi verranno eliminati automaticamente", size=10, color=ft.Colors.GREY_600)
                        ], spacing=16),

                        # Pulsanti azioni Drive - Prima riga
                        ft.Row([
                            ft.ElevatedButton(
                                text="Connetti Google Drive",
                                icon=ft.Icons.LOGIN,
                                on_click=self._authenticate_google_drive,
                                bgcolor=ft.Colors.GREEN_600,
                                color=ft.Colors.WHITE,
                                disabled=self.google_drive_service.is_authenticated
                            ),
                            ft.OutlinedButton(
                                text="Disconnetti",
                                icon=ft.Icons.LOGOUT,
                                on_click=self._disconnect_google_drive,
                                disabled=not self.google_drive_service.is_authenticated
                            ),
                            ft.OutlinedButton(
                                text="Backup Manuale",
                                icon=ft.Icons.BACKUP,
                                on_click=self._manual_google_drive_backup,
                                disabled=not self.google_drive_service.is_authenticated
                            )
                        ], spacing=8),

                        # Pulsanti azioni Drive - Seconda riga
                        ft.Row([
                            ft.OutlinedButton(
                                text="Visualizza Backup",
                                icon=ft.Icons.LIST,
                                on_click=self._show_google_drive_backups,
                                disabled=not self.google_drive_service.is_authenticated
                            ),
                            ft.OutlinedButton(
                                text="Statistiche",
                                icon=ft.Icons.ANALYTICS,
                                on_click=lambda e: self._show_backup_statistics(),
                                disabled=not self.google_drive_service.is_authenticated
                            ),
                            ft.OutlinedButton(
                                text="Info Database",
                                icon=ft.Icons.INFO,
                                on_click=self._show_database_info,
                                style=ft.ButtonStyle(color=ft.Colors.BLUE_600)
                            )
                        ], spacing=8),

                        # Pulsanti azioni Drive - Terza riga
                        ft.Row([
                            ft.OutlinedButton(
                                text="Pulisci Vecchi",
                                icon=ft.Icons.CLEANING_SERVICES,
                                on_click=self._cleanup_old_backups,
                                disabled=not self.google_drive_service.is_authenticated,
                                style=ft.ButtonStyle(color=ft.Colors.ORANGE_600)
                            ),
                            ft.OutlinedButton(
                                text="Fix Visibilità",
                                icon=ft.Icons.VISIBILITY,
                                on_click=self._fix_backup_visibility,
                                disabled=not self.google_drive_service.is_authenticated,
                                style=ft.ButtonStyle(color=ft.Colors.PURPLE_600)
                            ),
                            ft.OutlinedButton(
                                text="Diagnosi",
                                icon=ft.Icons.SEARCH,
                                on_click=self._diagnose_backup_visibility,
                                disabled=not self.google_drive_service.is_authenticated,
                                style=ft.ButtonStyle(color=ft.Colors.TEAL_600)
                            )
                        ], spacing=8)
                    ], spacing=12),
                    padding=ft.padding.all(16),
                    bgcolor=ft.Colors.BLUE_50,
                    border_radius=8,
                    border=ft.border.all(1, ft.Colors.BLUE_200)
                ),
                
                ft.Container(height=16),  # Spacer
                
                # Google Calendar Section  
                ft.Container(
                    content=ft.Column([
                        ft.Row([
                            ft.Icon(ft.Icons.CALENDAR_MONTH, size=20, color=ft.Colors.PURPLE_600),
                            ft.Text("Google Calendar", size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_800)
                        ], spacing=8),
                        
                        ft.Text("Sincronizza scadenze con Google Calendar", size=12, color=ft.Colors.GREY_500),
                        ft.Divider(height=1, color=ft.Colors.GREY_300),
                        
                        # Status connessione Calendar
                        ft.Row([
                            ft.Text(
                                f"Stato: {'Connesso' if self.google_calendar_service.is_authenticated() else 'Non connesso'}",
                                size=12,
                                color=ft.Colors.GREEN if self.google_calendar_service.is_authenticated() else ft.Colors.RED,
                                weight=ft.FontWeight.BOLD
                            )
                        ]),
                        
                        # Switch abilitazione Calendar
                        ft.Switch(
                            label="Abilita Google Calendar",
                            value=self.google_services_settings['calendar_enabled'] and self.google_calendar_service.is_authenticated(),
                            on_change=lambda e: self._update_google_calendar_setting('calendar_enabled', e.control.value)
                        ),
                        
                        # Configurazioni Calendar
                        ft.Row([
                            ft.Switch(
                                label="Sync Automatico",
                                value=self.google_services_settings['calendar_auto_sync'],
                                on_change=lambda e: self._update_google_calendar_setting('calendar_auto_sync', e.control.value),
                                disabled=not self.google_calendar_service.is_authenticated()
                            ),
                            ft.Checkbox(
                                label="Colori Priorità",
                                value=self.google_services_settings['calendar_color_priority'],
                                on_change=lambda e: self._update_google_calendar_setting('calendar_color_priority', e.control.value),
                                disabled=not self.google_calendar_service.is_authenticated()
                            )
                        ], spacing=32),
                        
                        # Pulsanti azioni Calendar
                        ft.Row([
                            ft.ElevatedButton(
                                text="Connetti Calendar",
                                icon=ft.Icons.LOGIN,
                                on_click=self._authenticate_google_calendar,
                                bgcolor=ft.Colors.PURPLE_600,
                                color=ft.Colors.WHITE,
                                disabled=self.google_calendar_service.is_authenticated()
                            ),
                            ft.OutlinedButton(
                                text="Disconnetti",
                                icon=ft.Icons.LOGOUT,
                                on_click=self._disconnect_google_calendar,
                                disabled=not self.google_calendar_service.is_authenticated()
                            ),
                            ft.OutlinedButton(
                                text="Sync Manuale",
                                icon=ft.Icons.SYNC,
                                on_click=self._sync_google_calendar,
                                disabled=not self.google_calendar_service.is_authenticated()
                            )
                        ], spacing=8)
                    ], spacing=12),
                    padding=ft.padding.all(16),
                    bgcolor=ft.Colors.PURPLE_50,
                    border_radius=8,
                    border=ft.border.all(1, ft.Colors.PURPLE_200)
                ),
                
                ft.Container(height=16),  # Spacer
                
                # Google Tasks Section
                ft.Container(
                    content=ft.Column([
                        ft.Row([
                            ft.Icon(ft.Icons.TASK, size=20, color=ft.Colors.TEAL_600),
                            ft.Text("Google Tasks", size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_800)
                        ], spacing=8),
                        
                        ft.Text("Configura la sincronizzazione delle attività con Google Tasks", size=12, color=ft.Colors.GREY_500),
                        ft.Divider(height=1, color=ft.Colors.GREY_300),
                        
                        # Status connessione Tasks
                        ft.Row([
                            ft.Text(
                                f"Stato: {'Connesso' if self.google_tasks_service.is_enabled() else 'Non connesso'}",
                                size=12,
                                color=ft.Colors.TEAL if self.google_tasks_service.is_enabled() else ft.Colors.RED,
                                weight=ft.FontWeight.BOLD
                            )
                        ]),
                        
                        # Switch abilitazione Tasks
                        ft.Switch(
                            label="Abilita Google Tasks",
                            value=self.google_services_settings['tasks_enabled'] and self.google_tasks_service.is_enabled(),
                            on_change=lambda e: self._update_google_tasks_setting('tasks_enabled', e.control.value)
                        ),
                        
                        # Configurazioni Tasks
                        ft.Row([
                            ft.Switch(
                                label="Sync Automatico",
                                value=self.google_services_settings['tasks_auto_sync'],
                                on_change=lambda e: self._update_google_tasks_setting('tasks_auto_sync', e.control.value),
                                disabled=not self.google_tasks_service.is_enabled()
                            ),
                            ft.Checkbox(
                                label="Crea Lista per Scadenza",
                                value=self.google_services_settings['tasks_create_list_per_deadline'],
                                on_change=lambda e: self._update_google_tasks_setting('tasks_create_list_per_deadline', e.control.value),
                                disabled=not self.google_tasks_service.is_enabled()
                            )
                        ], spacing=32),
                        
                        # Pulsanti azioni Tasks
                        ft.Row([
                            ft.ElevatedButton(
                                text="Connetti Google Tasks",
                                icon=ft.Icons.LOGIN,
                                on_click=self._authenticate_google_tasks,
                                bgcolor=ft.Colors.TEAL_600,
                                color=ft.Colors.WHITE,
                                disabled=self.google_tasks_service.is_enabled()
                            ),
                            ft.OutlinedButton(
                                text="Disconnetti",
                                icon=ft.Icons.LOGOUT,
                                on_click=self._disconnect_google_tasks,
                                disabled=not self.google_tasks_service.is_enabled()
                            ),
                            ft.OutlinedButton(
                                text="Sync Manuale",
                                icon=ft.Icons.SYNC,
                                on_click=self._sync_google_tasks,
                                disabled=not self.google_tasks_service.is_enabled()
                            )
                        ], spacing=8)
                    ], spacing=12),
                    padding=ft.padding.all(16),
                    bgcolor=ft.Colors.TEAL_50,
                    border_radius=8,
                    border=ft.border.all(1, ft.Colors.TEAL_200)
                )
            ], spacing=0),
            padding=ft.padding.all(20),
            bgcolor=ft.Colors.WHITE,
            border_radius=ft.border_radius.only(bottom_left=8, bottom_right=8),
            border=ft.border.all(1, ft.Colors.GREY_300)
        )
    
    def _update_google_calendar_setting(self, key: str, value: Any):
        """Aggiorna un'impostazione Google Calendar"""
        self.google_services_settings[key] = value
        logger.debug(f"Google Calendar setting updated: {key} = {value}")
    
    def _update_google_tasks_setting(self, key: str, value: Any):
        """Aggiorna un'impostazione Google Tasks"""
        self.google_services_settings[key] = value
        logger.debug(f"Google Tasks setting updated: {key} = {value}")
    
    def _update_smtp_setting(self, key: str, value: Any):
        """Aggiorna un'impostazione SMTP"""
        self.smtp_settings[key] = value
        logger.debug(f"SMTP setting updated: {key} = {value}")
    
    def _update_alert_setting(self, key: str, value: Any):
        """Aggiorna un'impostazione alert"""
        self.alert_settings[key] = value
        logger.debug(f"Alert setting updated: {key} = {value}")
    
    def _update_app_setting(self, key: str, value: Any):
        """Aggiorna un'impostazione applicazione"""
        self.app_settings[key] = value
        logger.debug(f"App setting updated: {key} = {value}")
    
    def _update_advance_days(self, value: str):
        """Aggiorna i giorni di anticipo per gli alert"""
        try:
            days = [int(d.strip()) for d in value.split(',') if d.strip().isdigit()]
            self.alert_settings['advance_days'] = sorted(days)
            logger.debug(f"Advance days updated: {days}")
        except Exception as e:
            logger.error(f"Errore parsing giorni anticipo: {e}")
    
    def _update_reminder_recipients(self, value: str):
        """Aggiorna i destinatari dei promemoria"""
        try:
            # Parse comma-separated email addresses
            emails = [email.strip() for email in value.split(',') if email.strip()]
            # Basic email validation
            valid_emails = []
            for email in emails:
                if '@' in email and '.' in email.split('@')[1]:
                    valid_emails.append(email)
            
            self.alert_settings['reminder_recipients'] = valid_emails
        except Exception as e:
            logger.error(f"Errore aggiornamento destinatari: {e}")
    
    def _update_scheduled_reports_setting(self, key: str, value):
        """Aggiorna un'impostazione dei report programmati"""
        try:
            self.scheduled_reports_settings[key] = value
            logger.info(f"Impostazione report programmati '{key}' aggiornata: {value}")
        except Exception as e:
            logger.error(f"Errore aggiornamento impostazione report: {e}")
    
    def _update_scheduled_reports_report_type(self, key: str, value: bool):
        """Aggiorna un tipo di report programmato"""
        try:
            self.scheduled_reports_settings['report_types'][key] = value
            logger.info(f"Tipo report '{key}' aggiornato: {value}")
        except Exception as e:
            logger.error(f"Errore aggiornamento tipo report: {e}")
    
    def _update_scheduled_reports_recipients(self, value: str):
        """Aggiorna i destinatari dei report programmati"""
        try:
            # Pulisce e divide gli indirizzi email
            emails = [email.strip() for email in value.split(',') if email.strip()]
            self.scheduled_reports_settings['recipients'] = emails
            logger.info(f"Destinatari report programmati aggiornati: {emails}")
        except Exception as e:
            logger.error(f"Errore aggiornamento destinatari report: {e}")
            
    def _send_manual_report(self, e):
        """Invia un report manuale"""
        logger.info("🔵 BUTTON CLICKED: _send_manual_report called")
        try:
            # Verifica se il servizio scheduler è disponibile
            if not hasattr(self.app, 'scheduler_service'):
                logger.error("Servizio scheduler non disponibile")
                self._show_notification("Servizio scheduler non disponibile", "error")
                return
            
            logger.info("[OK] Scheduler service found, proceeding with report sending")
            
            # Determina i destinatari
            logger.info(f"[INFO] Scheduled reports settings: {self.scheduled_reports_settings}")
            recipients = self.scheduled_reports_settings.get('recipients', [])
            logger.info(f"[INFO] Recipients found: {recipients}")
            
            if not recipients:
                sender_email = self.config.email_config.get('from_email', 'N/A')
                recipients_text = f"Email mittente: {sender_email}"
                logger.warning("No recipients configured, using sender email")
            else:
                recipients_text = f"Destinatari: {', '.join(recipients)}"
            
            logger.info(f"[INFO] Recipients text: {recipients_text}")
            
            # Mostra dialog di conferma usando overlay
            self._show_manual_report_confirmation_dialog(recipients_text)
                
        except Exception as ex:
            logger.error(f"Errore invio report manuale: {ex}")
            self._show_notification("Errore nell'invio del report", "error")
    
    def _show_manual_report_confirmation_dialog(self, recipients_text: str):
        """Mostra dialog di conferma per l'invio manuale usando overlay"""
        try:
            def confirm_send(e):
                logger.info("[DEBUG] CONFIRM SEND: User confirmed manual report")
                # Chiudi dialog
                self.app.page.overlay.clear()
                self.app.page.update()
                
                try:
                    # Mostra loading
                    self._show_loading_dialog("Invio report in corso...")
                    
                    # Force UI update
                    self.app.page.update()
                    
                    # Small delay to ensure loading dialog shows
                    import time
                    time.sleep(0.5)
                    
                    # Invia il report
                    logger.info("[EMAIL] Calling scheduler_service.send_manual_report()")
                    success = self.app.scheduler_service.send_manual_report()
                    logger.info(f"[EMAIL] Manual report result: {success}")
                    
                    # Chiudi loading
                    self.app.page.overlay.clear()
                    self.app.page.update()
                    
                    if success:
                        self._show_notification("Report inviato con successo!", "success")
                        self._show_success_dialog("Report inviato con successo ai destinatari configurati!")
                    else:
                        self._show_notification("Errore nell'invio del report", "error")
                        self._show_error_dialog("Errore durante l'invio del report. Verifica la configurazione SMTP.")
                        
                except Exception as send_ex:
                    logger.error(f"[ERROR] Errore durante l'invio: {send_ex}")
                    import traceback
                    logger.error(f"[ERROR] Traceback: {traceback.format_exc()}")
                    
                    # Chiudi loading
                    try:
                        self.app.page.overlay.clear()
                        self.app.page.update()
                    except:
                        pass
                    
                    self._show_error_dialog(f"Errore durante l'invio: {str(send_ex)}")
                        
            def cancel_send(e):
                logger.info("[DEBUG] CANCEL SEND: User cancelled manual report")
                self.app.page.overlay.clear()
                self.app.page.update()
            
            def send_direct(e):
                logger.info("[DEBUG] DIRECT SEND: User chose direct send without confirmation")
                # Chiudi dialog
                self.app.page.overlay.clear()
                self.app.page.update()
                
                # Invia direttamente
                self._send_manual_report_direct()
            
            dialog = ft.AlertDialog(
                modal=True,
                title=ft.Text("Conferma Invio Report", weight=ft.FontWeight.BOLD),
                content=ft.Container(
                    content=ft.Column([
                        ft.Text("Sei sicuro di voler inviare il report ora?", size=14),
                        ft.Container(height=10),
                        ft.Text(recipients_text, size=12, color=ft.Colors.GREY_600, weight=ft.FontWeight.BOLD),
                        ft.Container(height=10),
                        ft.Text("Il report verrà generato e inviato via email.", size=11, color=ft.Colors.GREY_500)
                    ], tight=True, spacing=5),
                    width=400
                ),
                actions=[
                    ft.Row([
                        ft.TextButton("Annulla", on_click=cancel_send),
                        ft.TextButton("Invia Diretto", on_click=send_direct, 
                                    style=ft.ButtonStyle(color=ft.Colors.ORANGE)),
                        ft.ElevatedButton("Conferma e Invia", on_click=confirm_send,
                                        bgcolor=ft.Colors.GREEN, color=ft.Colors.WHITE)
                    ], alignment=ft.MainAxisAlignment.END, spacing=10)
                ],
                actions_alignment=ft.MainAxisAlignment.END
            )
            
            logger.info("[DEBUG] Adding dialog to overlay...")
            self.app.page.overlay.append(dialog)
            dialog.open = True
            self.app.page.update()
            logger.info("[DEBUG] Dialog should be visible now")
            
        except Exception as e:
            logger.error(f"Errore creazione dialog conferma: {e}")
            # Fallback: invio diretto
            self._send_manual_report_direct()
    
    def _send_manual_report_direct(self):
        """Invia il report manuale direttamente senza conferma"""
        logger.info("[EMAIL] DIRECT SEND: Sending manual report directly")
        
        try:
            # Mostra loading
            self._show_loading_dialog("Invio report in corso...")
            
            # Force UI update
            self.app.page.update()
            
            # Small delay to ensure loading dialog shows
            import time
            time.sleep(0.5)
            
            logger.info("[INFO] Inizio invio report...")
            success = self.app.scheduler_service.send_manual_report()
            logger.info(f"[EMAIL] Direct manual report result: {success}")
            
            # Chiudi loading
            self.app.page.overlay.clear()
            self.app.page.update()
            
            if success:
                self._show_notification("Report inviato con successo!", "success")
                self._show_success_dialog("Report inviato con successo!")
            else:
                self._show_notification("Errore nell'invio del report", "error")
                self._show_error_dialog("Errore durante l'invio del report. Verifica la configurazione SMTP.")
                
        except Exception as e:
            logger.error(f"[ERROR] Errore invio diretto report: {e}")
            import traceback
            logger.error(f"[ERROR] Traceback: {traceback.format_exc()}")
            
            # Chiudi loading in caso di errore
            try:
                self.app.page.overlay.clear()
                self.app.page.update()
            except:
                pass
            
            self._show_error_dialog(f"Errore durante l'invio: {str(e)}")
    
    def _show_loading_dialog(self, message: str):
        """Mostra un dialog di caricamento"""
        try:
            # Create and store the loading dialog
            self.loading_dialog = ft.AlertDialog(
                modal=True,
                title=ft.Text("⏳ Caricamento", color=ft.Colors.BLUE_600, weight=ft.FontWeight.BOLD),
                content=ft.Text(message, size=14),
                actions_alignment=ft.MainAxisAlignment.CENTER
            )

            self.app.page.overlay.clear()
            self.app.page.overlay.append(self.loading_dialog)
            self.loading_dialog.open = True
            self.app.page.update()

        except Exception as e:
            logger.error(f"Errore loading dialog: {e}")
            # Fallback: use notification
            self._show_notification(f"[LOADING] {message}", "info")
    
    def _show_success_dialog(self, message: str, on_close_callback=None):
        """Mostra dialog di successo"""
        def close_dialog(e):
            self.app.page.overlay.clear()
            self.app.page.update()
            # Call the callback after closing the dialog
            if on_close_callback:
                on_close_callback()

        dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text("[OK] Successo", color=ft.Colors.GREEN, weight=ft.FontWeight.BOLD),
            content=ft.Text(message, size=14),
            actions=[
                ft.ElevatedButton("OK", on_click=close_dialog, bgcolor=ft.Colors.GREEN, color=ft.Colors.WHITE)
            ],
            actions_alignment=ft.MainAxisAlignment.CENTER
        )

        self.app.page.overlay.clear()
        self.app.page.overlay.append(dialog)
        dialog.open = True
        self.app.page.update()
    
    def _show_error_dialog(self, message: str):
        """Mostra dialog di errore"""
        def close_dialog(e):
            self.app.page.overlay.clear()
            self.app.page.update()
        
        dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text("[ERROR] Errore", color=ft.Colors.RED, weight=ft.FontWeight.BOLD),
            content=ft.Container(
                content=ft.Text(message, size=14),
                width=400
            ),
            actions=[
                ft.ElevatedButton("OK", on_click=close_dialog, bgcolor=ft.Colors.RED, color=ft.Colors.WHITE)
            ],
            actions_alignment=ft.MainAxisAlignment.CENTER
        )
        
        self.app.page.overlay.clear()
        self.app.page.overlay.append(dialog)
        dialog.open = True
        self.app.page.update()
    
    def _show_notification(self, message: str, type: str = "info"):
        """Mostra una notifica"""
        try:
            if hasattr(self.app, 'main_layout') and hasattr(self.app.main_layout, 'show_notification'):
                self.app.main_layout.show_notification(message, type)
            else:
                # Fallback: mostra un snackbar
                snack = ft.SnackBar(
                    content=ft.Text(message),
                    bgcolor=ft.Colors.GREEN if type == "success" else ft.Colors.RED if type == "error" else ft.Colors.BLUE
                )
                self.page.snack_bar = snack
                snack.open = True
                self.page.update()
        except Exception as e:
            logger.error(f"Errore mostra notifica: {e}")
    
    def _test_smtp_connection(self):
        """Testa la connessione SMTP"""
        try:
            # Aggiorna configurazione temporanea nell'email service
            temp_config = self.smtp_settings.copy()
            
            # Backup della configurazione originale
            original_config = self.email_service.smtp_config.copy()
            
            # Aggiorna temporaneamente la configurazione
            self.email_service.smtp_config.update({
                'smtp_server': temp_config['server'],
                'smtp_port': temp_config['port'],
                'smtp_username': temp_config['username'],
                'smtp_password': temp_config['password'],
                'smtp_use_tls': temp_config['use_tls'],
                'from_name': temp_config['sender_name'],
                'from_email': temp_config['sender_email']
            })
            
            # Test connessione
            success = self.email_service.test_connection()
            
            # Ripristina configurazione originale
            self.email_service.smtp_config = original_config
            
            if success:
                logger.info("[OK] Connessione SMTP riuscita")
            else:
                logger.error("[ERROR] Connessione SMTP fallita")
                
        except Exception as e:
            logger.error(f"Errore test SMTP: {e}")
    
    def _send_test_email(self):
        """Invia un'email di test"""
        try:
            # Aggiorna configurazione temporanea nell'email service
            temp_config = self.smtp_settings.copy()
            
            # Backup della configurazione originale
            original_config = self.email_service.smtp_config.copy()
            
            # Aggiorna temporaneamente la configurazione
            self.email_service.smtp_config.update({
                'smtp_server': temp_config['server'],
                'smtp_port': temp_config['port'],
                'smtp_username': temp_config['username'],
                'smtp_password': temp_config['password'],
                'smtp_use_tls': temp_config['use_tls'],
                'from_name': temp_config['sender_name'],
                'from_email': temp_config['sender_email']
            })
            
            success = self.email_service.send_test_email(
                to_email=temp_config['username']  # Invia a se stesso
            )
            
            # Ripristina configurazione originale
            self.email_service.smtp_config = original_config
            
            if success:
                logger.info("[OK] Email di test inviata")
            else:
                logger.error("[ERROR] Invio email di test fallito")
                
        except Exception as e:
            logger.error(f"Errore invio email test: {e}")
    
    def _save_settings(self):
        """Salva tutte le impostazioni"""
        try:
            # Aggiorna la configurazione dell'applicazione
            self.config.email_config.update({
                'smtp_server': self.smtp_settings['server'],
                'smtp_port': self.smtp_settings['port'],
                'smtp_username': self.smtp_settings['username'],
                'smtp_password': self.smtp_settings['password'],
                'smtp_use_tls': self.smtp_settings['use_tls'],
                'from_name': self.smtp_settings['sender_name'],
                'from_email': self.smtp_settings['sender_email']
            })
            
            self.config.alert_config.update({
                'email_notifications_enabled': self.alert_settings['enabled'],
                'check_interval_hours': self.alert_settings['check_interval'],
                'days_before_deadline': self.alert_settings['advance_days'][0] if self.alert_settings['advance_days'] else 15,
                'reminder_recipients': self.alert_settings.get('reminder_recipients', [])
            })
            
            # Add scheduled reports config to the main config
            if not hasattr(self.config, 'scheduled_reports_config'):
                self.config.scheduled_reports_config = {}
            
            self.config.scheduled_reports_config.update(self.scheduled_reports_settings)
            
            # Add Windows config to the main config
            if not hasattr(self.config, 'windows_config'):
                self.config.windows_config = {}
            
            self.config.windows_config.update(self.windows_settings)
            
            # Add Google Services config to the main config
            if not hasattr(self.config, 'google_services_config'):
                self.config.google_services_config = {}
            
            self.config.google_services_config.update(self.google_services_settings)
            
            # Aggiorna anche il servizio email
            self.email_service.smtp_config = self.config.email_config.copy()
            
            # Salva in un file di configurazione JSON
            self._save_config_to_file()
            
            logger.info("[OK] Impostazioni salvate")
            
        except Exception as e:
            logger.error(f"Errore salvataggio impostazioni: {e}")
    
    def _save_config_to_file(self):
        """DEPRECATED: This method is disabled to prevent conflicts with the new settings system"""
        import json
        from core.utils.logger import logger

        logger.warning("Old settings system save attempt blocked - using new settings controller instead")

        # Don't save anything to prevent conflicts with the new settings system
        # The new settings controller in src/ui/views/settings/settings_controller.py
        # is now the single source of truth for all settings
        return
    
    def _load_config_from_file(self):
        """Carica la configurazione da file JSON se esiste"""
        import json
        
        config_file = self.config.data_dir / 'settings.json'
        
        if config_file.exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # Aggiorna le configurazioni
                if 'email_config' in config_data:
                    self.config.email_config.update(config_data['email_config'])
                
                if 'alert_config' in config_data:
                    self.config.alert_config.update(config_data['alert_config'])
                
                if 'scheduled_reports_config' in config_data:
                    if not hasattr(self.config, 'scheduled_reports_config'):
                        self.config.scheduled_reports_config = {}
                    self.config.scheduled_reports_config.update(config_data['scheduled_reports_config'])
                    self.scheduled_reports_settings.update(config_data['scheduled_reports_config'])
                
                if 'windows_config' in config_data:
                    if not hasattr(self.config, 'windows_config'):
                        self.config.windows_config = {}
                    self.config.windows_config.update(config_data['windows_config'])
                    self.windows_settings.update(config_data['windows_config'])
                
                if 'google_services_config' in config_data:
                    if not hasattr(self.config, 'google_services_config'):
                        self.config.google_services_config = {}
                    self.config.google_services_config.update(config_data['google_services_config'])
                    self.google_services_settings.update(config_data['google_services_config'])
                    
            except Exception as e:
                logger.error(f"Errore caricamento configurazione: {e}")
    
    def _reset_to_defaults(self):
        """Ripristina le impostazioni predefinite"""
        try:
            # Reset SMTP
            self.smtp_settings = {
                'server': '',
                'port': 587,
                'username': '',
                'password': '',
                'use_tls': True,
                'sender_name': 'Agevolami PM',
                'sender_email': ''
            }
            
            # Reset Alert
            self.alert_settings = {
                'enabled': True,
                'email_enabled': False,
                'check_interval': 24,
                'advance_days': [1, 3, 7, 15],
                'working_hours_only': True,
                'weekend_alerts': False
            }
            
            # Reset App
            self.app_settings = {
                'theme': 'light',
                'language': 'it',
                'auto_backup': True,
                'backup_interval': 7,
                'max_backups': 10
            }
            
            # Reset Windows settings
            self.windows_settings = {
                'startup_enabled': False,
                'startup_minimized': True,
                'notifications_enabled': True,
                'notification_sound': True,
                'notification_priority_filter': 'normal',
                'notification_quiet_hours_enabled': False,
                'notification_quiet_start': '22:00',
                'notification_quiet_end': '08:00',
                'notification_desktop_enabled': True,
                'notification_email_enabled': True
            }
            
            # Reset Google Services settings
            self.google_services_settings = {
                'drive_enabled': False,
                'drive_auto_backup': False,
                'drive_backup_frequency': 'daily',
                'drive_retention_days': 30,
                'drive_include_logs': True,
                'drive_include_assets': True,
                'drive_authenticated': False,
                
                'calendar_enabled': False,
                'calendar_auto_sync': True,
                'calendar_sync_completed': False,
                'calendar_color_priority': True,
                'calendar_reminders': True,
                'calendar_authenticated': False,
                
                'tasks_enabled': False,
                'tasks_auto_sync': True,
                'tasks_sync_on_deadline_change': True,
                'tasks_create_list_per_deadline': True,
                'tasks_sync_completed': True,
                'tasks_authenticated': False
            }
            
            # Reset Scheduled Reports
            self.scheduled_reports_settings = {
                'enabled': False,
                'morning_enabled': True,
                'morning_time': '09:00',
                'evening_enabled': True,
                'evening_time': '17:30',
                'workdays_only': True,
                'report_types': {
                    'full_report': True,
                    'deadlines_only': False,
                    'project_completion': True,
                    'incomplete_tasks': True
                },
                'recipients': [],
                'include_project_percentages': True,
                'include_overdue_items': True,
                'include_upcoming_deadlines': True,
                'days_ahead_filter': 7
            }
            
            # Disable startup if it was enabled
            if self.windows_integration.is_startup_enabled():
                self.windows_integration.configure_startup(False)
            
            logger.info("[OK] Impostazioni ripristinate ai valori predefiniti")
            
            # TODO: Rebuild UI
            
        except Exception as e:
            logger.error(f"Errore ripristino impostazioni: {e}")
    
    def _export_data(self):
        """Esporta i dati"""
        try:
            # TODO: Implementare esportazione dati
            logger.info("Esportazione dati")
        except Exception as e:
            logger.error(f"Errore esportazione: {e}")
    
    def _check_for_updates(self):
        """Controlla aggiornamenti disponibili"""
        try:
            # Import the update manager here to avoid circular imports
            from core.update_manager import UpdateManager
            
            # Get current version from pyproject.toml
            current_version = "2.0.10"  # TODO: Read from config or file
            
            # Create update manager and check for updates
            update_manager = UpdateManager(self.app.page, current_version)
            update_manager.check_for_updates_manual()
            
            logger.info("Controllo aggiornamenti avviato")
        except Exception as e:
            logger.error(f"Errore controllo aggiornamenti: {e}")
            self._show_notification("Errore durante il controllo aggiornamenti", "error")
    
    def _configure_github_token(self):
        """Configura il token GitHub per repository privati"""
        try:
            # NUCLEAR PREVENTION: Clear overlays first
            self._nuclear_cleanup_ui()
            
            # Import the update manager here to avoid circular imports
            from core.update_manager import UpdateManager
            
            # Get current version from pyproject.toml
            current_version = "2.0.10"  # TODO: Read from config or file
            
            # Create update manager and show token dialog
            update_manager = UpdateManager(self.app.page, current_version)
            update_manager.show_github_token_dialog()
            
            logger.info("Dialog configurazione GitHub token aperto")
        except Exception as e:
            logger.error(f"Errore apertura dialog GitHub token: {e}")
            self._show_notification("Errore durante l'apertura configurazione GitHub", "error")
    
    def _nuclear_cleanup_ui(self):
        """NUCLEAR CLEANUP - Emergency grey screen fix"""
        try:
            logger.info("[CLEANUP] Nuclear UI cleanup initiated")
            
            # Clear all overlays
            self.app.page.overlay.clear()
            self.app.page.update()
            
            # Force refresh
            import time
            time.sleep(0.1)
            self.app.page.update()
            
            logger.info("[CLEANUP] Nuclear UI cleanup completed")
        except Exception as e:
            logger.error(f"Error in nuclear cleanup: {e}")
            # Last resort: just try to update
            try:
                self.app.page.update()
            except:
                pass
    
    def _open_support(self):
        """Apre il supporto"""
        logger.info("Apertura supporto")
    
    def _show_license(self):
        """Mostra la licenza"""
        logger.info("Mostra licenza")
    
    def _update_windows_setting(self, key: str, value: Any):
        """Aggiorna un'impostazione Windows"""
        self.windows_settings[key] = value
        logger.debug(f"Windows setting updated: {key} = {value}")
        
        # Gestione speciale per startup
        if key == 'startup_enabled':
            try:
                if value:
                    success = self.windows_integration.configure_startup(True)
                    if success:
                        self._show_notification("Avvio automatico abilitato", "success")
                    else:
                        self._show_notification("Errore abilitazione avvio automatico", "error")
                        # Revert the switch
                        self.windows_settings[key] = False
                else:
                    success = self.windows_integration.configure_startup(False)
                    if success:
                        self._show_notification("Avvio automatico disabilitato", "success")
                    else:
                        self._show_notification("Errore disabilitazione avvio automatico", "error")
                        
            except Exception as e:
                logger.error(f"Errore configurazione startup: {e}")
                self._show_notification(f"Errore: {e}", "error")
    
    def _test_notification(self, e):
        """Testa il sistema di notifiche"""
        try:
            success = self.windows_integration.send_notification(
                "Test Agevolami PM",
                "Sistema di notifiche funzionante!\nQuesto è un test delle notifiche desktop.",
                "normal"
            )
            
            if success:
                self._show_notification("Notifica di test inviata!", "success")
            else:
                self._show_notification("Sistema notifiche non disponibile", "warning")
                
        except Exception as ex:
            logger.error(f"Errore test notifica: {ex}")
            self._show_notification("Errore nel test notifiche", "error")
    
    def _test_deadline_notification(self, e):
        """Testa una notifica di scadenza"""
        try:
            success = self.windows_integration.send_deadline_alert(
                "Test Scadenza Fiscale",
                3,  # 3 giorni rimanenti
                "Progetto Test"
            )
            
            if success:
                self._show_notification("Notifica scadenza di test inviata!", "success")
            else:
                self._show_notification("Sistema notifiche non disponibile", "warning")
                
        except Exception as ex:
            logger.error(f"Errore test notifica scadenza: {ex}")
            self._show_notification("Errore nel test notifiche scadenza", "error")
    
    def _update_google_drive_setting(self, key: str, value: Any):
        """Aggiorna un'impostazione Google Drive"""
        self.google_services_settings[key] = value
        logger.debug(f"Google Drive setting updated: {key} = {value}")
    
    def _authenticate_google_drive(self, e):
        """Gestisce l'autenticazione con Google Drive"""
        try:
            logger.info("[AUTH] Avvio autenticazione Google Drive")
            
            self._show_loading_dialog("Connessione a Google Drive in corso...")
            
            # Attempt manual authentication with embedded credentials
            success = self.google_drive_service.authenticate()
            
            # Chiudi loading
            self.app.page.overlay.clear()
            self.app.page.update()
            
            if success:
                # Update settings
                self.google_services_settings['drive_enabled'] = True
                self.google_services_settings['drive_authenticated'] = True

                logger.info("[OK] Google Drive autenticato con successo")

                # Refresh the page after dialog is closed
                def refresh_after_dialog():
                    self.refresh_data()

                self._show_success_dialog("Google Drive connesso con successo!\n\nOra puoi eseguire backup automatici sul cloud.",
                                        on_close_callback=refresh_after_dialog)
            else:
                logger.error("[ERROR] Autenticazione Google Drive fallita")
                self._show_error_dialog("Autenticazione Google Drive fallita.\n\nVerifica la connessione internet e riprova.")
                
        except Exception as ex:
            logger.error(f"Errore autenticazione Google Drive: {ex}")
            # Chiudi loading in caso di errore
            try:
                self.app.page.overlay.clear()
                self.app.page.update()
            except:
                pass
            self._show_error_dialog(f"Errore durante l'autenticazione Google Drive:\n{str(ex)}")
    
    def _disconnect_google_drive(self, e):
        """Disconnette da Google Drive"""
        try:
            success = self.google_drive_service.disconnect()
            if success:
                self.google_services_settings['drive_authenticated'] = False
                self._show_notification("Disconnesso da Google Drive", "success")
                # Ricarica la vista per aggiornare i pulsanti
                self.refresh_data()
            else:
                self._show_notification("Errore durante la disconnessione", "error")
        except Exception as ex:
            logger.error(f"Errore disconnessione Google Drive: {ex}")
            self._show_notification("Errore durante la disconnessione", "error")
    
    def _test_google_drive_connection(self, e):
        """Testa la connessione a Google Drive"""
        try:
            self._show_loading_dialog("Test connessione...")
            
            success = self.google_drive_service.test_connection()
            
            # Chiudi loading
            self.app.page.overlay.clear()
            self.app.page.update()
            
            if success:
                self._show_notification("Connessione Google Drive OK!", "success")
            else:
                self._show_notification("Test connessione fallito", "error")
                
        except Exception as ex:
            logger.error(f"Errore test connessione Google Drive: {ex}")
            # Chiudi loading
            self.app.page.overlay.clear()
            self.app.page.update()
            self._show_notification("Errore durante il test", "error")
    
    def _manual_google_drive_backup(self, e):
        """Esegue un backup manuale su Google Drive - VERSIONE NUCLEAR"""
        try:
            # NUCLEAR SOLUTION: Nessun loading dialog, solo notifica iniziale
            self._show_notification("[BACKUP] Creazione backup in corso...", "info")

            # Determina i percorsi - cerca il database in diverse posizioni
            database_path = self._find_database_file()
            data_dir = self.config.data_dir

            # Usa il retention configurato
            retention_days = self.google_services_settings.get('drive_retention_days', 30)
            backup_id = self.google_drive_service.create_backup(database_path, data_dir, retention_days)

            # NUCLEAR CLEANUP: Solo page update
            self.app.page.update()

            if backup_id:
                self._show_notification("[OK] Backup Google Drive completato!", "success")
                logger.info(f"Backup completato con ID: {backup_id}")
            else:
                self._show_notification("[ERROR] Errore durante il backup", "error")

        except Exception as ex:
            logger.error(f"Errore backup Google Drive: {ex}")
            # NUCLEAR CLEANUP: Solo page update
            self.app.page.update()
            self._show_notification(f"[ERROR] Errore: {str(ex)}", "error")

    def _find_database_file(self) -> Path:
        """Trova il file database in diverse posizioni possibili"""
        from pathlib import Path

        # Lista di possibili percorsi del database (in ordine di priorità)
        possible_paths = [
            # Percorso standard configurato (PRIORITÀ MASSIMA)
            self.config.database_path,

            # Percorso standard con nome corretto
            self.config.data_dir / "agevolami_pm.db",

            # Percorso alternativo
            self.config.data_dir / "agevolami.db",

            # Percorso di sviluppo (cartella parent)
            Path("D:/pro projects/flet/AGevolami_PM/agevolami_pm.db"),

            # Percorso relativo alla directory corrente
            Path.cwd() / "agevolami_pm.db",
            Path.cwd() / "agevolami.db",

            # Nella directory parent del progetto
            Path.cwd().parent / "agevolami_pm.db",
            Path.cwd().parent / "AGevolami_PM" / "agevolami_pm.db",

            # Cerca qualsiasi file .db nella data directory
            *list(self.config.data_dir.glob("*.db")),

            # Cerca nella directory corrente
            *list(Path.cwd().glob("*.db"))
        ]

        logger.debug(f"Ricerca database in {len(possible_paths)} posizioni...")

        for db_path in possible_paths:
            if db_path and db_path.exists() and db_path.is_file():
                size = db_path.stat().st_size
                if size > 1024:  # Almeno 1KB
                    logger.info(f"Database trovato: {db_path} ({size} bytes)")
                    return db_path
                else:
                    logger.debug(f"Database troppo piccolo ignorato: {db_path} ({size} bytes)")

        # Se non trovato, usa il percorso standard configurato (anche se non esiste)
        default_path = self.config.database_path
        logger.warning(f"Database non trovato, uso percorso standard configurato: {default_path}")
        return default_path

    def _show_google_drive_backups(self, e):
        """Mostra la lista dei backup su Google Drive"""
        try:
            backups = self.google_drive_service.list_backups()
            self._show_backups_list_dialog(backups)
        except Exception as ex:
            logger.error(f"Errore lista backup Google Drive: {ex}")
            self._show_notification("Errore durante il recupero dei backup", "error")
    
    def _show_backups_list_dialog(self, backups: List[Dict[str, Any]]):
        """Mostra dialog con lista backup"""
        def close_dialog(e):
            try:
                # Chiudi il dialog specifico
                if hasattr(e, 'control') and hasattr(e.control, 'parent'):
                    dialog = e.control.parent
                    if hasattr(dialog, 'open'):
                        dialog.open = False

                # Pulisci tutti gli overlay
                self.app.page.overlay.clear()
                self.app.page.update()

                # Forza refresh per evitare schermo grigio
                import time
                time.sleep(0.1)
                self.app.page.update()

            except Exception as ex:
                logger.error(f"Errore chiusura dialog backup: {ex}")
                # Fallback: pulisci tutto
                self.app.page.overlay.clear()
                self.app.page.update()
        
        def restore_backup(backup_id: str, backup_name: str):
            def confirm_restore(e):
                self.app.page.overlay.clear()
                self.app.page.update()
                self._restore_specific_backup(backup_id, backup_name)
            
            def cancel_restore(e):
                self.app.page.overlay.clear()
                self.app.page.update()
                # Mostra di nuovo la lista
                self._show_backups_list_dialog(backups)
            
            confirm_dialog = ft.AlertDialog(
                modal=True,
                title=ft.Text("Conferma Ripristino", weight=ft.FontWeight.BOLD),
                content=ft.Text(f"Sei sicuro di voler ripristinare il backup:\n{backup_name}?\n\nI dati attuali verranno sostituiti."),
                actions=[
                    ft.TextButton("Annulla", on_click=cancel_restore),
                    ft.ElevatedButton("Ripristina", on_click=confirm_restore,
                                    bgcolor=ft.Colors.ORANGE, color=ft.Colors.WHITE)
                ],
                actions_alignment=ft.MainAxisAlignment.END
            )
            
            self.app.page.overlay.clear()
            self.app.page.overlay.append(confirm_dialog)
            confirm_dialog.open = True
            self.app.page.update()
        
        def delete_backup(backup_id: str, backup_name: str):
            def confirm_delete(e):
                self.app.page.overlay.clear()
                self.app.page.update()
                
                try:
                    success = self.google_drive_service.delete_backup(backup_id)
                    if success:
                        self._show_notification("Backup eliminato", "success")
                        # Ricarica la lista
                        updated_backups = self.google_drive_service.list_backups()
                        self._show_backups_list_dialog(updated_backups)
                    else:
                        self._show_notification("Errore eliminazione backup", "error")
                except Exception as ex:
                    logger.error(f"Errore eliminazione backup: {ex}")
                    self._show_notification("Errore eliminazione backup", "error")
            
            def cancel_delete(e):
                self.app.page.overlay.clear()
                self.app.page.update()
                # Mostra di nuovo la lista
                self._show_backups_list_dialog(backups)
            
            confirm_dialog = ft.AlertDialog(
                modal=True,
                title=ft.Text("Conferma Eliminazione", weight=ft.FontWeight.BOLD),
                content=ft.Text(f"Sei sicuro di voler eliminare il backup:\n{backup_name}?"),
                actions=[
                    ft.TextButton("Annulla", on_click=cancel_delete),
                    ft.ElevatedButton("Elimina", on_click=confirm_delete,
                                    bgcolor=ft.Colors.RED, color=ft.Colors.WHITE)
                ],
                actions_alignment=ft.MainAxisAlignment.END
            )
            
            self.app.page.overlay.clear()
            self.app.page.overlay.append(confirm_dialog)
            confirm_dialog.open = True
            self.app.page.update()
        
        if not backups:
            content = ft.Text("Nessun backup trovato su Google Drive")
        else:
            backup_rows = []
            for backup in backups[:15]:  # Mostra max 15 backup
                size_mb = backup.get('size_mb', backup['size'] / (1024*1024))
                created_date = backup['created'][:10]  # Solo la data
                age_days = backup.get('age_days', 0)

                # Colore basato sull'età
                age_color = ft.Colors.GREEN if backup.get('is_recent', False) else (
                    ft.Colors.ORANGE if backup.get('is_old', False) else ft.Colors.GREY_600
                )

                # Icona di stato
                status_icon = ft.Icons.NEW_RELEASES if backup.get('is_recent', False) else (
                    ft.Icons.WARNING if backup.get('is_old', False) else ft.Icons.BACKUP
                )

                backup_rows.append(
                    ft.Container(
                        content=ft.Row([
                            ft.Icon(status_icon, size=16, color=age_color),
                            ft.Column([
                                ft.Text(backup['name'], size=12, weight=ft.FontWeight.BOLD),
                                ft.Text(f"{created_date} - {size_mb:.1f} MB - {age_days} giorni fa",
                                        size=10, color=age_color)
                            ], expand=True),

                            ft.Row([
                                ft.IconButton(
                                    icon=ft.Icons.RESTORE,
                                    tooltip="Ripristina",
                                    on_click=lambda e, bid=backup['id'], bname=backup['name']: restore_backup(bid, bname)
                                ),
                                ft.IconButton(
                                    icon=ft.Icons.DELETE,
                                    tooltip="Elimina",
                                    icon_color=ft.Colors.RED,
                                    on_click=lambda e, bid=backup['id'], bname=backup['name']: delete_backup(bid, bname)
                                )
                            ], spacing=0)
                        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                        padding=8,
                        border=ft.border.all(1, ft.Colors.GREY_300),
                        border_radius=8,
                        margin=ft.margin.only(bottom=4)
                    )
                )

            # Aggiungi statistiche in cima
            stats = self.google_drive_service.get_backup_statistics()
            if stats.get('success'):
                stats_text = ft.Container(
                    content=ft.Text(
                        f"📊 {stats['total_backups']} backup totali - {stats['total_size_mb']} MB - "
                        f"{stats['recent_backups']} recenti - {stats['old_backups']} vecchi",
                        size=10, color=ft.Colors.BLUE_600, weight=ft.FontWeight.BOLD
                    ),
                    padding=8,
                    bgcolor=ft.Colors.BLUE_50,
                    border_radius=4,
                    margin=ft.margin.only(bottom=8)
                )
                backup_rows.insert(0, stats_text)

            content = ft.Column(backup_rows, spacing=4, scroll=ft.ScrollMode.AUTO, height=400)
        
        dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text("Backup Google Drive", weight=ft.FontWeight.BOLD),
            content=ft.Container(
                content=content,
                width=500,
                height=350
            ),
            actions=[
                ft.TextButton("Chiudi", on_click=close_dialog)
            ],
            actions_alignment=ft.MainAxisAlignment.CENTER
        )

        try:
            # Pulisci overlay esistenti prima di aggiungere il nuovo dialog
            self.app.page.overlay.clear()
            self.app.page.update()

            # Aggiungi e mostra il dialog
            self.app.page.overlay.append(dialog)
            dialog.open = True
            self.app.page.update()

        except Exception as ex:
            logger.error(f"Errore apertura dialog backup: {ex}")
            self._show_notification("Errore apertura dialog", "error")
    
    def _restore_specific_backup(self, backup_id: str, backup_name: str):
        """Ripristina un backup specifico"""
        try:
            self._show_loading_dialog("Ripristino backup in corso...")

            # Crea cartella temporanea per il ripristino
            import tempfile
            temp_dir = Path(tempfile.mkdtemp())

            success = self.google_drive_service.restore_backup(backup_id, temp_dir)

            if success:
                # Ora copia effettivamente i file ripristinati
                success = self._apply_restored_backup(temp_dir)

            # Chiudi loading
            self.app.page.overlay.clear()
            self.app.page.update()

            if success:
                self._show_success_dialog(f"Backup ripristinato:\n{backup_name}\n\nRiavvia l'applicazione per applicare le modifiche.")
            else:
                self._show_notification("Errore durante il ripristino", "error")
                
        except Exception as ex:
            logger.error(f"Errore ripristino backup: {ex}")
            # Chiudi loading
            self.app.page.overlay.clear()
            self.app.page.update()
            self._show_notification(f"Errore: {str(ex)}", "error")

    def _apply_restored_backup(self, temp_dir: Path) -> bool:
        """Applica effettivamente i file del backup ripristinato"""
        try:
            import shutil

            # Debug: Lista tutti i file nel backup
            all_files = list(temp_dir.rglob("*"))
            logger.info(f"[RESTORE] File nel backup estratto: {[f.name for f in all_files if f.is_file()]}")

            # Trova il database nel backup
            database_file = None
            db_files = list(temp_dir.rglob("*.db"))
            logger.info(f"[RESTORE] File .db trovati: {[f.name for f in db_files]}")

            for file_path in db_files:
                if file_path.name in ["database.db", "agevolami.db", "agevolami_pm.db"]:
                    database_file = file_path
                    size = file_path.stat().st_size
                    logger.info(f"[RESTORE] Database selezionato: {file_path.name} ({size} bytes)")
                    break

            if not database_file:
                logger.error("[RESTORE] Database non trovato nel backup")
                logger.error(f"[RESTORE] File disponibili: {[f.name for f in all_files if f.is_file()]}")
                return False

            # Determina il percorso del database attuale
            current_db_path = self._find_database_file()
            logger.info(f"[RESTORE] Database attuale: {current_db_path}")
            logger.info(f"[RESTORE] Database attuale esiste: {current_db_path.exists()}")

            if not current_db_path:
                logger.error("[RESTORE] Impossibile determinare il percorso del database attuale")
                return False

            # Crea backup del database attuale se esiste
            if current_db_path.exists():
                backup_current = current_db_path.parent / f"{current_db_path.stem}_backup_before_restore.db"
                shutil.copy2(current_db_path, backup_current)
                logger.info(f"[RESTORE] Backup database attuale creato: {backup_current}")
            else:
                logger.info(f"[RESTORE] Database attuale non esiste, verrà creato: {current_db_path}")
                # Assicurati che la directory esista
                current_db_path.parent.mkdir(parents=True, exist_ok=True)

            # Sostituisci il database
            shutil.copy2(database_file, current_db_path)
            logger.info(f"[RESTORE] Database ripristinato da {database_file} a {current_db_path}")

            # Verifica che il file sia stato copiato correttamente
            if current_db_path.exists():
                new_size = current_db_path.stat().st_size
                logger.info(f"[RESTORE] Database ripristinato con successo: {new_size} bytes")
            else:
                logger.error(f"[RESTORE] Errore: database non trovato dopo il ripristino: {current_db_path}")
                return False

            # Ripristina anche i file di configurazione se presenti
            config_dir = self.config.data_dir / "config"
            config_files = list(temp_dir.rglob("*.json"))
            logger.info(f"[RESTORE] File di configurazione trovati: {[f.name for f in config_files]}")

            for config_file in config_files:
                if config_file.name in ["settings.json", "app_config.json"]:
                    target_config = config_dir / config_file.name

                    # Assicurati che la directory config esista
                    config_dir.mkdir(parents=True, exist_ok=True)

                    if target_config.exists():
                        # Backup configurazione attuale
                        backup_config = target_config.parent / f"{target_config.stem}_backup_before_restore.json"
                        shutil.copy2(target_config, backup_config)
                        logger.info(f"[RESTORE] Backup configurazione attuale: {backup_config}")

                    # Copia nuova configurazione
                    shutil.copy2(config_file, target_config)
                    logger.info(f"[RESTORE] Configurazione ripristinata: {config_file.name}")

            # Pulisci cartella temporanea
            shutil.rmtree(temp_dir, ignore_errors=True)
            logger.info(f"[RESTORE] Cartella temporanea pulita: {temp_dir}")

            return True

        except Exception as e:
            logger.error(f"[RESTORE] Errore applicazione backup: {e}")
            import traceback
            logger.error(f"[RESTORE] Traceback: {traceback.format_exc()}")
            return False

    def _show_database_info(self, e):
        """Mostra informazioni sul database corrente"""
        try:
            database_path = self._find_database_file()

            if database_path.exists():
                size = database_path.stat().st_size
                message = f"[DATABASE] Database Info:\n• Percorso: {database_path}\n• Dimensione: {size} bytes ({size/1024:.1f} KB)"
            else:
                message = f"[ERROR] Database non trovato\n• Percorso cercato: {database_path}"

            self._show_notification(message, "info")

        except Exception as ex:
            logger.error(f"Errore info database: {ex}")
            self._show_notification(f"Errore: {str(ex)}", "error")

    def _show_backup_statistics(self):
        """Mostra statistiche sui backup"""
        try:
            # Assicurati che non ci siano overlay aperti
            self.app.page.overlay.clear()
            self.app.page.update()

            stats = self.google_drive_service.get_backup_statistics()
            if stats.get('success'):
                message = f"""[STATS] Statistiche Backup:
• Totale backup: {stats['total_backups']}
• Spazio utilizzato: {stats['total_size_mb']} MB
• Backup recenti (< 7 giorni): {stats['recent_backups']}
• Backup vecchi (> 30 giorni): {stats['old_backups']}
• Dimensione media: {stats['average_size_mb']} MB"""

                if stats['old_backups'] > 0:
                    message += f"\n\n[WARNING] Hai {stats['old_backups']} backup vecchi che potrebbero essere eliminati"

                self._show_notification(message, "info")
            else:
                self._show_notification(f"Errore nel recupero statistiche: {stats.get('error', 'Unknown')}", "error")

        except Exception as e:
            logger.error(f"Errore statistiche backup: {e}")
            self._show_notification(f"Errore statistiche: {str(e)}", "error")

    def _cleanup_old_backups(self, e):
        """Pulisce i backup vecchi"""
        try:
            retention_days = self.google_services_settings.get('drive_retention_days', 30)

            # Conferma dall'utente
            def confirm_cleanup(e):
                self.app.page.overlay.clear()
                self.app.page.update()

                self._show_loading_dialog(f"Eliminazione backup più vecchi di {retention_days} giorni...")

                results = self.google_drive_service.manual_cleanup_old_backups(retention_days)

                self.app.page.overlay.clear()
                self.app.page.update()

                if results.get('success'):
                    message = f"[OK] Pulizia completata!\n• Eliminati: {results['deleted_count']} backup\n• Spazio liberato: {results['freed_space'] / (1024*1024):.1f} MB"
                    if results.get('errors'):
                        message += f"\n[WARNING] Errori: {len(results['errors'])}"
                    self._show_notification(message, "success")
                else:
                    self._show_notification(f"Errore pulizia: {results.get('error', 'Unknown')}", "error")

            def cancel_cleanup(e):
                try:
                    self.app.page.overlay.clear()
                    self.app.page.update()
                    # Forza refresh
                    import time
                    time.sleep(0.1)
                    self.app.page.update()
                except Exception as ex:
                    logger.error(f"Errore chiusura dialog cleanup: {ex}")

            # Dialog di conferma
            confirm_dialog = ft.AlertDialog(
                modal=True,
                title=ft.Text("Conferma Pulizia"),
                content=ft.Text(f"Eliminare tutti i backup più vecchi di {retention_days} giorni?\n\nQuesta operazione non può essere annullata."),
                actions=[
                    ft.TextButton("Annulla", on_click=cancel_cleanup),
                    ft.TextButton("Elimina", on_click=confirm_cleanup, style=ft.ButtonStyle(color=ft.Colors.RED))
                ]
            )

            try:
                self.app.page.overlay.clear()
                self.app.page.update()
                self.app.page.overlay.append(confirm_dialog)
                confirm_dialog.open = True
                self.app.page.update()
            except Exception as ex:
                logger.error(f"Errore apertura dialog cleanup: {ex}")
                self._show_notification("Errore apertura dialog", "error")

        except Exception as ex:
            logger.error(f"Errore pulizia backup: {ex}")
            self._show_notification(f"Errore: {str(ex)}", "error")

    def _fix_backup_visibility(self, e):
        """Corregge la visibilità dei backup esistenti - VERSIONE NUCLEAR"""
        try:
            # NUCLEAR SOLUTION: Solo notifica iniziale
            self._show_notification("[LOADING] Correzione visibilità backup...", "info")

            results = self.google_drive_service.fix_backup_visibility()

            # NUCLEAR CLEANUP: Solo page update
            self.app.page.update()

            if results.get('success'):
                message = f"[FIX] Fix visibilità completato!\n• Backup controllati: {results['checked']}\n• Backup spostati: {results['moved']}"
                if results.get('errors'):
                    message += f"\n[WARNING] Errori: {len(results['errors'])}"
                self._show_notification(message, "success")

                # Aggiorna la lista backup se ci sono stati cambiamenti
                if results['moved'] > 0:
                    logger.info("Backup spostati, aggiornamento lista...")

            else:
                self._show_notification(f"[ERROR] Errore fix visibilità: {results.get('error', 'Unknown')}", "error")

        except Exception as ex:
            logger.error(f"Errore fix visibilità: {ex}")
            # NUCLEAR CLEANUP: Solo page update
            self.app.page.update()
            self._show_notification(f"[ERROR] Errore: {str(ex)}", "error")

    def _diagnose_backup_visibility(self, e):
        """Esegue una diagnosi completa della visibilità dei backup - VERSIONE NUCLEAR"""
        try:
            # NUCLEAR SOLUTION: Solo notifica iniziale
            self._show_notification("[LOADING] Diagnosi visibilità backup...", "info")

            results = self.google_drive_service.diagnose_folder_visibility()

            # NUCLEAR CLEANUP: Solo page update
            self.app.page.update()

            if results.get('success'):
                # Costruisci messaggio dettagliato
                folder_info = results.get('folder_info', {})
                backups_in_folder = len(results.get('backups_in_folder', []))
                all_backups = len(results.get('all_backups', []))
                issues = results.get('issues', [])

                message = f"[DIAGNOSI] Diagnosi Backup Google Drive:\n\n"
                message += f"[FOLDER] Cartella: {folder_info.get('name', 'N/A')}\n"
                message += f"[FOLDER] ID: {folder_info.get('id', 'N/A')}\n"
                message += f"[FOLDER] Link: {folder_info.get('webViewLink', 'N/A')}\n\n"
                message += f"[STATS] Backup nella cartella: {backups_in_folder}\n"
                message += f"[STATS] Backup totali: {all_backups}\n\n"

                if issues:
                    message += f"[WARNING] Problemi trovati:\n"
                    for issue in issues:
                        message += f"  • {issue}\n"
                else:
                    message += "[OK] Nessun problema trovato"

                # Aggiungi link diretto alla cartella se disponibile
                if folder_info.get('webViewLink'):
                    message += f"\n[LINK] Apri cartella in Google Drive:\n{folder_info['webViewLink']}"

                self._show_notification(message, "info")

            else:
                self._show_notification(f"[ERROR] Errore diagnosi: {results.get('error', 'Unknown')}", "error")

        except Exception as ex:
            logger.error(f"Errore diagnosi visibilità: {ex}")
            # NUCLEAR CLEANUP: Solo page update
            self.app.page.update()
            self._show_notification(f"[ERROR] Errore: {str(ex)}", "error")

    def _cleanup_ui_overlays(self):
        """NUCLEAR CLEANUP - Evita completamente gli overlay"""
        try:
            # NUCLEAR SOLUTION: Non toccare gli overlay, forza solo refresh della pagina
            logger.info("[CLEANUP] Cleanup UI - evitando overlay system")

            # Forza refresh della pagina principale
            self.app.page.update()

            # Piccola pausa per permettere al sistema di processare
            import time
            time.sleep(0.1)
            self.app.page.update()

        except Exception as e:
            logger.error(f"Errore cleanup UI: {e}")
            # Ultimo tentativo: solo page update
            try:
                self.app.page.update()
            except:
                pass

    def _restore_google_drive_backup(self, e):
        """Avvia il processo di ripristino da Google Drive"""
        self._show_google_drive_backups(e)
    
    def _authenticate_google_calendar(self, e):
        """Gestisce l'autenticazione con Google Calendar"""
        try:
            logger.info("[AUTH] Avvio autenticazione Google Calendar")
            
            self._show_loading_dialog("Connessione a Google Calendar in corso...")
            
            # Attempt manual authentication with embedded credentials
            success = self.google_calendar_service.authenticate()
            
            # Chiudi loading
            self.app.page.overlay.clear()
            self.app.page.update()
            
            if success:
                # Update settings
                self.google_services_settings['calendar_enabled'] = True
                self.google_services_settings['calendar_authenticated'] = True

                logger.info("[OK] Google Calendar autenticato con successo")

                # Refresh the page after dialog is closed
                def refresh_after_dialog():
                    self.refresh_data()

                self._show_success_dialog("Google Calendar connesso con successo!\n\nOra puoi sincronizzare le scadenze automaticamente.",
                                        on_close_callback=refresh_after_dialog)
            else:
                logger.error("[ERROR] Autenticazione Google Calendar fallita")
                self._show_error_dialog("Autenticazione Google Calendar fallita.\n\nVerifica la connessione internet e riprova.")
                
        except Exception as ex:
            logger.error(f"Errore autenticazione Google Calendar: {ex}")
            # Chiudi loading in caso di errore
            try:
                self.app.page.overlay.clear()
                self.app.page.update()
            except:
                pass
            self._show_error_dialog(f"Errore durante l'autenticazione Google Calendar:\n{str(ex)}")

    def _disconnect_google_calendar(self, e):
        """Disconnette da Google Calendar"""
        try:
            success = self.google_calendar_service.disconnect()
            if success:
                self.google_services_settings['calendar_authenticated'] = False
                self._show_notification("Disconnesso da Google Calendar", "success")
                # Ricarica la vista per aggiornare i pulsanti
                self.refresh_data()
            else:
                self._show_notification("Errore durante la disconnessione", "error")
        except Exception as ex:
            logger.error(f"Errore disconnessione Google Calendar: {ex}")
            self._show_notification("Errore durante la disconnessione", "error")
    
    def _test_google_calendar_connection(self, e):
        """Testa la connessione a Google Calendar"""
        try:
            self._show_loading_dialog("Test connessione...")
            
            success = self.google_calendar_service.test_connection()
            
            # Chiudi loading
            self.app.page.overlay.clear()
            self.app.page.update()
            
            if success:
                self._show_notification("Connessione Google Calendar OK!", "success")
            else:
                self._show_notification("Test connessione fallito", "error")
                
        except Exception as ex:
            logger.error(f"Errore test connessione Google Calendar: {ex}")
            # Chiudi loading
            self.app.page.overlay.clear()
            self.app.page.update()
            self._show_notification("Errore durante il test", "error")
    
    def _add_google_calendar_event(self, e):
        """Aggiunge un nuovo evento a Google Calendar"""
        try:
            # Implementa la logica per aggiungere un evento a Google Calendar
            logger.info("Nuovo evento da aggiungere a Google Calendar")
        except Exception as ex:
            logger.error(f"Errore aggiunta evento: {ex}")
            self._show_notification("Errore durante l'aggiunta evento", "error")
    
    def _show_google_calendar_events(self, e):
        """Mostra la lista degli eventi su Google Calendar"""
        try:
            # Implementa la logica per mostrare gli eventi su Google Calendar
            logger.info("Lista eventi da mostrare su Google Calendar")
        except Exception as ex:
            logger.error(f"Errore lista eventi: {ex}")
            self._show_notification("Errore durante il recupero degli eventi", "error")
    
    def _sync_google_calendar(self, e):
        """Sincronizza le scadenze con Google Calendar"""
        logger.info("🔄 Avvio sincronizzazione Google Calendar")
        
        try:
            self._show_loading_dialog("Sincronizzazione con Google Calendar in corso...")
            
            # Get deadlines from database
            if hasattr(self.app, 'db_manager'):
                deadlines = self.app.db_manager.get_all_deadlines()
                logger.info(f"📋 Trovate {len(deadlines)} scadenze da sincronizzare")
                
                # Sync with Google Calendar
                results = self.google_calendar_service.sync_all_deadlines(deadlines)
                
                if results.get('success'):
                    synced = results.get('synced', 0)
                    total = results.get('total', 0)
                    errors = results.get('errors', [])
                    
                    if errors:
                        message = f"Sincronizzazione completata con avvisi:\n{synced}/{total} scadenze sincronizzate\n\nErrori: {len(errors)}"
                        self._show_error_dialog(message)
                    else:
                        message = f"Sincronizzazione completata!\n{synced}/{total} scadenze sincronizzate con Google Calendar"
                        self._show_success_dialog(message)
                else:
                    error_msg = results.get('error', 'Errore sconosciuto')
                    self._show_error_dialog(f"Errore sincronizzazione: {error_msg}")
            else:
                self._show_error_dialog("Database non disponibile per la sincronizzazione")
                
        except Exception as e:
            logger.error(f"Errore sincronizzazione Google Calendar: {e}")
            self._show_error_dialog(f"Errore durante la sincronizzazione: {str(e)}")

    def _authenticate_google_tasks(self, e):
        """Autentica con Google Tasks usando credenziali embedded"""
        logger.info("[AUTH] Avvio autenticazione Google Tasks")
        
        try:
            self._show_loading_dialog("Connessione a Google Tasks in corso...")
            
            # Attempt authentication with embedded credentials
            success = self.google_tasks_service.authenticate()
            
            # Chiudi loading
            self.app.page.overlay.clear()
            self.app.page.update()
            
            if success:
                # Update settings
                self.google_services_settings['tasks_enabled'] = True
                self.google_services_settings['tasks_authenticated'] = True

                logger.info("[OK] Google Tasks autenticato con successo")

                # Refresh the page after dialog is closed
                def refresh_after_dialog():
                    self.refresh_data()

                self._show_success_dialog("Google Tasks connesso con successo!\n\nOra puoi sincronizzare le tue attività automaticamente.",
                                        on_close_callback=refresh_after_dialog)
            else:
                logger.error("[ERROR] Autenticazione Google Tasks fallita")
                self._show_error_dialog("Autenticazione Google Tasks fallita.\n\nVerifica la connessione internet e riprova.")
                
        except Exception as e:
            logger.error(f"Errore autenticazione Google Tasks: {e}")
            # Chiudi loading in caso di errore
            try:
                self.app.page.overlay.clear()
                self.app.page.update()
            except:
                pass
            self._show_error_dialog(f"Errore durante l'autenticazione Google Tasks:\n{str(e)}")

    def _disconnect_google_tasks(self, e):
        """Disconnetti da Google Tasks"""
        logger.info("[DISCONNECT] Disconnessione Google Tasks")
        
        try:
            success = self.google_tasks_service.disconnect()
            
            if success:
                # Update settings
                self.google_services_settings['tasks_enabled'] = False
                self.google_services_settings['tasks_authenticated'] = False
                
                logger.info("[OK] Google Tasks disconnesso")
                self._show_success_dialog("Google Tasks disconnesso con successo.")
                
                # Refresh the page to update UI
                self.refresh_data()
            else:
                self._show_error_dialog("Errore durante la disconnessione da Google Tasks.")
                
        except Exception as e:
            logger.error(f"Errore disconnessione Google Tasks: {e}")
            self._show_error_dialog(f"Errore durante la disconnessione: {str(e)}")

    def _sync_google_tasks(self, e):
        """Sincronizza le task con Google Tasks"""
        logger.info("🔄 Avvio sincronizzazione Google Tasks")
        
        # Show loading dialog
        self._show_loading_dialog("Sincronizzazione con Google Tasks in corso...")
        
        try:
            # Check if Google Tasks is authenticated
            if not self.google_tasks_service.is_enabled():
                self._show_error_dialog("Google Tasks non è autenticato. Effettua prima l'autenticazione.")
                return
            
            total_synced = 0
            total_errors = []
            
            # Get ALL tasks from database that are not completed
            if hasattr(self.app, 'db_manager'):
                all_tasks = self.app.db_manager.get_all_tasks()
                tasks_to_sync = [t for t in all_tasks if t.status != 'completato']
                logger.info(f"📋 Trovate {len(tasks_to_sync)} task da sincronizzare")

                if not tasks_to_sync:
                    self._show_info_dialog("Nessuna Attività da Sincronizzare", "Tutte le attività sono già completate o non ci sono attività da sincronizzare.")
                    return

                for task in tasks_to_sync:
                    deadline = self.app.db_manager.get_deadline_by_id(task.deadline_id)
                    if not deadline:
                        logger.warning(f"Nessuna scadenza trovata per l'attività: {task.title}")
                    
                    google_task_id = self.google_tasks_service.sync_task_to_google(task, deadline)
                    
                    if google_task_id:
                        task.google_task_id = google_task_id
                        self.app.db_manager.update_task(task)
                        total_synced += 1
                        logger.info(f"[OK] Task '{task.title}' sincronizzata")
                    else:
                        total_errors.append(task.title)
                        logger.error(f"[FAIL] Task '{task.title}' non sincronizzata")
            
            # Show final result dialog
            if not total_errors:
                self._show_success_dialog(f"Sincronizzazione completata!\n\n{total_synced} attività sono state sincronizzate con successo.")
            else:
                errors_str = "\n- ".join(total_errors)
                self._show_error_dialog(f"Sincronizzazione completata con {len(total_errors)} errori.\n\nNon è stato possibile sincronizzare:\n- {errors_str}")
        
        except Exception as e:
            logger.error(f"Errore imprevisto durante la sincronizzazione di Google Tasks: {e}")
            self._show_error_dialog(f"Si è verificato un errore imprevisto:\n{str(e)}")
        
        finally:
            # ESSENTIAL: Ensure loading dialog is always closed
            if self.app.page.overlay:
                self.app.page.overlay.clear()
            self.app.page.update()

    def build(self) -> ft.Container:
        """Costruisce la vista impostazioni"""
        sections = []
        
        # Email Section
        if self.section_expanded['email']:
            email_header = self._create_section_header("Configurazione Email", ft.Icons.EMAIL, 'email', True)
            email_content = self._create_smtp_section()
            sections.append(self._create_collapsible_section(email_header, email_content, 'email'))
        else:
            email_header = self._create_section_header("Configurazione Email", ft.Icons.EMAIL, 'email', False)
            sections.append(self._create_collapsible_section(email_header, ft.Container(), 'email'))
            
        # Alerts Section  
        if self.section_expanded['alerts']:
            alerts_header = self._create_section_header("Notifiche e Alert", ft.Icons.NOTIFICATIONS, 'alerts', True)
            alerts_content = self._create_alerts_section()
            sections.append(self._create_collapsible_section(alerts_header, alerts_content, 'alerts'))
        else:
            alerts_header = self._create_section_header("Notifiche e Alert", ft.Icons.NOTIFICATIONS, 'alerts', False)
            sections.append(self._create_collapsible_section(alerts_header, ft.Container(), 'alerts'))
        
        # Reports Section
        if self.section_expanded['reports']:
            reports_header = self._create_section_header("Report Automatici", ft.Icons.ASSESSMENT, 'reports', True)
            reports_content = self._create_scheduled_reports_section()
            sections.append(self._create_collapsible_section(reports_header, reports_content, 'reports'))
        else:
            reports_header = self._create_section_header("Report Automatici", ft.Icons.ASSESSMENT, 'reports', False)
            sections.append(self._create_collapsible_section(reports_header, ft.Container(), 'reports'))

        # Custom Reports Section
        if self.section_expanded['custom_reports']:
            custom_reports_header = self._create_section_header("Report Personalizzati", ft.Icons.ANALYTICS, 'custom_reports', True)
            custom_reports_content = self._create_custom_reports_section()
            sections.append(self._create_collapsible_section(custom_reports_header, custom_reports_content, 'custom_reports'))
        else:
            custom_reports_header = self._create_section_header("Report Personalizzati", ft.Icons.ANALYTICS, 'custom_reports', False)
            sections.append(self._create_collapsible_section(custom_reports_header, ft.Container(), 'custom_reports'))

        # App Section
        if self.section_expanded['app']:
            app_header = self._create_section_header("Impostazioni Applicazione", ft.Icons.SETTINGS, 'app', True)
            app_content = self._create_app_section()
            sections.append(self._create_collapsible_section(app_header, app_content, 'app'))
        else:
            app_header = self._create_section_header("Impostazioni Applicazione", ft.Icons.SETTINGS, 'app', False)
            sections.append(self._create_collapsible_section(app_header, ft.Container(), 'app'))
        
        # Windows Section
        if self.section_expanded['windows']:
            windows_header = self._create_section_header("Integrazione Windows", ft.Icons.COMPUTER, 'windows', True)
            windows_content = self._create_windows_section()
            sections.append(self._create_collapsible_section(windows_header, windows_content, 'windows'))
        else:
            windows_header = self._create_section_header("Integrazione Windows", ft.Icons.COMPUTER, 'windows', False)
            sections.append(self._create_collapsible_section(windows_header, ft.Container(), 'windows'))
        
        # Google Services Section
        if self.section_expanded['google']:
            google_header = self._create_section_header("Servizi Google", ft.Icons.CLOUD, 'google', True)
            google_content = self._create_google_services_section()
            sections.append(self._create_collapsible_section(google_header, google_content, 'google'))
        else:
            google_header = self._create_section_header("Servizi Google", ft.Icons.CLOUD, 'google', False)
            sections.append(self._create_collapsible_section(google_header, ft.Container(), 'google'))
        
        # Info Section
        if self.section_expanded['info']:
            info_header = self._create_section_header("Informazioni", ft.Icons.INFO, 'info', True)
            info_content = self._create_info_section()
            sections.append(self._create_collapsible_section(info_header, info_content, 'info'))
        else:
            info_header = self._create_section_header("Informazioni", ft.Icons.INFO, 'info', False)
            sections.append(self._create_collapsible_section(info_header, ft.Container(), 'info'))

        # Create scrollable content
        scrollable_content = ft.Column([
            self._create_header(),
            *sections,
            ft.Container(height=20),  # Bottom spacer
            
            # Save buttons row
            ft.Row([
                ft.ElevatedButton(
                    text="Salva Impostazioni",
                    icon=ft.Icons.SAVE,
                    on_click=lambda e: self._save_settings(),
                    bgcolor=ft.Colors.BLUE_600,
                    color=ft.Colors.WHITE,
                    expand=True
                ),
                ft.OutlinedButton(
                    text="Ripristina Default",
                    icon=ft.Icons.RESTORE,
                    on_click=lambda e: self._reset_to_defaults(),
                    expand=True
                )
            ], spacing=10)
        ], spacing=10, scroll=ft.ScrollMode.AUTO)

        return ft.Container(
            content=scrollable_content,
            padding=ft.padding.all(20),
            expand=True,
            bgcolor=ft.Colors.GREY_50
        )
    
    def refresh_data(self):
        """Aggiorna i dati delle impostazioni"""
        self._load_settings()
        logger.info("Impostazioni aggiornate")
    
    def _create_info_section(self) -> ft.Container:
        """Crea la sezione informazioni applicazione"""
        return ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Icon(
                        ft.Icons.INFO,
                        size=20,
                        color=ft.Colors.PURPLE_600
                    ),
                    ft.Text(
                        "Informazioni Applicazione",
                        size=16,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.GREY_800
                    )
                ], spacing=8),
                
                ft.Divider(height=1, color=ft.Colors.GREY_300),
                
                ft.Row([
                    ft.Column([
                        ft.Text("Versione:", size=12, color=ft.Colors.GREY_600),
                        ft.Text("Database:", size=12, color=ft.Colors.GREY_600),
                        ft.Text("Percorso Dati:", size=12, color=ft.Colors.GREY_600),
                        ft.Text("Ultimo Backup:", size=12, color=ft.Colors.GREY_600)
                    ], spacing=8),
                    
                    ft.Column([
                        ft.Text("1.0.0", size=12, weight=ft.FontWeight.BOLD),
                        ft.Text("SQLite", size=12, weight=ft.FontWeight.BOLD),
                        ft.Text(str(self.config.data_dir), size=12, weight=ft.FontWeight.BOLD),
                        ft.Text("Mai", size=12, weight=ft.FontWeight.BOLD)  # TODO: Implementare tracking backup
                    ], spacing=8)
                ], spacing=32),
                
                ft.Container(
                    content=ft.Row([
                        ft.TextButton(
                            text="Agevolami.it",
                            url="https://www.agevolami.it",
                            icon=ft.Icons.OPEN_IN_NEW
                        ),
                        
                        ft.TextButton(
                            text="Supporto",
                            on_click=lambda _: self._open_support()
                        ),
                        
                        ft.TextButton(
                            text="Licenza",
                            on_click=lambda _: self._show_license()
                        )
                    ], spacing=16),
                    padding=ft.padding.only(top=12)
                )
            ], spacing=12),
            padding=ft.padding.all(20),
            bgcolor=ft.Colors.WHITE,
            border_radius=ft.border_radius.only(bottom_left=8, bottom_right=8),
            border=ft.border.all(1, ft.Colors.GREY_300)
        )

    def _update_custom_report_setting(self, key: str, value):
        """Aggiorna un'impostazione dei report personalizzati"""
        self.custom_report_settings[key] = value
        logger.info(f"Impostazione custom report aggiornata: {key} = {value}")

    def _update_custom_report_recipients(self, value: str):
        """Aggiorna i destinatari dei report personalizzati"""
        if value.strip():
            recipients = [email.strip() for email in value.split(',') if email.strip()]
            self.custom_report_settings['recipients'] = recipients
        else:
            self.custom_report_settings['recipients'] = []
        logger.info(f"Destinatari custom report aggiornati: {self.custom_report_settings['recipients']}")

    def _send_custom_report(self, e):
        """Invia un report personalizzato"""
        try:
            # Mostra loading
            self._show_loading_dialog("Generazione e invio report personalizzato...")

            # Force UI update
            self.app.page.update()

            # Small delay to ensure loading dialog shows
            import time
            time.sleep(0.5)

            logger.info("🔄 Inizio invio report personalizzato...")

            # Prepara i filtri
            filters = {
                'include_clients': self.custom_report_settings['include_clients'],
                'include_projects': self.custom_report_settings['include_projects'],
                'include_tasks': self.custom_report_settings['include_tasks'],
                'include_deadlines': self.custom_report_settings['include_deadlines'],
                'include_expired_only': self.custom_report_settings['include_expired_only'],
                'days_ahead': self.custom_report_settings['days_ahead']
            }

            # Prepara i destinatari
            recipients = self.custom_report_settings['recipients']
            if not recipients:
                # Usa l'email del mittente se non ci sono destinatari
                sender_email = self.app.config.email_config.get('from_email')
                if sender_email:
                    recipients = [sender_email]
                else:
                    raise Exception("Nessun destinatario specificato e nessun mittente configurato")

            # Invia il report
            success = self.app.statistics_service.send_custom_report_email(recipients, filters)

            # Chiudi loading
            self.app.page.overlay.clear()
            self.app.page.update()

            if success:
                self._show_notification("Report personalizzato inviato con successo!", "success")
                self._show_success_dialog("Report personalizzato inviato con successo!")
            else:
                self._show_notification("Errore nell'invio del report personalizzato", "error")
                self._show_error_dialog("Errore durante l'invio del report personalizzato. Verifica la configurazione SMTP.")

        except Exception as ex:
            # Chiudi loading in caso di errore
            self.app.page.overlay.clear()
            self.app.page.update()

            logger.error(f"Errore invio report personalizzato: {ex}")
            self._show_notification(f"Errore: {str(ex)}", "error")
            self._show_error_dialog(f"Errore durante l'invio del report personalizzato:\n{str(ex)}")

    def _preview_custom_report(self, e):
        """Mostra un'anteprima del report personalizzato"""
        try:
            # Mostra loading
            self._show_loading_dialog("Generazione anteprima report...")

            # Force UI update
            self.app.page.update()

            # Small delay to ensure loading dialog shows
            import time
            time.sleep(0.5)

            logger.info("🔄 Generazione anteprima report personalizzato...")

            # Prepara i filtri
            filters = {
                'include_clients': self.custom_report_settings['include_clients'],
                'include_projects': self.custom_report_settings['include_projects'],
                'include_tasks': self.custom_report_settings['include_tasks'],
                'include_deadlines': self.custom_report_settings['include_deadlines'],
                'include_expired_only': self.custom_report_settings['include_expired_only'],
                'days_ahead': self.custom_report_settings['days_ahead']
            }

            # Genera il report
            report = self.app.statistics_service.generate_custom_report(filters)

            # Chiudi loading
            self.app.page.overlay.clear()
            self.app.page.update()

            if report:
                # Mostra dialog con anteprima
                self._show_report_preview_dialog(report)
            else:
                self._show_error_dialog("Errore durante la generazione dell'anteprima del report.")

        except Exception as ex:
            # Chiudi loading in caso di errore
            self.app.page.overlay.clear()
            self.app.page.update()

            logger.error(f"Errore anteprima report: {ex}")
            self._show_error_dialog(f"Errore durante la generazione dell'anteprima:\n{str(ex)}")

    def _show_report_preview_dialog(self, report: dict):
        """Mostra un dialog con l'anteprima del report"""
        summary = report.get('summary', {})
        tasks = report.get('tasks', {})

        # Crea contenuto anteprima
        preview_content = ft.Column([
            ft.Text("📊 Anteprima Report Personalizzato", size=18, weight=ft.FontWeight.BOLD),
            ft.Divider(),

            # Statistiche generali
            ft.Text("📈 Statistiche Generali:", size=14, weight=ft.FontWeight.BOLD),
            ft.Row([
                ft.Column([
                    ft.Text(f"👥 Clienti: {summary.get('total_clients', 0)}", size=12),
                    ft.Text(f"📁 Progetti: {summary.get('total_projects', 0)}", size=12),
                ], spacing=4),
                ft.Column([
                    ft.Text(f"📋 Task: {summary.get('total_tasks', 0)}", size=12),
                    ft.Text(f"📅 Scadenze: {summary.get('total_deadlines', 0)}", size=12),
                ], spacing=4)
            ], spacing=32),

            ft.Divider(),

            # Task details se incluse
            ft.Text("📋 Dettagli Task:", size=14, weight=ft.FontWeight.BOLD) if tasks else ft.Container(),
            ft.Row([
                ft.Column([
                    ft.Text(f"[OK] Completate: {tasks.get('completed', 0)}", size=12),
                    ft.Text(f"🔄 In Corso: {tasks.get('in_progress', 0)}", size=12),
                ], spacing=4),
                ft.Column([
                    ft.Text(f"🚨 Scadute: {tasks.get('expired', 0)}", size=12, color=ft.Colors.RED),
                    ft.Text(f"⏰ In Scadenza: {tasks.get('expiring_soon', 0)}", size=12, color=ft.Colors.ORANGE),
                ], spacing=4)
            ], spacing=32) if tasks else ft.Container(),

            ft.Text(f"📊 Tasso Completamento: {tasks.get('completion_rate', 0)}%", size=12) if tasks else ft.Container(),

            ft.Divider(),

            # Filtri applicati
            ft.Text("🔍 Filtri Applicati:", size=14, weight=ft.FontWeight.BOLD),
            ft.Text(f"• Giorni anticipo: {report.get('filters_applied', {}).get('days_ahead', 30)}", size=11),
            ft.Text(f"• Solo scadute: {'Sì' if report.get('filters_applied', {}).get('include_expired_only') else 'No'}", size=11),
        ], spacing=8, scroll=ft.ScrollMode.AUTO)

        # Dialog
        dialog = ft.AlertDialog(
            title=ft.Text("Anteprima Report"),
            content=ft.Container(
                content=preview_content,
                width=500,
                height=400
            ),
            actions=[
                ft.TextButton("Chiudi", on_click=lambda _: self._close_dialog())
            ]
        )

        self.app.page.dialog = dialog
        dialog.open = True
        self.app.page.update()

    def _close_dialog(self):
        """Chiude il dialog corrente"""
        if self.app.page.dialog:
            self.app.page.dialog.open = False
            self.app.page.update()