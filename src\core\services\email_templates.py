#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Enhanced HTML Email Templates for Agevolami PM Reports
"""

from typing import Dict, Any, List
from datetime import datetime, date
from ..utils import get_logger

logger = get_logger(__name__)

class EmailTemplates:
    """Professional HTML email templates for reports"""
    
    @staticmethod
    def get_base_style() -> str:
        """Returns the base CSS styles for all email templates"""
        return """
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 800px;
                margin: 0 auto;
                background-color: #f5f5f5;
                padding: 20px;
            }
            .container {
                background-color: white;
                border-radius: 10px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                overflow: hidden;
            }
            .header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 30px;
                text-align: center;
            }
            .header h1 {
                margin: 0;
                font-size: 28px;
                font-weight: 300;
            }
            .header .subtitle {
                margin: 10px 0 0 0;
                font-size: 16px;
                opacity: 0.9;
            }
            .content {
                padding: 30px;
            }
            .summary-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 20px;
                margin: 20px 0;
            }
            .summary-card {
                background: #f8f9fa;
                border-left: 4px solid #667eea;
                padding: 20px;
                border-radius: 5px;
            }
            .summary-card.critical {
                border-left-color: #dc3545;
                background: #fff5f5;
            }
            .summary-card.warning {
                border-left-color: #ffc107;
                background: #fffbf0;
            }
            .summary-card.success {
                border-left-color: #28a745;
                background: #f0fff4;
            }
            .summary-card h3 {
                margin: 0 0 10px 0;
                font-size: 14px;
                text-transform: uppercase;
                color: #666;
                font-weight: 600;
            }
            .summary-card .value {
                font-size: 32px;
                font-weight: bold;
                color: #333;
                margin: 0;
            }
            .section {
                margin: 30px 0;
                border-bottom: 1px solid #eee;
                padding-bottom: 20px;
            }
            .section:last-child {
                border-bottom: none;
            }
            .section h2 {
                color: #667eea;
                font-size: 20px;
                margin-bottom: 15px;
                display: flex;
                align-items: center;
            }
            .section h2::before {
                content: '';
                width: 4px;
                height: 20px;
                background: #667eea;
                margin-right: 10px;
                border-radius: 2px;
            }
            .task-list {
                background: #f8f9fa;
                border-radius: 5px;
                padding: 15px;
                margin: 15px 0;
            }
            .task-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 10px 0;
                border-bottom: 1px solid #dee2e6;
            }
            .task-item:last-child {
                border-bottom: none;
            }
            .task-title {
                font-weight: 600;
                color: #333;
            }
            .task-meta {
                font-size: 12px;
                color: #666;
                margin-top: 5px;
            }
            .priority-badge {
                padding: 4px 8px;
                border-radius: 12px;
                font-size: 11px;
                font-weight: 600;
                text-transform: uppercase;
            }
            .priority-critica {
                background: #dc3545;
                color: white;
            }
            .priority-alta {
                background: #fd7e14;
                color: white;
            }
            .priority-media {
                background: #ffc107;
                color: #333;
            }
            .priority-bassa {
                background: #6c757d;
                color: white;
            }
            .days-badge {
                padding: 4px 8px;
                border-radius: 12px;
                font-size: 11px;
                font-weight: 600;
                margin-left: 10px;
            }
            .overdue {
                background: #dc3545;
                color: white;
            }
            .due-soon {
                background: #ffc107;
                color: #333;
            }
            .footer {
                background: #f8f9fa;
                padding: 20px 30px;
                text-align: center;
                color: #666;
                font-size: 14px;
                border-top: 1px solid #eee;
            }
            .footer a {
                color: #667eea;
                text-decoration: none;
            }
            .no-data {
                text-align: center;
                color: #666;
                font-style: italic;
                padding: 20px;
            }
            .progress-bar {
                background: #e9ecef;
                border-radius: 10px;
                height: 8px;
                overflow: hidden;
                margin: 10px 0;
            }
            .progress-fill {
                height: 100%;
                background: linear-gradient(90deg, #28a745, #20c997);
                transition: width 0.3s ease;
            }
        </style>
        """
    
    @staticmethod
    def generate_automatic_report_html(report: Dict[str, Any]) -> str:
        """Generate HTML for comprehensive automatic daily/scheduled reports"""
        try:
            summary = report.get('summary', {})
            tasks = report.get('tasks', {})
            alerts = report.get('alerts', {})
            database_stats = report.get('database_statistics', {})
            upcoming_deadlines = report.get('upcoming_deadlines_detailed', [])
            performance = report.get('performance_indicators', {})

            # Generate date info
            generated_date = datetime.fromisoformat(report.get('generated_at', datetime.now().isoformat()))
            date_str = generated_date.strftime('%d/%m/%Y alle %H:%M')
            period_desc = report.get('period', {}).get('description', '')

            html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Report Automatico Completo Agevolami PM</title>
                {EmailTemplates.get_base_style()}
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>📊 Report Automatico Completo</h1>
                        <p class="subtitle">Agevolami PM - {date_str}</p>
                        <p style="color: #666; font-size: 14px; margin-top: 10px;">{period_desc}</p>
                    </div>

                    <div class="content">
                        <!-- Alert Summary -->
                        <div class="summary-grid">
                            <div class="summary-card critical">
                                <h3>🚨 Elementi Critici</h3>
                                <p class="value">{alerts.get('critical_count', 0)}</p>
                            </div>
                            <div class="summary-card warning">
                                <h3>⚠️ Attenzione Richiesta</h3>
                                <p class="value">{alerts.get('warning_count', 0)}</p>
                            </div>
                            <div class="summary-card">
                                <h3>📋 Task Totali</h3>
                                <p class="value">{summary.get('total_tasks', 0)}</p>
                            </div>
                            <div class="summary-card success">
                                <h3>✅ Completamento</h3>
                                <p class="value">{tasks.get('completion_rate', 0)}%</p>
                            </div>
                        </div>

                        <!-- Comprehensive Database Statistics -->
                        <div class="section">
                            <h2>📊 Statistiche Complete Database</h2>
                            <div class="summary-grid">
                                <div class="summary-card">
                                    <h3>👥 Clienti Totali</h3>
                                    <p class="value">{database_stats.get('clients', {}).get('total', 0)}</p>
                                </div>
                                <div class="summary-card">
                                    <h3>📁 Progetti Totali</h3>
                                    <p class="value">{database_stats.get('projects', {}).get('total', 0)}</p>
                                </div>
                                <div class="summary-card success">
                                    <h3>✅ Progetti Completati</h3>
                                    <p class="value">{summary.get('completed_projects', 0)}</p>
                                </div>
                                <div class="summary-card">
                                    <h3>🔄 Progetti Attivi</h3>
                                    <p class="value">{summary.get('active_projects', 0)}</p>
                                </div>
                                <div class="summary-card">
                                    <h3>📋 Task Completate</h3>
                                    <p class="value">{summary.get('completed_tasks', 0)}</p>
                                </div>
                                <div class="summary-card warning">
                                    <h3>⏳ Task Pendenti</h3>
                                    <p class="value">{summary.get('pending_tasks', 0)}</p>
                                </div>
                                <div class="summary-card">
                                    <h3>📅 Scadenze Totali</h3>
                                    <p class="value">{summary.get('total_deadlines', 0)}</p>
                                </div>
                                <div class="summary-card critical">
                                    <h3>🚨 Scadenze Superate</h3>
                                    <p class="value">{summary.get('overdue_deadlines', 0)}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Performance Indicators -->
                        <div class="section">
                            <h2>📈 Indicatori di Performance</h2>
                            <div class="summary-grid">
                                <div class="summary-card">
                                    <h3>🎯 Completamento Progetti</h3>
                                    <p class="value">{performance.get('project_completion_rate', 0)}%</p>
                                </div>
                                <div class="summary-card">
                                    <h3>⏰ Rispetto Scadenze</h3>
                                    <p class="value">{performance.get('deadline_adherence_rate', 0)}%</p>
                                </div>
                                <div class="summary-card">
                                    <h3>⚡ Efficienza Task</h3>
                                    <p class="value">{performance.get('task_efficiency_score', 0)}%</p>
                                </div>
                                <div class="summary-card">
                                    <h3>📊 Attività Clienti</h3>
                                    <p class="value" style="font-size: 14px;">{performance.get('client_activity_level', 'N/A')}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Progress Bar -->
                        <div class="section">
                            <h2>📈 Progresso Generale</h2>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: {tasks.get('completion_rate', 0)}%"></div>
                            </div>
                            <p style="text-align: center; margin-top: 10px; color: #666;">
                                {tasks.get('completion_rate', 0)}% delle task completate
                            </p>
                        </div>
            """
            
            # Add expired tasks section
            expired_tasks = tasks.get('expired_tasks', [])
            if expired_tasks:
                html += f"""
                        <div class="section">
                            <h2>🚨 Task Scadute ({len(expired_tasks)})</h2>
                            <div class="task-list">
                """
                
                for task in expired_tasks[:5]:  # Show max 5 expired tasks
                    html += f"""
                                <div class="task-item">
                                    <div>
                                        <div class="task-title">{task.get('title', 'N/A')}</div>
                                        <div class="task-meta">
                                            📁 {task.get('project_name', 'N/A')} • 
                                            🎯 {task.get('deadline_title', 'N/A')}
                                        </div>
                                    </div>
                                    <div>
                                        <span class="priority-badge priority-{task.get('priority', 'media')}">{task.get('priority', 'media')}</span>
                                        <span class="days-badge overdue">{task.get('days_overdue', 0)} giorni fa</span>
                                    </div>
                                </div>
                    """
                
                html += """
                            </div>
                        </div>
                """
            
            # Add detailed upcoming deadlines section
            if upcoming_deadlines:
                html += f"""
                        <div class="section">
                            <h2>📅 Scadenze Dettagliate Prossimi 15 Giorni ({len(upcoming_deadlines)})</h2>
                            <div class="task-list">
                """

                for deadline in upcoming_deadlines[:10]:  # Show max 10 deadlines
                    days_remaining = deadline.get('days_remaining', 0)
                    urgency_class = 'overdue' if days_remaining <= 1 else 'due-soon' if days_remaining <= 3 else 'normal'
                    urgency_text = 'oggi' if days_remaining == 0 else f'{days_remaining} giorni'

                    project_info = deadline.get('project_info', {})
                    client_info = deadline.get('client_info', {})
                    related_tasks = deadline.get('related_tasks', [])

                    html += f"""
                                <div class="task-item">
                                    <div>
                                        <div class="task-title">{deadline.get('title', 'N/A')}</div>
                                        <div class="task-meta">
                                            👤 {client_info.get('name', 'N/A')} •
                                            📁 {project_info.get('name', 'N/A')} •
                                            🎯 {deadline.get('priority', 'normale')}
                                        </div>
                                        <div class="task-meta" style="font-size: 12px; color: #888;">
                                            📋 {len(related_tasks)} task collegate
                                            {' • 📝 ' + deadline.get('description', '')[:50] + '...' if deadline.get('description') else ''}
                                        </div>
                                    </div>
                                    <div>
                                        <span class="priority-badge priority-{deadline.get('priority', 'media')}">{deadline.get('priority', 'media')}</span>
                                        <span class="days-badge {urgency_class}">{urgency_text}</span>
                                    </div>
                                </div>
                    """

                html += """
                            </div>
                        </div>
                """

            # Add expiring tasks section
            expiring_tasks = tasks.get('expiring_tasks', [])
            if expiring_tasks:
                html += f"""
                        <div class="section">
                            <h2>⏰ Task in Scadenza ({len(expiring_tasks)})</h2>
                            <div class="task-list">
                """

                for task in expiring_tasks[:8]:  # Show max 8 expiring tasks
                    days_remaining = task.get('days_remaining', 0)
                    urgency_class = 'overdue' if days_remaining <= 1 else 'due-soon'
                    urgency_text = 'oggi' if days_remaining == 0 else f'{days_remaining} giorni'

                    html += f"""
                                <div class="task-item">
                                    <div>
                                        <div class="task-title">{task.get('title', 'N/A')}</div>
                                        <div class="task-meta">
                                            📁 {task.get('project_name', 'N/A')} •
                                            🎯 {task.get('deadline_title', 'N/A')}
                                        </div>
                                    </div>
                                    <div>
                                        <span class="priority-badge priority-{task.get('priority', 'media')}">{task.get('priority', 'media')}</span>
                                        <span class="days-badge {urgency_class}">{urgency_text}</span>
                                    </div>
                                </div>
                    """

                html += """
                            </div>
                        </div>
                """
            
            # Add top clients section
            top_clients = database_stats.get('clients', {}).get('top_clients', [])
            if top_clients:
                html += f"""
                        <div class="section">
                            <h2>👥 Top Clienti per Attività ({len(top_clients)})</h2>
                            <div class="task-list">
                """

                for client in top_clients:
                    html += f"""
                                <div class="task-item">
                                    <div>
                                        <div class="task-title">{client.get('name', 'N/A')}</div>
                                        <div class="task-meta">
                                            🏢 {client.get('company', 'N/A')} •
                                            🔄 {client.get('active_projects', 0)} progetti attivi
                                        </div>
                                    </div>
                                    <div>
                                        <span class="days-badge due-soon">{client.get('projects_count', 0)} progetti</span>
                                    </div>
                                </div>
                    """

                html += """
                            </div>
                        </div>
                """

            # Add project status breakdown
            project_stats = database_stats.get('projects', {})
            if project_stats.get('by_status'):
                html += f"""
                        <div class="section">
                            <h2>📁 Distribuzione Progetti per Stato</h2>
                            <div class="summary-grid">
                """

                status_mapping = {
                    'in_progress': ('🔄', 'In Corso'),
                    'completed': ('✅', 'Completati'),
                    'on_hold': ('⏸️', 'In Pausa'),
                    'cancelled': ('❌', 'Annullati'),
                    'planning': ('📋', 'Pianificazione')
                }

                for status, count in project_stats.get('by_status', {}).items():
                    icon, label = status_mapping.get(status, ('📁', status.title()))
                    html += f"""
                                <div class="summary-card">
                                    <h3>{icon} {label}</h3>
                                    <p class="value">{count}</p>
                                </div>
                    """

                html += """
                            </div>
                        </div>
                """

            # Add summary statistics
            html += f"""
                        <div class="section">
                            <h2>📊 Riepilogo Sistema Finale</h2>
                            <div class="summary-grid">
                                <div class="summary-card">
                                    <h3>👥 Clienti Totali</h3>
                                    <p class="value">{summary.get('total_clients', 0)}</p>
                                </div>
                                <div class="summary-card">
                                    <h3>📁 Progetti Totali</h3>
                                    <p class="value">{summary.get('total_projects', 0)}</p>
                                </div>
                                <div class="summary-card">
                                    <h3>🔄 Progetti Attivi</h3>
                                    <p class="value">{summary.get('active_projects', 0)}</p>
                                </div>
                                <div class="summary-card warning">
                                    <h3>⚠️ Problemi Totali</h3>
                                    <p class="value">{alerts.get('total_issues', 0)}</p>
                                </div>
                            </div>

                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 20px;">
                                <h3 style="margin: 0 0 10px 0; color: #333;">📈 Indicatori Chiave</h3>
                                <p style="margin: 5px 0; color: #666;">
                                    🎯 <strong>Efficienza Progetti:</strong> {performance.get('project_completion_rate', 0)}% completamento
                                </p>
                                <p style="margin: 5px 0; color: #666;">
                                    ⏰ <strong>Puntualità:</strong> {performance.get('deadline_adherence_rate', 0)}% rispetto scadenze
                                </p>
                                <p style="margin: 5px 0; color: #666;">
                                    ⚡ <strong>Produttività:</strong> {performance.get('task_efficiency_score', 0)}% efficienza task
                                </p>
                                <p style="margin: 5px 0; color: #666;">
                                    📊 <strong>Attività Clienti:</strong> {performance.get('client_activity_level', 'N/A')}
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="footer">
                        <p>📧 Report automatico completo generato da <strong>Agevolami PM</strong></p>
                        <p>🕒 Questo report include statistiche complete del database e analisi dettagliate</p>
                        <p>📊 Include tutti i clienti, progetti, task e scadenze con indicatori di performance</p>
                        <p>💡 Per modificare le impostazioni dei report automatici, accedi alle impostazioni dell'applicazione</p>
                    </div>
                </div>
            </body>
            </html>
            """
            
            return html
            
        except Exception as e:
            logger.error(f"Errore generazione template automatico: {e}")
            return EmailTemplates._get_error_template("Errore nella generazione del report automatico")
    
    @staticmethod
    def generate_custom_report_html(report: Dict[str, Any]) -> str:
        """Generate HTML for custom/manual reports"""
        try:
            summary = report.get('summary', {})
            clients = report.get('clients', {})
            projects = report.get('projects', {})
            tasks = report.get('tasks', {})
            deadlines = report.get('deadlines', {})
            filters = report.get('filters_applied', {})

            # Generate date info
            generated_date = datetime.fromisoformat(report.get('generated_at', datetime.now().isoformat()))
            date_str = generated_date.strftime('%d/%m/%Y alle %H:%M')

            html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Report Personalizzato Agevolami PM</title>
                {EmailTemplates.get_base_style()}
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>📋 Report Personalizzato</h1>
                        <p class="subtitle">Agevolami PM - {date_str}</p>
                    </div>

                    <div class="content">
                        <!-- Summary Statistics -->
                        <div class="summary-grid">
                            <div class="summary-card">
                                <h3>👥 Clienti</h3>
                                <p class="value">{summary.get('total_clients', 0)}</p>
                            </div>
                            <div class="summary-card">
                                <h3>📁 Progetti</h3>
                                <p class="value">{summary.get('total_projects', 0)}</p>
                            </div>
                            <div class="summary-card">
                                <h3>📋 Task</h3>
                                <p class="value">{summary.get('total_tasks', 0)}</p>
                            </div>
                            <div class="summary-card">
                                <h3>📅 Scadenze</h3>
                                <p class="value">{summary.get('total_deadlines', 0)}</p>
                            </div>
                        </div>
            """

            # Add filters info if any
            if filters:
                html += """
                        <div class="section">
                            <h2>🔍 Filtri Applicati</h2>
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px;">
                """

                for key, value in filters.items():
                    if value and key != 'include_expired_only':
                        display_key = key.replace('_', ' ').title()
                        html += f"<p><strong>{display_key}:</strong> {value}</p>"

                html += """
                            </div>
                        </div>
                """

            # Add task details if included
            if tasks and isinstance(tasks, dict):
                html += f"""
                        <div class="section">
                            <h2>📋 Dettagli Task</h2>
                            <div class="summary-grid">
                                <div class="summary-card">
                                    <h3>✅ Completate</h3>
                                    <p class="value">{tasks.get('completed', 0)}</p>
                                </div>
                                <div class="summary-card">
                                    <h3>🔄 In Corso</h3>
                                    <p class="value">{tasks.get('in_progress', 0)}</p>
                                </div>
                                <div class="summary-card critical">
                                    <h3>🚨 Scadute</h3>
                                    <p class="value">{tasks.get('expired', 0)}</p>
                                </div>
                                <div class="summary-card warning">
                                    <h3>⏰ In Scadenza</h3>
                                    <p class="value">{tasks.get('expiring_soon', 0)}</p>
                                </div>
                            </div>

                            <div class="progress-bar">
                                <div class="progress-fill" style="width: {tasks.get('completion_rate', 0)}%"></div>
                            </div>
                            <p style="text-align: center; margin-top: 10px; color: #666;">
                                Tasso di completamento: {tasks.get('completion_rate', 0)}%
                            </p>
                        </div>
                """

                # Add expired tasks if any
                expired_tasks = tasks.get('expired_tasks', [])
                if expired_tasks:
                    html += f"""
                        <div class="section">
                            <h2>🚨 Task Scadute ({len(expired_tasks)})</h2>
                            <div class="task-list">
                    """

                    for task in expired_tasks:
                        html += f"""
                                <div class="task-item">
                                    <div>
                                        <div class="task-title">{task.get('title', 'N/A')}</div>
                                        <div class="task-meta">
                                            📁 {task.get('project_name', 'N/A')} •
                                            🎯 {task.get('deadline_title', 'N/A')}
                                        </div>
                                    </div>
                                    <div>
                                        <span class="priority-badge priority-{task.get('priority', 'media')}">{task.get('priority', 'media')}</span>
                                        <span class="days-badge overdue">{task.get('days_overdue', 0)} giorni fa</span>
                                    </div>
                                </div>
                        """

                    html += """
                            </div>
                        </div>
                    """

                # Add expiring tasks if any
                expiring_tasks = tasks.get('expiring_tasks', [])
                if expiring_tasks:
                    html += f"""
                        <div class="section">
                            <h2>⏰ Task in Scadenza ({len(expiring_tasks)})</h2>
                            <div class="task-list">
                    """

                    for task in expiring_tasks:
                        days_remaining = task.get('days_remaining', 0)
                        urgency_class = 'overdue' if days_remaining <= 1 else 'due-soon'
                        urgency_text = 'oggi' if days_remaining == 0 else f'{days_remaining} giorni'

                        html += f"""
                                <div class="task-item">
                                    <div>
                                        <div class="task-title">{task.get('title', 'N/A')}</div>
                                        <div class="task-meta">
                                            📁 {task.get('project_name', 'N/A')} •
                                            🎯 {task.get('deadline_title', 'N/A')}
                                        </div>
                                    </div>
                                    <div>
                                        <span class="priority-badge priority-{task.get('priority', 'media')}">{task.get('priority', 'media')}</span>
                                        <span class="days-badge {urgency_class}">{urgency_text}</span>
                                    </div>
                                </div>
                        """

                    html += """
                            </div>
                        </div>
                    """

            # Add detailed upcoming deadlines section
            upcoming_deadlines = report.get('upcoming_deadlines_detailed', [])
            if upcoming_deadlines:
                days_ahead = report.get('filters_applied', {}).get('days_ahead', 15)
                html += f"""
                        <div class="section">
                            <h2>📅 Scadenze Dettagliate Prossimi {days_ahead} Giorni ({len(upcoming_deadlines)})</h2>
                            <div class="task-list">
                """

                for deadline in upcoming_deadlines[:10]:  # Show max 10 deadlines
                    days_remaining = deadline.get('days_remaining', 0)
                    urgency_class = 'overdue' if days_remaining <= 1 else 'due-soon' if days_remaining <= 3 else 'normal'
                    urgency_text = 'oggi' if days_remaining == 0 else f'{days_remaining} giorni'

                    project_info = deadline.get('project_info', {})
                    client_info = deadline.get('client_info', {})
                    related_tasks = deadline.get('related_tasks', [])

                    html += f"""
                                <div class="task-item">
                                    <div>
                                        <div class="task-title">{deadline.get('title', 'N/A')}</div>
                                        <div class="task-meta">
                                            👤 {client_info.get('name', 'N/A')} •
                                            📁 {project_info.get('name', 'N/A')}
                                        </div>
                                        <div class="task-description">{deadline.get('description', '')[:100]}{'...' if len(deadline.get('description', '')) > 100 else ''}</div>
                    """

                    if related_tasks:
                        html += f"""
                                        <div class="task-meta">🔗 {len(related_tasks)} task collegat{'e' if len(related_tasks) > 1 else 'a'}</div>
                        """

                    html += f"""
                                    </div>
                                    <div>
                                        <span class="priority-badge priority-{deadline.get('priority', 'media')}">{deadline.get('priority', 'media')}</span>
                                        <span class="days-badge {urgency_class}">{urgency_text}</span>
                                    </div>
                                </div>
                    """

                html += """
                            </div>
                        </div>
                """

            # Add client details if included
            if clients and isinstance(clients, dict):
                top_clients = clients.get('top_clients', [])
                if top_clients:
                    html += f"""
                        <div class="section">
                            <h2>👥 Top Clienti ({clients.get('total', 0)} totali)</h2>
                            <div class="task-list">
                    """

                    for client in top_clients[:10]:
                        html += f"""
                                <div class="task-item">
                                    <div>
                                        <div class="task-title">{client.get('name', 'N/A')}</div>
                                        <div class="task-meta">{client.get('company', 'N/A')}</div>
                                    </div>
                                    <div>
                                        <span class="days-badge due-soon">{client.get('projects_count', 0)} progetti</span>
                                    </div>
                                </div>
                        """

                    html += """
                            </div>
                        </div>
                    """

            # Add project details if included
            if projects and isinstance(projects, dict):
                active_projects = projects.get('active_projects', [])
                if active_projects:
                    html += f"""
                        <div class="section">
                            <h2>📁 Progetti Attivi ({len(active_projects)})</h2>
                            <div class="task-list">
                    """

                    for project in active_projects[:10]:
                        completion = project.get('completion_percentage', 0)
                        html += f"""
                                <div class="task-item">
                                    <div>
                                        <div class="task-title">{project.get('name', 'N/A')}</div>
                                        <div class="task-meta">
                                            👤 {project.get('client_name', 'N/A')} •
                                            📊 {completion}% completato
                                        </div>
                                    </div>
                                    <div>
                                        <span class="days-badge due-soon">{completion}%</span>
                                    </div>
                                </div>
                        """

                    html += """
                            </div>
                        </div>
                    """

            html += """
                    </div>

                    <div class="footer">
                        <p>📧 Report personalizzato generato da <strong>Agevolami PM</strong></p>
                        <p>🎯 Questo report è stato creato con filtri specifici per le tue esigenze</p>
                        <p>💡 Per generare altri report personalizzati, accedi all'applicazione</p>
                    </div>
                </div>
            </body>
            </html>
            """

            return html

        except Exception as e:
            logger.error(f"Errore generazione template personalizzato: {e}")
            return EmailTemplates._get_error_template("Errore nella generazione del report personalizzato")

    @staticmethod
    def generate_full_report_html(report: Dict[str, Any]) -> str:
        """Generate HTML for comprehensive full reports"""
        try:
            summary = report.get('summary', {})
            clients = report.get('clients', {})
            projects = report.get('projects', {})
            tasks = report.get('tasks', {})
            deadlines = report.get('deadlines', [])

            # Generate date info
            generated_date = datetime.fromisoformat(report.get('generated_at', datetime.now().isoformat()))
            date_str = generated_date.strftime('%d/%m/%Y alle %H:%M')

            html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Report Completo Agevolami PM</title>
                {EmailTemplates.get_base_style()}
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>📊 Report Completo</h1>
                        <p class="subtitle">Agevolami PM - {date_str}</p>
                    </div>

                    <div class="content">
                        <!-- Executive Summary -->
                        <div class="summary-grid">
                            <div class="summary-card">
                                <h3>👥 Clienti Totali</h3>
                                <p class="value">{summary.get('total_clients', 0)}</p>
                            </div>
                            <div class="summary-card">
                                <h3>📁 Progetti Totali</h3>
                                <p class="value">{summary.get('total_projects', 0)}</p>
                            </div>
                            <div class="summary-card success">
                                <h3>🔄 Progetti Attivi</h3>
                                <p class="value">{summary.get('active_projects', 0)}</p>
                            </div>
                            <div class="summary-card">
                                <h3>📋 Task Totali</h3>
                                <p class="value">{summary.get('total_tasks', 0)}</p>
                            </div>
                            <div class="summary-card critical">
                                <h3>🚨 Task Scadute</h3>
                                <p class="value">{summary.get('expired_tasks', 0)}</p>
                            </div>
                            <div class="summary-card warning">
                                <h3>⏰ Task in Scadenza</h3>
                                <p class="value">{summary.get('expiring_tasks', 0)}</p>
                            </div>
                            <div class="summary-card">
                                <h3>📅 Scadenze Totali</h3>
                                <p class="value">{summary.get('total_deadlines', 0)}</p>
                            </div>
                            <div class="summary-card critical">
                                <h3>🚨 Scadenze Superate</h3>
                                <p class="value">{summary.get('overdue_deadlines', 0)}</p>
                            </div>
                        </div>
            """

            # Add task completion progress
            if tasks and isinstance(tasks, dict):
                completion_rate = tasks.get('completion_rate', 0)
                html += f"""
                        <div class="section">
                            <h2>📈 Progresso Task</h2>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: {completion_rate}%"></div>
                            </div>
                            <p style="text-align: center; margin-top: 10px; color: #666;">
                                {completion_rate}% delle task completate
                            </p>

                            <div class="summary-grid" style="margin-top: 20px;">
                                <div class="summary-card success">
                                    <h3>✅ Completate</h3>
                                    <p class="value">{tasks.get('completed', 0)}</p>
                                </div>
                                <div class="summary-card">
                                    <h3>🔄 In Corso</h3>
                                    <p class="value">{tasks.get('in_progress', 0)}</p>
                                </div>
                                <div class="summary-card">
                                    <h3>⏸️ In Attesa</h3>
                                    <p class="value">{tasks.get('pending', 0)}</p>
                                </div>
                            </div>
                        </div>
                """

                # Add expired tasks section
                expired_tasks = tasks.get('expired_tasks', [])
                if expired_tasks:
                    html += f"""
                        <div class="section">
                            <h2>🚨 Attività Scadute ({len(expired_tasks)})</h2>
                            <div class="task-list">
                    """

                    for task in expired_tasks[:10]:  # Show max 10 expired tasks
                        html += f"""
                                <div class="task-item">
                                    <div>
                                        <div class="task-title">{task.get('title', 'N/A')}</div>
                                        <div class="task-meta">
                                            📁 {task.get('project_name', 'N/A')} •
                                            🎯 {task.get('deadline_title', 'N/A')}
                                        </div>
                                    </div>
                                    <div>
                                        <span class="priority-badge priority-{task.get('priority', 'media')}">{task.get('priority', 'media')}</span>
                                        <span class="days-badge overdue">{task.get('days_overdue', 0)} giorni fa</span>
                                    </div>
                                </div>
                        """

                    html += """
                            </div>
                        </div>
                    """

                # Add expiring tasks section
                expiring_tasks = tasks.get('expiring_tasks', [])
                if expiring_tasks:
                    html += f"""
                        <div class="section">
                            <h2>⏰ Attività in Scadenza ({len(expiring_tasks)})</h2>
                            <div class="task-list">
                    """

                    for task in expiring_tasks[:10]:  # Show max 10 expiring tasks
                        days_remaining = task.get('days_remaining', 0)
                        urgency_class = 'overdue' if days_remaining <= 1 else 'due-soon'
                        urgency_text = 'oggi' if days_remaining == 0 else f'{days_remaining} giorni'

                        html += f"""
                                <div class="task-item">
                                    <div>
                                        <div class="task-title">{task.get('title', 'N/A')}</div>
                                        <div class="task-meta">
                                            📁 {task.get('project_name', 'N/A')} •
                                            🎯 {task.get('deadline_title', 'N/A')}
                                        </div>
                                    </div>
                                    <div>
                                        <span class="priority-badge priority-{task.get('priority', 'media')}">{task.get('priority', 'media')}</span>
                                        <span class="days-badge {urgency_class}">{urgency_text}</span>
                                    </div>
                                </div>
                        """

                    html += """
                            </div>
                        </div>
                    """

            html += """
                    </div>

                    <div class="footer">
                        <p>📧 Report completo generato da <strong>Agevolami PM</strong></p>
                        <p>📊 Questo report include tutte le statistiche del sistema</p>
                        <p>🔄 Report generato automaticamente per il monitoraggio completo</p>
                    </div>
                </div>
            </body>
            </html>
            """

            return html

        except Exception as e:
            logger.error(f"Errore generazione template completo: {e}")
            return EmailTemplates._get_error_template("Errore nella generazione del report completo")

    @staticmethod
    def _get_error_template(error_message: str) -> str:
        """Generate a simple error template"""
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Errore Report</title>
        </head>
        <body style="font-family: Arial, sans-serif; padding: 20px;">
            <h2 style="color: #dc3545;">Errore nella generazione del report</h2>
            <p>{error_message}</p>
            <p>Si prega di contattare l'amministratore del sistema.</p>
        </body>
        </html>
        """
