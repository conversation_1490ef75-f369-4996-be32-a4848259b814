#!/usr/bin/env python3
"""
🧪 Test Grey Screen Fix - Update Check on Launch

This test verifies that the auto-update check on launch works correctly
without causing grey screens after applying the fix from grey-screen-fix.md.

✅ Tests:
- Silent update check on startup
- Manual update check with various scenarios
- GitHub token configuration dialog
- Error handling scenarios
- All dialog types (checking, no updates, error, token dialogs)

🎯 Expected Result: 
- No grey screens at any point
- Proper dialog open/close behavior
- Clean UI transitions
"""

import flet as ft
import sys
import os
from pathlib import Path
import time
import threading

# Add src to path
current_dir = Path(__file__).parent
src_path = current_dir / "src"
sys.path.insert(0, str(src_path))

# Import update manager
from core.update_manager import UpdateManager

class UpdateTestApp:
    """Test application for update functionality"""
    
    def __init__(self):
        self.update_manager = None
        self.test_results = []
        
    def test_main(self, page: ft.Page):
        """Main test application"""
        page.title = "🧪 Update Grey Screen Fix Test"
        page.window_width = 800
        page.window_height = 600
        page.theme_mode = ft.ThemeMode.LIGHT
        
        # Initialize update manager
        self.update_manager = UpdateManager(page, "2.0.10")
        
        # Test results display
        self.results_column = ft.Column([], spacing=5, scroll=True, expand=True)
        
        # Create UI
        page.add(
            ft.Container(
                content=ft.Column([
                    ft.Container(
                        content=ft.Row([
                            ft.Icon(ft.Icons.PSYCHOLOGY, color=ft.Colors.BLUE, size=30),
                            ft.Text("🧪 Update Grey Screen Fix Test", 
                                   size=24, weight=ft.FontWeight.BOLD)
                        ], spacing=10),
                        bgcolor=ft.Colors.BLUE_50,
                        padding=15,
                        border_radius=10
                    ),
                    
                    ft.Container(height=20),
                    
                    ft.Text("Test Scenarios:", size=18, weight=ft.FontWeight.BOLD),
                    
                    ft.Row([
                        ft.ElevatedButton(
                            "🔄 Silent Startup Check",
                            on_click=self._test_silent_startup_check,
                            bgcolor=ft.Colors.GREEN,
                            color=ft.Colors.WHITE
                        ),
                        ft.ElevatedButton(
                            "🔍 Manual Update Check",
                            on_click=self._test_manual_update_check,
                            bgcolor=ft.Colors.BLUE,
                            color=ft.Colors.WHITE
                        )
                    ], wrap=True),
                    
                    ft.Row([
                        ft.ElevatedButton(
                            "🔑 GitHub Token Dialog",
                            on_click=self._test_github_token_dialog,
                            bgcolor=ft.Colors.PURPLE,
                            color=ft.Colors.WHITE
                        ),
                        ft.ElevatedButton(
                            "❌ Error Scenarios",
                            on_click=self._test_error_scenarios,
                            bgcolor=ft.Colors.RED,
                            color=ft.Colors.WHITE
                        )
                    ], wrap=True),
                    
                    ft.Row([
                        ft.ElevatedButton(
                            "🔄 Double-Check Test",
                            on_click=self._test_double_check,
                            bgcolor=ft.Colors.ORANGE,
                            color=ft.Colors.WHITE
                        ),
                        ft.ElevatedButton(
                            "🧹 Clear Results",
                            on_click=self._clear_results,
                            bgcolor=ft.Colors.GREY,
                            color=ft.Colors.WHITE
                        )
                    ], wrap=True),
                    
                    ft.Container(height=20),
                    
                    ft.Text("Test Results:", size=16, weight=ft.FontWeight.BOLD),
                    
                    ft.Container(
                        content=self.results_column,
                        bgcolor=ft.Colors.GREY_100,
                        padding=10,
                        border_radius=10,
                        height=300
                    )
                    
                ], spacing=10),
                padding=20
            )
        )
        
        # Auto-run silent startup check like real app
        self._log_result("🚀 Starting auto-update check on launch (silent mode)...")
        self.update_manager.check_for_updates_on_startup(silent=True)
        
    def _test_silent_startup_check(self, e):
        """Test silent startup check"""
        self._log_result("🔄 Testing silent startup check...")
        self.update_manager.check_for_updates_on_startup(silent=True)
        self._log_result("✅ Silent startup check initiated successfully")
        
    def _test_manual_update_check(self, e):
        """Test manual update check"""
        self._log_result("🔍 Testing manual update check...")
        self.update_manager.check_for_updates_manual()
        self._log_result("✅ Manual update check initiated successfully")
        
    def _test_github_token_dialog(self, e):
        """Test GitHub token configuration dialog"""
        self._log_result("🔑 Testing GitHub token dialog...")
        try:
            self.update_manager.show_github_token_dialog()
            self._log_result("✅ GitHub token dialog opened successfully")
        except Exception as ex:
            self._log_result(f"❌ GitHub token dialog error: {ex}")
            
    def _test_error_scenarios(self, e):
        """Test various error scenarios"""
        self._log_result("❌ Testing error scenarios...")
        
        # Test update error
        self.update_manager._show_update_error("Test error message for testing purposes")
        self._log_result("✅ Update error dialog displayed")
        
        # Small delay then test no updates
        def delayed_test():
            time.sleep(2)
            self.update_manager.page.run_thread(
                lambda: self.update_manager._show_no_updates_available()
            )
            self.update_manager.page.run_thread(
                lambda: self._log_result("✅ No updates dialog displayed")
            )
            
        threading.Thread(target=delayed_test, daemon=True).start()
        
    def _test_double_check(self, e):
        """Test double-checking (should show 'already checking' dialog)"""
        self._log_result("🔄 Testing double-check scenario...")
        
        # Start first check
        self.update_manager.check_for_updates_manual()
        
        # Immediately try second check
        def immediate_second_check():
            time.sleep(0.5)  # Small delay
            self.update_manager.page.run_thread(
                lambda: self.update_manager.check_for_updates_manual()
            )
            self.update_manager.page.run_thread(
                lambda: self._log_result("✅ Double-check test completed")
            )
            
        threading.Thread(target=immediate_second_check, daemon=True).start()
        
    def _clear_results(self, e):
        """Clear test results"""
        self.results_column.controls.clear()
        self.results_column.update()
        self._log_result("🧹 Results cleared")
        
    def _log_result(self, message: str):
        """Log a test result"""
        timestamp = time.strftime("%H:%M:%S")
        
        # Determine color based on message type
        color = ft.Colors.BLACK
        if "✅" in message:
            color = ft.Colors.GREEN
        elif "❌" in message:
            color = ft.Colors.RED
        elif "🔄" in message or "🚀" in message:
            color = ft.Colors.BLUE
        elif "🔑" in message:
            color = ft.Colors.PURPLE
            
        result_text = ft.Text(
            f"[{timestamp}] {message}",
            size=12,
            color=color
        )
        
        self.results_column.controls.append(result_text)
        
        # Keep only last 50 results
        if len(self.results_column.controls) > 50:
            self.results_column.controls.pop(0)
            
        self.results_column.update()

def main():
    """Run the test application"""
    print("🧪 Starting Update Grey Screen Fix Test...")
    print("📋 This test verifies that update dialogs work without grey screens")
    print("🎯 All dialogs should open/close cleanly using proper Flet APIs")
    print("-" * 60)
    
    app = UpdateTestApp()
    
    ft.app(
        target=app.test_main,
        name="Update Grey Screen Fix Test",
        assets_dir="assets"
    )

if __name__ == "__main__":
    main() 