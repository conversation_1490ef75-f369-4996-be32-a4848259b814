name: Build Alternative (Fallback)

on:
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to build (e.g., v1.0.1)'
        required: true
        default: 'v1.0.1'

jobs:
  build-windows-alternative:
    runs-on: windows-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements-build.txt
        
    - name: Create simple build script
      run: |
        @"
        import os
        import sys
        import subprocess
        
        # Set environment variables
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        os.environ['PYTHONUTF8'] = '1'
        
        # Run build with minimal output
        try:
            result = subprocess.run([
                sys.executable, '-m', 'flet_cli', 'build', 'windows'
            ], capture_output=True, text=True, encoding='utf-8', errors='replace')
            
            print("STDOUT:")
            print(result.stdout)
            
            if result.stderr:
                print("STDERR:")
                print(result.stderr)
            
            if result.returncode == 0:
                print("Build successful!")
                sys.exit(0)
            else:
                print(f"Build failed with code: {result.returncode}")
                sys.exit(1)
                
        except Exception as e:
            print(f"Error: {e}")
            sys.exit(1)
        "@ | Out-File -FilePath "build_simple.py" -Encoding UTF8
        
    - name: Run simple build
      run: |
        python build_simple.py
        
    - name: Check build output
      run: |
        if (Test-Path "dist") {
            Write-Host "✅ Build artifacts found:"
            Get-ChildItem -Path "dist" -Recurse | ForEach-Object {
                Write-Host "  $($_.FullName) ($($_.Length) bytes)"
            }
        } else {
            Write-Host "❌ No build artifacts found"
            exit 1
        }
        
    - name: Package build
      run: |
        if (Test-Path "dist") {
            Compress-Archive -Path "dist\*" -DestinationPath "AgevolamiPM-${{ github.event.inputs.version }}-Windows-Alternative.zip"
            Write-Host "✅ Package created successfully"
        } else {
            Write-Host "❌ Cannot package - no dist directory"
            exit 1
        }
        
    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: windows-build-alternative
        path: |
          AgevolamiPM-*.zip
          dist/
