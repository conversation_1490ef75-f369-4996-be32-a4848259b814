#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Google Services Settings Section
Modern, self-contained Google integrations configuration component
"""

import flet as ft
from typing import Dict, Any, List
from ..components import SettingsCard, SettingsCardSection, FormField, SwitchField, DropdownField, ButtonGroup, InfoBox, StatusIndicator
from core import get_logger

logger = get_logger(__name__)

class GoogleServicesSection:
    """
    Self-contained Google services settings section with modern UX
    Handles Drive, Calendar, and Tasks integrations
    """
    
    def __init__(self, controller):
        self.controller = controller
        self.google_settings = controller.get_setting('google_services')
        
        # Service status cache
        self.service_status = {
            'drive': 'disconnected',
            'calendar': 'disconnected', 
            'tasks': 'disconnected'
        }
        
        # Register for setting changes
        controller.register_change_callback('google_services', self._on_setting_changed)
        
        # Update initial status
        self._update_service_status()
    
    def _on_setting_changed(self, key: str, new_value: Any, old_value: Any):
        """Handle setting changes"""
        logger.debug(f"Google services setting changed: {key} = {new_value}")
        self._update_service_status()
    
    def _update_service_status(self):
        """Update service connection status"""
        settings = self.controller.get_setting('google_services')
        
        # Update status based on authentication
        self.service_status['drive'] = 'connected' if settings.get('drive_authenticated') else 'disconnected'
        self.service_status['calendar'] = 'connected' if settings.get('calendar_authenticated') else 'disconnected'
        self.service_status['tasks'] = 'connected' if settings.get('tasks_authenticated') else 'disconnected'
    
    def _create_google_drive_section(self) -> SettingsCardSection:
        """Create Google Drive configuration section"""
        
        def on_drive_enabled_change(e):
            self.controller.set_setting('google_services', 'drive_enabled', e.control.value)
        
        def on_auto_backup_change(e):
            self.controller.set_setting('google_services', 'drive_auto_backup', e.control.value)
        
        def on_backup_frequency_change(e):
            self.controller.set_setting('google_services', 'drive_backup_frequency', e.control.value)
        
        def on_retention_change(value):
            self.controller.set_setting('google_services', 'drive_retention_days', value)
        
        def authenticate_drive(e):
            """Authenticate with Google Drive using real service"""
            self.service_status['drive'] = 'loading'
            self._show_info_notification("🔗 Avvio autenticazione Google Drive...")
            try:
                success = self.controller.authenticate_google_drive()
                if success:
                    self._show_success_notification("✅ Google Drive autenticato con successo!")
                    self.service_status['drive'] = 'connected'
                    self.refresh_data()  # Refresh the UI to show new status
                else:
                    self._show_error_notification("❌ Autenticazione Google Drive fallita")
                    self.service_status['drive'] = 'disconnected'
            except Exception as ex:
                self._show_error_notification(f"❌ Errore autenticazione: {ex}")
                self.service_status['drive'] = 'error'
        
        def disconnect_drive(e):
            """Disconnect from Google Drive using real service"""
            try:
                success = self.controller.disconnect_google_service('drive')
                if success:
                    self.service_status['drive'] = 'disconnected'
                    self._show_success_notification("✅ Google Drive disconnesso")
                    self.refresh_data()
                else:
                    self._show_error_notification("❌ Errore durante la disconnessione")
            except Exception as ex:
                self._show_error_notification(f"❌ Errore disconnessione: {ex}")
        
        def manual_backup(e):
            """Perform manual backup"""
            self._show_success_notification("Backup manuale avviato...")
        
        def view_backups(e):
            """View existing backups"""
            self._show_success_notification("Apertura lista backup...")
        
        settings = self.controller.get_setting('google_services')
        is_authenticated = settings.get('drive_authenticated', False)
        
        # Service status indicator
        status_indicator = StatusIndicator.create(
            label="Google Drive Backup",
            status=self.service_status['drive'],
            description="Backup automatico dei dati su Google Drive"
        )
        
        # Connection controls
        if is_authenticated:
            connection_buttons = ButtonGroup.create_action_group([
                {
                    "text": "Disconnetti",
                    "icon": ft.Icons.LOGOUT,
                    "on_click": disconnect_drive,
                    "style": "secondary"
                },
                {
                    "text": "Backup Manuale",
                    "icon": ft.Icons.BACKUP,
                    "on_click": manual_backup,
                    "style": "primary"
                },
                {
                    "text": "Visualizza Backup",
                    "icon": ft.Icons.LIST,
                    "on_click": view_backups,
                    "style": "secondary"
                }
            ])
        else:
            connection_buttons = ButtonGroup.create_primary_secondary(
                primary_action={
                    "text": "Connetti Google Drive",
                    "icon": ft.Icons.LOGIN,
                    "on_click": authenticate_drive
                },
                secondary_action={
                    "text": "Info Autenticazione",
                    "icon": ft.Icons.HELP,
                    "on_click": lambda e: self._show_drive_help()
                }
            )
        
        # Configuration controls (only if authenticated)
        config_controls = []
        if is_authenticated:
            drive_enabled_switch = SwitchField.create(
                label="Abilita Backup Google Drive",
                description="Attiva il backup automatico su Google Drive",
                value=settings.get('drive_enabled', False),
                on_change=on_drive_enabled_change,
                color=ft.Colors.BLUE_600
            )
            
            auto_backup_switch = SwitchField.create(
                label="Backup Automatico",
                description="Esegui backup automatici secondo la frequenza impostata",
                value=settings.get('drive_auto_backup', False),
                on_change=on_auto_backup_change,
                disabled=not settings.get('drive_enabled', False)
            )
            
            frequency_dropdown = DropdownField.create(
                label="Frequenza Backup",
                options=[
                    {"text": "Giornaliero", "value": "daily"},
                    {"text": "Settimanale", "value": "weekly"},
                    {"text": "Mensile", "value": "monthly"}
                ],
                value=settings.get('drive_backup_frequency', 'daily'),
                width=200,
                on_change=on_backup_frequency_change,
                disabled=not settings.get('drive_auto_backup', False)
            )
            
            retention_field = FormField.create_number_field(
                label="Giorni Retention",
                value=settings.get('drive_retention_days', 30),
                min_value=1,
                max_value=365,
                width=150,
                on_change=on_retention_change
            )
            
            config_row = ft.Row([
                frequency_dropdown,
                retention_field
            ], spacing=16)
            
            config_controls = [
                drive_enabled_switch,
                auto_backup_switch,
                config_row
            ]
        
        return SettingsCardSection(
            title="Google Drive Backup",
            description="Backup automatico sicuro dei tuoi dati su Google Drive",
            controls=[
                status_indicator,
                connection_buttons,
                *config_controls
            ]
        )
    
    def _create_google_calendar_section(self) -> SettingsCardSection:
        """Create Google Calendar configuration section"""
        
        def on_calendar_enabled_change(e):
            self.controller.set_setting('google_services', 'calendar_enabled', e.control.value)
        
        def on_auto_sync_change(e):
            self.controller.set_setting('google_services', 'calendar_auto_sync', e.control.value)
        
        def on_sync_completed_change(e):
            self.controller.set_setting('google_services', 'calendar_sync_completed', e.control.value)
        
        def authenticate_calendar(e):
            """Authenticate with Google Calendar using real service"""
            self.service_status['calendar'] = 'loading'
            self._show_info_notification("🔗 Avvio autenticazione Google Calendar...")
            try:
                success = self.controller.authenticate_google_calendar()
                if success:
                    self._show_success_notification("✅ Google Calendar autenticato con successo!")
                    self.service_status['calendar'] = 'connected'
                    self.refresh_data()
                else:
                    self._show_error_notification("❌ Autenticazione Google Calendar fallita")
                    self.service_status['calendar'] = 'disconnected'
            except Exception as ex:
                self._show_error_notification(f"❌ Errore autenticazione: {ex}")
                self.service_status['calendar'] = 'error'
        
        def disconnect_calendar(e):
            """Disconnect from Google Calendar"""
            self.controller.set_setting('google_services', 'calendar_authenticated', False)
            self.controller.set_setting('google_services', 'calendar_enabled', False)
            self.service_status['calendar'] = 'disconnected'
            self._show_success_notification("Google Calendar disconnesso")
        
        def manual_sync(e):
            """Perform manual sync"""
            self._show_success_notification("Sincronizzazione manuale avviata...")
        
        settings = self.controller.get_setting('google_services')
        is_authenticated = settings.get('calendar_authenticated', False)
        
        # Service status indicator
        status_indicator = StatusIndicator.create(
            label="Google Calendar",
            status=self.service_status['calendar'],
            description="Sincronizzazione scadenze con Google Calendar"
        )
        
        # Connection controls
        if is_authenticated:
            connection_buttons = ButtonGroup.create_action_group([
                {
                    "text": "Disconnetti",
                    "icon": ft.Icons.LOGOUT,
                    "on_click": disconnect_calendar,
                    "style": "secondary"
                },
                {
                    "text": "Sync Manuale",
                    "icon": ft.Icons.SYNC,
                    "on_click": manual_sync,
                    "style": "primary"
                }
            ])
        else:
            connection_buttons = ButtonGroup.create_primary_secondary(
                primary_action={
                    "text": "Connetti Calendar",
                    "icon": ft.Icons.LOGIN,
                    "on_click": authenticate_calendar
                }
            )
        
        # Configuration controls (only if authenticated)
        config_controls = []
        if is_authenticated:
            calendar_enabled_switch = SwitchField.create(
                label="Abilita Google Calendar",
                description="Sincronizza le scadenze con Google Calendar",
                value=settings.get('calendar_enabled', False),
                on_change=on_calendar_enabled_change,
                color=ft.Colors.PURPLE_600
            )
            
            auto_sync_switch = SwitchField.create(
                label="Sincronizzazione Automatica",
                description="Sincronizza automaticamente le nuove scadenze",
                value=settings.get('calendar_auto_sync', True),
                on_change=on_auto_sync_change,
                disabled=not settings.get('calendar_enabled', False)
            )
            
            sync_completed_switch = SwitchField.create(
                label="Sincronizza Completate",
                description="Includi anche le scadenze già completate",
                value=settings.get('calendar_sync_completed', False),
                on_change=on_sync_completed_change,
                disabled=not settings.get('calendar_enabled', False)
            )
            
            config_controls = [
                calendar_enabled_switch,
                auto_sync_switch,
                sync_completed_switch
            ]
        
        return SettingsCardSection(
            title="Google Calendar",
            description="Sincronizza le scadenze con il tuo calendario Google",
            controls=[
                status_indicator,
                connection_buttons,
                *config_controls
            ]
        )
    
    def _create_google_tasks_section(self) -> SettingsCardSection:
        """Create Google Tasks configuration section"""
        
        def on_tasks_enabled_change(e):
            self.controller.set_setting('google_services', 'tasks_enabled', e.control.value)
        
        def on_auto_sync_change(e):
            self.controller.set_setting('google_services', 'tasks_auto_sync', e.control.value)
        
        def on_sync_deadline_change(e):
            self.controller.set_setting('google_services', 'tasks_sync_on_deadline_change', e.control.value)
        
        def authenticate_tasks(e):
            """Authenticate with Google Tasks using real service"""
            self.service_status['tasks'] = 'loading'
            self._show_info_notification("🔗 Avvio autenticazione Google Tasks...")
            try:
                success = self.controller.authenticate_google_tasks()
                if success:
                    self._show_success_notification("✅ Google Tasks autenticato con successo!")
                    self.service_status['tasks'] = 'connected'
                    self.refresh_data()
                else:
                    self._show_error_notification("❌ Autenticazione Google Tasks fallita")
                    self.service_status['tasks'] = 'disconnected'
            except Exception as ex:
                self._show_error_notification(f"❌ Errore autenticazione: {ex}")
                self.service_status['tasks'] = 'error'
        
        def disconnect_tasks(e):
            """Disconnect from Google Tasks"""
            self.controller.set_setting('google_services', 'tasks_authenticated', False)
            self.controller.set_setting('google_services', 'tasks_enabled', False)
            self.service_status['tasks'] = 'disconnected'
            self._show_success_notification("Google Tasks disconnesso")
        
        def manual_sync(e):
            """Perform manual sync"""
            self._show_success_notification("Sincronizzazione tasks avviata...")
        
        settings = self.controller.get_setting('google_services')
        is_authenticated = settings.get('tasks_authenticated', False)
        
        # Service status indicator
        status_indicator = StatusIndicator.create(
            label="Google Tasks",
            status=self.service_status['tasks'],
            description="Gestione attività con Google Tasks"
        )
        
        # Connection controls
        if is_authenticated:
            connection_buttons = ButtonGroup.create_action_group([
                {
                    "text": "Disconnetti",
                    "icon": ft.Icons.LOGOUT,
                    "on_click": disconnect_tasks,
                    "style": "secondary"
                },
                {
                    "text": "Sync Tasks",
                    "icon": ft.Icons.SYNC,
                    "on_click": manual_sync,
                    "style": "primary"
                }
            ])
        else:
            connection_buttons = ButtonGroup.create_primary_secondary(
                primary_action={
                    "text": "Connetti Tasks",
                    "icon": ft.Icons.LOGIN,
                    "on_click": authenticate_tasks
                }
            )
        
        # Configuration controls (only if authenticated)
        config_controls = []
        if is_authenticated:
            tasks_enabled_switch = SwitchField.create(
                label="Abilita Google Tasks",
                description="Sincronizza le attività con Google Tasks",
                value=settings.get('tasks_enabled', False),
                on_change=on_tasks_enabled_change,
                color=ft.Colors.TEAL_600
            )
            
            auto_sync_switch = SwitchField.create(
                label="Sincronizzazione Automatica",
                description="Sincronizza automaticamente le nuove attività",
                value=settings.get('tasks_auto_sync', True),
                on_change=on_auto_sync_change,
                disabled=not settings.get('tasks_enabled', False)
            )
            
            sync_deadline_switch = SwitchField.create(
                label="Sync su Modifica Scadenza",
                description="Aggiorna automaticamente quando cambiano le scadenze",
                value=settings.get('tasks_sync_on_deadline_change', True),
                on_change=on_sync_deadline_change,
                disabled=not settings.get('tasks_enabled', False)
            )
            
            config_controls = [
                tasks_enabled_switch,
                auto_sync_switch,
                sync_deadline_switch
            ]
        
        return SettingsCardSection(
            title="Google Tasks",
            description="Sincronizza le attività e task con Google Tasks",
            controls=[
                status_indicator,
                connection_buttons,
                *config_controls
            ]
        )
    
    def _show_drive_help(self):
        """Show Google Drive help information"""
        self._show_success_notification("Guida autenticazione Google Drive in sviluppo")
    
    def _show_success_notification(self, message: str):
        """Show success notification"""
        logger.info(f"SUCCESS: {message}")

    def _show_error_notification(self, message: str):
        """Show error notification"""
        logger.error(f"ERROR: {message}")

    def _show_info_notification(self, message: str):
        """Show info notification"""
        logger.info(f"INFO: {message}")

    def refresh_data(self):
        """Refresh section data"""
        # This would trigger a UI refresh in a real implementation
        logger.info("Google Services section data refreshed")
    
    def build(self) -> SettingsCard:
        """Build the Google services settings card"""
        
        # Create sections
        drive_section = self._create_google_drive_section()
        calendar_section = self._create_google_calendar_section()
        tasks_section = self._create_google_tasks_section()
        
        # Create overview info
        settings = self.controller.get_setting('google_services')
        connected_services = []
        if settings.get('drive_authenticated'):
            connected_services.append("Google Drive")
        if settings.get('calendar_authenticated'):
            connected_services.append("Google Calendar")
        if settings.get('tasks_authenticated'):
            connected_services.append("Google Tasks")
        
        if connected_services:
            services_text = ", ".join(connected_services)
            overview_message = f"Servizi connessi: {services_text}"
            overview_type = "success"
        else:
            overview_message = "Nessun servizio Google connesso"
            overview_type = "warning"
        
        overview_info = InfoBox.create(
            title="🔗 Stato Integrazioni Google",
            message=overview_message,
            type=overview_type,
            items=[
                "Google Drive: Backup automatico sicuro dei dati",
                "Google Calendar: Sincronizzazione scadenze e appuntamenti",
                "Google Tasks: Gestione attività e task integrate",
                "Ogni servizio può essere configurato indipendentemente"
            ]
        )
        
        # Create card content
        card_content = [
            overview_info,
            drive_section.build(),
            calendar_section.build(),
            tasks_section.build()
        ]
        
        # Create save/reset actions
        def save_settings(e):
            success = self.controller.save_settings()
            if success:
                self._show_success_notification("Impostazioni Google Services salvate!")
            else:
                self._show_error_notification("Errore durante il salvataggio")
        
        def disconnect_all(e):
            """Disconnect all Google services"""
            self.controller.set_setting('google_services', 'drive_authenticated', False)
            self.controller.set_setting('google_services', 'calendar_authenticated', False)
            self.controller.set_setting('google_services', 'tasks_authenticated', False)
            self.controller.set_setting('google_services', 'drive_enabled', False)
            self.controller.set_setting('google_services', 'calendar_enabled', False)
            self.controller.set_setting('google_services', 'tasks_enabled', False)
            self._update_service_status()
            self._show_success_notification("Tutti i servizi Google disconnessi")
        
        card_actions = [
            ft.ElevatedButton(
                text="Salva Integrazioni",
                icon=ft.Icons.SAVE,
                on_click=save_settings,
                bgcolor=ft.Colors.BLUE_600,
                color=ft.Colors.WHITE
            ),
            ft.OutlinedButton(
                text="Disconnetti Tutto",
                icon=ft.Icons.POWER_SETTINGS_NEW,
                on_click=disconnect_all,
                style=ft.ButtonStyle(
                    color=ft.Colors.RED_600
                )
            )
        ]
        
        # Determine card status
        connected_count = len(connected_services)
        if connected_count == 3:
            card_icon = ft.Icons.CLOUD_DONE
            card_icon_color = ft.Colors.GREEN_600
        elif connected_count > 0:
            card_icon = ft.Icons.CLOUD_SYNC
            card_icon_color = ft.Colors.BLUE_600
        else:
            card_icon = ft.Icons.CLOUD_OFF
            card_icon_color = ft.Colors.GREY_600
        
        return SettingsCard(
            title="Integrazioni Google Services",
            description="Connetti Drive, Calendar e Tasks per sincronizzare i tuoi dati con Google",
            icon=card_icon,
            icon_color=card_icon_color,
            expanded=True,
            content=card_content,
            actions=card_actions,
            accent_color=ft.Colors.BLUE_600
        ) 