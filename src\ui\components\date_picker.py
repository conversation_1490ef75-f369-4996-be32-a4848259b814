# -*- coding: utf-8 -*-
"""
Componente Date Picker con Calendario per Agevolami PM
"""

import flet as ft
from datetime import datetime, date, timedelta
from typing import Optional, Callable
import calendar

class DatePicker(ft.Container):
    """Componente per la selezione delle date con calendario"""
    
    def __init__(
        self,
        label: str = "Seleziona Data",
        value: Optional[date] = None,
        on_change: Optional[Callable[[date], None]] = None,
        min_date: Optional[date] = None,
        max_date: Optional[date] = None,
        **kwargs
    ):
        self.label = label
        self._value = value
        self.on_change = on_change
        self.min_date = min_date or date(2020, 1, 1)
        self.max_date = max_date or date(2030, 12, 31)
        
        # Stato del calendario
        self.current_month = value.month if value else date.today().month
        self.current_year = value.year if value else date.today().year
        self.calendar_visible = False
        
        # Componenti
        self.date_field = None
        self.calendar_container = None
        self.calendar_dialog = None
        
        # Inizializza il contenuto
        content = self._build_content()
        super().__init__(content=content, **kwargs)
        
    @property
    def value(self) -> Optional[date]:
        return self._value
    
    @value.setter
    def value(self, new_value: Optional[date]):
        self._value = new_value
        if self.date_field:
            self.date_field.value = new_value.strftime("%d/%m/%Y") if new_value else ""
            self.date_field.update()
    
    def _build_content(self):
        # Campo di input per la data
        self.date_field = ft.TextField(
            label=self.label,
            value=self._value.strftime("%d/%m/%Y") if self._value else "",
            hint_text="DD/MM/YYYY",
            suffix_icon=ft.Icons.CALENDAR_MONTH,
            on_change=self._on_text_change,
            on_focus=self._on_focus,
            expand=True
        )
        
        # Contenitore principale
        return ft.Column([
            ft.Row([
                self.date_field,
                ft.IconButton(
                    icon=ft.Icons.CALENDAR_TODAY,
                    tooltip="Apri Calendario",
                    on_click=self._show_calendar
                )
            ])
        ])
    
    def _on_text_change(self, e):
        """Gestisce il cambio di testo nel campo"""
        try:
            if e.control.value:
                # Prova a parsare la data
                parsed_date = datetime.strptime(e.control.value, "%d/%m/%Y").date()
                if self.min_date <= parsed_date <= self.max_date:
                    self._value = parsed_date
                    if self.on_change:
                        self.on_change(parsed_date)
                else:
                    # Data fuori range
                    e.control.error_text = f"Data deve essere tra {self.min_date.strftime('%d/%m/%Y')} e {self.max_date.strftime('%d/%m/%Y')}"
            else:
                self._value = None
                if self.on_change:
                    self.on_change(None)
                e.control.error_text = None
        except ValueError:
            # Formato non valido
            if len(e.control.value) >= 10:  # Solo se la stringa è completa
                e.control.error_text = "Formato non valido (DD/MM/YYYY)"
            else:
                e.control.error_text = None
        
        e.control.update()
    
    def _on_focus(self, e):
        """Gestisce il focus sul campo"""
        if e.control.focus:
            self._show_calendar()
    
    def _show_calendar(self, e=None):
        """Mostra il calendario"""
        if self._value:
            self.current_month = self._value.month
            self.current_year = self._value.year
        
        self.calendar_dialog = ft.AlertDialog(
            title=ft.Text("Seleziona Data"),
            content=self._build_calendar(),
            actions=[
                ft.TextButton("Annulla", on_click=self._close_calendar),
                ft.TextButton("Oggi", on_click=self._select_today),
            ],
            actions_alignment=ft.MainAxisAlignment.SPACE_BETWEEN
        )
        
        self.page.dialog = self.calendar_dialog
        self.calendar_dialog.open = True
        self.page.update()
    
    def _build_calendar(self) -> ft.Container:
        """Costruisce il calendario"""
        # Header con navigazione mese/anno
        month_names = [
            "Gennaio", "Febbraio", "Marzo", "Aprile", "Maggio", "Giugno",
            "Luglio", "Agosto", "Settembre", "Ottobre", "Novembre", "Dicembre"
        ]
        
        header = ft.Row([
            ft.IconButton(
                icon=ft.Icons.CHEVRON_LEFT,
                on_click=self._prev_month
            ),
            ft.Text(
                f"{month_names[self.current_month - 1]} {self.current_year}",
                size=18,
                weight=ft.FontWeight.BOLD,
                expand=True,
                text_align=ft.TextAlign.CENTER
            ),
            ft.IconButton(
                icon=ft.Icons.CHEVRON_RIGHT,
                on_click=self._next_month
            )
        ])
        
        # Giorni della settimana
        weekdays = ft.Row([
            ft.Container(
                content=ft.Text(day, size=12, weight=ft.FontWeight.BOLD, text_align=ft.TextAlign.CENTER),
                width=40,
                height=30,
                alignment=ft.alignment.center
            )
            for day in ["Lun", "Mar", "Mer", "Gio", "Ven", "Sab", "Dom"]
        ])
        
        # Griglia dei giorni
        cal = calendar.monthcalendar(self.current_year, self.current_month)
        day_rows = []
        
        for week in cal:
            day_cells = []
            for day in week:
                if day == 0:
                    # Giorno vuoto
                    day_cells.append(ft.Container(width=40, height=40))
                else:
                    day_date = date(self.current_year, self.current_month, day)
                    is_today = day_date == date.today()
                    is_selected = day_date == self._value
                    is_disabled = day_date < self.min_date or day_date > self.max_date
                    
                    # Stile del giorno
                    if is_disabled:
                        bg_color = ft.Colors.GREY_300
                        text_color = ft.Colors.GREY_500
                    elif is_selected:
                        bg_color = ft.Colors.BLUE
                        text_color = ft.Colors.WHITE
                    elif is_today:
                        bg_color = ft.Colors.BLUE_100
                        text_color = ft.Colors.BLUE_800
                    else:
                        bg_color = None
                        text_color = ft.Colors.BLACK
                    
                    day_button = ft.Container(
                        content=ft.Text(
                            str(day),
                            size=14,
                            color=text_color,
                            text_align=ft.TextAlign.CENTER
                        ),
                        width=40,
                        height=40,
                        bgcolor=bg_color,
                        border_radius=20,
                        alignment=ft.alignment.center,
                        on_click=None if is_disabled else lambda _, d=day_date: self._select_date(d),
                        ink=not is_disabled
                    )
                    
                    day_cells.append(day_button)
            
            day_rows.append(ft.Row(day_cells))
        
        return ft.Container(
            content=ft.Column([
                header,
                ft.Divider(),
                weekdays,
                ft.Column(day_rows)
            ]),
            width=300,
            padding=10
        )
    
    def _prev_month(self, e):
        """Mese precedente"""
        if self.current_month == 1:
            self.current_month = 12
            self.current_year -= 1
        else:
            self.current_month -= 1
        
        self.calendar_dialog.content = self._build_calendar()
        self.calendar_dialog.update()
    
    def _next_month(self, e):
        """Mese successivo"""
        if self.current_month == 12:
            self.current_month = 1
            self.current_year += 1
        else:
            self.current_month += 1
        
        self.calendar_dialog.content = self._build_calendar()
        self.calendar_dialog.update()
    
    def _select_date(self, selected_date: date):
        """Seleziona una data"""
        self._value = selected_date
        self.date_field.value = selected_date.strftime("%d/%m/%Y")
        self.date_field.error_text = None
        
        if self.on_change:
            self.on_change(selected_date)
        
        self._close_calendar()
    
    def _select_today(self, e=None):
        """Seleziona oggi"""
        today = date.today()
        if self.min_date <= today <= self.max_date:
            self._select_date(today)
    
    def _close_calendar(self, e=None):
        """Chiude il calendario"""
        if self.calendar_dialog:
            self.calendar_dialog.open = False
            self.page.update()