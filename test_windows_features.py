#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script per le funzionalità Windows di Agevolami PM
"""

import sys
from pathlib import Path

# Aggiungi src al path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from core.utils.windows_utils import WindowsSystemIntegration

def test_windows_features():
    """Testa tutte le funzionalità Windows"""
    print("🔧 Test Funzionalità Windows - Agevolami PM")
    print("=" * 50)
    
    # Inizializza integrazione Windows
    windows_integration = WindowsSystemIntegration("Agevolami PM Test")
    
    # Test 1: Verifica se siamo su Windows
    print("\n1. Test Piattaforma:")
    if windows_integration.startup_manager.is_windows():
        print("   ✅ Sistema Windows rilevato")
    else:
        print("   ❌ Non siamo su Windows")
        return
    
    # Test 2: Stato avvio automatico
    print("\n2. Test Avvio Automatico:")
    is_enabled = windows_integration.is_startup_enabled()
    print(f"   Stato attuale: {'Abilitato' if is_enabled else 'Disabilitato'}")
    
    # Test 3: Notifiche
    print("\n3. Test Notifiche:")
    
    print("   Invio notifica normale...")
    success = windows_integration.send_notification(
        "Test Agevolami PM",
        "Questa è una notifica di test normale",
        "normal"
    )
    print(f"   Risultato: {'✅ Successo' if success else '❌ Fallito'}")
    
    print("   Invio notifica urgente...")
    success = windows_integration.send_notification(
        "Test Urgente",
        "Questa è una notifica di test urgente",
        "urgent"
    )
    print(f"   Risultato: {'✅ Successo' if success else '❌ Fallito'}")
    
    print("   Invio notifica scadenza...")
    success = windows_integration.send_deadline_alert(
        "Scadenza Test",
        3,  # 3 giorni rimanenti
        "Progetto Test"
    )
    print(f"   Risultato: {'✅ Successo' if success else '❌ Fallito'}")
    
    # Test 4: Test sistema completo
    print("\n4. Test Sistema:")
    success, message = windows_integration.test_notification_system()
    print(f"   Sistema notifiche: {'✅' if success else '❌'} {message}")
    
    # Test 5: Interazione con utente per avvio automatico
    print("\n5. Test Avvio Automatico (Interattivo):")
    
    current_status = windows_integration.is_startup_enabled()
    print(f"   Stato attuale: {'Abilitato' if current_status else 'Disabilitato'}")
    
    response = input("   Vuoi testare l'avvio automatico? (s/n): ").lower().strip()
    
    if response == 's':
        if current_status:
            print("   Disabilitazione avvio automatico...")
            success = windows_integration.configure_startup(False)
            print(f"   Risultato: {'✅ Disabilitato' if success else '❌ Errore'}")
        else:
            print("   Abilitazione avvio automatico...")
            success = windows_integration.configure_startup(True)
            print(f"   Risultato: {'✅ Abilitato' if success else '❌ Errore'}")
        
        # Verifica nuovo stato
        new_status = windows_integration.is_startup_enabled()
        print(f"   Nuovo stato: {'Abilitato' if new_status else 'Disabilitato'}")
        
        # Ripristina stato originale se richiesto
        restore = input("   Vuoi ripristinare lo stato originale? (s/n): ").lower().strip()
        if restore == 's':
            print("   Ripristino stato originale...")
            success = windows_integration.configure_startup(current_status)
            print(f"   Risultato: {'✅ Ripristinato' if success else '❌ Errore'}")
    
    print("\n" + "=" * 50)
    print("🎉 Test completati!")
    print("\nFunzionalità disponibili:")
    print("• Avvio automatico con Windows")
    print("• Notifiche desktop toast (come WhatsApp)")
    print("• Priorità notifiche (normale, alta, urgente)")
    print("• Notifiche specifiche per scadenze")
    print("• Configurazione tramite settings dell'app")

def demo_notifications():
    """Demo delle diverse tipologie di notifiche"""
    print("\n🔔 Demo Notifiche")
    print("-" * 30)
    
    windows_integration = WindowsSystemIntegration("Agevolami PM")
    
    notifications = [
        ("Notifica Normale", "Questa è una notifica normale", "normal"),
        ("Notifica Importante", "Questa è una notifica importante", "high"),
        ("NOTIFICA URGENTE", "Questa è una notifica urgente!", "urgent"),
    ]
    
    for title, message, priority in notifications:
        print(f"   Invio: {title} ({priority})")
        windows_integration.send_notification(title, message, priority)
        
        import time
        time.sleep(2)  # Pausa tra notifiche
    
    # Demo scadenza
    print("   Invio: Notifica Scadenza")
    windows_integration.send_deadline_alert(
        "Presentazione Modello Unico",
        1,  # 1 giorno rimanente
        "Cliente ABC S.r.l."
    )

if __name__ == "__main__":
    try:
        test_windows_features()
        
        # Demo aggiuntivo
        demo_choice = input("\nVuoi vedere una demo delle notifiche? (s/n): ").lower().strip()
        if demo_choice == 's':
            demo_notifications()
            
    except KeyboardInterrupt:
        print("\n\n👋 Test interrotto dall'utente")
    except Exception as e:
        print(f"\n❌ Errore durante i test: {e}")
        import traceback
        traceback.print_exc() 