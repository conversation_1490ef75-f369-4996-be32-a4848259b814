# -*- coding: utf-8 -*-
"""
LLM Configuration Section for Settings
"""

import flet as ft
import json
from pathlib import Path
from typing import Dict, Any, List
from core.utils.logger import logger


class LLMSection:
    """LLM configuration section"""
    
    def __init__(self, controller):
        self.controller = controller
        self.settings = self._load_settings()
        
        # References for dynamic controls
        self.api_key_field = ft.Ref[ft.TextField]()
        self.api_url_field = ft.Ref[ft.TextField]()
        self.model_dropdown = ft.Ref[ft.Dropdown]()
        self.enabled_switch = ft.Ref[ft.Switch]()
        self.connection_status = ft.Ref[ft.Text]()
    
    def build(self) -> ft.Container:
        """Build the LLM configuration section"""
        return ft.Container(
            content=ft.Column([
                # Header
                ft.Row([
                    ft.Icon(ft.Icons.SMART_TOY, size=24, color=ft.Colors.BLUE_600),
                    ft.Text(
                        "Configurazione LLM",
                        size=20,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.GREY_800
                    )
                ], spacing=12),
                
                ft.Text(
                    "Configura l'integrazione con servizi LLM per l'analisi automatica degli incentivi",
                    size=14,
                    color=ft.Colors.GREY_600
                ),
                
                ft.Divider(),
                
                # Main configuration
                self._create_main_config(),
                
                ft.Container(height=12),
                
                # Advanced settings
                self._create_advanced_config(),
                
                ft.Container(height=12),
                
                # Connection test
                self._create_connection_test(),
                
                ft.Container(height=12),
                
                # Custom models management
                self._create_models_management(),
                
            ], spacing=16),
            padding=ft.padding.all(20),
            bgcolor=ft.Colors.WHITE,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )
    
    def _create_main_config(self) -> ft.Container:
        """Create main LLM configuration"""
        return ft.Container(
            content=ft.Column([
                ft.Text(
                    "🔧 Configurazione Principale",
                    size=16,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.GREY_800
                ),
                
                # Enable/Disable switch
                ft.Row([
                    ft.Switch(
                        label="Abilita analisi LLM",
                        value=self.settings.get('llm_enabled', True),
                        ref=self.enabled_switch,
                        on_change=self._on_enabled_changed
                    ),
                    ft.Container(expand=True),
                    ft.Text(
                        "Abilita l'analisi automatica degli incentivi tramite LLM",
                        size=12,
                        color=ft.Colors.GREY_600,
                        italic=True
                    )
                ]),
                
                ft.Container(height=8),
                
                # API Configuration
                ft.Row([
                    # API URL
                    ft.Column([
                        ft.Text("API URL:", size=12, color=ft.Colors.GREY_600),
                        ft.TextField(
                            hint_text="https://openrouter.ai/api/v1",
                            width=250,
                            value=self.settings.get('llm_api_url', 'https://openrouter.ai/api/v1'),
                            ref=self.api_url_field,
                            on_change=self._on_config_changed
                        )
                    ], spacing=4),
                    
                    ft.Container(width=16),
                    
                    # API Key
                    ft.Column([
                        ft.Text("API Key:", size=12, color=ft.Colors.GREY_600),
                        ft.TextField(
                            hint_text="sk-or-v1-...",
                            width=300,
                            password=True,
                            value=self.settings.get('openrouter_api_key', ''),
                            ref=self.api_key_field,
                            on_change=self._on_config_changed
                        )
                    ], spacing=4)
                ]),
                
                ft.Container(height=8),
                
                # Model selection
                ft.Row([
                    ft.Column([
                        ft.Text("Modello LLM:", size=12, color=ft.Colors.GREY_600),
                        ft.Dropdown(
                            width=300,
                            options=self._get_model_options(),
                            value=self.settings.get('llm_model', 'deepseek/deepseek-r1-0528-qwen3-8b:free'),
                            ref=self.model_dropdown,
                            on_change=self._on_config_changed
                        )
                    ], spacing=4),
                    
                    ft.Container(width=16),
                    
                    ft.Column([
                        ft.Text("Stato:", size=12, color=ft.Colors.GREY_600),
                        ft.Text(
                            "Non configurato",
                            size=12,
                            color=ft.Colors.ORANGE_600,
                            ref=self.connection_status
                        )
                    ], spacing=4)
                ])
            ], spacing=12),
            padding=ft.padding.all(16),
            bgcolor=ft.Colors.BLUE_50,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.BLUE_200)
        )
    
    def _create_advanced_config(self) -> ft.Container:
        """Create advanced configuration options"""
        return ft.Container(
            content=ft.Column([
                ft.Text(
                    "⚙️ Impostazioni Avanzate",
                    size=16,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.GREY_800
                ),
                
                ft.Row([
                    ft.Column([
                        ft.Text("Temperatura:", size=12, color=ft.Colors.GREY_600),
                        ft.Slider(
                            min=0.0,
                            max=1.0,
                            value=self.settings.get('llm_temperature', 0.3),
                            divisions=10,
                            label="{value}",
                            width=200
                        )
                    ], spacing=4),
                    
                    ft.Container(width=20),
                    
                    ft.Column([
                        ft.Text("Max Token:", size=12, color=ft.Colors.GREY_600),
                        ft.TextField(
                            hint_text="500",
                            width=100,
                            value=str(self.settings.get('llm_max_tokens', 500)),
                            keyboard_type=ft.KeyboardType.NUMBER
                        )
                    ], spacing=4),
                    
                    ft.Container(width=20),
                    
                    ft.Column([
                        ft.Text("Timeout (s):", size=12, color=ft.Colors.GREY_600),
                        ft.TextField(
                            hint_text="30",
                            width=100,
                            value=str(self.settings.get('llm_timeout', 30)),
                            keyboard_type=ft.KeyboardType.NUMBER
                        )
                    ], spacing=4)
                ])
            ], spacing=12),
            padding=ft.padding.all(16),
            bgcolor=ft.Colors.GREY_50,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.GREY_300)
        )
    
    def _create_connection_test(self) -> ft.Container:
        """Create connection test section"""
        return ft.Container(
            content=ft.Column([
                ft.Text(
                    "🔍 Test Connessione",
                    size=16,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.GREY_800
                ),
                
                ft.Row([
                    ft.ElevatedButton(
                        text="Testa Connessione",
                        icon=ft.Icons.WIFI_FIND,
                        on_click=self._test_connection
                    ),
                    ft.Container(width=16),
                    ft.ElevatedButton(
                        text="Salva Configurazione",
                        icon=ft.Icons.SAVE,
                        on_click=self._save_config
                    )
                ])
            ], spacing=12),
            padding=ft.padding.all(16),
            bgcolor=ft.Colors.GREEN_50,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.GREEN_200)
        )
    
    def _create_models_management(self) -> ft.Container:
        """Create custom models management section"""
        return ft.Container(
            content=ft.Column([
                ft.Text(
                    "🤖 Gestione Modelli Personalizzati",
                    size=16,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.GREY_800
                ),
                
                ft.Row([
                    ft.ElevatedButton(
                        text="Gestisci Modelli",
                        icon=ft.Icons.EDIT,
                        on_click=self._show_models_manager
                    ),
                    ft.Container(width=16),
                    ft.Text(
                        f"Modelli personalizzati: {len(self.settings.get('custom_models', {}))}",
                        size=12,
                        color=ft.Colors.GREY_600
                    )
                ])
            ], spacing=12),
            padding=ft.padding.all(16),
            bgcolor=ft.Colors.PURPLE_50,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.PURPLE_200)
        )
    
    def _get_model_options(self) -> List[ft.dropdown.Option]:
        """Get available model options"""
        default_models = [
            ("deepseek/deepseek-r1-0528-qwen3-8b:free", "DeepSeek R1 (Free)"),
            ("gpt-4o-mini", "GPT-4o Mini"),
            ("gpt-3.5-turbo", "GPT-3.5 Turbo"),
            ("claude-3-haiku", "Claude 3 Haiku"),
            ("meta-llama/llama-3.1-8b-instruct:free", "Llama 3.1 8B (Free)"),
            ("microsoft/wizardlm-2-8x22b", "WizardLM 2 8x22B"),
            ("anthropic/claude-3-sonnet", "Claude 3 Sonnet"),
            ("openai/gpt-4-turbo", "GPT-4 Turbo")
        ]
        
        custom_models = self.settings.get('custom_models', {})
        
        options = []
        for model_id, display_name in default_models:
            options.append(ft.dropdown.Option(model_id, display_name))
        
        for model_id, display_name in custom_models.items():
            options.append(ft.dropdown.Option(model_id, f"{display_name} (Custom)"))
        
        return options
    
    def _load_settings(self) -> Dict[str, Any]:
        """Load LLM settings"""
        try:
            # Try incentives config first
            config_path = Path("data/incentives_config.json")
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            
            # Fallback to main settings
            settings_path = Path("data/settings.json")
            if settings_path.exists():
                with open(settings_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('incentives', {})
            
            return {}
        except Exception as e:
            logger.error(f"Error loading LLM settings: {e}")
            return {}
    
    def _on_enabled_changed(self, e):
        """Handle enabled switch change"""
        self._update_connection_status()
    
    def _on_config_changed(self, e):
        """Handle configuration change"""
        self._update_connection_status()
    
    def _update_connection_status(self):
        """Update connection status display"""
        if not self.connection_status.current:
            return
        
        enabled = self.enabled_switch.current.value if self.enabled_switch.current else False
        api_key = self.api_key_field.current.value if self.api_key_field.current else ""
        api_url = self.api_url_field.current.value if self.api_url_field.current else ""
        
        if not enabled:
            self.connection_status.current.value = "Disabilitato"
            self.connection_status.current.color = ft.Colors.GREY_600
        elif not api_key:
            self.connection_status.current.value = "API Key mancante"
            self.connection_status.current.color = ft.Colors.RED_600
        elif not api_url:
            self.connection_status.current.value = "URL API mancante"
            self.connection_status.current.color = ft.Colors.RED_600
        else:
            self.connection_status.current.value = "Configurato"
            self.connection_status.current.color = ft.Colors.GREEN_600
        
        self.connection_status.current.update()
    
    def _test_connection(self, e):
        """Test LLM connection"""
        try:
            # Import here to avoid circular imports
            from core.services.llm_service import LLMService
            
            # Get current configuration
            config = {
                'openrouter_api_key': self.api_key_field.current.value if self.api_key_field.current else "",
                'llm_model': self.model_dropdown.current.value if self.model_dropdown.current else "",
                'llm_enabled': self.enabled_switch.current.value if self.enabled_switch.current else False,
                'llm_api_url': self.api_url_field.current.value if self.api_url_field.current else ""
            }
            
            if not config['openrouter_api_key']:
                self._show_error("API Key non configurata!")
                return
            
            if not config['llm_model']:
                self._show_error("Modello non selezionato!")
                return
            
            # Test connection
            llm_service = LLMService(config)
            
            if llm_service.test_connection():
                self._show_success("✅ Connessione LLM riuscita!")
                self.connection_status.current.value = "Connesso"
                self.connection_status.current.color = ft.Colors.GREEN_600
            else:
                self._show_error("❌ Test di connessione fallito!")
                self.connection_status.current.value = "Errore connessione"
                self.connection_status.current.color = ft.Colors.RED_600
            
            self.connection_status.current.update()
            
        except Exception as ex:
            logger.error(f"Error testing LLM connection: {ex}")
            self._show_error(f"Errore nel test: {ex}")
    
    def _save_config(self, e):
        """Save LLM configuration"""
        try:
            # Get current values
            config = {
                'openrouter_api_key': self.api_key_field.current.value if self.api_key_field.current else "",
                'llm_model': self.model_dropdown.current.value if self.model_dropdown.current else "",
                'llm_enabled': self.enabled_switch.current.value if self.enabled_switch.current else False,
                'llm_api_url': self.api_url_field.current.value if self.api_url_field.current else "https://openrouter.ai/api/v1"
            }
            
            # Save to incentives config
            config_path = Path("data/incentives_config.json")
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            else:
                data = {}
            
            data.update(config)
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            self._show_success("✅ Configurazione LLM salvata!")
            logger.info("LLM configuration saved successfully")
            
        except Exception as ex:
            logger.error(f"Error saving LLM config: {ex}")
            self._show_error(f"Errore nel salvataggio: {ex}")
    
    def _show_models_manager(self, e):
        """Show models management dialog"""
        # This would open the models manager dialog
        # Implementation similar to the one in incentives_section.py
        pass
    
    def _show_success(self, message: str):
        """Show success message"""
        logger.info(f"SUCCESS: {message}")
    
    def _show_error(self, message: str):
        """Show error message"""
        logger.error(f"ERROR: {message}")
    
    def refresh_data(self):
        """Refresh section data"""
        self.settings = self._load_settings()
