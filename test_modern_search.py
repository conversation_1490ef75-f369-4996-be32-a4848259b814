#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Modern Search Implementation
Testing Flet's native SearchBar for proper overlay and visibility
"""

import flet as ft
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "src"))

from ui.components.modern_header import ModernHeader

class MockApp:
    """Mock app instance for testing"""
    
    def __init__(self):
        self.page = None
        self.db_manager = None
        self.config = MockConfig()
        self.main_layout = MockMainLayout()

class MockConfig:
    """Mock configuration"""
    
    def is_email_configured(self):
        return False
    
    @property
    def email_config(self):
        return {}
    
    @property
    def alert_config(self):
        return {}

class MockMainLayout:
    """Mock main layout"""
    
    def navigate_to_detail(self, detail_type, detail_id):
        print(f"Navigate to detail: {detail_type} - {detail_id}")
    
    def _navigate_to(self, view_name):
        print(f"Navigate to: {view_name}")
    
    def get_view(self, view_name):
        return MockView()
    
    def go_back(self):
        print("Go back")

class MockView:
    """Mock view"""
    
    def _show_client_form(self):
        print("Show client form")
    
    def _show_project_form(self):
        print("Show project form")
    
    def _show_deadline_form(self):
        print("Show deadline form")

def main(page: ft.Page):
    """Test the modern search implementation"""
    page.title = "🔍 Modern Search Test - Flet Native SearchBar"
    page.window_width = 1400
    page.window_height = 900
    page.padding = 0
    
    # Create mock app
    app = MockApp()
    app.page = page
    
    # Create modern header with native SearchBar
    header = ModernHeader(
        on_search=lambda query: print(f"Search: {query}"),
        on_notification_click=lambda: print("Notifications"),
        on_settings_click=lambda: print("Settings"),
        app_instance=app
    )
    
    # Test content with detailed instructions
    test_content = ft.Container(
        content=ft.Column([
            # Hero section
            ft.Container(
                content=ft.Column([
                    ft.Text(
                        "🚀 Modern Search Test",
                        size=32,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.BLUE_600,
                        text_align=ft.TextAlign.CENTER
                    ),
                    ft.Text(
                        "Testing Flet's Native SearchBar Component",
                        size=16,
                        color=ft.Colors.GREY_600,
                        text_align=ft.TextAlign.CENTER
                    ),
                    ft.Container(
                        content=ft.Text(
                            "✨ Flet 28.x+ Compatible",
                            size=14,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.WHITE
                        ),
                        bgcolor=ft.Colors.GREEN_600,
                        padding=ft.padding.symmetric(horizontal=12, vertical=6),
                        border_radius=20
                    )
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=8),
                padding=ft.padding.all(24),
                bgcolor=ft.Colors.BLUE_50,
                border_radius=12,
                margin=ft.margin.all(16)
            ),
            
            # Feature highlights
            ft.Container(
                content=ft.Column([
                    ft.Text(
                        "🎯 Key Features",
                        size=20,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.GREY_800
                    ),
                    ft.Row([
                        # Feature 1
                        ft.Container(
                            content=ft.Column([
                                ft.Icon(ft.Icons.SEARCH, size=32, color=ft.Colors.BLUE_600),
                                ft.Text(
                                    "Native SearchBar",
                                    size=14,
                                    weight=ft.FontWeight.BOLD,
                                    color=ft.Colors.GREY_800,
                                    text_align=ft.TextAlign.CENTER
                                ),
                                ft.Text(
                                    "Uses Flet's built-in SearchBar control for proper overlay behavior",
                                    size=12,
                                    color=ft.Colors.GREY_600,
                                    text_align=ft.TextAlign.CENTER
                                )
                            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=8),
                            padding=ft.padding.all(16),
                            bgcolor=ft.Colors.WHITE,
                            border_radius=8,
                            border=ft.border.all(1, ft.Colors.BLUE_200),
                            expand=True
                        ),
                        
                        # Feature 2
                        ft.Container(
                            content=ft.Column([
                                ft.Icon(ft.Icons.VISIBILITY, size=32, color=ft.Colors.GREEN_600),
                                ft.Text(
                                    "Perfect Visibility",
                                    size=14,
                                    weight=ft.FontWeight.BOLD,
                                    color=ft.Colors.GREY_800,
                                    text_align=ft.TextAlign.CENTER
                                ),
                                ft.Text(
                                    "Text input is always visible with proper Material Design styling",
                                    size=12,
                                    color=ft.Colors.GREY_600,
                                    text_align=ft.TextAlign.CENTER
                                )
                            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=8),
                            padding=ft.padding.all(16),
                            bgcolor=ft.Colors.WHITE,
                            border_radius=8,
                            border=ft.border.all(1, ft.Colors.GREEN_200),
                            expand=True
                        ),
                        
                        # Feature 3
                        ft.Container(
                            content=ft.Column([
                                ft.Icon(ft.Icons.LAYERS, size=32, color=ft.Colors.ORANGE_600),
                                ft.Text(
                                    "Proper Overlay",
                                    size=14,
                                    weight=ft.FontWeight.BOLD,
                                    color=ft.Colors.GREY_800,
                                    text_align=ft.TextAlign.CENTER
                                ),
                                ft.Text(
                                    "Search results appear as proper overlay with native behavior",
                                    size=12,
                                    color=ft.Colors.GREY_600,
                                    text_align=ft.TextAlign.CENTER
                                )
                            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=8),
                            padding=ft.padding.all(16),
                            bgcolor=ft.Colors.WHITE,
                            border_radius=8,
                            border=ft.border.all(1, ft.Colors.ORANGE_200),
                            expand=True
                        )
                    ], spacing=16)
                ], spacing=16),
                padding=ft.padding.all(16),
                margin=ft.margin.symmetric(horizontal=16)
            ),
            
            # Test instructions
            ft.Container(
                content=ft.Column([
                    ft.Text(
                        "🧪 Test Instructions",
                        size=18,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.GREY_800
                    ),
                    ft.Column([
                        ft.Row([
                            ft.Container(
                                content=ft.Text("1", color=ft.Colors.WHITE, weight=ft.FontWeight.BOLD),
                                width=24,
                                height=24,
                                bgcolor=ft.Colors.BLUE_600,
                                border_radius=12,
                                alignment=ft.alignment.center
                            ),
                            ft.Text(
                                "Click the search bar in the header above",
                                size=14,
                                color=ft.Colors.GREY_700,
                                expand=True
                            )
                        ], spacing=12),
                        
                        ft.Row([
                            ft.Container(
                                content=ft.Text("2", color=ft.Colors.WHITE, weight=ft.FontWeight.BOLD),
                                width=24,
                                height=24,
                                bgcolor=ft.Colors.GREEN_600,
                                border_radius=12,
                                alignment=ft.alignment.center
                            ),
                            ft.Text(
                                "See the search view open with popular searches",
                                size=14,
                                color=ft.Colors.GREY_700,
                                expand=True
                            )
                        ], spacing=12),
                        
                        ft.Row([
                            ft.Container(
                                content=ft.Text("3", color=ft.Colors.WHITE, weight=ft.FontWeight.BOLD),
                                width=24,
                                height=24,
                                bgcolor=ft.Colors.ORANGE_600,
                                border_radius=12,
                                alignment=ft.alignment.center
                            ),
                            ft.Text(
                                "Type to search - text should be clearly visible",
                                size=14,
                                color=ft.Colors.GREY_700,
                                expand=True
                            )
                        ], spacing=12),
                        
                        ft.Row([
                            ft.Container(
                                content=ft.Text("4", color=ft.Colors.WHITE, weight=ft.FontWeight.BOLD),
                                width=24,
                                height=24,
                                bgcolor=ft.Colors.PURPLE_600,
                                border_radius=12,
                                alignment=ft.alignment.center
                            ),
                            ft.Text(
                                "Search results appear as proper overlay with Material Design",
                                size=14,
                                color=ft.Colors.GREY_700,
                                expand=True
                            )
                        ], spacing=12)
                    ], spacing=16)
                ], spacing=16),
                padding=ft.padding.all(20),
                bgcolor=ft.Colors.WHITE,
                border_radius=8,
                border=ft.border.all(1, ft.Colors.GREY_200),
                margin=ft.margin.all(16)
            ),
            
            # Comparison section
            ft.Container(
                content=ft.Column([
                    ft.Text(
                        "⚡ Why This Approach Works",
                        size=18,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.GREY_800
                    ),
                    ft.Row([
                        # Old approach
                        ft.Container(
                            content=ft.Column([
                                ft.Icon(ft.Icons.CLOSE, color=ft.Colors.RED_600, size=24),
                                ft.Text("Old Approach", weight=ft.FontWeight.BOLD, color=ft.Colors.RED_600),
                                ft.Text("• Manual Stack positioning", size=12, color=ft.Colors.GREY_600),
                                ft.Text("• Z-index conflicts", size=12, color=ft.Colors.GREY_600),
                                ft.Text("• Clipping issues", size=12, color=ft.Colors.GREY_600),
                                ft.Text("• Visibility problems", size=12, color=ft.Colors.GREY_600),
                                ft.Text("• Complex overlay logic", size=12, color=ft.Colors.GREY_600)
                            ], spacing=8),
                            padding=ft.padding.all(16),
                            bgcolor=ft.Colors.RED_50,
                            border_radius=8,
                            border=ft.border.all(1, ft.Colors.RED_200),
                            expand=True
                        ),
                        
                        ft.Icon(ft.Icons.ARROW_FORWARD, color=ft.Colors.GREY_400, size=32),
                        
                        # New approach
                        ft.Container(
                            content=ft.Column([
                                ft.Icon(ft.Icons.CHECK_CIRCLE, color=ft.Colors.GREEN_600, size=24),
                                ft.Text("New Approach", weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN_600),
                                ft.Text("• Native SearchBar control", size=12, color=ft.Colors.GREY_600),
                                ft.Text("• Built-in overlay behavior", size=12, color=ft.Colors.GREY_600),
                                ft.Text("• Material Design styling", size=12, color=ft.Colors.GREY_600),
                                ft.Text("• Perfect visibility", size=12, color=ft.Colors.GREY_600),
                                ft.Text("• Simple implementation", size=12, color=ft.Colors.GREY_600)
                            ], spacing=8),
                            padding=ft.padding.all(16),
                            bgcolor=ft.Colors.GREEN_50,
                            border_radius=8,
                            border=ft.border.all(1, ft.Colors.GREEN_200),
                            expand=True
                        )
                    ], spacing=16, alignment=ft.MainAxisAlignment.CENTER)
                ], spacing=16),
                padding=ft.padding.all(16),
                margin=ft.margin.all(16)
            ),
            
            # Sample content to test overlay
            ft.Container(
                content=ft.Column([
                    ft.Text(
                        "📄 Sample Content",
                        size=16,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.GREY_700,
                        text_align=ft.TextAlign.CENTER
                    ),
                    ft.Text(
                        "Search results should appear ABOVE this content with proper overlay behavior",
                        size=14,
                        color=ft.Colors.GREY_500,
                        text_align=ft.TextAlign.CENTER
                    ),
                    ft.Row([
                        ft.Container(
                            content=ft.Column([
                                ft.Icon(ft.Icons.DASHBOARD, color=ft.Colors.WHITE, size=24),
                                ft.Text("Dashboard", color=ft.Colors.WHITE, weight=ft.FontWeight.BOLD)
                            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                            width=150,
                            height=100,
                            bgcolor=ft.Colors.BLUE_400,
                            border_radius=8,
                            alignment=ft.alignment.center,
                            padding=ft.padding.all(8)
                        ),
                        ft.Container(
                            content=ft.Column([
                                ft.Icon(ft.Icons.PEOPLE, color=ft.Colors.WHITE, size=24),
                                ft.Text("Clienti", color=ft.Colors.WHITE, weight=ft.FontWeight.BOLD)
                            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                            width=150,
                            height=100,
                            bgcolor=ft.Colors.GREEN_400,
                            border_radius=8,
                            alignment=ft.alignment.center,
                            padding=ft.padding.all(8)
                        ),
                        ft.Container(
                            content=ft.Column([
                                ft.Icon(ft.Icons.WORK, color=ft.Colors.WHITE, size=24),
                                ft.Text("Progetti", color=ft.Colors.WHITE, weight=ft.FontWeight.BOLD)
                            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                            width=150,
                            height=100,
                            bgcolor=ft.Colors.ORANGE_400,
                            border_radius=8,
                            alignment=ft.alignment.center,
                            padding=ft.padding.all(8)
                        ),
                        ft.Container(
                            content=ft.Column([
                                ft.Icon(ft.Icons.SCHEDULE, color=ft.Colors.WHITE, size=24),
                                ft.Text("Scadenze", color=ft.Colors.WHITE, weight=ft.FontWeight.BOLD)
                            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                            width=150,
                            height=100,
                            bgcolor=ft.Colors.PURPLE_400,
                            border_radius=8,
                            alignment=ft.alignment.center,
                            padding=ft.padding.all(8)
                        )
                    ], spacing=16, alignment=ft.MainAxisAlignment.CENTER)
                ], spacing=16),
                padding=ft.padding.all(24),
                bgcolor=ft.Colors.GREY_50,
                border_radius=8,
                margin=ft.margin.all(16)
            )
        ], spacing=0),
        expand=True
    )
    
    # Main layout
    main_layout = ft.Column([
        # Modern header with native SearchBar
        header.build(),
        
        # Scrollable test content
        ft.Container(
            content=test_content,
            expand=True
        )
    ], spacing=0, expand=True)
    
    # Add to page
    page.add(main_layout)

if __name__ == "__main__":
    ft.app(target=main, view=ft.AppView.WEB_BROWSER, port=8080) 