# Build-specific requirements for GitHub Actions
# These are the minimal dependencies needed for building the application

# Core framework - with desktop support for building
flet[desktop]>=0.27.0

# Essential dependencies
packaging>=21.0
pydantic>=2.0.0
python-dotenv>=1.0.0
loguru>=0.7.0

# Build tools
cookiecutter>=2.1.0

# PDF generation
reportlab>=4.0.0

# Windows-specific (for notifications)
plyer>=2.1.0
win10toast>=0.9.0
pywin32>=306

# Google APIs (for backup functionality)
google-api-python-client>=2.0.0
google-auth>=2.0.0
google-auth-oauthlib>=1.0.0
google-auth-httplib2>=0.2.0

# Additional Google dependencies
googleapis-common-protos>=1.56.0
google-auth-httplib2>=0.2.0
