#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple URL test script
"""

import requests
import json
from pathlib import Path

def test_urls():
    """Test if the URLs in configuration are accessible"""
    
    # Load configuration
    config_path = Path("data/incentives_config.json")
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    })
    
    for website in config.get('websites', []):
        if not website.get('enabled', True):
            continue
            
        print(f"\nTesting {website['name']}:")
        base_url = website['url']
        
        # Test base URL
        try:
            response = session.get(base_url, timeout=10)
            print(f"  Base URL ({base_url}): {response.status_code}")
        except Exception as e:
            print(f"  Base URL ({base_url}): ERROR - {e}")
        
        # Test search paths
        for path in website.get('search_paths', []):
            full_url = base_url + path
            try:
                response = session.get(full_url, timeout=10)
                print(f"  {path}: {response.status_code}")
            except Exception as e:
                print(f"  {path}: ERROR - {e}")
    
    session.close()

if __name__ == "__main__":
    test_urls()
