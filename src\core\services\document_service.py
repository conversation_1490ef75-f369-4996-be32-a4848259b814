#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Servizio per la gestione dei documenti
"""

import os
import shutil
import mimetypes
from pathlib import Path
from typing import Optional, List, Dict, Any
from uuid import UUID, uuid4
from datetime import datetime

from ..models import Document
from ..utils import get_logger

logger = get_logger(__name__)


class DocumentService:
    """Servizio per la gestione dei documenti"""
    
    def __init__(self, data_dir: Path, db_manager):
        self.data_dir = data_dir
        self.db_manager = db_manager
        self.documents_dir = data_dir / "documents"
        self.documents_dir.mkdir(exist_ok=True)
        
        # Crea sottocartelle per organizzazione
        (self.documents_dir / "projects").mkdir(exist_ok=True)
        (self.documents_dir / "clients").mkdir(exist_ok=True)
        (self.documents_dir / "deadlines").mkdir(exist_ok=True)
        (self.documents_dir / "temp").mkdir(exist_ok=True)
    
    def upload_document(self, 
                       file_path: str, 
                       name: str,
                       description: Optional[str] = None,
                       project_id: Optional[UUID] = None,
                       client_id: Optional[UUID] = None,
                       deadline_id: Optional[UUID] = None,
                       sal_id: Optional[UUID] = None,
                       tags: Optional[List[str]] = None) -> Optional[Document]:
        """Carica un documento nel sistema"""
        try:
            source_path = Path(file_path)
            if not source_path.exists():
                logger.error(f"File non trovato: {file_path}")
                return None
            
            # Determina la cartella di destinazione
            if project_id:
                dest_folder = self.documents_dir / "projects" / str(project_id)
            elif client_id:
                dest_folder = self.documents_dir / "clients" / str(client_id)
            elif deadline_id:
                dest_folder = self.documents_dir / "deadlines" / str(deadline_id)
            else:
                dest_folder = self.documents_dir / "general"
            
            dest_folder.mkdir(parents=True, exist_ok=True)
            
            # Genera nome file unico
            file_extension = source_path.suffix
            unique_filename = f"{uuid4()}{file_extension}"
            dest_path = dest_folder / unique_filename
            
            # Copia il file
            shutil.copy2(source_path, dest_path)
            
            # Ottieni informazioni sul file
            file_size = dest_path.stat().st_size
            mime_type, _ = mimetypes.guess_type(str(dest_path))
            
            # Crea il documento nel database
            document = Document(
                id=uuid4(),
                name=name,
                description=description,
                file_path=str(dest_path.relative_to(self.data_dir)),
                file_size=file_size,
                mime_type=mime_type,
                project_id=project_id,
                client_id=client_id,
                deadline_id=deadline_id,
                sal_id=sal_id,
                tags=tags or [],
                is_public=False,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            # Salva nel database
            saved_document = self.db_manager.create_document(document)
            
            logger.info(f"Documento caricato: {name} ({file_size} bytes)")
            return saved_document
            
        except Exception as e:
            logger.error(f"Errore caricamento documento: {e}")
            return None
    
    def get_document_path(self, document: Document) -> Path:
        """Ottiene il percorso completo di un documento"""
        return self.data_dir / document.file_path
    
    def delete_document(self, document_id: UUID) -> bool:
        """Elimina un documento"""
        try:
            document = self.db_manager.get_document(document_id)
            if not document:
                logger.error(f"Documento non trovato: {document_id}")
                return False
            
            # Elimina il file fisico
            file_path = self.get_document_path(document)
            if file_path.exists():
                file_path.unlink()
            
            # Elimina dal database
            success = self.db_manager.delete_document(document_id)
            
            if success:
                logger.info(f"Documento eliminato: {document.name}")
            
            return success
            
        except Exception as e:
            logger.error(f"Errore eliminazione documento: {e}")
            return False
    
    def get_documents_by_project(self, project_id: UUID) -> List[Document]:
        """Ottiene tutti i documenti di un progetto"""
        return self.db_manager.get_documents_by_project(project_id)
    
    def get_documents_by_client(self, client_id: UUID) -> List[Document]:
        """Ottiene tutti i documenti di un cliente"""
        return self.db_manager.get_documents_by_client(client_id)
    
    def get_document_stats(self) -> Dict[str, Any]:
        """Ottiene statistiche sui documenti"""
        try:
            all_documents = self.db_manager.get_all_documents()
            
            total_size = sum(doc.file_size for doc in all_documents)
            
            # Raggruppa per tipo MIME
            mime_types = {}
            for doc in all_documents:
                mime_type = doc.mime_type or "unknown"
                if mime_type not in mime_types:
                    mime_types[mime_type] = {"count": 0, "size": 0}
                mime_types[mime_type]["count"] += 1
                mime_types[mime_type]["size"] += doc.file_size
            
            return {
                "total_documents": len(all_documents),
                "total_size": total_size,
                "total_size_mb": round(total_size / (1024 * 1024), 2),
                "mime_types": mime_types
            }
            
        except Exception as e:
            logger.error(f"Errore statistiche documenti: {e}")
            return {}
    
    def cleanup_orphaned_files(self) -> Dict[str, Any]:
        """Pulisce i file orfani (non referenziati nel database)"""
        try:
            all_documents = self.db_manager.get_all_documents()
            db_file_paths = {doc.file_path for doc in all_documents}
            
            orphaned_files = []
            total_orphaned_size = 0
            
            # Scansiona tutte le cartelle documenti
            for folder in ["projects", "clients", "deadlines", "general"]:
                folder_path = self.documents_dir / folder
                if folder_path.exists():
                    for file_path in folder_path.rglob("*"):
                        if file_path.is_file():
                            relative_path = str(file_path.relative_to(self.data_dir))
                            if relative_path not in db_file_paths:
                                orphaned_files.append(relative_path)
                                total_orphaned_size += file_path.stat().st_size
            
            return {
                "orphaned_files": orphaned_files,
                "count": len(orphaned_files),
                "total_size": total_orphaned_size,
                "total_size_mb": round(total_orphaned_size / (1024 * 1024), 2)
            }
            
        except Exception as e:
            logger.error(f"Errore cleanup file orfani: {e}")
            return {"error": str(e)}
    
    def get_supported_file_types(self) -> List[str]:
        """Restituisce i tipi di file supportati"""
        return [
            # Documenti
            ".pdf", ".doc", ".docx", ".txt", ".rtf", ".odt",
            # Fogli di calcolo
            ".xls", ".xlsx", ".ods", ".csv",
            # Presentazioni
            ".ppt", ".pptx", ".odp",
            # Immagini
            ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".svg",
            # Archivi
            ".zip", ".rar", ".7z", ".tar", ".gz",
            # Altri
            ".xml", ".json", ".html", ".htm"
        ]
    
    def is_file_type_supported(self, file_path: str) -> bool:
        """Verifica se il tipo di file è supportato"""
        file_extension = Path(file_path).suffix.lower()
        return file_extension in self.get_supported_file_types()
