#!/usr/bin/env python3
"""
Check the current status of Google services authentication and sync
"""

import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def check_google_services_status():
    """Check the current status of all Google services"""
    print("🔍 Checking Google Services Status...")
    print("=" * 60)
    
    try:
        from core.config.app_config import AppConfig
        from core.services.google_calendar_service import GoogleCalendarService
        from core.services.google_tasks_service import GoogleTasksService
        from core.services.google_drive_service import GoogleDriveService
        
        # Initialize config
        config = AppConfig()
        config_dir = str(config.data_dir)
        
        print(f"📁 Config directory: {config_dir}")
        print(f"📁 Token directory: {os.path.join(config_dir, 'config')}")
        print()
        
        # Check token files
        token_dir = os.path.join(config_dir, "config")
        tokens = {
            "Google Calendar": os.path.join(token_dir, "google_token.pickle"),
            "Google Tasks": os.path.join(token_dir, "google_tasks_token.pickle"),
            "Google Drive": os.path.join(token_dir, "google_drive_token.pickle")
        }
        
        print("📄 Token Files Status:")
        print("-" * 30)
        for service, token_path in tokens.items():
            exists = os.path.exists(token_path)
            status = "✅ EXISTS" if exists else "❌ MISSING"
            print(f"   {service}: {status}")
            if exists:
                stat = os.stat(token_path)
                print(f"      Size: {stat.st_size} bytes")
                print(f"      Modified: {stat.st_mtime}")
        print()
        
        # Initialize services
        print("🔧 Service Initialization:")
        print("-" * 30)
        
        # Google Calendar
        try:
            calendar_service = GoogleCalendarService(config_dir)
            cal_enabled = calendar_service.is_enabled()
            print(f"   Google Calendar: {'✅ ENABLED' if cal_enabled else '❌ DISABLED'}")
            if cal_enabled:
                print(f"      Calendar ID: {calendar_service.calendar_id}")
        except Exception as e:
            print(f"   Google Calendar: ❌ ERROR - {e}")
        
        # Google Tasks
        try:
            tasks_service = GoogleTasksService(config_dir)
            tasks_enabled = tasks_service.is_enabled()
            tasks_auth = tasks_service.is_authenticated()
            print(f"   Google Tasks: {'✅ ENABLED' if tasks_enabled else '❌ DISABLED'}")
            print(f"   Tasks Auth: {'✅ AUTHENTICATED' if tasks_auth else '❌ NOT AUTHENTICATED'}")
            
            # Get detailed status
            auth_status = tasks_service.get_authentication_status()
            print(f"      Google Available: {auth_status['google_available']}")
            print(f"      Service Object: {auth_status['service_object']}")
            print(f"      Token File: {auth_status['token_file_exists']}")
        except Exception as e:
            print(f"   Google Tasks: ❌ ERROR - {e}")
        
        # Google Drive
        try:
            drive_service = GoogleDriveService(Path(config_dir))
            drive_enabled = drive_service.is_authenticated
            print(f"   Google Drive: {'✅ AUTHENTICATED' if drive_enabled else '❌ NOT AUTHENTICATED'}")
            if drive_enabled:
                print(f"      App Folder ID: {drive_service.app_folder_id}")
        except Exception as e:
            print(f"   Google Drive: ❌ ERROR - {e}")
        
        print()
        
        # Check settings file
        print("⚙️ Settings File Status:")
        print("-" * 30)
        settings_file = config.data_dir / 'settings.json'
        if settings_file.exists():
            import json
            with open(settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)
            
            google_settings = settings.get('google_services', {})
            print(f"   Drive Authenticated: {google_settings.get('drive_authenticated', False)}")
            print(f"   Drive Enabled: {google_settings.get('drive_enabled', False)}")
            print(f"   Calendar Authenticated: {google_settings.get('calendar_authenticated', False)}")
            print(f"   Calendar Enabled: {google_settings.get('calendar_enabled', False)}")
            print(f"   Tasks Authenticated: {google_settings.get('tasks_authenticated', False)}")
            print(f"   Tasks Enabled: {google_settings.get('tasks_enabled', False)}")
        else:
            print("   ❌ Settings file not found")
        
        print()
        
        # Test actual functionality
        print("🧪 Functionality Tests:")
        print("-" * 30)
        
        # Test Calendar
        try:
            if calendar_service.is_enabled():
                # Try to get events for today
                from datetime import date
                today = date.today()
                events = calendar_service.get_google_events(today, today)
                print(f"   Calendar Events Today: {len(events)} events")
            else:
                print("   Calendar: Not enabled, skipping test")
        except Exception as e:
            print(f"   Calendar Test: ❌ ERROR - {e}")
        
        # Test Tasks
        try:
            if tasks_service.is_enabled():
                # Try to get task lists
                task_lists = tasks_service.get_task_lists()
                print(f"   Task Lists: {len(task_lists)} lists found")
            else:
                print("   Tasks: Not enabled, skipping test")
        except Exception as e:
            print(f"   Tasks Test: ❌ ERROR - {e}")
        
        # Test Drive
        try:
            if drive_service.is_authenticated:
                # Try to test connection
                connection_ok = drive_service.test_connection()
                print(f"   Drive Connection: {'✅ OK' if connection_ok else '❌ FAILED'}")
            else:
                print("   Drive: Not authenticated, skipping test")
        except Exception as e:
            print(f"   Drive Test: ❌ ERROR - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking Google services: {e}")
        return False

def check_ui_integration():
    """Check how the UI integrates with Google services"""
    print("\n🖥️ UI Integration Status:")
    print("=" * 60)
    
    try:
        from ui.views.settings.settings_controller import SettingsController
        from core.config.app_config import AppConfig
        
        # Create mock app
        class MockApp:
            def __init__(self):
                self.config = AppConfig()
        
        app = MockApp()
        controller = SettingsController(app)
        
        # Check Google services settings
        google_settings = controller.get_setting('google_services')
        
        print("📊 Settings Controller Status:")
        print("-" * 30)
        print(f"   Drive Authenticated: {google_settings.get('drive_authenticated', False)}")
        print(f"   Drive Enabled: {google_settings.get('drive_enabled', False)}")
        print(f"   Calendar Authenticated: {google_settings.get('calendar_authenticated', False)}")
        print(f"   Calendar Enabled: {google_settings.get('calendar_enabled', False)}")
        print(f"   Tasks Authenticated: {google_settings.get('tasks_authenticated', False)}")
        print(f"   Tasks Enabled: {google_settings.get('tasks_enabled', False)}")
        
        # Test service connections through controller
        print("\n🔗 Controller Service Tests:")
        print("-" * 30)
        
        # Test Google Drive
        try:
            drive_test = controller.test_google_drive_connection()
            print(f"   Drive Connection: {'✅ OK' if drive_test else '❌ FAILED'}")
        except Exception as e:
            print(f"   Drive Test: ❌ ERROR - {e}")
        
        # Test Google Calendar
        try:
            calendar_test = controller.test_google_calendar_connection()
            print(f"   Calendar Connection: {'✅ OK' if calendar_test else '❌ FAILED'}")
        except Exception as e:
            print(f"   Calendar Test: ❌ ERROR - {e}")
        
        # Test Google Tasks
        try:
            tasks_test = controller.test_google_tasks_connection()
            print(f"   Tasks Connection: {'✅ OK' if tasks_test else '❌ FAILED'}")
        except Exception as e:
            print(f"   Tasks Test: ❌ ERROR - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking UI integration: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Google Services Status Check")
    print("=" * 60)
    
    # Check services
    services_ok = check_google_services_status()
    
    # Check UI integration
    ui_ok = check_ui_integration()
    
    print("\n" + "=" * 60)
    print("📋 SUMMARY")
    print("=" * 60)
    
    if services_ok and ui_ok:
        print("✅ Google services check completed successfully")
        print("💡 Check the output above for detailed status information")
    else:
        print("⚠️ Some issues were found with Google services")
        print("💡 Review the errors above for troubleshooting")
    
    return services_ok and ui_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
