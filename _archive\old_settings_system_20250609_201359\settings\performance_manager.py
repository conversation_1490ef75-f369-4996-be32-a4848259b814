#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Performance Manager for Settings Interface
Optimizes performance with lazy loading, caching, and efficient updates
"""

import asyncio
import weakref
from typing import Dict, List, Any, Optional, Callable, Set
from dataclasses import dataclass, field
from enum import Enum
import time
from core import get_logger

logger = get_logger(__name__)

class UpdatePriority(Enum):
    """Update priority levels"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4

@dataclass
class UpdateTask:
    """Represents a UI update task"""
    component_id: str
    update_function: Callable
    priority: UpdatePriority
    timestamp: float = field(default_factory=time.time)
    data: Any = None

class ComponentCache:
    """Cache for expensive component operations"""
    
    def __init__(self, max_size: int = 100, ttl: float = 300.0):
        self.cache: Dict[str, Any] = {}
        self.timestamps: Dict[str, float] = {}
        self.max_size = max_size
        self.ttl = ttl  # Time to live in seconds
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache if valid"""
        if key in self.cache:
            if time.time() - self.timestamps[key] < self.ttl:
                return self.cache[key]
            else:
                # Expired - remove
                self.remove(key)
        return None
    
    def set(self, key: str, value: Any):
        """Set value in cache"""
        # Clean up if at max size
        if len(self.cache) >= self.max_size:
            self._cleanup_old_entries()
        
        self.cache[key] = value
        self.timestamps[key] = time.time()
    
    def remove(self, key: str):
        """Remove value from cache"""
        self.cache.pop(key, None)
        self.timestamps.pop(key, None)
    
    def clear(self):
        """Clear entire cache"""
        self.cache.clear()
        self.timestamps.clear()
    
    def _cleanup_old_entries(self):
        """Remove expired entries"""
        current_time = time.time()
        expired_keys = [
            key for key, timestamp in self.timestamps.items()
            if current_time - timestamp >= self.ttl
        ]
        
        for key in expired_keys:
            self.remove(key)
        
        # If still at max size, remove oldest entries
        if len(self.cache) >= self.max_size:
            sorted_items = sorted(self.timestamps.items(), key=lambda x: x[1])
            remove_count = len(self.cache) - self.max_size + 10  # Remove extra for buffer
            
            for key, _ in sorted_items[:remove_count]:
                self.remove(key)

class LazyLoader:
    """Handles lazy loading of expensive components"""
    
    def __init__(self):
        self.loaded_sections: Set[str] = set()
        self.loading_callbacks: Dict[str, List[Callable]] = {}
        self.load_results: Dict[str, Any] = {}
    
    def register_section(self, section_id: str, load_callback: Callable):
        """Register a section for lazy loading"""
        if section_id not in self.loading_callbacks:
            self.loading_callbacks[section_id] = []
        self.loading_callbacks[section_id].append(load_callback)
    
    async def load_section(self, section_id: str) -> Any:
        """Load a section asynchronously"""
        if section_id in self.loaded_sections:
            return self.load_results.get(section_id)
        
        logger.debug(f"Lazy loading section: {section_id}")
        
        if section_id in self.loading_callbacks:
            try:
                # Execute all callbacks for this section
                results = []
                for callback in self.loading_callbacks[section_id]:
                    if asyncio.iscoroutinefunction(callback):
                        result = await callback()
                    else:
                        result = callback()
                    results.append(result)
                
                self.load_results[section_id] = results
                self.loaded_sections.add(section_id)
                
                logger.debug(f"Section {section_id} loaded successfully")
                return results
                
            except Exception as e:
                logger.error(f"Error loading section {section_id}: {e}")
                return None
        
        return None
    
    def is_loaded(self, section_id: str) -> bool:
        """Check if section is loaded"""
        return section_id in self.loaded_sections
    
    def unload_section(self, section_id: str):
        """Unload a section to free memory"""
        self.loaded_sections.discard(section_id)
        self.load_results.pop(section_id, None)
        logger.debug(f"Section {section_id} unloaded")

class UpdateBatcher:
    """Batches UI updates for better performance"""
    
    def __init__(self, batch_interval: float = 0.1):
        self.batch_interval = batch_interval
        self.pending_updates: Dict[str, UpdateTask] = {}
        self._update_timer: Optional[asyncio.Task] = None
        self._running = False
    
    def schedule_update(self, task: UpdateTask):
        """Schedule an update task"""
        # Replace existing update for same component if lower priority
        existing = self.pending_updates.get(task.component_id)
        if existing is None or task.priority.value > existing.priority.value:
            self.pending_updates[task.component_id] = task
            
            if not self._running:
                self._schedule_batch_update()
    
    def _schedule_batch_update(self):
        """Schedule batch update execution"""
        if self._update_timer:
            self._update_timer.cancel()
        
        self._update_timer = asyncio.create_task(self._execute_batch_updates())
    
    async def _execute_batch_updates(self):
        """Execute batched updates"""
        await asyncio.sleep(self.batch_interval)
        
        if not self.pending_updates:
            return
        
        self._running = True
        logger.debug(f"Executing {len(self.pending_updates)} batched updates")
        
        # Sort by priority
        sorted_tasks = sorted(
            self.pending_updates.values(),
            key=lambda t: t.priority.value,
            reverse=True
        )
        
        # Execute updates
        for task in sorted_tasks:
            try:
                if asyncio.iscoroutinefunction(task.update_function):
                    await task.update_function(task.data)
                else:
                    task.update_function(task.data)
            except Exception as e:
                logger.error(f"Error executing update for {task.component_id}: {e}")
        
        self.pending_updates.clear()
        self._running = False

class PerformanceMonitor:
    """Monitors performance metrics"""
    
    def __init__(self):
        self.metrics: Dict[str, List[float]] = {}
        self.start_times: Dict[str, float] = {}
    
    def start_timing(self, operation: str):
        """Start timing an operation"""
        self.start_times[operation] = time.time()
    
    def end_timing(self, operation: str):
        """End timing an operation"""
        if operation in self.start_times:
            duration = time.time() - self.start_times[operation]
            
            if operation not in self.metrics:
                self.metrics[operation] = []
            
            self.metrics[operation].append(duration)
            
            # Keep only last 100 measurements
            if len(self.metrics[operation]) > 100:
                self.metrics[operation] = self.metrics[operation][-100:]
            
            del self.start_times[operation]
            return duration
        
        return None
    
    def get_average_time(self, operation: str) -> Optional[float]:
        """Get average time for an operation"""
        if operation in self.metrics and self.metrics[operation]:
            return sum(self.metrics[operation]) / len(self.metrics[operation])
        return None
    
    def get_performance_report(self) -> Dict[str, Dict[str, float]]:
        """Get performance report"""
        report = {}
        
        for operation, times in self.metrics.items():
            if times:
                report[operation] = {
                    'average': sum(times) / len(times),
                    'min': min(times),
                    'max': max(times),
                    'count': len(times)
                }
        
        return report

class SettingsPerformanceManager:
    """
    Main performance manager for settings interface
    """
    
    def __init__(self):
        self.cache = ComponentCache()
        self.lazy_loader = LazyLoader()
        self.update_batcher = UpdateBatcher()
        self.performance_monitor = PerformanceMonitor()
        
        # Component references for cleanup
        self.component_refs: Dict[str, weakref.ref] = {}
        
        # Settings for performance optimization
        self.optimize_animations = True
        self.batch_updates = True
        self.use_virtualization = True
        
        logger.info("Settings Performance Manager initialized")
    
    def register_component(self, component_id: str, component: Any):
        """Register a component for performance management"""
        self.component_refs[component_id] = weakref.ref(component)
    
    def unregister_component(self, component_id: str):
        """Unregister a component"""
        self.component_refs.pop(component_id, None)
        self.cache.remove(f"component_{component_id}")
    
    def cache_component_data(self, component_id: str, data: Any):
        """Cache expensive component data"""
        self.cache.set(f"component_{component_id}", data)
    
    def get_cached_component_data(self, component_id: str) -> Optional[Any]:
        """Get cached component data"""
        return self.cache.get(f"component_{component_id}")
    
    def schedule_component_update(self, component_id: str, update_function: Callable, 
                                priority: UpdatePriority = UpdatePriority.NORMAL, data: Any = None):
        """Schedule a component update"""
        if self.batch_updates:
            task = UpdateTask(
                component_id=component_id,
                update_function=update_function,
                priority=priority,
                data=data
            )
            self.update_batcher.schedule_update(task)
        else:
            # Execute immediately
            try:
                if asyncio.iscoroutinefunction(update_function):
                    asyncio.create_task(update_function(data))
                else:
                    update_function(data)
            except Exception as e:
                logger.error(f"Error executing immediate update for {component_id}: {e}")
    
    async def load_section_lazy(self, section_id: str) -> Any:
        """Load a section using lazy loading"""
        self.performance_monitor.start_timing(f"load_section_{section_id}")
        
        result = await self.lazy_loader.load_section(section_id)
        
        duration = self.performance_monitor.end_timing(f"load_section_{section_id}")
        if duration:
            logger.debug(f"Section {section_id} loaded in {duration:.3f}s")
        
        return result
    
    def optimize_for_device(self, device_info: Dict[str, Any]):
        """Optimize settings based on device capabilities"""
        # CPU performance based optimization
        cpu_cores = device_info.get('cpu_cores', 2)
        memory_gb = device_info.get('memory_gb', 4)
        
        if cpu_cores <= 2 or memory_gb <= 4:
            # Low-end device optimizations
            self.cache.max_size = 50
            self.cache.ttl = 600  # Longer cache time
            self.update_batcher.batch_interval = 0.2  # Longer batching
            self.optimize_animations = False
            logger.info("Applied low-end device optimizations")
        elif cpu_cores >= 8 and memory_gb >= 16:
            # High-end device optimizations
            self.cache.max_size = 200
            self.cache.ttl = 180  # Shorter cache time
            self.update_batcher.batch_interval = 0.05  # Faster batching
            self.optimize_animations = True
            logger.info("Applied high-end device optimizations")
    
    def preload_critical_sections(self, section_ids: List[str]):
        """Preload critical sections for better performance"""
        async def preload():
            for section_id in section_ids:
                if not self.lazy_loader.is_loaded(section_id):
                    await self.load_section_lazy(section_id)
        
        asyncio.create_task(preload())
    
    def cleanup_unused_components(self):
        """Clean up unused components and free memory"""
        # Clean up dead weak references
        dead_refs = []
        for component_id, ref in self.component_refs.items():
            if ref() is None:
                dead_refs.append(component_id)
        
        for component_id in dead_refs:
            self.unregister_component(component_id)
        
        # Clean up cache
        self.cache._cleanup_old_entries()
        
        logger.debug(f"Cleaned up {len(dead_refs)} unused components")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        return {
            'cache': {
                'size': len(self.cache.cache),
                'max_size': self.cache.max_size,
                'hit_ratio': self._calculate_cache_hit_ratio()
            },
            'lazy_loading': {
                'loaded_sections': len(self.lazy_loader.loaded_sections),
                'total_sections': len(self.lazy_loader.loading_callbacks)
            },
            'updates': {
                'pending_updates': len(self.update_batcher.pending_updates),
                'batch_interval': self.update_batcher.batch_interval
            },
            'timing': self.performance_monitor.get_performance_report()
        }
    
    def _calculate_cache_hit_ratio(self) -> float:
        """Calculate cache hit ratio (placeholder)"""
        # This would require tracking hits/misses in a real implementation
        return 0.85  # Placeholder value
    
    def enable_debug_mode(self, enabled: bool = True):
        """Enable performance debugging"""
        if enabled:
            logger.setLevel("DEBUG")
            logger.info("Performance debugging enabled")
        else:
            logger.setLevel("INFO")
    
    def create_virtualized_list(self, items: List[Any], item_height: int = 60, 
                              visible_count: int = 10) -> Dict[str, Any]:
        """Create configuration for virtualized list rendering"""
        if not self.use_virtualization or len(items) < 20:
            # Don't virtualize small lists
            return {
                'virtualized': False,
                'items': items
            }
        
        return {
            'virtualized': True,
            'total_items': len(items),
            'item_height': item_height,
            'visible_count': visible_count,
            'buffer_size': max(5, visible_count // 2),
            'items': items  # Full list for now, would be windowed in real implementation
        }

# Decorator for performance monitoring
def monitor_performance(operation_name: str):
    """Decorator to monitor function performance"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            # Get performance manager from global state or create one
            perf_manager = getattr(wrapper, '_perf_manager', None)
            if not perf_manager:
                perf_manager = SettingsPerformanceManager()
                wrapper._perf_manager = perf_manager
            
            perf_manager.performance_monitor.start_timing(operation_name)
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                duration = perf_manager.performance_monitor.end_timing(operation_name)
                if duration and duration > 0.1:  # Log slow operations
                    logger.warning(f"Slow operation {operation_name}: {duration:.3f}s")
        
        return wrapper
    return decorator

# Global performance manager instance
_global_performance_manager: Optional[SettingsPerformanceManager] = None

def get_performance_manager() -> SettingsPerformanceManager:
    """Get or create global performance manager"""
    global _global_performance_manager
    if _global_performance_manager is None:
        _global_performance_manager = SettingsPerformanceManager()
    return _global_performance_manager 