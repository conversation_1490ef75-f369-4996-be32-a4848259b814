#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vista Clienti per Agevolami PM
"""

import flet as ft
from typing import List, Dict, Any, Optional
from datetime import datetime
import threading

from core import get_logger
from core.models import Client, ProjectStatus

logger = get_logger(__name__)

class ClientsView:
    """Vista per la gestione dei clienti"""
    
    def __init__(self, app_instance):
        self.app = app_instance
        self.page = app_instance.page
        self.clients_data = []
        self.filtered_clients = []
        self.search_query = ""
        self.selected_client = None
        
        # Search debouncing
        self.search_timer = None
        
        # Componenti UI
        self.search_field = None
        self.clients_list = None
        self.client_form = None
        self.show_form = False
        
        self._init_components()
    
    def _init_components(self):
        """Inizializza i componenti della vista"""
        # Campo di ricerca
        self.search_field = ft.TextField(
            label="Cerca clienti...",
            prefix_icon=ft.Icons.SEARCH,
            on_change=self._on_search_change,
            expand=True
        )
    
    def _on_search_change(self, e):
        """Gestisce il cambio del testo di ricerca con debouncing"""
        # Cancella il timer precedente se esiste
        if self.search_timer:
            self.search_timer.cancel()
        
        # Imposta la nuova query di ricerca
        self.search_query = e.control.value.lower()
        
        # Se la query è vuota, filtra immediatamente
        if not self.search_query.strip():
            self._filter_clients()
            return
        
        # Altrimenti, crea un nuovo timer per il debouncing (500ms)
        self.search_timer = threading.Timer(0.5, self._filter_clients)
        self.search_timer.start()
    
    def _filter_clients(self):
        """Filtra i clienti in base alla ricerca"""
        if not self.search_query:
            self.filtered_clients = self.clients_data.copy()
        else:
            self.filtered_clients = [
                client for client in self.clients_data
                if (self.search_query in client.name.lower() or
                    self.search_query in client.email.lower() or
                    (client.vat_number and self.search_query in client.vat_number.lower()) or
                    (client.tax_code and self.search_query in client.tax_code.lower()))
            ]
        
        self._update_clients_list()
    
    def _create_header(self) -> ft.Container:
        """Crea l'header della vista"""
        return ft.Container(
            content=ft.Row([
                ft.Column([
                    ft.Text(
                        "Gestione Clienti",
                        size=24,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.GREY_800
                    ),
                    ft.Text(
                        f"{len(self.clients_data)} clienti totali",
                        size=12,
                        color=ft.Colors.GREY_500
                    )
                ], spacing=4),
                
                ft.Container(expand=True),
                
                ft.ElevatedButton(
                    text="Nuovo Cliente",
                    icon=ft.Icons.ADD,
                    on_click=lambda _: self._show_client_form(),
                    bgcolor=ft.Colors.BLUE_600,
                     color=ft.Colors.WHITE
                )
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            padding=ft.padding.only(bottom=20)
        )
    
    def _create_search_bar(self) -> ft.Container:
        """Crea la barra di ricerca"""
        return ft.Container(
            content=ft.Row([
                self.search_field,
                
                ft.IconButton(
                    icon=ft.Icons.FILTER_LIST,
                    tooltip="Filtri avanzati",
                    on_click=lambda _: self._show_filters()
                ),
                
                ft.IconButton(
                    icon=ft.Icons.REFRESH,
                    tooltip="Aggiorna",
                    on_click=lambda _: self.refresh_data()
                )
            ], spacing=8),
            padding=ft.padding.only(bottom=16)
        )
    
    def _create_clients_list(self) -> ft.Container:
        """Crea la lista dei clienti"""
        if not self.filtered_clients:
            return ft.Container(
                content=ft.Column([
                    ft.Icon(
                        ft.Icons.PEOPLE_OUTLINE,
                        size=48,
                        color=ft.Colors.GREY_400
                    ),
                    ft.Text(
                        "Nessun cliente trovato" if self.search_query else "Nessun cliente presente",
                        size=16,
                        color=ft.Colors.GREY_500,
                        weight=ft.FontWeight.W_500
                    ),
                    ft.Text(
                        "Prova a modificare i criteri di ricerca" if self.search_query else "Aggiungi il primo cliente",
                        size=12,
                        color=ft.Colors.GREY_400
                    )
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=8),
                padding=ft.padding.all(40),
                alignment=ft.alignment.center,
                expand=True
            )
        
        # Crea gli elementi della lista
        client_items = []
        for client in self.filtered_clients:
            client_items.append(self._create_client_item(client))
        
        return ft.Container(
            content=ft.Column(
                controls=client_items,
                spacing=8,
                scroll=ft.ScrollMode.AUTO
            ),
            expand=True
        )
    
    def _create_client_item(self, client: Client) -> ft.Container:
        """Crea un elemento cliente"""
        # Calcola progetti attivi
        projects = self.app.db.get_projects_by_client(client.id)
        active_projects = len([p for p in projects if p.status == ProjectStatus.IN_PROGRESS])
        
        return ft.Container(
            content=ft.Row([
                # Avatar/Icona
                ft.Container(
                    content=ft.Text(
                        client.name[0].upper(),
                        size=16,
                        color=ft.Colors.WHITE,
                        weight=ft.FontWeight.BOLD
                    ),
                    width=40,
                    height=40,
                    bgcolor=ft.Colors.BLUE_600,
                    border_radius=20,
                    alignment=ft.alignment.center
                ),
                
                # Informazioni principali
                ft.Column([
                    ft.Text(
                        client.name,
                        size=14,
                        weight=ft.FontWeight.W_500,
                        color=ft.Colors.GREY_800
                    ),
                    ft.Row([
                        ft.Text(
                            client.email,
                            size=11,
                            color=ft.Colors.GREY_500
                        ),
                        ft.Text(
                            "•",
                            size=11,
                            color=ft.Colors.GREY_300
                        ),
                        ft.Text(
                            client.phone or "N/A",
                            size=11,
                            color=ft.Colors.GREY_500
                        )
                    ], spacing=4) if client.phone else ft.Text(
                        client.email,
                        size=11,
                        color=ft.Colors.GREY_500
                    )
                ], spacing=4, expand=True),
                
                # Statistiche
                ft.Column([
                    ft.Text(
                        f"{active_projects} progetti attivi",
                        size=11,
                        color=ft.Colors.BLUE_600,
                        weight=ft.FontWeight.W_500
                    ),
                    ft.Text(
                        f"{len(projects)} totali",
                        size=10,
                        color=ft.Colors.GREY_400
                    )
                ], spacing=2, horizontal_alignment=ft.CrossAxisAlignment.END),
                
                # Azioni
                ft.Row([
                    ft.IconButton(
                        icon=ft.Icons.VISIBILITY,
                        icon_size=16,
                        tooltip="Visualizza",
                        on_click=lambda _, c=client: self._view_client(c)
                    ),
                    ft.IconButton(
                        icon=ft.Icons.EDIT,
                        icon_size=16,
                        tooltip="Modifica",
                        on_click=lambda _, c=client: self._edit_client(c)
                    ),
                    ft.IconButton(
                        icon=ft.Icons.DELETE,
                        icon_size=16,
                        tooltip="Elimina",
                        on_click=lambda _, c=client: self._delete_client(c)
                    )
                ], spacing=0)
            ], spacing=12),
            padding=ft.padding.all(16),
            margin=ft.margin.symmetric(vertical=2),
            bgcolor=ft.Colors.WHITE,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.GREY_200),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=2,
                color=ft.Colors.BLACK12
            )
        )
    
    def _show_client_form(self, client: Optional[Client] = None):
        """Mostra il form per creare/modificare un cliente"""
        self.selected_client = client
        self.show_form = True
        self.refresh()
        logger.info(f"Mostra form cliente: {'modifica' if client else 'nuovo'}")
    
    def _view_client(self, client: Client):
        """Visualizza i dettagli di un cliente"""
        try:
            logger.info(f"Visualizza dettagli cliente: {client.name}")
            
            # Use the new navigation system for detail views
            self.app.main_layout.navigate_to_detail("client_detail", str(client.id))
            
            # Crea e mostra la vista dettagli
            from .client_detail import ClientDetailView
            detail_view = ClientDetailView(self.app, client.id)
            
            # Aggiorna il content area del layout principale
            self.app.main_layout.content_area.content = detail_view.build()
            self.app.main_layout.page.update()
            
        except Exception as e:
            logger.error(f"Errore visualizzazione cliente: {e}")
            # Mostra dialog di errore
            error_dialog = ft.AlertDialog(
                modal=True,
                title=ft.Text("Errore"),
                content=ft.Text(f"Impossibile aprire i dettagli del cliente: {str(e)}"),
                actions=[
                    ft.TextButton("OK", on_click=lambda _: self.page.close(error_dialog))
                ],
                actions_alignment=ft.MainAxisAlignment.END,
            )
            self.page.open(error_dialog)
    
    def _edit_client(self, client: Client):
        """Modifica un cliente"""
        self._show_client_form(client)
    
    def _delete_client(self, client: Client):
        """Elimina un cliente"""
        def confirm_delete(e):
            # Close confirmation dialog first
            self.page.close(dialog)
            
            if self.app.db.delete_client(client.id):
                # Show success dialog
                success_dialog = ft.AlertDialog(
                    title=ft.Text("Successo", color=ft.Colors.GREEN),
                    content=ft.Text(f"Cliente {client.name} eliminato con successo!"),
                    actions=[
                        ft.TextButton(
                            "OK",
                            on_click=lambda _: self._handle_delete_success_dialog_close(success_dialog)
                        )
                    ]
                )
                self.page.open(success_dialog)
            else:
                # Show error dialog
                error_dialog = ft.AlertDialog(
                    title=ft.Text("Errore", color=ft.Colors.RED),
                    content=ft.Text("Errore durante l'eliminazione del cliente"),
                    actions=[
                        ft.TextButton(
                            "OK",
                            on_click=lambda _: self.page.close(error_dialog)
                        )
                    ]
                )
                self.page.open(error_dialog)
        
        def cancel_delete(e):
            self.page.close(dialog)
        
        dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text("Conferma eliminazione"),
            content=ft.Text(f"Sei sicuro di voler eliminare il cliente '{client.name}'?\n\nQuesta azione non può essere annullata."),
            actions=[
                ft.TextButton("Annulla", on_click=cancel_delete),
                ft.TextButton(
                    "Elimina", 
                    on_click=confirm_delete,
                    style=ft.ButtonStyle(color=ft.Colors.RED)
                ),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        self.page.open(dialog)
    
    def _show_filters(self):
        """Mostra i filtri avanzati"""
        # TODO: Implementare filtri avanzati
        logger.info("Mostra filtri avanzati")
    
    def _update_clients_list(self):
        """Aggiorna la lista dei clienti ricostruendo la UI"""
        # Ricostruisce la vista per riflettere i cambiamenti nei dati
        if hasattr(self, 'page') and self.page:
            # Aggiorna il content area con la nuova build
            if hasattr(self.app, 'main_layout') and self.app.main_layout:
                self.app.main_layout.content_area.content = self.build()
                self.page.update()
    
    def build(self) -> ft.Container:
        """Costruisce la vista clienti"""
        if self.show_form:
            return self._build_client_form()
        
        return ft.Container(
            content=ft.Column([
                # Header
                self._create_header(),
                
                # Barra di ricerca
                self._create_search_bar(),
                
                # Lista clienti
                self._create_clients_list()
            ], spacing=0),
            padding=ft.padding.all(20),
            expand=True
        )
    
    def _build_client_form(self) -> ft.Container:
        """Costruisce il form per cliente con UI moderna"""
        is_edit = self.selected_client is not None
        title = "Modifica Cliente" if is_edit else "Nuovo Cliente"

        # Stile comune per i campi
        field_style = {
            "bgcolor": ft.Colors.WHITE,
            "border_color": ft.Colors.GREY_300,
            "border_radius": 8,
            "content_padding": ft.padding.symmetric(horizontal=12, vertical=8),
            "text_style": ft.TextStyle(size=14)
        }

        # Campi del form - Informazioni base
        name_field = ft.TextField(
            label="🏢 Nome/Ragione Sociale *",
            value=self.selected_client.name if is_edit else "",
            expand=True,
            **field_style
        )

        business_name_field = ft.TextField(
            label="🏪 Denominazione Commerciale",
            value=self.selected_client.business_name if is_edit else "",
            expand=True,
            **field_style
        )

        email_field = ft.TextField(
            label="📧 Email *",
            value=self.selected_client.email if is_edit else "",
            expand=True,
            **field_style
        )

        phone_field = ft.TextField(
            label="📞 Telefono",
            value=self.selected_client.phone if is_edit else "",
            expand=True,
            **field_style
        )

        # Campi indirizzo dettagliato
        street_field = ft.TextField(
            label="🛣️ Via/Strada",
            value=self.selected_client.street if is_edit else "",
            expand=True,
            **field_style
        )

        civic_number_field = ft.TextField(
            label="🏠 Numero Civico",
            value=self.selected_client.civic_number if is_edit else "",
            expand=True,
            **field_style
        )

        city_field = ft.TextField(
            label="🏙️ Città",
            value=self.selected_client.city if is_edit else "",
            expand=True,
            **field_style
        )

        commune_field = ft.TextField(
            label="🏘️ Comune",
            value=self.selected_client.commune if is_edit else "",
            expand=True,
            **field_style
        )

        province_field = ft.TextField(
            label="🗺️ Provincia",
            value=self.selected_client.province if is_edit else "",
            expand=True,
            **field_style
        )

        province_code_field = ft.TextField(
            label="📍 Codice Provincia",
            value=self.selected_client.province_code if is_edit else "",
            expand=True,
            **field_style
        )

        postal_code_field = ft.TextField(
            label="📮 CAP",
            value=self.selected_client.postal_code if is_edit else "",
            expand=True,
            **field_style
        )

        address_field = ft.TextField(
            label="📍 Indirizzo (Legacy)",
            value=self.selected_client.address if is_edit else "",
            multiline=True,
            min_lines=2,
            max_lines=3,
            **field_style
        )

        # Campi fiscali
        vat_field = ft.TextField(
            label="💼 Partita IVA",
            value=self.selected_client.vat_number if is_edit else "",
            expand=True,
            **field_style
        )

        fiscal_field = ft.TextField(
            label="🆔 Codice Fiscale",
            value=self.selected_client.tax_code if is_edit else "",
            expand=True,
            **field_style
        )

        # Campi attività
        activity_code_field = ft.TextField(
            label="🏷️ Codice Attività",
            value=self.selected_client.activity_code if is_edit else "",
            expand=True,
            **field_style
        )

        activity_description_field = ft.TextField(
            label="📝 Descrizione Attività",
            value=self.selected_client.activity_description if is_edit else "",
            multiline=True,
            min_lines=2,
            max_lines=3,
            **field_style
        )

        # Campi rappresentante legale
        legal_rep_name_field = ft.TextField(
            label="👤 Nome Rappresentante Legale",
            value=self.selected_client.legal_rep_name if is_edit else "",
            expand=True,
            **field_style
        )

        legal_rep_surname_field = ft.TextField(
            label="👤 Cognome Rappresentante Legale",
            value=self.selected_client.legal_rep_surname if is_edit else "",
            expand=True,
            **field_style
        )

        legal_rep_tax_code_field = ft.TextField(
            label="🆔 Codice Fiscale Rappresentante",
            value=self.selected_client.legal_rep_tax_code if is_edit else "",
            expand=True,
            **field_style
        )

        legal_rep_birth_date_field = ft.TextField(
            label="📅 Data di Nascita (YYYY-MM-DD)",
            value=self.selected_client.legal_rep_birth_date if is_edit else "",
            expand=True,
            **field_style
        )

        legal_rep_birth_place_field = ft.TextField(
            label="🏥 Luogo di Nascita",
            value=self.selected_client.legal_rep_birth_place if is_edit else "",
            expand=True,
            **field_style
        )

        legal_rep_residence_street_field = ft.TextField(
            label="🏠 Via Residenza",
            value=self.selected_client.legal_rep_residence_street if is_edit else "",
            expand=True,
            **field_style
        )

        legal_rep_residence_civic_field = ft.TextField(
            label="🏠 Numero Civico Residenza",
            value=self.selected_client.legal_rep_residence_civic if is_edit else "",
            expand=True,
            **field_style
        )

        legal_rep_residence_city_field = ft.TextField(
            label="🏙️ Città Residenza",
            value=self.selected_client.legal_rep_residence_city if is_edit else "",
            expand=True,
            **field_style
        )

        legal_rep_residence_province_field = ft.TextField(
            label="🗺️ Provincia Residenza",
            value=self.selected_client.legal_rep_residence_province if is_edit else "",
            expand=True,
            **field_style
        )

        legal_rep_residence_postal_code_field = ft.TextField(
            label="📮 CAP Residenza",
            value=self.selected_client.legal_rep_residence_postal_code if is_edit else "",
            expand=True,
            **field_style
        )

        legal_rep_email_field = ft.TextField(
            label="📧 Email Rappresentante",
            value=self.selected_client.legal_rep_email if is_edit else "",
            expand=True,
            **field_style
        )

        legal_rep_phone_field = ft.TextField(
            label="📞 Telefono Rappresentante",
            value=self.selected_client.legal_rep_phone if is_edit else "",
            expand=True,
            **field_style
        )

        legal_rep_role_field = ft.TextField(
            label="👔 Qualità/Ruolo",
            value=self.selected_client.legal_rep_role if is_edit else "",
            expand=True,
            **field_style
        )

        notes_field = ft.TextField(
            label="📝 Note",
            value=self.selected_client.notes if is_edit else "",
            multiline=True,
            min_lines=3,
            max_lines=5,
            **field_style
        )

        return ft.Container(
            content=ft.Column([
                # Modern Header with gradient background
                ft.Container(
                    content=ft.Row([
                        ft.IconButton(
                            icon=ft.Icons.ARROW_BACK_IOS,
                            on_click=lambda _: self._close_form(),
                            icon_color=ft.Colors.WHITE,
                            bgcolor=ft.Colors.with_opacity(0.2, ft.Colors.WHITE),
                            style=ft.ButtonStyle(
                                shape=ft.CircleBorder()
                            )
                        ),
                        ft.Container(width=12),
                        ft.Column([
                            ft.Text(
                                title,
                                size=26,
                                weight=ft.FontWeight.BOLD,
                                color=ft.Colors.WHITE
                            ),
                            ft.Text(
                                "Gestisci le informazioni complete del cliente",
                                size=14,
                                color=ft.Colors.with_opacity(0.9, ft.Colors.WHITE)
                            )
                        ], spacing=4),
                        ft.Container(expand=True)
                    ]),
                    padding=ft.padding.all(24),
                    gradient=ft.LinearGradient(
                        colors=[ft.Colors.GREEN_600, ft.Colors.GREEN_800],
                        begin=ft.alignment.top_left,
                        end=ft.alignment.bottom_right
                    ),
                    border_radius=ft.border_radius.only(top_left=16, top_right=16),
                    margin=ft.margin.only(left=-24, right=-24, top=-24)
                ),

                # Scrollable form content with modern card-based layout
                ft.Container(
                    content=ft.ListView(
                        controls=[
                            # Basic Information Section
                            ft.Container(
                                content=ft.Column([
                                    ft.Row([
                                        ft.Icon(ft.Icons.BUSINESS, color=ft.Colors.GREEN_600, size=20),
                                        ft.Text(
                                            "Informazioni Base",
                                            size=18,
                                            weight=ft.FontWeight.W_600,
                                            color=ft.Colors.GREY_800
                                        )
                                    ], spacing=8),
                                    ft.Container(height=16),
                                    name_field,
                                    ft.Container(height=12),
                                    business_name_field,
                                    ft.Container(height=12),
                                    ft.Row([
                                        email_field,
                                        phone_field
                                    ], spacing=16)
                                ]),
                                bgcolor=ft.Colors.WHITE,
                                border=ft.border.all(1, ft.Colors.GREY_200),
                                border_radius=16,
                                padding=24,
                                margin=ft.margin.only(bottom=16),
                                shadow=ft.BoxShadow(
                                    spread_radius=0,
                                    blur_radius=4,
                                    color=ft.Colors.with_opacity(0.05, ft.Colors.BLACK),
                                    offset=ft.Offset(0, 2)
                                )
                            ),

                            # Address Section
                            ft.Container(
                                content=ft.Column([
                                    ft.Row([
                                        ft.Icon(ft.Icons.LOCATION_ON, color=ft.Colors.BLUE_600, size=20),
                                        ft.Text(
                                            "Indirizzo Dettagliato",
                                            size=18,
                                            weight=ft.FontWeight.W_600,
                                            color=ft.Colors.GREY_800
                                        )
                                    ], spacing=8),
                                    ft.Container(height=16),
                                    ft.Row([
                                        street_field,
                                        civic_number_field
                                    ], spacing=16),
                                    ft.Container(height=12),
                                    ft.Row([
                                        city_field,
                                        commune_field
                                    ], spacing=16),
                                    ft.Container(height=12),
                                    ft.Row([
                                        province_field,
                                        province_code_field,
                                        postal_code_field
                                    ], spacing=16),
                                    ft.Container(height=12),
                                    address_field
                                ]),
                                bgcolor=ft.Colors.WHITE,
                                border=ft.border.all(1, ft.Colors.GREY_200),
                                border_radius=16,
                                padding=24,
                                margin=ft.margin.only(bottom=16),
                                shadow=ft.BoxShadow(
                                    spread_radius=0,
                                    blur_radius=4,
                                    color=ft.Colors.with_opacity(0.05, ft.Colors.BLACK),
                                    offset=ft.Offset(0, 2)
                                )
                            ),

                            # Fiscal Data Section
                            ft.Container(
                                content=ft.Column([
                                    ft.Row([
                                        ft.Icon(ft.Icons.RECEIPT_LONG, color=ft.Colors.ORANGE_600, size=20),
                                        ft.Text(
                                            "Dati Fiscali",
                                            size=18,
                                            weight=ft.FontWeight.W_600,
                                            color=ft.Colors.GREY_800
                                        )
                                    ], spacing=8),
                                    ft.Container(height=16),
                                    ft.Row([
                                        vat_field,
                                        fiscal_field
                                    ], spacing=16)
                                ]),
                                bgcolor=ft.Colors.WHITE,
                                border=ft.border.all(1, ft.Colors.GREY_200),
                                border_radius=16,
                                padding=24,
                                margin=ft.margin.only(bottom=16),
                                shadow=ft.BoxShadow(
                                    spread_radius=0,
                                    blur_radius=4,
                                    color=ft.Colors.with_opacity(0.05, ft.Colors.BLACK),
                                    offset=ft.Offset(0, 2)
                                )
                            ),

                            # Activity Information Section
                            ft.Container(
                                content=ft.Column([
                                    ft.Row([
                                        ft.Icon(ft.Icons.WORK, color=ft.Colors.PURPLE_600, size=20),
                                        ft.Text(
                                            "Informazioni Attività",
                                            size=18,
                                            weight=ft.FontWeight.W_600,
                                            color=ft.Colors.GREY_800
                                        )
                                    ], spacing=8),
                                    ft.Container(height=16),
                                    activity_code_field,
                                    ft.Container(height=12),
                                    activity_description_field
                                ]),
                                bgcolor=ft.Colors.WHITE,
                                border=ft.border.all(1, ft.Colors.GREY_200),
                                border_radius=16,
                                padding=24,
                                margin=ft.margin.only(bottom=16),
                                shadow=ft.BoxShadow(
                                    spread_radius=0,
                                    blur_radius=4,
                                    color=ft.Colors.with_opacity(0.05, ft.Colors.BLACK),
                                    offset=ft.Offset(0, 2)
                                )
                            ),

                            # Legal Representative Section
                            ft.Container(
                                content=ft.Column([
                                    ft.Row([
                                        ft.Icon(ft.Icons.PERSON, color=ft.Colors.INDIGO_600, size=20),
                                        ft.Text(
                                            "Rappresentante Legale",
                                            size=18,
                                            weight=ft.FontWeight.W_600,
                                            color=ft.Colors.GREY_800
                                        )
                                    ], spacing=8),
                                    ft.Container(height=16),
                                    ft.Row([
                                        legal_rep_name_field,
                                        legal_rep_surname_field
                                    ], spacing=16),
                                    ft.Container(height=12),
                                    ft.Row([
                                        legal_rep_tax_code_field,
                                        legal_rep_role_field
                                    ], spacing=16),
                                    ft.Container(height=12),
                                    ft.Row([
                                        legal_rep_birth_date_field,
                                        legal_rep_birth_place_field
                                    ], spacing=16),
                                    ft.Container(height=12),
                                    ft.Row([
                                        legal_rep_email_field,
                                        legal_rep_phone_field
                                    ], spacing=16),
                                    ft.Container(height=16),
                                    ft.Text(
                                        "🏠 Indirizzo Residenza Rappresentante",
                                        size=16,
                                        weight=ft.FontWeight.W_500,
                                        color=ft.Colors.GREY_700
                                    ),
                                    ft.Container(height=8),
                                    ft.Row([
                                        legal_rep_residence_street_field,
                                        legal_rep_residence_civic_field
                                    ], spacing=16),
                                    ft.Container(height=12),
                                    ft.Row([
                                        legal_rep_residence_city_field,
                                        legal_rep_residence_province_field,
                                        legal_rep_residence_postal_code_field
                                    ], spacing=16)
                                ]),
                                bgcolor=ft.Colors.WHITE,
                                border=ft.border.all(1, ft.Colors.GREY_200),
                                border_radius=16,
                                padding=24,
                                margin=ft.margin.only(bottom=16),
                                shadow=ft.BoxShadow(
                                    spread_radius=0,
                                    blur_radius=4,
                                    color=ft.Colors.with_opacity(0.05, ft.Colors.BLACK),
                                    offset=ft.Offset(0, 2)
                                )
                            ),

                            # Notes Section
                            ft.Container(
                                content=ft.Column([
                                    ft.Row([
                                        ft.Icon(ft.Icons.NOTE_ALT, color=ft.Colors.TEAL_600, size=20),
                                        ft.Text(
                                            "Note",
                                            size=18,
                                            weight=ft.FontWeight.W_600,
                                            color=ft.Colors.GREY_800
                                        )
                                    ], spacing=8),
                                    ft.Container(height=16),
                                    notes_field,
                                    ft.Container(height=8),
                                    ft.Text(
                                        "💡 Aggiungi note aggiuntive o informazioni specifiche del cliente",
                                        size=12,
                                        color=ft.Colors.GREY_600,
                                        italic=True
                                    )
                                ]),
                                bgcolor=ft.Colors.WHITE,
                                border=ft.border.all(1, ft.Colors.GREY_200),
                                border_radius=16,
                                padding=24,
                                margin=ft.margin.only(bottom=24),
                                shadow=ft.BoxShadow(
                                    spread_radius=0,
                                    blur_radius=4,
                                    color=ft.Colors.with_opacity(0.05, ft.Colors.BLACK),
                                    offset=ft.Offset(0, 2)
                                )
                            ),

                            # Action Buttons with modern styling
                            ft.Container(
                                content=ft.Row([
                                    ft.OutlinedButton(
                                        content=ft.Row([
                                            ft.Icon(ft.Icons.CLOSE, size=18),
                                            ft.Text("Annulla", size=14, weight=ft.FontWeight.W_500)
                                        ], spacing=8, alignment=ft.MainAxisAlignment.CENTER),
                                        on_click=lambda _: self._close_form(),
                                        style=ft.ButtonStyle(
                                            color=ft.Colors.GREY_700,
                                            side=ft.BorderSide(2, ft.Colors.GREY_300),
                                            padding=ft.padding.symmetric(horizontal=32, vertical=16),
                                            shape=ft.RoundedRectangleBorder(radius=12)
                                        ),
                                        height=56,
                                        width=140
                                    ),
                                    ft.Container(expand=True),
                                    ft.ElevatedButton(
                                        content=ft.Row([
                                            ft.Icon(
                                                ft.Icons.SAVE if not is_edit else ft.Icons.UPDATE,
                                                size=18,
                                                color=ft.Colors.WHITE
                                            ),
                                            ft.Text(
                                                "Salva Cliente" if not is_edit else "Aggiorna Cliente",
                                                size=14,
                                                weight=ft.FontWeight.W_600,
                                                color=ft.Colors.WHITE
                                            )
                                        ], spacing=8, alignment=ft.MainAxisAlignment.CENTER),
                                        on_click=lambda _: self._save_client({
                                            'name': name_field.value,
                                            'business_name': business_name_field.value,
                                            'email': email_field.value,
                                            'phone': phone_field.value,
                                            'street': street_field.value,
                                            'civic_number': civic_number_field.value,
                                            'city': city_field.value,
                                            'commune': commune_field.value,
                                            'province': province_field.value,
                                            'province_code': province_code_field.value,
                                            'postal_code': postal_code_field.value,
                                            'address': address_field.value,
                                            'vat_number': vat_field.value,
                                            'tax_code': fiscal_field.value,
                                            'activity_code': activity_code_field.value,
                                            'activity_description': activity_description_field.value,
                                            'legal_rep_name': legal_rep_name_field.value,
                                            'legal_rep_surname': legal_rep_surname_field.value,
                                            'legal_rep_tax_code': legal_rep_tax_code_field.value,
                                            'legal_rep_birth_date': legal_rep_birth_date_field.value,
                                            'legal_rep_birth_place': legal_rep_birth_place_field.value,
                                            'legal_rep_residence_street': legal_rep_residence_street_field.value,
                                            'legal_rep_residence_civic': legal_rep_residence_civic_field.value,
                                            'legal_rep_residence_city': legal_rep_residence_city_field.value,
                                            'legal_rep_residence_province': legal_rep_residence_province_field.value,
                                            'legal_rep_residence_postal_code': legal_rep_residence_postal_code_field.value,
                                            'legal_rep_email': legal_rep_email_field.value,
                                            'legal_rep_phone': legal_rep_phone_field.value,
                                            'legal_rep_role': legal_rep_role_field.value,
                                            'notes': notes_field.value
                                        }),
                                        style=ft.ButtonStyle(
                                            bgcolor=ft.Colors.GREEN_600,
                                            color=ft.Colors.WHITE,
                                            padding=ft.padding.symmetric(horizontal=32, vertical=16),
                                            elevation=4,
                                            shadow_color=ft.Colors.GREEN_200,
                                            shape=ft.RoundedRectangleBorder(radius=12)
                                        ),
                                        height=56,
                                        width=180
                                    )
                                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                                padding=ft.padding.all(24),
                                bgcolor=ft.Colors.WHITE,
                                border=ft.border.only(top=ft.BorderSide(1, ft.Colors.GREY_200)),
                                margin=ft.margin.only(left=-24, right=-24, bottom=-24)
                            )
                        ],
                        spacing=0,
                        padding=ft.padding.all(24),
                        auto_scroll=False
                    ),
                    expand=True
                )
            ]),
            bgcolor=ft.Colors.GREY_50,
            border_radius=16,
            border=ft.border.all(1, ft.Colors.GREY_200),
            padding=24,
            shadow=ft.BoxShadow(
                spread_radius=2,
                blur_radius=20,
                color=ft.Colors.with_opacity(0.15, ft.Colors.BLACK),
                offset=ft.Offset(0, 8)
            )
        )
    
    def _close_form(self):
        """Chiude il form"""
        self.show_form = False
        self.selected_client = None
        self.refresh()
    
    def _save_client(self, data: Dict[str, str]):
        """Salva il cliente"""
        try:
            # Validazione base
            if not data['name']:
                self.app.main_layout.show_notification("Il nome è obbligatorio", "error")
                return
            
            # Controllo duplicati per codice fiscale e partita IVA
            exclude_id = self.selected_client.id if self.selected_client else None
            
            if data['tax_code'] or data['vat_number']:
                if self.app.db.check_client_exists(
                    fiscal_code=data['tax_code'] if data['tax_code'] else None,
                    vat_number=data['vat_number'] if data['vat_number'] else None,
                    exclude_id=exclude_id
                ):  
                    # Mostra dialog di errore per duplicato
                    error_dialog = ft.AlertDialog(
                        title=ft.Text("Errore", color=ft.Colors.RED),
                        content=ft.Text("Esiste già un cliente con lo stesso codice fiscale o partita IVA"),
                        actions=[
                            ft.TextButton(
                                "OK",
                                on_click=lambda _: self.page.close(error_dialog)
                            )
                        ]
                    )
                    self.page.open(error_dialog)
                    return
            
            if self.selected_client:
                # Modifica cliente esistente
                self.selected_client.name = data['name']
                self.selected_client.business_name = data.get('business_name')
                self.selected_client.email = data['email']
                self.selected_client.phone = data['phone']
                self.selected_client.street = data.get('street')
                self.selected_client.civic_number = data.get('civic_number')
                self.selected_client.city = data.get('city')
                self.selected_client.commune = data.get('commune')
                self.selected_client.province = data.get('province')
                self.selected_client.province_code = data.get('province_code')
                self.selected_client.postal_code = data.get('postal_code')
                self.selected_client.address = data['address']
                self.selected_client.vat_number = data['vat_number']
                self.selected_client.tax_code = data['tax_code']
                self.selected_client.activity_code = data.get('activity_code')
                self.selected_client.activity_description = data.get('activity_description')
                self.selected_client.legal_rep_name = data.get('legal_rep_name')
                self.selected_client.legal_rep_surname = data.get('legal_rep_surname')
                self.selected_client.legal_rep_tax_code = data.get('legal_rep_tax_code')
                self.selected_client.legal_rep_birth_date = data.get('legal_rep_birth_date')
                self.selected_client.legal_rep_birth_place = data.get('legal_rep_birth_place')
                self.selected_client.legal_rep_residence_street = data.get('legal_rep_residence_street')
                self.selected_client.legal_rep_residence_civic = data.get('legal_rep_residence_civic')
                self.selected_client.legal_rep_residence_city = data.get('legal_rep_residence_city')
                self.selected_client.legal_rep_residence_province = data.get('legal_rep_residence_province')
                self.selected_client.legal_rep_residence_postal_code = data.get('legal_rep_residence_postal_code')
                self.selected_client.legal_rep_email = data.get('legal_rep_email')
                self.selected_client.legal_rep_phone = data.get('legal_rep_phone')
                self.selected_client.legal_rep_role = data.get('legal_rep_role')
                self.selected_client.notes = data['notes']
                self.selected_client.updated_at = datetime.now()
                
                success = self.app.db.update_client(self.selected_client)
                action = "modificato"
            else:
                # Crea nuovo cliente
                new_client = Client(
                    name=data['name'],
                    business_name=data.get('business_name'),
                    email=data['email'],
                    phone=data['phone'],
                    street=data.get('street'),
                    civic_number=data.get('civic_number'),
                    city=data.get('city'),
                    commune=data.get('commune'),
                    province=data.get('province'),
                    province_code=data.get('province_code'),
                    postal_code=data.get('postal_code'),
                    address=data['address'],
                    vat_number=data['vat_number'],
                    tax_code=data['tax_code'],
                    activity_code=data.get('activity_code'),
                    activity_description=data.get('activity_description'),
                    legal_rep_name=data.get('legal_rep_name'),
                    legal_rep_surname=data.get('legal_rep_surname'),
                    legal_rep_tax_code=data.get('legal_rep_tax_code'),
                    legal_rep_birth_date=data.get('legal_rep_birth_date'),
                    legal_rep_birth_place=data.get('legal_rep_birth_place'),
                    legal_rep_residence_street=data.get('legal_rep_residence_street'),
                    legal_rep_residence_civic=data.get('legal_rep_residence_civic'),
                    legal_rep_residence_city=data.get('legal_rep_residence_city'),
                    legal_rep_residence_province=data.get('legal_rep_residence_province'),
                    legal_rep_residence_postal_code=data.get('legal_rep_residence_postal_code'),
                    legal_rep_email=data.get('legal_rep_email'),
                    legal_rep_phone=data.get('legal_rep_phone'),
                    legal_rep_role=data.get('legal_rep_role'),
                    notes=data['notes']
                )
                
                success = self.app.db.create_client(new_client)
                action = "creato"
            
            if success:
                logger.info(f"Cliente {action} con successo")
                # Mostra dialog di successo
                success_dialog = ft.AlertDialog(
                    title=ft.Text("Successo", color=ft.Colors.GREEN),
                    content=ft.Text(f"Cliente {action} con successo!"),
                    actions=[
                        ft.TextButton(
                            "OK",
                            on_click=lambda _: self._handle_success_dialog_close(success_dialog)
                        )
                    ]
                )
                self.page.open(success_dialog)
            else:
                logger.error(f"Errore durante il salvataggio del cliente")
                # Mostra dialog di errore
                error_dialog = ft.AlertDialog(
                    title=ft.Text("Errore", color=ft.Colors.RED),
                    content=ft.Text("Errore durante il salvataggio del cliente"),
                    actions=[
                        ft.TextButton(
                            "OK",
                            on_click=lambda _: self.page.close(error_dialog)
                        )
                    ]
                )
                self.page.open(error_dialog)
                
        except Exception as e:
            logger.error(f"Errore salvataggio cliente: {e}")
            # Mostra dialog di errore per eccezione
            error_dialog = ft.AlertDialog(
                title=ft.Text("Errore", color=ft.Colors.RED),
                content=ft.Text(f"Errore durante il salvataggio: {str(e)}"),
                actions=[
                    ft.TextButton(
                        "OK",
                        on_click=lambda _: self.page.close(error_dialog)
                    )
                ]
            )
            self.page.open(error_dialog)
    
    def _handle_success_dialog_close(self, dialog):
        """Gestisce la chiusura del dialog di successo per salvataggio"""
        self.page.close(dialog)
        self._close_form()
        self.refresh_data()
        # Navigate to clients view
        self.app.main_layout._navigate_to("clients")
    
    def _handle_delete_success_dialog_close(self, dialog):
        """Gestisce la chiusura del dialog di successo per eliminazione"""
        self.page.close(dialog)
        self.refresh_data()
    
    def refresh_data(self):
        """Aggiorna i dati dei clienti"""
        try:
            self.clients_data = self.app.db.get_all_clients()
            self._filter_clients()
            logger.info(f"Caricati {len(self.clients_data)} clienti")
        except Exception as e:
            logger.error(f"Errore caricamento clienti: {e}")
            self.clients_data = []
    
    def refresh(self):
        """Aggiorna la vista clienti"""
        self.refresh_data()
    
    def cleanup(self):
        """Pulisce le risorse quando la vista viene distrutta"""
        if self.search_timer:
            self.search_timer.cancel()
            self.search_timer = None

    def cancel_current_form(self):
        """Cancel current form - called by keyboard shortcuts"""
        if self.show_form:
            self._close_form()
            logger.info("Form cancelled via keyboard shortcut")

    def save_current_form(self):
        """Save current form - called by keyboard shortcuts"""
        if self.show_form:
            # We need to extract the form data from the currently built form
            # This is a bit tricky since the form fields are created in _build_client_form
            # For now, we'll show a message that Ctrl+S requires clicking the save button
            logger.info("Save shortcut triggered in client form")
            if hasattr(self, 'app') and hasattr(self.app, 'main_layout'):
                self.app.main_layout._show_shortcut_feedback("Use the Save button in the form or press Ctrl+S while focused on a field")