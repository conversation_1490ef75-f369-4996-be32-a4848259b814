#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Background Service Manager for Agevolami PM
Handles initialization and management of Google services in background threads
"""

import threading
import time
from typing import Dict, Optional, Callable, Any
from enum import Enum
from dataclasses import dataclass
from ..utils import get_logger

logger = get_logger(__name__)

class ServiceStatus(Enum):
    """Service initialization status"""
    NOT_STARTED = "not_started"
    INITIALIZING = "initializing"
    READY = "ready"
    ERROR = "error"
    DISABLED = "disabled"

@dataclass
class ServiceInfo:
    """Information about a background service"""
    status: ServiceStatus
    service_instance: Optional[Any] = None
    error_message: Optional[str] = None
    initialized_at: Optional[float] = None
    initialization_time: Optional[float] = None

class BackgroundServiceManager:
    """Manages background initialization of services"""
    
    def __init__(self):
        self._services: Dict[str, ServiceInfo] = {}
        self._lock = threading.RLock()
        self._callbacks: Dict[str, list] = {}  # Service name -> list of callbacks
        
    def register_service(self, name: str, initializer: Callable[[], Any], 
                        on_ready: Optional[Callable[[Any], None]] = None) -> None:
        """Register a service for background initialization"""
        with self._lock:
            if name in self._services:
                logger.warning(f"Service {name} already registered, skipping")
                return
            
            self._services[name] = ServiceInfo(status=ServiceStatus.NOT_STARTED)
            self._callbacks[name] = []
            
            if on_ready:
                self._callbacks[name].append(on_ready)
            
            # Start initialization in background thread
            thread = threading.Thread(
                target=self._initialize_service,
                args=(name, initializer),
                daemon=True,
                name=f"ServiceInit-{name}"
            )
            thread.start()
            
            logger.info(f"Started background initialization for service: {name}")
    
    def _initialize_service(self, name: str, initializer: Callable[[], Any]) -> None:
        """Initialize a service in background thread"""
        start_time = time.time()
        
        with self._lock:
            self._services[name].status = ServiceStatus.INITIALIZING
        
        try:
            logger.info(f"Initializing service: {name}")
            service_instance = initializer()
            
            initialization_time = time.time() - start_time
            
            with self._lock:
                self._services[name].status = ServiceStatus.READY
                self._services[name].service_instance = service_instance
                self._services[name].initialized_at = time.time()
                self._services[name].initialization_time = initialization_time
            
            logger.info(f"Service {name} initialized successfully in {initialization_time:.2f}s")
            
            # Call ready callbacks
            callbacks = self._callbacks.get(name, [])
            for callback in callbacks:
                try:
                    callback(service_instance)
                except Exception as e:
                    logger.error(f"Error in callback for service {name}: {e}")
                    
        except Exception as e:
            error_msg = f"Failed to initialize service {name}: {e}"
            logger.error(error_msg)
            
            with self._lock:
                self._services[name].status = ServiceStatus.ERROR
                self._services[name].error_message = error_msg
    
    def get_service(self, name: str) -> Optional[Any]:
        """Get service instance if ready, None otherwise"""
        with self._lock:
            service_info = self._services.get(name)
            if service_info and service_info.status == ServiceStatus.READY:
                return service_info.service_instance
            return None
    
    def get_service_status(self, name: str) -> ServiceStatus:
        """Get service initialization status"""
        with self._lock:
            service_info = self._services.get(name)
            return service_info.status if service_info else ServiceStatus.NOT_STARTED
    
    def is_service_ready(self, name: str) -> bool:
        """Check if service is ready"""
        return self.get_service_status(name) == ServiceStatus.READY
    
    def wait_for_service(self, name: str, timeout: float = 30.0) -> Optional[Any]:
        """Wait for service to be ready with timeout"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            service = self.get_service(name)
            if service is not None:
                return service
            
            status = self.get_service_status(name)
            if status == ServiceStatus.ERROR:
                logger.error(f"Service {name} failed to initialize")
                return None
            
            time.sleep(0.1)  # Check every 100ms
        
        logger.warning(f"Timeout waiting for service {name}")
        return None
    
    def add_ready_callback(self, name: str, callback: Callable[[Any], None]) -> None:
        """Add callback to be called when service is ready"""
        with self._lock:
            if name not in self._callbacks:
                self._callbacks[name] = []
            
            # If service is already ready, call callback immediately
            service_info = self._services.get(name)
            if service_info and service_info.status == ServiceStatus.READY:
                try:
                    callback(service_info.service_instance)
                except Exception as e:
                    logger.error(f"Error in immediate callback for service {name}: {e}")
            else:
                self._callbacks[name].append(callback)
    
    def get_all_services_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all registered services"""
        with self._lock:
            result = {}
            for name, info in self._services.items():
                result[name] = {
                    "status": info.status.value,
                    "error_message": info.error_message,
                    "initialized_at": info.initialized_at,
                    "initialization_time": info.initialization_time,
                    "is_ready": info.status == ServiceStatus.READY
                }
            return result
    
    def mark_service_disabled(self, name: str, reason: str = "Service disabled") -> None:
        """Mark a service as disabled (e.g., when dependencies are missing)"""
        with self._lock:
            if name not in self._services:
                self._services[name] = ServiceInfo(status=ServiceStatus.DISABLED)
            else:
                self._services[name].status = ServiceStatus.DISABLED
            self._services[name].error_message = reason
            
        logger.info(f"Service {name} marked as disabled: {reason}")

# Global background service manager
_service_manager = None
_manager_lock = threading.Lock()

def get_service_manager() -> BackgroundServiceManager:
    """Get global service manager instance (singleton)"""
    global _service_manager
    if _service_manager is None:
        with _manager_lock:
            if _service_manager is None:
                _service_manager = BackgroundServiceManager()
    return _service_manager

def register_google_services(app_instance) -> None:
    """Register Google services for background initialization"""
    manager = get_service_manager()
    
    # Check if Google libraries are available
    try:
        from google.auth.transport.requests import Request
        google_available = True
    except ImportError:
        google_available = False
        logger.warning("Google libraries not available")
    
    if not google_available:
        manager.mark_service_disabled("google_calendar", "Google libraries not installed")
        manager.mark_service_disabled("google_tasks", "Google libraries not installed")
        manager.mark_service_disabled("google_drive", "Google libraries not installed")
        return
    
    # Get config directory - ensure consistent path structure
    config_dir = "config"
    if hasattr(app_instance, 'config') and hasattr(app_instance.config, 'data_dir'):
        config_dir = str(app_instance.config.data_dir)

    # Register Google Calendar service
    def init_google_calendar():
        from .google_calendar_service import GoogleCalendarService
        return GoogleCalendarService(config_dir)

    manager.register_service("google_calendar", init_google_calendar)

    # Register Google Tasks service
    def init_google_tasks():
        from .google_tasks_service import GoogleTasksService
        return GoogleTasksService(config_dir)

    manager.register_service("google_tasks", init_google_tasks)

    # Register Google Drive service
    def init_google_drive():
        from .google_drive_service import GoogleDriveService
        from pathlib import Path
        return GoogleDriveService(Path(config_dir))

    manager.register_service("google_drive", init_google_drive)
    
    manager.register_service("google_drive", init_google_drive)
    
    logger.info("Google services registered for background initialization")

def get_google_calendar_service() -> Optional[Any]:
    """Get Google Calendar service if ready"""
    return get_service_manager().get_service("google_calendar")

def get_google_tasks_service() -> Optional[Any]:
    """Get Google Tasks service if ready"""
    return get_service_manager().get_service("google_tasks")

def get_google_drive_service() -> Optional[Any]:
    """Get Google Drive service if ready"""
    return get_service_manager().get_service("google_drive")
