#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Demo Settings View with Working Search
Demonstrates the search functionality in settings
"""

import flet as ft
from typing import Dict, List, Any
from core import get_logger

logger = get_logger(__name__)

class DemoSettingsWithSearch:
    """Demo settings view that shows working search functionality"""
    
    def __init__(self, app_instance):
        self.app = app_instance
        self.search_query = ""
        self.filtered_sections = []
        
        # Demo sections with searchable content
        self.sections = {
            'email': {
                'title': '📧 Configurazione Email',
                'description': 'Impostazioni server SMTP per invio email e notifiche',
                'keywords': ['smtp', 'mail', 'email', 'server', 'port', 'tls', 'password', 'username', 'mittente'],
                'content': 'Configura il server SMTP per l\'invio di email automatiche, report e notifiche.'
            },
            'notifications': {
                'title': '🔔 Notifiche',
                'description': 'Gestione alert e promemoria per scadenze e task',
                'keywords': ['alert', 'notification', 'remind', 'schedule', 'deadline', 'scadenza', 'promemoria'],
                'content': 'Imposta notifiche per scadenze, task in ritardo e promemoria automatici.'
            },
            'google_services': {
                'title': '🔗 Servizi Google',
                'description': 'Integrazione con Google Drive, Calendar e Tasks',
                'keywords': ['google', 'drive', 'calendar', 'tasks', 'cloud', 'sync', 'backup'],
                'content': 'Connetti i servizi Google per backup automatici e sincronizzazione.'
            },
            'reports': {
                'title': '📊 Report Automatici',
                'description': 'Configurazione report via email e analisi personalizzate',
                'keywords': ['report', 'analysis', 'schedule', 'export', 'statistics', 'email', 'automatico'],
                'content': 'Configura report automatici con statistiche complete del database.'
            },
            'windows': {
                'title': '🪟 Integrazione Windows',
                'description': 'Impostazioni sistema Windows e notifiche desktop',
                'keywords': ['startup', 'notification', 'system', 'integration', 'windows', 'desktop'],
                'content': 'Integrazione con il sistema Windows per notifiche e avvio automatico.'
            }
        }
        
        self._apply_search_filter()
    
    def _apply_search_filter(self):
        """Apply search filter to sections"""
        if not self.search_query:
            self.filtered_sections = list(self.sections.keys())
            return
        
        query = self.search_query.lower().strip()
        filtered = []
        
        for section_key, section_data in self.sections.items():
            # Check section title
            if query in section_data['title'].lower():
                filtered.append(section_key)
                continue
            
            # Check description
            if query in section_data['description'].lower():
                filtered.append(section_key)
                continue
            
            # Check keywords
            if any(query in keyword for keyword in section_data['keywords']):
                filtered.append(section_key)
                continue
            
            # Check content
            if query in section_data['content'].lower():
                filtered.append(section_key)
                continue
        
        self.filtered_sections = filtered
    
    def _create_search_header(self) -> ft.Container:
        """Create search header"""
        def on_search_change(e):
            self.search_query = e.control.value.lower().strip()
            self._apply_search_filter()
            self._refresh_content()
        
        def clear_search(e):
            self.search_query = ""
            self.search_field.value = ""
            self._apply_search_filter()
            self._refresh_content()
        
        # Create search field
        self.search_field = ft.TextField(
            hint_text="🔍 Cerca nelle impostazioni (email, notifiche, report, google, windows)...",
            width=500,
            on_change=on_search_change,
            suffix=ft.IconButton(
                icon=ft.Icons.CLEAR,
                on_click=clear_search,
                tooltip="Cancella ricerca"
            ) if self.search_query else None,
            border_radius=ft.border_radius.all(25),
            bgcolor=ft.Colors.WHITE,
            border_color=ft.Colors.BLUE_200
        )
        
        return ft.Container(
            content=ft.Column([
                ft.Text(
                    "⚙️ Impostazioni con Ricerca Funzionante",
                    size=24,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.GREY_800
                ),
                ft.Text(
                    "Prova a cercare: email, smtp, notifiche, google, report, windows",
                    size=12,
                    color=ft.Colors.GREY_600
                ),
                ft.Row([
                    self.search_field,
                    ft.Text(
                        f"📊 {len(self.filtered_sections)}/{len(self.sections)} sezioni trovate",
                        size=12,
                        color=ft.Colors.BLUE_600
                    )
                ], spacing=20)
            ], spacing=10),
            padding=ft.padding.all(20),
            bgcolor=ft.Colors.WHITE,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.GREY_200),
            margin=ft.margin.only(bottom=16)
        )
    
    def _create_section_card(self, section_key: str, section_data: Dict[str, Any]) -> ft.Container:
        """Create a section card"""
        # Highlight search terms
        title = section_data['title']
        description = section_data['description']
        content = section_data['content']
        
        if self.search_query:
            # Simple highlighting (in a real app, you'd use proper highlighting)
            query = self.search_query
            if query in title.lower():
                title = title.replace(query, f"**{query}**")
            if query in description.lower():
                description = description.replace(query, f"**{query}**")
        
        return ft.Container(
            content=ft.Column([
                ft.Text(
                    title,
                    size=18,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.GREY_800
                ),
                ft.Text(
                    description,
                    size=14,
                    color=ft.Colors.GREY_600
                ),
                ft.Text(
                    content,
                    size=12,
                    color=ft.Colors.GREY_500
                ),
                ft.Row([
                    ft.Chip(
                        label=ft.Text(keyword, size=10),
                        bgcolor=ft.Colors.BLUE_50,
                        color=ft.Colors.BLUE_600
                    )
                    for keyword in section_data['keywords'][:5]  # Show first 5 keywords
                ], wrap=True, spacing=5)
            ], spacing=8),
            padding=ft.padding.all(16),
            bgcolor=ft.Colors.WHITE,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.GREY_200),
            margin=ft.margin.only(bottom=12)
        )
    
    def _create_content(self) -> List[ft.Control]:
        """Create main content"""
        content = []
        
        if not self.filtered_sections and self.search_query:
            # No results found
            content.append(
                ft.Container(
                    content=ft.Column([
                        ft.Icon(ft.Icons.SEARCH_OFF, size=48, color=ft.Colors.GREY_400),
                        ft.Text(
                            f"Nessun risultato per '{self.search_query}'",
                            size=16,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.GREY_600
                        ),
                        ft.Text(
                            "Prova con termini diversi come: email, notifiche, google, report",
                            size=12,
                            color=ft.Colors.GREY_500
                        )
                    ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=10),
                    padding=ft.padding.all(40),
                    bgcolor=ft.Colors.GREY_50,
                    border_radius=8,
                    alignment=ft.alignment.center
                )
            )
        else:
            # Show filtered sections
            for section_key in self.filtered_sections:
                if section_key in self.sections:
                    section_card = self._create_section_card(section_key, self.sections[section_key])
                    content.append(section_card)
        
        return content
    
    def _refresh_content(self):
        """Refresh content (placeholder for real implementation)"""
        logger.info(f"Search query: '{self.search_query}' - Found {len(self.filtered_sections)} sections")
        # In a real implementation, this would update the UI
        if hasattr(self.app, 'page') and self.app.page:
            self.app.page.update()
    
    def build(self) -> ft.Container:
        """Build the demo settings view"""
        content = self._create_content()
        
        main_content = ft.Column([
            self._create_search_header(),
            *content
        ], spacing=0, scroll=ft.ScrollMode.AUTO)
        
        return ft.Container(
            content=main_content,
            padding=ft.padding.all(20),
            expand=True,
            bgcolor=ft.Colors.GREY_50
        )
