#!/usr/bin/env python3
"""
Test for subtask creation functionality.
This test verifies that subtasks are properly created with the parent_task_id set correctly.
"""

import logging
import sys
import os
from datetime import datetime, date, timedelta
from uuid import uuid4

# Add project root to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

import flet as ft
from src.core.database.database import DatabaseManager
from src.core.models.base_models import Project, Deadline, Task, Priority, TaskStatus

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_test_data(db_manager: DatabaseManager):
    """Create test project, deadline, and parent task"""
    logger.info("Creating test data...")
    
    # Create test project
    project = Project(
        id=uuid4(),
        name="Test Project",
        description="Test project for subtask creation",
        status="active",
        start_date=date.today(),
        end_date=date.today() + timedelta(days=60),
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    db_manager.create_project(project)
    
    # Create test deadline
    deadline = Deadline(
        id=uuid4(),
        title="Test Deadline",
        description="Test deadline for subtask creation",
        due_date=date.today() + timedelta(days=30),
        project_id=project.id,
        priority=Priority.HIGH,
        status="active",
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    db_manager.create_deadline(deadline)
    
    # Create parent task
    parent_task = Task(
        id=uuid4(),
        title="Parent Task for Subtask Testing",
        description="This task will have subtasks",
        priority=Priority.MEDIUM,
        status=TaskStatus.IN_PROGRESS,
        deadline_id=deadline.id,
        project_id=project.id,
        due_date=deadline.due_date - timedelta(days=5),
        estimated_hours=10.0,
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    db_manager.create_task(parent_task)
    
    logger.info(f"Created parent task: {parent_task.title} (ID: {parent_task.id})")
    return project, deadline, parent_task

def verify_subtask_creation():
    """Test subtask creation directly"""
    db_manager = DatabaseManager()
    
    # Create test data
    project, deadline, parent_task = create_test_data(db_manager)
    
    # Create a subtask manually to test
    subtask = Task(
        id=uuid4(),
        title="Test Subtask",
        description="This should be a subtask of the parent task",
        priority=Priority.LOW,
        status=TaskStatus.PENDING,
        deadline_id=deadline.id,
        project_id=project.id,
        parent_task_id=parent_task.id,  # This is the key field
        due_date=parent_task.due_date,
        estimated_hours=2.0,
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    
    # Save the subtask
    success = db_manager.create_task(subtask)
    
    if success:
        logger.info(f"✅ Subtask created successfully: {subtask.title}")
        logger.info(f"   Parent Task ID: {subtask.parent_task_id}")
        logger.info(f"   Project ID: {subtask.project_id}")
        logger.info(f"   Deadline ID: {subtask.deadline_id}")
        
        # Verify it's properly linked
        subtasks = db_manager.get_subtasks(parent_task.id)
        logger.info(f"   Number of subtasks for parent: {len(subtasks)}")
        
        if len(subtasks) > 0:
            logger.info("✅ Subtask relationship verified!")
            return True
        else:
            logger.error("❌ Subtask not found when querying by parent ID")
            return False
    else:
        logger.error("❌ Failed to create subtask")
        return False

def run_ui_test(page: ft.Page):
    """Run the subtask creation UI test"""
    page.title = "Test - Subtask Creation"
    page.theme_mode = ft.ThemeMode.LIGHT
    page.window.width = 1400
    page.window.height = 900
    page.window.center()
    page.padding = 20
    
    # Run verification test first
    verification_result = verify_subtask_creation()
    
    # Initialize database and create test data
    db_manager = DatabaseManager()
    project, deadline, parent_task = create_test_data(db_manager)
    
    # Import TasksView
    from src.ui.views.tasks import TasksView
    
    # Mock app instance
    class MockApp:
        def __init__(self, page):
            self.page = page
    
    mock_app = MockApp(page)
    tasks_view = TasksView(mock_app)
    
    # Create test instructions
    instructions = ft.Container(
        content=ft.Column([
            ft.Text(
                "🧪 Test - Subtask Creation",
                size=24,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.BLUE_800
            ),
            ft.Container(height=10),
            ft.Text(
                f"Verification test result: {'✅ PASSED' if verification_result else '❌ FAILED'}",
                size=16,
                color=ft.Colors.GREEN_600 if verification_result else ft.Colors.RED_600,
                weight=ft.FontWeight.BOLD
            ),
            ft.Container(height=15),
            ft.Card(
                content=ft.Container(
                    content=ft.Column([
                        ft.Text(
                            "🎯 Come testare la creazione di sottotask:",
                            size=16,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.ORANGE_800
                        ),
                        ft.Container(height=8),
                        ft.Text("1. Trova il task 'Parent Task for Subtask Testing' nella lista", size=14),
                        ft.Text("2. Clicca sui tre puntini (⋮) del task", size=14),
                        ft.Text("3. Seleziona 'Aggiungi Sottotask'", size=14),
                        ft.Text("4. Verifica che il campo 'Task Padre' sia pre-compilato", size=14),
                        ft.Text("5. Inserisci un titolo (es: 'Nuova Sottotask Test')", size=14),
                        ft.Text("6. Salva e verifica che appaia come sottotask nell'interfaccia", size=14),
                    ]),
                    padding=15
                ),
                elevation=2
            ),
            ft.Container(height=10),
            ft.Row([
                ft.Icon(ft.Icons.INFO, color=ft.Colors.BLUE_600),
                ft.Text(
                    f"Task padre creato: '{parent_task.title}' (ID: {str(parent_task.id)[:8]}...)",
                    size=12,
                    color=ft.Colors.GREY_600
                )
            ], spacing=8),
        ]),
        padding=20,
        bgcolor=ft.Colors.WHITE,
        border_radius=10,
        border=ft.border.all(1, ft.Colors.BLUE_200)
    )
    
    # Add layout
    page.add(
        ft.Column([
            instructions,
            ft.Container(height=20),
            ft.Divider(),
            ft.Container(height=10),
            tasks_view.build()
        ], scroll=ft.ScrollMode.AUTO)
    )
    
    logger.info("Subtask creation UI test loaded successfully")

if __name__ == "__main__":
    # Run the test
    ft.app(target=run_ui_test, port=8560) 