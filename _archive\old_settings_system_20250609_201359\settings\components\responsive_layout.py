#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Responsive Layout System
Provides adaptive layouts for different screen sizes and optimal UX across devices
"""

import flet as ft
from typing import Dict, List, Any, Optional, Union
from enum import Enum
from core import get_logger

logger = get_logger(__name__)

class BreakPoint(Enum):
    """Screen size breakpoints"""
    MOBILE = "mobile"      # < 768px
    TABLET = "tablet"      # 768px - 1024px  
    DESKTOP = "desktop"    # 1024px - 1440px
    WIDE = "wide"          # > 1440px

class ResponsiveConfig:
    """Configuration for responsive behavior"""
    
    BREAKPOINTS = {
        BreakPoint.MOBILE: 768,
        BreakPoint.TABLET: 1024,
        BreakPoint.DESKTOP: 1440,
        BreakPoint.WIDE: float('inf')
    }
    
    # Layout configurations for different screen sizes
    LAYOUT_CONFIG = {
        BreakPoint.MOBILE: {
            'tabs_orientation': 'horizontal',
            'tabs_scrollable': True,
            'sections_per_row': 1,
            'cards_padding': 12,
            'cards_margin': 8,
            'header_compact': True,
            'sidebar_collapsed': True,
            'search_width': '100%',
            'form_columns': 1
        },
        BreakPoint.TABLET: {
            'tabs_orientation': 'horizontal',
            'tabs_scrollable': False,
            'sections_per_row': 1,
            'cards_padding': 16,
            'cards_margin': 12,
            'header_compact': False,
            'sidebar_collapsed': False,
            'search_width': 400,
            'form_columns': 2
        },
        BreakPoint.DESKTOP: {
            'tabs_orientation': 'horizontal',
            'tabs_scrollable': False,
            'sections_per_row': 2,
            'cards_padding': 20,
            'cards_margin': 16,
            'header_compact': False,
            'sidebar_collapsed': False,
            'search_width': 450,
            'form_columns': 2
        },
        BreakPoint.WIDE: {
            'tabs_orientation': 'horizontal',
            'tabs_scrollable': False,
            'sections_per_row': 3,
            'cards_padding': 24,
            'cards_margin': 20,
            'header_compact': False,
            'sidebar_collapsed': False,
            'search_width': 500,
            'form_columns': 3
        }
    }

class ResponsiveContainer:
    """
    Responsive container that adapts its layout based on screen size
    """
    
    def __init__(self, content: List[ft.Control], responsive_config: Optional[Dict] = None):
        self.content = content
        self.current_breakpoint = BreakPoint.DESKTOP
        self.responsive_config = responsive_config or {}
        
        # Layout state
        self._width = 1200
        self._height = 800
        
        logger.debug("Responsive Container initialized")
    
    def update_size(self, width: float, height: float):
        """Update container size and recalculate layout"""
        self._width = width
        self._height = height
        self.current_breakpoint = self._get_breakpoint(width)
        logger.debug(f"Layout updated: {width}x{height} -> {self.current_breakpoint.value}")
    
    def _get_breakpoint(self, width: float) -> BreakPoint:
        """Determine current breakpoint based on width"""
        for breakpoint, min_width in ResponsiveConfig.BREAKPOINTS.items():
            if width < min_width:
                return breakpoint
        return BreakPoint.WIDE
    
    def get_layout_config(self) -> Dict[str, Any]:
        """Get layout configuration for current breakpoint"""
        base_config = ResponsiveConfig.LAYOUT_CONFIG[self.current_breakpoint]
        return {**base_config, **self.responsive_config}
    
    def create_responsive_row(self, items: List[ft.Control], wrap: bool = True) -> ft.Control:
        """Create a responsive row that adapts to screen size"""
        config = self.get_layout_config()
        sections_per_row = config['sections_per_row']
        
        if self.current_breakpoint == BreakPoint.MOBILE:
            # Mobile: Stack vertically
            return ft.Column(
                items,
                spacing=config['cards_margin'],
                scroll=ft.ScrollMode.AUTO
            )
        elif sections_per_row == 1:
            # Single column layout
            return ft.Column(
                items,
                spacing=config['cards_margin']
            )
        else:
            # Multi-column layout
            rows = []
            for i in range(0, len(items), sections_per_row):
                row_items = items[i:i + sections_per_row]
                
                # Make items expand equally
                for item in row_items:
                    if hasattr(item, 'expand'):
                        item.expand = True
                
                rows.append(
                    ft.Row(
                        row_items,
                        spacing=config['cards_margin'],
                        wrap=wrap
                    )
                )
            
            return ft.Column(
                rows,
                spacing=config['cards_margin']
            )
    
    def create_responsive_form(self, fields: List[ft.Control]) -> ft.Control:
        """Create a responsive form layout"""
        config = self.get_layout_config()
        columns = config['form_columns']
        
        if columns == 1:
            return ft.Column(
                fields,
                spacing=12
            )
        else:
            # Multi-column form
            rows = []
            for i in range(0, len(fields), columns):
                row_fields = fields[i:i + columns]
                
                # Make fields expand equally
                for field in row_fields:
                    if hasattr(field, 'expand'):
                        field.expand = True
                
                rows.append(
                    ft.Row(
                        row_fields,
                        spacing=16
                    )
                )
            
            return ft.Column(
                rows,
                spacing=12
            )

class ResponsiveSettingsCard:
    """
    Settings card that adapts to different screen sizes
    """
    
    def __init__(self, title: str, content: List[ft.Control], breakpoint: BreakPoint = BreakPoint.DESKTOP):
        self.title = title
        self.content = content
        self.breakpoint = breakpoint
    
    def build(self) -> ft.Container:
        """Build responsive card"""
        config = ResponsiveConfig.LAYOUT_CONFIG[self.breakpoint]
        
        # Adjust padding and margins based on screen size
        padding = config['cards_padding']
        margin = config['cards_margin']
        
        # Compact header for mobile
        if config['header_compact']:
            header = ft.Text(
                self.title,
                size=16,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.GREY_800
            )
        else:
            header = ft.Row([
                ft.Text(
                    self.title,
                    size=18,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.GREY_800
                ),
                ft.Container(expand=True),
                ft.IconButton(
                    icon=ft.Icons.MORE_VERT,
                    tooltip="Opzioni",
                    icon_size=16
                )
            ])
        
        return ft.Container(
            content=ft.Column([
                header,
                ft.Divider(height=1, color=ft.Colors.GREY_300),
                *self.content
            ], spacing=12),
            padding=ft.padding.all(padding),
            margin=ft.margin.all(margin),
            bgcolor=ft.Colors.WHITE,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.GREY_200),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=4,
                color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
                offset=ft.Offset(0, 2)
            )
        )

class ResponsiveNavigation:
    """
    Navigation component that adapts to screen size
    """
    
    def __init__(self, tabs_config: Dict[str, Dict], breakpoint: BreakPoint = BreakPoint.DESKTOP):
        self.tabs_config = tabs_config
        self.breakpoint = breakpoint
        self.current_tab = list(tabs_config.keys())[0] if tabs_config else ""
    
    def create_mobile_navigation(self) -> ft.Container:
        """Create mobile-friendly navigation"""
        nav_items = []
        
        for tab_key, tab_config in self.tabs_config.items():
            is_active = tab_key == self.current_tab
            
            nav_item = ft.Container(
                content=ft.Column([
                    ft.Icon(
                        tab_config['icon'],
                        size=20,
                        color=ft.Colors.BLUE_600 if is_active else ft.Colors.GREY_600
                    ),
                    ft.Text(
                        tab_config['title'],
                        size=10,
                        color=ft.Colors.BLUE_600 if is_active else ft.Colors.GREY_600,
                        text_align=ft.TextAlign.CENTER
                    )
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=4),
                padding=ft.padding.symmetric(horizontal=8, vertical=4),
                expand=True,
                on_click=lambda e, key=tab_key: self._on_tab_change(key)
            )
            nav_items.append(nav_item)
        
        return ft.Container(
            content=ft.Row(nav_items, spacing=0),
            bgcolor=ft.Colors.WHITE,
            border=ft.border.only(top=ft.BorderSide(1, ft.Colors.GREY_300)),
            padding=ft.padding.symmetric(vertical=8)
        )
    
    def create_desktop_navigation(self) -> ft.Container:
        """Create desktop navigation"""
        nav_items = []
        
        for tab_key, tab_config in self.tabs_config.items():
            is_active = tab_key == self.current_tab
            
            nav_item = ft.Container(
                content=ft.Row([
                    ft.Icon(
                        tab_config['icon'],
                        size=20,
                        color=ft.Colors.WHITE if is_active else ft.Colors.GREY_600
                    ),
                    ft.Text(
                        tab_config['title'],
                        size=14,
                        weight=ft.FontWeight.BOLD if is_active else ft.FontWeight.NORMAL,
                        color=ft.Colors.WHITE if is_active else ft.Colors.GREY_700
                    )
                ], spacing=8),
                bgcolor=ft.Colors.BLUE_600 if is_active else ft.Colors.TRANSPARENT,
                padding=ft.padding.all(12),
                border_radius=8,
                on_click=lambda e, key=tab_key: self._on_tab_change(key),
                expand=True
            )
            nav_items.append(nav_item)
        
        return ft.Container(
            content=ft.Row(nav_items, spacing=8),
            padding=ft.padding.all(16),
            bgcolor=ft.Colors.GREY_100,
            border_radius=8
        )
    
    def _on_tab_change(self, tab_key: str):
        """Handle tab change"""
        self.current_tab = tab_key
        logger.debug(f"Tab changed to: {tab_key}")
    
    def build(self) -> ft.Container:
        """Build navigation based on current breakpoint"""
        config = ResponsiveConfig.LAYOUT_CONFIG[self.breakpoint]
        
        if self.breakpoint == BreakPoint.MOBILE:
            return self.create_mobile_navigation()
        else:
            return self.create_desktop_navigation()

class ResponsiveHeader:
    """
    Header component that adapts to screen size
    """
    
    def __init__(self, title: str, subtitle: str, actions: List[ft.Control], breakpoint: BreakPoint = BreakPoint.DESKTOP):
        self.title = title
        self.subtitle = subtitle
        self.actions = actions
        self.breakpoint = breakpoint
    
    def build(self) -> ft.Container:
        """Build responsive header"""
        config = ResponsiveConfig.LAYOUT_CONFIG[self.breakpoint]
        
        if config['header_compact']:
            # Compact mobile header
            content = ft.Column([
                ft.Row([
                    ft.Text(
                        self.title,
                        size=20,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.GREY_800
                    ),
                    ft.Container(expand=True),
                    ft.PopupMenuButton(
                        items=[
                            ft.PopupMenuItem(
                                content=action,
                                height=40
                            ) for action in self.actions
                        ],
                        icon=ft.Icons.MORE_VERT
                    )
                ]),
                ft.Text(
                    self.subtitle,
                    size=12,
                    color=ft.Colors.GREY_600
                )
            ], spacing=4)
        else:
            # Full desktop header
            content = ft.Column([
                ft.Row([
                    ft.Column([
                        ft.Text(
                            self.title,
                            size=28,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.GREY_800
                        ),
                        ft.Text(
                            self.subtitle,
                            size=14,
                            color=ft.Colors.GREY_600
                        )
                    ], spacing=4, expand=True),
                    ft.Row(self.actions, spacing=8)
                ])
            ])
        
        return ft.Container(
            content=content,
            padding=ft.padding.all(20),
            bgcolor=ft.Colors.WHITE,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.GREY_200),
            margin=ft.margin.only(bottom=16)
        )

class ResponsiveLayoutManager:
    """
    Main layout manager that coordinates responsive behavior
    """
    
    def __init__(self):
        self.current_breakpoint = BreakPoint.DESKTOP
        self.size_callbacks: List[callable] = []
        
        logger.info("Responsive Layout Manager initialized")
    
    def register_size_callback(self, callback: callable):
        """Register callback for size changes"""
        self.size_callbacks.append(callback)
    
    def update_layout(self, width: float, height: float):
        """Update layout based on new size"""
        old_breakpoint = self.current_breakpoint
        new_breakpoint = self._get_breakpoint(width)
        
        if old_breakpoint != new_breakpoint:
            self.current_breakpoint = new_breakpoint
            logger.info(f"Breakpoint changed: {old_breakpoint.value} -> {new_breakpoint.value}")
            
            # Notify all registered callbacks
            for callback in self.size_callbacks:
                try:
                    callback(new_breakpoint, width, height)
                except Exception as e:
                    logger.error(f"Error in size callback: {e}")
    
    def _get_breakpoint(self, width: float) -> BreakPoint:
        """Determine breakpoint based on width"""
        for breakpoint, min_width in ResponsiveConfig.BREAKPOINTS.items():
            if width < min_width:
                return breakpoint
        return BreakPoint.WIDE
    
    def get_current_config(self) -> Dict[str, Any]:
        """Get current layout configuration"""
        return ResponsiveConfig.LAYOUT_CONFIG[self.current_breakpoint]
    
    def create_responsive_grid(self, items: List[ft.Control], min_item_width: int = 300) -> ft.Control:
        """Create a responsive grid layout"""
        config = self.get_current_config()
        
        if self.current_breakpoint == BreakPoint.MOBILE:
            return ft.Column(items, spacing=config['cards_margin'])
        
        # Calculate optimal columns based on item width and available space
        container_width = 1200  # Default, would be actual container width
        max_columns = max(1, container_width // (min_item_width + config['cards_margin']))
        actual_columns = min(max_columns, config['sections_per_row'])
        
        # Create grid
        rows = []
        for i in range(0, len(items), actual_columns):
            row_items = items[i:i + actual_columns]
            
            # Ensure items expand to fill space
            for item in row_items:
                if hasattr(item, 'expand'):
                    item.expand = True
            
            rows.append(
                ft.Row(
                    row_items,
                    spacing=config['cards_margin']
                )
            )
        
        return ft.Column(rows, spacing=config['cards_margin']) 