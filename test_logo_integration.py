#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Logo Integration
Tests the new logo implementation in sidebar and header components
"""

import flet as ft
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from ui.components.sidebar import Sidebar
from ui.components.header import Header
from ui.components.modern_header import ModernHeader

def main(page: ft.Page):
    """Test the logo integration"""
    page.title = "Logo Integration Test - Agevolami PM"
    page.window.width = 1200
    page.window.height = 800
    page.padding = 0
    page.theme_mode = ft.ThemeMode.LIGHT
    
    # Mock app instance
    class MockApp:
        def __init__(self):
            self.page = page
    
    app = MockApp()
    
    # Test Sidebar with Logo
    sidebar = Sidebar(
        on_menu_click=lambda menu: print(f"Menu clicked: {menu}"),
        on_alert_click=lambda: print("Alert clicked")
    )
    
    # Test Header with Logo
    header = Header(
        on_search=lambda query: print(f"Search: {query}"),
        on_notification_click=lambda: print("Notifications clicked"),
        on_settings_click=lambda: print("Settings clicked"),
        app_instance=app
    )
    
    # Test Modern Header with Logo
    modern_header = ModernHeader(
        on_search=lambda query: print(f"Modern search: {query}"),
        on_notification_click=lambda: print("Modern notifications clicked"),
        on_settings_click=lambda: print("Modern settings clicked"),
        app_instance=app
    )
    
    # Create tabs to test different components
    tabs = ft.Tabs(
        selected_index=0,
        animation_duration=300,
        tabs=[
            ft.Tab(
                text="🎨 Sidebar + Header",
                content=ft.Row([
                    sidebar.build(),
                    ft.Column([
                        header.build(),
                        ft.Container(
                            content=ft.Column([
                                ft.Text(
                                    "🎯 Logo Integration Test",
                                    size=28,
                                    weight=ft.FontWeight.BOLD,
                                    color=ft.Colors.INDIGO_700,
                                    text_align=ft.TextAlign.CENTER
                                ),
                                ft.Text(
                                    "✅ Sidebar logo: assets/logo_v2.png (40x40px)",
                                    size=16,
                                    color=ft.Colors.GREEN_600
                                ),
                                ft.Text(
                                    "✅ Header logo: assets/logo_v2.png (32x32px)",
                                    size=16,
                                    color=ft.Colors.GREEN_600
                                ),
                                ft.Divider(),
                                ft.Text(
                                    "The logo should now appear in both the sidebar and header components, replacing the previous business center icon.",
                                    size=14,
                                    color=ft.Colors.GREY_600,
                                    text_align=ft.TextAlign.CENTER
                                )
                            ], 
                            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                            spacing=16),
                            padding=ft.padding.all(40),
                            expand=True,
                            alignment=ft.alignment.center
                        )
                    ], expand=True, spacing=0)
                ], spacing=0, expand=True)
            ),
            ft.Tab(
                text="🚀 Modern Header",
                content=ft.Column([
                    modern_header.build(),
                    ft.Container(
                        content=ft.Column([
                            ft.Text(
                                "🎯 Modern Header Logo Test",
                                size=28,
                                weight=ft.FontWeight.BOLD,
                                color=ft.Colors.INDIGO_700,
                                text_align=ft.TextAlign.CENTER
                            ),
                            ft.Text(
                                "✅ Modern header logo: assets/logo_v2.png (32x32px)",
                                size=16,
                                color=ft.Colors.GREEN_600
                            ),
                            ft.Text(
                                "✅ Consistent logo implementation across all header variants",
                                size=16,
                                color=ft.Colors.GREEN_600
                            ),
                            ft.Divider(),
                            ft.Text(
                                "The modern header component also includes the logo for consistency across the application.",
                                size=14,
                                color=ft.Colors.GREY_600,
                                text_align=ft.TextAlign.CENTER
                            )
                        ], 
                        horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                        spacing=16),
                        padding=ft.padding.all(40),
                        expand=True,
                        alignment=ft.alignment.center
                    )
                ], spacing=0, expand=True)
            ),
            ft.Tab(
                text="📋 Logo Info",
                content=ft.Container(
                    content=ft.Column([
                        ft.Text(
                            "📋 Logo Integration Summary",
                            size=28,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.INDIGO_700,
                            text_align=ft.TextAlign.CENTER
                        ),
                        ft.Card(
                            content=ft.Container(
                                content=ft.Column([
                                    ft.Text(
                                        "🎨 Logo Details",
                                        size=20,
                                        weight=ft.FontWeight.BOLD,
                                        color=ft.Colors.BLUE_700
                                    ),
                                    ft.Text("📁 File: assets/logo_v2.png", size=14),
                                    ft.Text("📐 Sidebar size: 40x40 pixels", size=14),
                                    ft.Text("📐 Header size: 32x32 pixels", size=14),
                                    ft.Text("🔧 Fit: CONTAIN (maintains aspect ratio)", size=14),
                                ], spacing=8),
                                padding=ft.padding.all(20)
                            ),
                            elevation=2
                        ),
                        ft.Card(
                            content=ft.Container(
                                content=ft.Column([
                                    ft.Text(
                                        "🔧 Components Updated",
                                        size=20,
                                        weight=ft.FontWeight.BOLD,
                                        color=ft.Colors.GREEN_700
                                    ),
                                    ft.Text("✅ src/ui/components/sidebar.py", size=14),
                                    ft.Text("✅ src/ui/components/header.py", size=14),
                                    ft.Text("✅ src/ui/components/modern_header.py", size=14),
                                    ft.Text("✅ src/main.py (window icon)", size=14),
                                ], spacing=8),
                                padding=ft.padding.all(20)
                            ),
                            elevation=2
                        ),
                        ft.Card(
                            content=ft.Container(
                                content=ft.Column([
                                    ft.Text(
                                        "🎯 Implementation Notes",
                                        size=20,
                                        weight=ft.FontWeight.BOLD,
                                        color=ft.Colors.ORANGE_700
                                    ),
                                    ft.Text("• Logo replaces the previous business center icon", size=14),
                                    ft.Text("• Consistent sizing across all components", size=14),
                                    ft.Text("• Also used for window and taskbar icon", size=14),
                                    ft.Text("• Maintains proper alignment with text elements", size=14),
                                    ft.Text("• Uses ft.ImageFit.CONTAIN for best quality", size=14),
                                ], spacing=8),
                                padding=ft.padding.all(20)
                            ),
                            elevation=2
                        )
                    ], 
                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                    spacing=20),
                    padding=ft.padding.all(40),
                    expand=True,
                    alignment=ft.alignment.center
                )
            )
        ],
        expand=True
    )
    
    page.add(tabs)
    page.update()

if __name__ == "__main__":
    ft.app(target=main, assets_dir="assets") 