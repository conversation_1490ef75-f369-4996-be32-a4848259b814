#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Enum Fix
Quick test to verify the enum handling fix works
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

def test_statistics_generation():
    """Test statistics generation with enum handling"""
    print("🔍 Testing statistics generation with enum handling...")
    
    try:
        from core.database import DatabaseManagerExtended
        from core.services.statistics_service import StatisticsService
        from core.config.app_config import AppConfig
        
        # Initialize services
        config = AppConfig()
        db_manager = DatabaseManagerExtended(config.database_path)
        stats_service = StatisticsService(db_manager, config)
        
        print("  ✅ Services initialized")
        
        # Test projects stats
        print("  🔧 Testing projects stats...")
        try:
            projects_stats = stats_service._get_projects_stats()
            print(f"    ✅ Projects stats generated: {projects_stats.get('total', 0)} projects")
        except Exception as e:
            print(f"    ❌ Projects stats failed: {e}")
            return False
        
        # Test deadlines stats
        print("  📅 Testing deadlines stats...")
        try:
            deadlines_stats = stats_service._get_deadlines_stats()
            print(f"    ✅ Deadlines stats generated: {deadlines_stats.get('total', 0)} deadlines")
        except Exception as e:
            print(f"    ❌ Deadlines stats failed: {e}")
            return False
        
        # Test custom report generation
        print("  📊 Testing custom report generation...")
        try:
            filters = {
                'include_clients': True,
                'include_projects': True,
                'include_tasks': True,
                'include_deadlines': True,
                'include_expired_only': False,
                'days_ahead': 15
            }
            
            report = stats_service.generate_custom_report(filters)
            print(f"    ✅ Custom report generated successfully")
            
            # Check report structure
            if 'summary' in report:
                summary = report['summary']
                print(f"      📊 Summary: {summary.get('total_clients', 0)} clients, {summary.get('total_projects', 0)} projects")
            
            if 'database_statistics' in report:
                print(f"      📊 Database statistics included")
            
            return True
            
        except Exception as e:
            print(f"    ❌ Custom report generation failed: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"  ❌ Service initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🚀 Enum Fix Test")
    print("=" * 50)
    
    # Test statistics generation
    if test_statistics_generation():
        print("\n🎉 All tests passed!")
        print("The enum handling fix is working correctly.")
        print("\nThe custom report should now generate without errors.")
    else:
        print("\n❌ Tests failed!")
        print("There are still issues with enum handling.")

if __name__ == "__main__":
    main()
