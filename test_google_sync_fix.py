#!/usr/bin/env python3
"""
Test script to verify Google services sync is working properly
"""

import os
import sys

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_google_services_integration():
    """Test Google services integration with the UI"""
    print("🔗 Testing Google Services Integration...")
    print("=" * 50)
    
    try:
        from core.config.app_config import AppConfig
        from ui.views.settings.settings_controller import SettingsController
        
        # Create mock app with settings controller
        class MockApp:
            def __init__(self):
                self.config = AppConfig()
                self.settings_controller = SettingsController(self)
        
        app = MockApp()
        
        # Check Google services settings
        google_settings = app.settings_controller.get_setting('google_services')
        
        print("📊 Current Google Services Settings:")
        print("-" * 40)
        print(f"   Calendar Enabled: {google_settings.get('calendar_enabled', False)}")
        print(f"   Calendar Authenticated: {google_settings.get('calendar_authenticated', False)}")
        print(f"   Tasks Enabled: {google_settings.get('tasks_enabled', False)}")
        print(f"   Tasks Authenticated: {google_settings.get('tasks_authenticated', False)}")
        print(f"   Drive Enabled: {google_settings.get('drive_enabled', False)}")
        print(f"   Drive Authenticated: {google_settings.get('drive_authenticated', False)}")
        
        # Test deadlines view integration
        print("\n📅 Testing Deadlines View Integration:")
        print("-" * 40)
        
        from ui.views.deadlines import DeadlinesView
        deadlines_view = DeadlinesView(app)
        
        # Test Google Calendar settings access
        calendar_settings = deadlines_view.google_calendar_settings
        print(f"   Calendar Settings Access: ✅ SUCCESS")
        print(f"   Calendar Enabled (via view): {calendar_settings.get('calendar_enabled', False)}")
        print(f"   Calendar Authenticated (via view): {calendar_settings.get('calendar_authenticated', False)}")
        
        # Test Google Calendar enabled check
        calendar_enabled = deadlines_view._is_google_calendar_enabled()
        print(f"   Calendar Sync Enabled: {'✅ YES' if calendar_enabled else '❌ NO'}")
        
        # Test auto-sync check
        auto_sync = deadlines_view._should_auto_sync()
        print(f"   Auto-sync Enabled: {'✅ YES' if auto_sync else '❌ NO'}")
        
        # Test tasks view integration
        print("\n📋 Testing Tasks View Integration:")
        print("-" * 40)
        
        from ui.views.tasks import TasksView
        tasks_view = TasksView(app)
        
        # Test Google Tasks settings access
        tasks_settings = tasks_view.google_tasks_settings
        print(f"   Tasks Settings Access: ✅ SUCCESS")
        print(f"   Tasks Enabled (via view): {tasks_settings.get('tasks_enabled', False)}")
        print(f"   Tasks Authenticated (via view): {tasks_settings.get('tasks_authenticated', False)}")
        
        # Test Google Tasks enabled check
        tasks_enabled = tasks_view._is_google_sync_enabled()
        print(f"   Tasks Sync Enabled: {'✅ YES' if tasks_enabled else '❌ NO'}")
        
        # Test auto-sync check
        tasks_auto_sync = tasks_view._should_auto_sync()
        print(f"   Tasks Auto-sync Enabled: {'✅ YES' if tasks_auto_sync else '❌ NO'}")
        
        # Summary
        print("\n📋 Integration Summary:")
        print("-" * 40)
        
        calendar_ready = calendar_enabled and calendar_settings.get('calendar_authenticated', False)
        tasks_ready = tasks_enabled and tasks_settings.get('tasks_authenticated', False)
        
        print(f"   Google Calendar Ready: {'✅ YES' if calendar_ready else '❌ NO'}")
        print(f"   Google Tasks Ready: {'✅ YES' if tasks_ready else '❌ NO'}")
        
        if calendar_ready and tasks_ready:
            print("\n🎉 Google services are properly integrated!")
            print("   - Deadlines will sync to Google Calendar")
            print("   - Tasks will sync to Google Tasks")
            print("   - The 'Google Calendar Non Configurato' dialog should not appear")
        elif calendar_ready:
            print("\n⚠️  Only Google Calendar is ready")
            print("   - Deadlines will sync to Google Calendar")
            print("   - Tasks sync is not available")
        elif tasks_ready:
            print("\n⚠️  Only Google Tasks is ready")
            print("   - Tasks will sync to Google Tasks")
            print("   - Calendar sync is not available")
        else:
            print("\n❌ Google services are not ready")
            print("   - You'll see 'Google Calendar Non Configurato' dialogs")
            print("   - No sync functionality will work")
        
        return calendar_ready or tasks_ready
        
    except Exception as e:
        print(f"❌ Error testing Google services integration: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_token_files():
    """Test that token files exist and are accessible"""
    print("\n🔐 Testing Token Files:")
    print("=" * 50)
    
    try:
        from core.config.app_config import AppConfig
        config = AppConfig()
        
        token_dir = config.data_dir / "config"
        tokens = {
            "Google Calendar": token_dir / "google_token.pickle",
            "Google Tasks": token_dir / "google_tasks_token.pickle",
            "Google Drive": token_dir / "google_drive_token.pickle"
        }
        
        all_exist = True
        for service, token_path in tokens.items():
            exists = token_path.exists()
            status = "✅ EXISTS" if exists else "❌ MISSING"
            print(f"   {service}: {status}")
            if exists:
                size = token_path.stat().st_size
                print(f"      Size: {size} bytes")
            if not exists:
                all_exist = False
        
        return all_exist
        
    except Exception as e:
        print(f"❌ Error checking token files: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Google Services Sync Fix Verification")
    print("=" * 60)
    
    # Test token files
    tokens_ok = test_token_files()
    
    # Test integration
    integration_ok = test_google_services_integration()
    
    print("\n" + "=" * 60)
    print("📋 FINAL RESULTS")
    print("=" * 60)
    
    if tokens_ok and integration_ok:
        print("✅ Google services sync is working properly!")
        print("💡 The 'Google Calendar Non Configurato' dialog should not appear anymore")
        print("💡 Deadlines and tasks should sync automatically")
    elif integration_ok:
        print("⚠️  Google services integration is working but some tokens are missing")
        print("💡 Some services may not be fully functional")
    elif tokens_ok:
        print("⚠️  Token files exist but integration has issues")
        print("💡 Check the error messages above for troubleshooting")
    else:
        print("❌ Google services are not properly configured")
        print("💡 You may need to re-authenticate with Google services")
    
    return tokens_ok and integration_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
