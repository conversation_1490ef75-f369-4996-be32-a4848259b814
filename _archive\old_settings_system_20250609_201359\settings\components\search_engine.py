#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Advanced Settings Search Engine
Provides intelligent search across all settings sections, keys, descriptions, and values
"""

import re
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass
from core import get_logger

logger = get_logger(__name__)

@dataclass
class SearchResult:
    """Represents a search result"""
    section_key: str
    section_title: str
    setting_key: Optional[str] = None
    setting_title: Optional[str] = None
    match_type: str = "general"  # general, exact, partial, description, value
    relevance_score: float = 0.0
    context: str = ""
    highlight_text: str = ""

class SettingsSearchEngine:
    """
    Advanced search engine for settings with intelligent matching and relevance scoring
    """
    
    def __init__(self):
        # Search index: maps terms to sections/settings
        self.search_index: Dict[str, Set[str]] = {}
        
        # Section metadata for enhanced search
        self.section_metadata = {
            'email': {
                'title': 'Configurazione Email',
                'description': 'Impostazioni server SMTP per invio email e notifiche',
                'keywords': ['smtp', 'mail', 'email', 'server', 'port', 'tls', 'ssl', 'authentication', 'sender'],
                'settings': {
                    'server': 'Server SMTP',
                    'port': 'Porta server',
                    'username': 'Nome utente',
                    'password': 'Password',
                    'use_tls': 'Crittografia TLS',
                    'sender_name': 'Nome mittente',
                    'sender_email': 'Email mittente'
                }
            },
            'notifications': {
                'title': 'Sistema Notifiche',
                'description': 'Configurazione alert e promemoria per scadenze e eventi',
                'keywords': ['alert', 'notification', 'remind', 'schedule', 'timing', 'recipients', 'email alerts'],
                'settings': {
                    'enabled': 'Sistema attivo',
                    'email_enabled': 'Notifiche email',
                    'check_interval': 'Intervallo controllo',
                    'advance_days': 'Giorni anticipo',
                    'working_hours_only': 'Solo orari lavorativi',
                    'weekend_alerts': 'Alert weekend',
                    'reminder_recipients': 'Destinatari promemoria'
                }
            },
            'google_services': {
                'title': 'Servizi Google',
                'description': 'Integrazione con Google Drive, Calendar e Tasks per sincronizzazione dati',
                'keywords': ['google', 'drive', 'calendar', 'tasks', 'cloud', 'sync', 'backup', 'integration'],
                'settings': {
                    'drive_enabled': 'Google Drive attivo',
                    'drive_auto_backup': 'Backup automatico',
                    'drive_backup_frequency': 'Frequenza backup',
                    'calendar_enabled': 'Google Calendar attivo',
                    'calendar_auto_sync': 'Sincronizzazione automatica',
                    'tasks_enabled': 'Google Tasks attivo',
                    'tasks_auto_sync': 'Sync automatico task'
                }
            },
            'windows': {
                'title': 'Integrazione Windows',
                'description': 'Configurazione integrazione con sistema Windows, startup e notifiche',
                'keywords': ['windows', 'startup', 'notification', 'system', 'integration', 'registry', 'toast'],
                'settings': {
                    'startup_enabled': 'Avvio automatico',
                    'startup_minimized': 'Avvio minimizzato',
                    'notifications_enabled': 'Notifiche desktop',
                    'notification_sound': 'Suoni notifiche',
                    'notification_quiet_hours_enabled': 'Orari silenziosi'
                }
            },
            'reports': {
                'title': 'Sistema Report',
                'description': 'Configurazione report automatici e analisi personalizzate',
                'keywords': ['report', 'analysis', 'schedule', 'export', 'statistics', 'email', 'automatic'],
                'settings': {
                    'scheduled_enabled': 'Report programmati',
                    'morning_enabled': 'Report mattutino',
                    'evening_enabled': 'Report serale',
                    'workdays_only': 'Solo giorni lavorativi',
                    'recipients': 'Destinatari report',
                    'days_ahead_filter': 'Giorni anticipo'
                }
            }
        }
        
        # Build search index
        self._build_search_index()
        
        logger.info("Advanced Settings Search Engine initialized")
    
    def _build_search_index(self):
        """Build the search index from section metadata"""
        for section_key, metadata in self.section_metadata.items():
            # Index section title and description
            for term in self._extract_search_terms(metadata['title'] + " " + metadata['description']):
                if term not in self.search_index:
                    self.search_index[term] = set()
                self.search_index[term].add(section_key)
            
            # Index keywords
            for keyword in metadata['keywords']:
                for term in self._extract_search_terms(keyword):
                    if term not in self.search_index:
                        self.search_index[term] = set()
                    self.search_index[term].add(section_key)
            
            # Index setting names and descriptions
            for setting_key, setting_title in metadata['settings'].items():
                for term in self._extract_search_terms(setting_key + " " + setting_title):
                    if term not in self.search_index:
                        self.search_index[term] = set()
                    self.search_index[term].add(f"{section_key}.{setting_key}")
    
    def _extract_search_terms(self, text: str) -> List[str]:
        """Extract searchable terms from text"""
        # Convert to lowercase and split on non-alphanumeric characters
        terms = re.findall(r'\w+', text.lower())
        
        # Add partial terms for better matching
        partial_terms = []
        for term in terms:
            if len(term) > 3:
                # Add 3-character prefixes for partial matching
                for i in range(3, len(term)):
                    partial_terms.append(term[:i])
        
        return terms + partial_terms
    
    def search(self, query: str, max_results: int = 20) -> List[SearchResult]:
        """
        Perform intelligent search across all settings
        """
        if not query or len(query.strip()) < 2:
            return []
        
        query = query.strip().lower()
        search_terms = self._extract_search_terms(query)
        
        # Collect potential matches
        matches: Dict[str, SearchResult] = {}
        
        # 1. Exact section/setting matches
        for section_key, metadata in self.section_metadata.items():
            # Check section title exact match
            if query in metadata['title'].lower():
                result = SearchResult(
                    section_key=section_key,
                    section_title=metadata['title'],
                    match_type="exact",
                    relevance_score=1.0,
                    context=metadata['description'],
                    highlight_text=metadata['title']
                )
                matches[f"section_{section_key}"] = result
            
            # Check settings exact matches
            for setting_key, setting_title in metadata['settings'].items():
                if query in setting_title.lower() or query in setting_key.lower():
                    result = SearchResult(
                        section_key=section_key,
                        section_title=metadata['title'],
                        setting_key=setting_key,
                        setting_title=setting_title,
                        match_type="exact",
                        relevance_score=0.9,
                        context=f"Impostazione in {metadata['title']}",
                        highlight_text=setting_title
                    )
                    matches[f"setting_{section_key}_{setting_key}"] = result
        
        # 2. Keyword matches
        for term in search_terms:
            if term in self.search_index:
                for match in self.search_index[term]:
                    if '.' in match:
                        # Setting match
                        section_key, setting_key = match.split('.', 1)
                        if section_key in self.section_metadata:
                            metadata = self.section_metadata[section_key]
                            setting_title = metadata['settings'].get(setting_key, setting_key)
                            
                            match_id = f"setting_{section_key}_{setting_key}"
                            if match_id not in matches:
                                result = SearchResult(
                                    section_key=section_key,
                                    section_title=metadata['title'],
                                    setting_key=setting_key,
                                    setting_title=setting_title,
                                    match_type="partial",
                                    relevance_score=0.7,
                                    context=f"Impostazione in {metadata['title']}",
                                    highlight_text=setting_title
                                )
                                matches[match_id] = result
                            else:
                                # Boost relevance for multiple term matches
                                matches[match_id].relevance_score += 0.1
                    else:
                        # Section match
                        section_key = match
                        if section_key in self.section_metadata:
                            metadata = self.section_metadata[section_key]
                            
                            match_id = f"section_{section_key}"
                            if match_id not in matches:
                                result = SearchResult(
                                    section_key=section_key,
                                    section_title=metadata['title'],
                                    match_type="partial",
                                    relevance_score=0.6,
                                    context=metadata['description'],
                                    highlight_text=metadata['title']
                                )
                                matches[match_id] = result
                            else:
                                # Boost relevance for multiple term matches
                                matches[match_id].relevance_score += 0.1
        
        # 3. Description matches
        for section_key, metadata in self.section_metadata.items():
            if any(term in metadata['description'].lower() for term in search_terms):
                match_id = f"section_{section_key}"
                if match_id not in matches:
                    result = SearchResult(
                        section_key=section_key,
                        section_title=metadata['title'],
                        match_type="description",
                        relevance_score=0.4,
                        context=metadata['description'],
                        highlight_text=metadata['title']
                    )
                    matches[match_id] = result
        
        # Sort by relevance score and return top results
        sorted_results = sorted(matches.values(), key=lambda x: x.relevance_score, reverse=True)
        return sorted_results[:max_results]
    
    def get_suggestions(self, partial_query: str, max_suggestions: int = 5) -> List[str]:
        """
        Get search suggestions based on partial query
        """
        if not partial_query or len(partial_query) < 2:
            return []
        
        partial_query = partial_query.lower()
        suggestions = set()
        
        # Find matching terms in index
        for term in self.search_index.keys():
            if term.startswith(partial_query):
                suggestions.add(term)
        
        # Add section and setting titles that match
        for section_key, metadata in self.section_metadata.items():
            if partial_query in metadata['title'].lower():
                suggestions.add(metadata['title'])
            
            for setting_key, setting_title in metadata['settings'].items():
                if partial_query in setting_title.lower():
                    suggestions.add(setting_title)
        
        return sorted(list(suggestions))[:max_suggestions]
    
    def get_popular_searches(self) -> List[str]:
        """Get list of popular/common search terms"""
        return [
            "email",
            "smtp", 
            "notifiche",
            "google drive",
            "backup",
            "report",
            "avvio automatico",
            "calendario",
            "alert",
            "windows"
        ]
    
    def highlight_text(self, text: str, query: str) -> str:
        """
        Add highlighting markup to text based on query
        Returns text with **highlighted** parts
        """
        if not query:
            return text
        
        query_terms = self._extract_search_terms(query)
        highlighted_text = text
        
        for term in query_terms:
            if len(term) >= 3:  # Only highlight meaningful terms
                pattern = re.compile(re.escape(term), re.IGNORECASE)
                highlighted_text = pattern.sub(f"**{term}**", highlighted_text)
        
        return highlighted_text
    
    def get_section_match_score(self, section_key: str, query: str) -> float:
        """
        Get relevance score for a section based on query
        """
        if not query or section_key not in self.section_metadata:
            return 0.0
        
        metadata = self.section_metadata[section_key]
        query_terms = self._extract_search_terms(query)
        
        score = 0.0
        total_terms = len(query_terms)
        
        if total_terms == 0:
            return 0.0
        
        # Check title matches
        title_matches = sum(1 for term in query_terms if term in metadata['title'].lower())
        score += (title_matches / total_terms) * 0.4
        
        # Check keyword matches
        keyword_matches = sum(1 for term in query_terms 
                            for keyword in metadata['keywords'] 
                            if term in keyword.lower())
        score += (keyword_matches / total_terms) * 0.3
        
        # Check description matches
        desc_matches = sum(1 for term in query_terms if term in metadata['description'].lower())
        score += (desc_matches / total_terms) * 0.2
        
        # Check settings matches
        settings_matches = sum(1 for term in query_terms
                             for setting_title in metadata['settings'].values()
                             if term in setting_title.lower())
        score += (settings_matches / total_terms) * 0.1
        
        return min(score, 1.0)  # Cap at 1.0 