#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Complete Modern Settings System
Integration of all components with proper initialization, testing, and documentation
"""

import flet as ft
from typing import Dict, List, Any, Optional, Callable
import asyncio
from pathlib import Path

from .settings_controller import SettingsController
from .modern_settings_view import ModernSettingsView
from .components.search_engine import SettingsSearchEngine
from .components.search_component import AdvancedSearchComponent
from .components.theme_manager import SettingsThemeManager, get_theme_manager, get_themed_components
from .components.validation_engine import SettingsValidationEngine
from .components.responsive_layout import ResponsiveLayoutManager
from .performance_manager import SettingsPerformanceManager, get_performance_manager
from .sections import *
from core import get_logger

logger = get_logger(__name__)

class CompleteSettingsSystem:
    """
    Complete modern settings system with all advanced features
    """
    
    def __init__(self, app_instance):
        self.app = app_instance
        
        # Core components
        self.settings_controller = SettingsController(app_instance)
        self.theme_manager = get_theme_manager()
        self.performance_manager = get_performance_manager()
        self.validation_engine = SettingsValidationEngine()
        self.search_engine = SettingsSearchEngine()
        self.responsive_manager = ResponsiveLayoutManager()
        
        # UI components
        self.search_component: Optional[AdvancedSearchComponent] = None
        self.main_view: Optional[ModernSettingsView] = None
        
        # State
        self.initialized = False
        self.current_section = "essentials"
        self.search_active = False
        
        logger.info("Complete Settings System initializing...")
        
        # Initialize system
        self._initialize_system()
    
    def _initialize_system(self):
        """Initialize the complete settings system"""
        try:
            # Set up performance optimizations
            self._setup_performance_optimizations()
            
            # Initialize theme system
            self._setup_theme_system()
            
            # Set up validation
            self._setup_validation_system()
            
            # Initialize responsive layout
            self._setup_responsive_system()
            
            # Set up search
            self._setup_search_system()
            
            # Create main view
            self.main_view = ModernSettingsView(
                self.settings_controller,
                self.search_component,
                self.theme_manager,
                self.validation_engine
            )
            
            # Connect event handlers
            self._connect_event_handlers()
            
            # Preload critical sections
            self._preload_critical_sections()
            
            self.initialized = True
            logger.info("✅ Complete Settings System initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Error initializing settings system: {e}")
            raise
    
    def _setup_performance_optimizations(self):
        """Set up performance optimizations"""
        # Detect device capabilities (mock for now)
        device_info = {
            'cpu_cores': 4,
            'memory_gb': 8,
            'screen_size': (1920, 1080)
        }
        
        self.performance_manager.optimize_for_device(device_info)
        
        # Register cleanup callback
        self.performance_manager.register_component("main_system", self)
        
        logger.debug("Performance optimizations configured")
    
    def _setup_theme_system(self):
        """Set up theme management"""
        # Load theme preference from settings
        theme_preference = self.settings_controller.get_setting('app', 'theme', 'light')
        
        from .components.theme_manager import ThemeMode
        if theme_preference == 'dark':
            mode = ThemeMode.DARK
        elif theme_preference == 'system':
            mode = ThemeMode.SYSTEM
        else:
            mode = ThemeMode.LIGHT
        
        self.theme_manager.set_theme_mode(mode)
        
        # Register theme change callback
        self.theme_manager.register_theme_callback(self._on_theme_changed)
        
        logger.debug(f"Theme system configured: {mode.value}")
    
    def _setup_validation_system(self):
        """Set up validation system with custom rules"""
        # Add custom validation rules
        from .components.validation_engine import validate_smtp_consistency, validate_time_consistency
        
        self.validation_engine.add_cross_field_validator(validate_smtp_consistency)
        self.validation_engine.add_cross_field_validator(validate_time_consistency)
        
        logger.debug("Validation system configured")
    
    def _setup_responsive_system(self):
        """Set up responsive layout management"""
        # Register size change callback
        self.responsive_manager.register_size_callback(self._on_size_changed)
        
        logger.debug("Responsive system configured")
    
    def _setup_search_system(self):
        """Set up advanced search functionality"""
        self.search_component = AdvancedSearchComponent(
            on_search_callback=self._on_search_results
        )
        
        logger.debug("Search system configured")
    
    def _connect_event_handlers(self):
        """Connect event handlers between components"""
        
        # Settings change events
        self.settings_controller.on_setting_changed = self._on_setting_changed
        self.settings_controller.on_section_changed = self._on_section_changed
        
        # Validation events
        self.settings_controller.validation_engine = self.validation_engine
        
        logger.debug("Event handlers connected")
    
    def _preload_critical_sections(self):
        """Preload critical sections for better performance"""
        critical_sections = ["essentials", "notifications", "google_services"]
        self.performance_manager.preload_critical_sections(critical_sections)
        
        logger.debug("Critical sections preloaded")
    
    def _on_theme_changed(self, new_mode, old_mode):
        """Handle theme changes"""
        logger.info(f"Theme changed from {old_mode.value} to {new_mode.value}")
        
        # Update settings controller
        self.settings_controller.update_setting('app', 'theme', new_mode.value)
        
        # Refresh UI if available
        if self.main_view and hasattr(self.main_view, 'refresh_theme'):
            self.main_view.refresh_theme()
    
    def _on_size_changed(self, new_breakpoint, width, height):
        """Handle responsive layout changes"""
        logger.debug(f"Layout changed to {new_breakpoint.value} ({width}x{height})")
        
        # Update main view if available
        if self.main_view and hasattr(self.main_view, 'update_responsive_layout'):
            self.main_view.update_responsive_layout(new_breakpoint, width, height)
    
    def _on_search_results(self, query: str, results: List):
        """Handle search results"""
        self.search_active = bool(query)
        
        if self.main_view and hasattr(self.main_view, 'update_search_results'):
            self.main_view.update_search_results(query, results)
        
        logger.debug(f"Search updated: '{query}' -> {len(results)} results")
    
    def _on_setting_changed(self, section: str, key: str, value: Any):
        """Handle individual setting changes"""
        logger.debug(f"Setting changed: {section}.{key} = {value}")
        
        # Perform validation
        validation_results = self.validation_engine.validate_field(key, value)
        
        # Update UI with validation results if needed
        if self.main_view and hasattr(self.main_view, 'update_field_validation'):
            self.main_view.update_field_validation(section, key, validation_results)
    
    def _on_section_changed(self, new_section: str):
        """Handle section changes"""
        old_section = self.current_section
        self.current_section = new_section
        
        logger.debug(f"Section changed from {old_section} to {new_section}")
        
        # Load section lazily if needed
        asyncio.create_task(self._load_section_async(new_section))
    
    async def _load_section_async(self, section_id: str):
        """Load section asynchronously"""
        await self.performance_manager.load_section_lazy(section_id)
        
        if self.main_view and hasattr(self.main_view, 'section_loaded'):
            self.main_view.section_loaded(section_id)
    
    def build(self) -> ft.Container:
        """Build the complete settings interface"""
        if not self.initialized:
            return ft.Container(
                content=ft.Column([
                    ft.ProgressRing(),
                    ft.Text("Inizializzazione sistema impostazioni...")
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=ft.padding.all(50)
            )
        
        return self.main_view.build()
    
    def get_section_component(self, section_id: str):
        """Get a specific section component"""
        section_map = {
            'email': EmailSection,
            'notifications': NotificationsSection,
            'google_services': GoogleServicesSection,
            'windows': WindowsSection,
            'reports': ReportsSection
        }
        
        if section_id in section_map:
            return section_map[section_id](
                self.settings_controller,
                self.validation_engine,
                get_themed_components()
            )
        
        return None
    
    def save_all_settings(self) -> bool:
        """Save all settings with validation"""
        try:
            # Validate all settings first
            all_settings = self.settings_controller.get_all_settings()
            validation_results = self.validation_engine.validate_all(all_settings)
            
            # Check for critical errors
            has_errors = any(
                any(not result.is_valid and result.level.value >= 3 for result in results)
                for results in validation_results.values()
            )
            
            if has_errors:
                logger.warning("Cannot save settings due to validation errors")
                return False
            
            # Save settings
            success = self.settings_controller.save_all_settings()
            
            if success:
                logger.info("✅ All settings saved successfully")
            else:
                logger.error("❌ Failed to save settings")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ Error saving settings: {e}")
            return False
    
    def reset_to_defaults(self) -> bool:
        """Reset all settings to defaults"""
        try:
            success = self.settings_controller.reset_to_defaults()
            
            if success and self.main_view:
                # Refresh the entire view
                if hasattr(self.main_view, 'refresh_all_sections'):
                    self.main_view.refresh_all_sections()
                
                logger.info("✅ Settings reset to defaults")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ Error resetting settings: {e}")
            return False
    
    def export_settings(self) -> Optional[Dict[str, Any]]:
        """Export all settings for backup/sharing"""
        try:
            settings_data = {
                'version': '2.0.0',
                'timestamp': self.settings_controller.get_current_timestamp(),
                'settings': self.settings_controller.get_all_settings(),
                'metadata': {
                    'app_version': getattr(self.app, 'version', '1.0.0'),
                    'export_source': 'modern_settings_system'
                }
            }
            
            logger.info("✅ Settings exported successfully")
            return settings_data
            
        except Exception as e:
            logger.error(f"❌ Error exporting settings: {e}")
            return None
    
    def import_settings(self, settings_data: Dict[str, Any]) -> bool:
        """Import settings from backup/sharing"""
        try:
            if 'settings' not in settings_data:
                logger.error("Invalid settings data format")
                return False
            
            # Validate imported settings
            imported_settings = settings_data['settings']
            validation_results = self.validation_engine.validate_all(imported_settings)
            
            # Count errors
            error_count = sum(
                sum(1 for result in results if not result.is_valid and result.level.value >= 3)
                for results in validation_results.values()
            )
            
            if error_count > 0:
                logger.warning(f"Imported settings have {error_count} validation errors")
            
            # Import settings
            success = self.settings_controller.import_settings(imported_settings)
            
            if success and self.main_view:
                # Refresh the entire view
                if hasattr(self.main_view, 'refresh_all_sections'):
                    self.main_view.refresh_all_sections()
                
                logger.info("✅ Settings imported successfully")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ Error importing settings: {e}")
            return False
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        return {
            'initialized': self.initialized,
            'current_section': self.current_section,
            'search_active': self.search_active,
            'theme': self.theme_manager.current_mode.value,
            'performance': self.performance_manager.get_performance_stats(),
            'validation': {
                'rules_count': len(self.validation_engine.validation_rules),
                'cross_validators': len(self.validation_engine.cross_field_validators)
            },
            'responsive': {
                'current_breakpoint': self.responsive_manager.current_breakpoint.value,
                'layout_config': self.responsive_manager.get_current_config()
            }
        }
    
    def run_diagnostics(self) -> Dict[str, Any]:
        """Run comprehensive system diagnostics"""
        diagnostics = {
            'timestamp': self.settings_controller.get_current_timestamp(),
            'system_status': self.get_system_status(),
            'component_health': {},
            'performance_metrics': self.performance_manager.get_performance_stats(),
            'recommendations': []
        }
        
        # Component health checks
        components = {
            'settings_controller': self.settings_controller,
            'theme_manager': self.theme_manager,
            'search_engine': self.search_engine,
            'validation_engine': self.validation_engine,
            'performance_manager': self.performance_manager
        }
        
        for name, component in components.items():
            try:
                # Basic health check
                health = getattr(component, 'get_health_status', lambda: {'status': 'unknown'})()
                diagnostics['component_health'][name] = health
            except Exception as e:
                diagnostics['component_health'][name] = {'status': 'error', 'error': str(e)}
        
        # Generate recommendations
        perf_stats = self.performance_manager.get_performance_stats()
        
        if perf_stats['cache']['size'] > perf_stats['cache']['max_size'] * 0.9:
            diagnostics['recommendations'].append('Consider increasing cache size for better performance')
        
        if len(perf_stats['updates']['pending_updates']) > 10:
            diagnostics['recommendations'].append('High number of pending updates may indicate performance issues')
        
        return diagnostics
    
    def cleanup(self):
        """Clean up resources"""
        try:
            # Cleanup performance manager
            self.performance_manager.cleanup_unused_components()
            
            # Clear caches
            if hasattr(self.search_engine, 'clear_cache'):
                self.search_engine.clear_cache()
            
            # Unregister components
            self.performance_manager.unregister_component("main_system")
            
            logger.info("✅ Settings system cleanup completed")
            
        except Exception as e:
            logger.error(f"❌ Error during cleanup: {e}")

def create_complete_settings_system(app_instance) -> CompleteSettingsSystem:
    """
    Factory function to create a complete settings system
    """
    return CompleteSettingsSystem(app_instance)

# Testing utilities
def run_settings_system_tests(system: CompleteSettingsSystem) -> Dict[str, Any]:
    """
    Run comprehensive tests on the settings system
    """
    test_results = {
        'timestamp': system.settings_controller.get_current_timestamp(),
        'tests': {},
        'summary': {'passed': 0, 'failed': 0, 'total': 0}
    }
    
    # Test 1: Initialization
    test_results['tests']['initialization'] = {
        'status': 'passed' if system.initialized else 'failed',
        'details': 'System initialization check'
    }
    
    # Test 2: Settings CRUD operations
    try:
        # Test setting update
        original_value = system.settings_controller.get_setting('app', 'theme', 'light')
        system.settings_controller.update_setting('app', 'theme', 'dark')
        new_value = system.settings_controller.get_setting('app', 'theme', 'light')
        
        # Restore original
        system.settings_controller.update_setting('app', 'theme', original_value)
        
        test_results['tests']['settings_crud'] = {
            'status': 'passed' if new_value == 'dark' else 'failed',
            'details': f'Setting update test: {original_value} -> {new_value} -> {original_value}'
        }
    except Exception as e:
        test_results['tests']['settings_crud'] = {
            'status': 'failed',
            'details': f'Error: {str(e)}'
        }
    
    # Test 3: Validation system
    try:
        validation_result = system.validation_engine.validate_field('smtp_port', '587')
        test_results['tests']['validation'] = {
            'status': 'passed' if validation_result else 'failed',
            'details': f'Validation test result count: {len(validation_result)}'
        }
    except Exception as e:
        test_results['tests']['validation'] = {
            'status': 'failed',
            'details': f'Error: {str(e)}'
        }
    
    # Test 4: Search functionality
    try:
        search_results = system.search_engine.search('email')
        test_results['tests']['search'] = {
            'status': 'passed' if search_results else 'failed',
            'details': f'Search test result count: {len(search_results)}'
        }
    except Exception as e:
        test_results['tests']['search'] = {
            'status': 'failed',
            'details': f'Error: {str(e)}'
        }
    
    # Count results
    for test_name, test_result in test_results['tests'].items():
        test_results['summary']['total'] += 1
        if test_result['status'] == 'passed':
            test_results['summary']['passed'] += 1
        else:
            test_results['summary']['failed'] += 1
    
    return test_results

# Documentation
"""
# Complete Modern Settings System

## Overview
This system provides a comprehensive, modern settings interface with advanced features including:

- **Modular Architecture**: Clean separation of concerns with dedicated components
- **Advanced Search**: Intelligent search with autocomplete and relevance scoring
- **Real-time Validation**: Smart validation with suggestions and auto-fix capabilities
- **Responsive Design**: Adaptive layout for different screen sizes
- **Performance Optimization**: Lazy loading, caching, and efficient updates
- **Theme Management**: Consistent theming across all components
- **Extensible Design**: Easy to add new sections and settings

## Usage

```python
# Create the complete settings system
settings_system = create_complete_settings_system(app_instance)

# Build the UI
settings_ui = settings_system.build()

# Add to your app
page.add(settings_ui)

# Save settings
success = settings_system.save_all_settings()

# Run diagnostics
diagnostics = settings_system.run_diagnostics()
```

## Architecture

The system consists of several key components:

1. **SettingsController**: Core business logic and state management
2. **ModernSettingsView**: Main UI component with tabbed interface
3. **SearchEngine + SearchComponent**: Advanced search functionality
4. **ValidationEngine**: Real-time validation with smart suggestions
5. **ThemeManager**: Consistent theming and styling
6. **PerformanceManager**: Optimization and resource management
7. **ResponsiveLayoutManager**: Adaptive layouts for different screen sizes

## Features

### Search System
- Intelligent search across all settings
- Autocomplete with suggestions
- Relevance scoring and highlighting
- Popular searches and recommendations

### Validation System
- Real-time validation with user-friendly messages
- Smart suggestions and auto-fix capabilities
- Cross-field validation (e.g., SMTP consistency)
- Extensible validation rules

### Performance Optimization
- Lazy loading of sections
- Component caching with TTL
- Update batching for smooth UI
- Memory management and cleanup

### Responsive Design
- Mobile, tablet, desktop, and wide screen support
- Adaptive navigation (bottom tabs on mobile)
- Responsive forms and layouts
- Touch-friendly controls

### Theme Management
- Light and dark themes
- Consistent color palette
- Material Design principles
- Smooth theme transitions

## Extensibility

Adding new sections:

```python
class CustomSection(SettingsSection):
    def __init__(self, controller, validation_engine, themed_components):
        super().__init__("custom", "Custom Settings", controller, validation_engine, themed_components)
    
    def build_content(self):
        # Build your custom settings UI
        pass
```

Adding validation rules:

```python
class CustomRule(ValidationRule):
    def validate(self, value, context=None):
        # Custom validation logic
        pass

validation_engine.add_rule('custom_field', CustomRule('custom_field', 'Custom validation message'))
```

## Testing

Run comprehensive tests:

```python
test_results = run_settings_system_tests(settings_system)
print(f"Tests: {test_results['summary']['passed']}/{test_results['summary']['total']} passed")
```

## Performance Considerations

- Components are lazy-loaded to reduce initial load time
- Caching reduces expensive operations
- Update batching prevents UI blocking
- Memory is managed automatically
- Device capabilities are detected for optimization

## Browser Compatibility

The system is designed to work across different platforms and screen sizes, with graceful degradation for older devices.
""" 