#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Full Report Tasks Inclusion
Verify that the full report now includes detailed task information
"""

import json
import os
import sys

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "src"))

from core.config import AppConfig
from core.services.statistics_service import StatisticsService
from core.database import DatabaseManagerExtended

def test_full_report_tasks():
    """Test that full report includes detailed task information"""
    print("🔧 Testing Full Report Tasks Inclusion")
    print("=" * 50)
    
    try:
        # 1. Setup database and services
        print("\n1. Setting up services...")
        from pathlib import Path
        db_path = Path("data") / "agevolami_pm.db"
        db_manager = DatabaseManagerExtended(db_path)
        config = AppConfig()
        stats_service = StatisticsService(db_manager, config)
        
        # 2. Load email settings from JSON
        print("\n2. Loading email settings...")
        settings_file = os.path.join("data", "settings.json")
        if os.path.exists(settings_file):
            with open(settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)
            
            email_settings = settings.get('email', {})
            
            # Update email service configuration
            stats_service.email_service.smtp_config.update({
                'smtp_server': email_settings.get('server', ''),
                'smtp_port': email_settings.get('port', 587),
                'smtp_username': email_settings.get('username', ''),
                'smtp_password': email_settings.get('password', ''),
                'smtp_use_tls': email_settings.get('use_tls', True),
                'from_name': email_settings.get('sender_name', 'Agevolami PM'),
                'from_email': email_settings.get('sender_email', ''),
                'enabled': bool(email_settings.get('server'))
            })
            
            print(f"   Email configured: {bool(email_settings.get('server'))}")
        else:
            print("   ❌ Settings file not found!")
            return False
        
        # 3. Generate full report
        print("\n3. Generating full report...")
        report = stats_service.generate_full_report()
        
        if not report:
            print("   ❌ Failed to generate report!")
            return False
        
        print(f"   ✅ Report generated successfully")
        
        # 4. Check report structure
        print("\n4. Checking report structure...")
        print(f"   Report type: {report.get('type', 'N/A')}")
        print(f"   Generated at: {report.get('generated_at', 'N/A')}")
        
        # Check if tasks data is included
        tasks_data = report.get('tasks', {})
        if tasks_data:
            print(f"   ✅ Tasks data included")
            print(f"     - Total tasks: {tasks_data.get('total', 0)}")
            print(f"     - Completed: {tasks_data.get('completed', 0)}")
            print(f"     - Expired: {tasks_data.get('expired', 0)}")
            print(f"     - Expiring soon: {tasks_data.get('expiring_soon', 0)}")
            
            # Check for detailed task lists
            expired_tasks = tasks_data.get('expired_tasks', [])
            expiring_tasks = tasks_data.get('expiring_tasks', [])
            
            print(f"     - Expired tasks list: {len(expired_tasks)} items")
            print(f"     - Expiring tasks list: {len(expiring_tasks)} items")
            
            # Show sample expired tasks
            if expired_tasks:
                print(f"\n   📋 Sample expired tasks:")
                for i, task in enumerate(expired_tasks[:3]):
                    print(f"     {i+1}. {task.get('title', 'N/A')} - {task.get('days_overdue', 0)} days overdue")
                    print(f"        Project: {task.get('project_name', 'N/A')}")
                    print(f"        Deadline: {task.get('deadline_title', 'N/A')}")
            
            # Show sample expiring tasks
            if expiring_tasks:
                print(f"\n   ⏰ Sample expiring tasks:")
                for i, task in enumerate(expiring_tasks[:3]):
                    print(f"     {i+1}. {task.get('title', 'N/A')} - {task.get('days_remaining', 0)} days remaining")
                    print(f"        Project: {task.get('project_name', 'N/A')}")
                    print(f"        Deadline: {task.get('deadline_title', 'N/A')}")
        else:
            print("   ❌ No tasks data found in report!")
            return False
        
        # 5. Generate HTML and check for task sections
        print("\n5. Generating HTML report...")
        html_content = stats_service._generate_email_html(report, 'full')
        
        if not html_content:
            print("   ❌ Failed to generate HTML!")
            return False
        
        print(f"   ✅ HTML generated: {len(html_content)} characters")
        
        # Check for task-related content in HTML
        task_indicators = [
            "Attività Scadute",
            "Attività in Scadenza", 
            "task-item",
            "priority-badge",
            "days-badge overdue",
            "days-badge due-soon"
        ]
        
        found_indicators = []
        for indicator in task_indicators:
            if indicator in html_content:
                found_indicators.append(indicator)
        
        print(f"\n   📊 Task content indicators found: {len(found_indicators)}/{len(task_indicators)}")
        for indicator in found_indicators:
            print(f"     ✅ {indicator}")
        
        missing_indicators = [ind for ind in task_indicators if ind not in found_indicators]
        for indicator in missing_indicators:
            print(f"     ❌ {indicator}")
        
        # 6. Test email sending (optional)
        print("\n6. Testing email functionality...")
        
        # Get recipients
        recipients = []
        if email_settings.get('username'):
            recipients.append(email_settings['username'])
        
        reports_recipients = settings.get('reports', {}).get('recipients', [])
        if reports_recipients:
            recipients.extend(reports_recipients)
        
        recipients = list(set(recipients))  # Remove duplicates
        
        if recipients:
            print(f"   Recipients configured: {len(recipients)}")
            for recipient in recipients:
                print(f"     - {recipient}")
            
            # Test SMTP connection
            connection_success = stats_service.email_service.test_connection()
            print(f"   SMTP connection: {'✅ SUCCESS' if connection_success else '❌ FAILED'}")
            
            if connection_success:
                print(f"\n   📧 Ready to send full report with detailed task information!")
            else:
                print(f"\n   ⚠️ Email configuration issue - report generated but cannot send")
        else:
            print("   ❌ No recipients configured")
        
        print("\n" + "=" * 50)
        print("✅ Full Report Tasks Test COMPLETED")
        print("✅ The full report now includes detailed task information")
        print("✅ Both expired and expiring tasks are listed with details")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_full_report_tasks()
    if success:
        print("\n🎉 Test completed successfully!")
        print("📧 The full report now includes task details from tasks.py!")
    else:
        print("\n💥 Test failed!")
