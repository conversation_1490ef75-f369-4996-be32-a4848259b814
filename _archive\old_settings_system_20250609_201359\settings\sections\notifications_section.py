#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Notifications Settings Section
Modern, self-contained notifications and alerts configuration component
"""

import flet as ft
from typing import Dict, Any, List
from ..components import SettingsCard, SettingsCardSection, FormField, SwitchField, DropdownField, ButtonGroup, InfoBox, CheckboxGroup
from core import get_logger

logger = get_logger(__name__)

class NotificationsSection:
    """
    Self-contained notifications settings section with modern UX
    """
    
    def __init__(self, controller):
        self.controller = controller
        self.notification_settings = controller.get_setting('notifications')
        
        # Validation state
        self.validation_errors = {}
        
        # Register for setting changes
        controller.register_change_callback('notifications', self._on_setting_changed)
    
    def _on_setting_changed(self, key: str, new_value: Any, old_value: Any):
        """Handle setting changes"""
        logger.debug(f"Notification setting changed: {key} = {new_value}")
        self._validate_settings()
    
    def _validate_settings(self):
        """Validate current notification settings"""
        settings = self.controller.get_setting('notifications')
        errors = {}
        
        # Validate advance days
        advance_days = settings.get('advance_days', [])
        if not advance_days:
            errors['advance_days'] = "Almeno un giorno di anticipo è richiesto"
        elif not all(isinstance(d, int) and d > 0 for d in advance_days):
            errors['advance_days'] = "I giorni di anticipo devono essere numeri positivi"
        
        # Validate check interval
        check_interval = settings.get('check_interval', 24)
        if check_interval < 1 or check_interval > 168:  # 1 hour to 1 week
            errors['check_interval'] = "Intervallo deve essere tra 1 e 168 ore"
        
        # Validate recipients if email is enabled
        if settings.get('email_enabled') and settings.get('reminder_recipients'):
            recipients = settings.get('reminder_recipients', [])
            for email in recipients:
                if '@' not in email or '.' not in email.split('@')[1]:
                    errors['recipients'] = f"Email non valida: {email}"
                    break
        
        self.validation_errors = errors
        return len(errors) == 0
    
    def _create_alert_system_section(self) -> SettingsCardSection:
        """Create alert system configuration section"""
        
        def on_enabled_change(e):
            self.controller.set_setting('notifications', 'enabled', e.control.value)
        
        def on_email_enabled_change(e):
            self.controller.set_setting('notifications', 'email_enabled', e.control.value)
        
        def on_working_hours_change(e):
            self.controller.set_setting('notifications', 'working_hours_only', e.control.value)
        
        def on_weekend_alerts_change(e):
            self.controller.set_setting('notifications', 'weekend_alerts', e.control.value)
        
        settings = self.controller.get_setting('notifications')
        
        # Main alert switches
        alerts_enabled_switch = SwitchField.create(
            label="Sistema Alert Attivo",
            description="Abilita il monitoraggio automatico delle scadenze",
            value=settings.get('enabled', True),
            on_change=on_enabled_change,
            color=ft.Colors.GREEN_600
        )
        
        email_alerts_switch = SwitchField.create(
            label="Notifiche Email",
            description="Invia alert via email quando configurato",
            value=settings.get('email_enabled', False),
            on_change=on_email_enabled_change,
            disabled=not settings.get('enabled', True),
            color=ft.Colors.BLUE_600
        )
        
        # Timing options
        working_hours_switch = SwitchField.create(
            label="Solo Orari Lavorativi (9-18)",
            description="Limita gli alert agli orari di ufficio",
            value=settings.get('working_hours_only', True),
            on_change=on_working_hours_change,
            disabled=not settings.get('enabled', True)
        )
        
        weekend_alerts_switch = SwitchField.create(
            label="Alert nei Weekend",
            description="Invia notifiche anche sabato e domenica",
            value=settings.get('weekend_alerts', False),
            on_change=on_weekend_alerts_change,
            disabled=not settings.get('enabled', True)
        )
        
        timing_row = ft.Row([
            ft.Container(
                content=working_hours_switch,
                expand=True
            ),
            ft.Container(
                content=weekend_alerts_switch,
                expand=True
            )
        ], spacing=16)
        
        return SettingsCardSection(
            title="Sistema Alert",
            description="Configurazione generale del sistema di notifiche",
            controls=[
                alerts_enabled_switch,
                email_alerts_switch,
                ft.Container(height=8),  # Spacer
                timing_row
            ]
        )
    
    def _create_timing_configuration_section(self) -> SettingsCardSection:
        """Create timing configuration section"""
        
        def on_check_interval_change(value):
            self.controller.set_setting('notifications', 'check_interval', value)
        
        def on_advance_days_change(e):
            try:
                # Parse comma-separated days
                days_text = e.control.value.strip()
                if days_text:
                    days = [int(d.strip()) for d in days_text.split(',') if d.strip().isdigit()]
                    days = sorted(list(set(days)))  # Remove duplicates and sort
                    self.controller.set_setting('notifications', 'advance_days', days)
                else:
                    self.controller.set_setting('notifications', 'advance_days', [])
            except ValueError:
                logger.warning(f"Invalid advance days format: {e.control.value}")
        
        settings = self.controller.get_setting('notifications')
        
        check_interval_field = FormField.create_number_field(
            label="Intervallo Controllo (ore)",
            value=settings.get('check_interval', 24),
            min_value=1,
            max_value=168,
            width=150,
            on_change=on_check_interval_change
        )
        
        # Format advance days for display
        advance_days = settings.get('advance_days', [1, 3, 7, 15])
        advance_days_text = ", ".join(map(str, sorted(advance_days)))
        
        advance_days_field = FormField.create_text_field(
            label="Giorni di Anticipo",
            value=advance_days_text,
            hint_text="1, 3, 7, 15",
            validation_message=self.validation_errors.get('advance_days'),
            on_change=on_advance_days_change,
            prefix_icon=ft.Icons.CALENDAR_TODAY
        )
        
        timing_row = ft.Row([
            check_interval_field,
            advance_days_field
        ], spacing=16)
        
        return SettingsCardSection(
            title="Configurazione Temporale",
            description="Quando e con che frequenza controllare le scadenze",
            controls=[
                timing_row,
                InfoBox.create(
                    title="💡 Suggerimento Tempistiche",
                    message="Configurazione ottimale per il monitoraggio delle scadenze",
                    type="info",
                    items=[
                        "Controllo ogni 24 ore è ideale per la maggior parte dei casi",
                        "Giorni di anticipo: 1, 3, 7, 15 coprono tutte le necessità",
                        "Orari lavorativi evitano notifiche fuori orario",
                        "Weekend alerts solo se necessario per urgenze"
                    ]
                )
            ]
        )
    
    def _create_recipients_section(self) -> SettingsCardSection:
        """Create email recipients configuration section"""
        
        def on_recipients_change(e):
            try:
                recipients_text = e.control.value.strip()
                if recipients_text:
                    recipients = [email.strip() for email in recipients_text.split(',') if email.strip()]
                    self.controller.set_setting('notifications', 'reminder_recipients', recipients)
                else:
                    self.controller.set_setting('notifications', 'reminder_recipients', [])
            except Exception as ex:
                logger.error(f"Error updating recipients: {ex}")
        
        settings = self.controller.get_setting('notifications')
        recipients = settings.get('reminder_recipients', [])
        recipients_text = ", ".join(recipients)
        
        recipients_field = FormField.create_text_field(
            label="Destinatari Email Alert",
            value=recipients_text,
            hint_text="<EMAIL>, <EMAIL>",
            validation_message=self.validation_errors.get('recipients'),
            on_change=on_recipients_change,
            prefix_icon=ft.Icons.PEOPLE
        )
        
        # Recipients management
        recipient_count = len(recipients)
        status_text = f"📧 {recipient_count} destinatar{'i' if recipient_count != 1 else 'o'} configurato/i"
        
        status_indicator = ft.Container(
            content=ft.Row([
                ft.Icon(
                    ft.Icons.EMAIL,
                    size=16,
                    color=ft.Colors.GREEN_600 if recipient_count > 0 else ft.Colors.GREY_500
                ),
                ft.Text(
                    status_text,
                    size=12,
                    color=ft.Colors.GREEN_600 if recipient_count > 0 else ft.Colors.GREY_500
                )
            ], spacing=8),
            padding=ft.padding.all(8),
            bgcolor=ft.Colors.GREEN_50 if recipient_count > 0 else ft.Colors.GREY_100,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.GREEN_200 if recipient_count > 0 else ft.Colors.GREY_300)
        )
        
        return SettingsCardSection(
            title="Destinatari Email",
            description="Chi riceve le notifiche email (oltre al mittente configurato)",
            controls=[
                recipients_field,
                status_indicator,
                InfoBox.create(
                    title="ℹ️ Informazioni Destinatari",
                    message="Gestione dei destinatari delle notifiche email",
                    type="info",
                    items=[
                        "Lascia vuoto per inviare solo al mittente SMTP configurato",
                        "Separa più email con virgole",
                        "Verifica che le email siano valide prima di salvare",
                        "I destinatari ricevono tutti gli alert configurati"
                    ]
                )
            ]
        )
    
    def _create_alert_testing_section(self) -> SettingsCardSection:
        """Create alert testing section"""
        
        def test_alert_system(e):
            """Test the complete alert system"""
            settings = self.controller.get_setting('notifications')
            
            if not settings.get('enabled'):
                self._show_error_notification("Sistema alert disabilitato")
                return
            
            # Simulate alert creation and sending
            self._show_success_notification("Test alert avviato - controlla email se configurate")
            
            # Here you would integrate with the actual alert system
            logger.info("Alert system test initiated")
        
        def send_test_notification(e):
            """Send a test notification"""
            self._show_success_notification("Notifica di test inviata!")
        
        # Test buttons
        test_actions = ButtonGroup.create_action_group([
            {
                "text": "Test Sistema Completo",
                "icon": ft.Icons.PLAY_CIRCLE_FILLED,
                "on_click": test_alert_system,
                "style": "primary"
            },
            {
                "text": "Test Notifica",
                "icon": ft.Icons.NOTIFICATIONS_ACTIVE,
                "on_click": send_test_notification,
                "style": "secondary"
            }
        ])
        
        # Current status summary
        settings = self.controller.get_setting('notifications')
        status_items = []
        
        if settings.get('enabled'):
            status_items.append("✅ Sistema alert attivo")
        else:
            status_items.append("❌ Sistema alert disattivato")
        
        if settings.get('email_enabled'):
            status_items.append("✅ Notifiche email abilitate")
        else:
            status_items.append("⚠️ Notifiche email disabilitate")
        
        if settings.get('reminder_recipients'):
            count = len(settings.get('reminder_recipients', []))
            status_items.append(f"📧 {count} destinatari configurati")
        else:
            status_items.append("⚠️ Nessun destinatario aggiuntivo")
        
        status_summary = InfoBox.create(
            title="📊 Stato Attuale Sistema",
            message="Riepilogo configurazione notifiche",
            type="info",
            items=status_items
        )
        
        return SettingsCardSection(
            title="Test e Verifica",
            description="Testa il sistema di notifiche prima di utilizzarlo",
            controls=[
                status_summary,
                test_actions
            ]
        )
    
    def _show_success_notification(self, message: str):
        """Show success notification"""
        logger.info(f"SUCCESS: {message}")
    
    def _show_error_notification(self, message: str):
        """Show error notification"""
        logger.error(f"ERROR: {message}")
    
    def build(self) -> SettingsCard:
        """Build the notifications settings card"""
        
        # Validate current settings
        self._validate_settings()
        
        # Create sections
        alert_system_section = self._create_alert_system_section()
        timing_section = self._create_timing_configuration_section()
        recipients_section = self._create_recipients_section()
        testing_section = self._create_alert_testing_section()
        
        # Create card content
        card_content = [
            alert_system_section.build(),
            timing_section.build(),
            recipients_section.build(),
            testing_section.build()
        ]
        
        # Create save/reset actions
        def save_settings(e):
            if self._validate_settings():
                success = self.controller.save_settings()
                if success:
                    self._show_success_notification("Impostazioni notifiche salvate!")
                else:
                    self._show_error_notification("Errore durante il salvataggio")
            else:
                self._show_error_notification("Correggi gli errori prima di salvare")
        
        def reset_settings(e):
            self.controller.reset_to_defaults('notifications')
            self._show_success_notification("Impostazioni notifiche ripristinate")
        
        card_actions = [
            ft.ElevatedButton(
                text="Salva Notifiche",
                icon=ft.Icons.SAVE,
                on_click=save_settings,
                bgcolor=ft.Colors.ORANGE_600,
                color=ft.Colors.WHITE,
                disabled=len(self.validation_errors) > 0
            ),
            ft.OutlinedButton(
                text="Ripristina",
                icon=ft.Icons.RESTORE,
                on_click=reset_settings
            )
        ]
        
        # Determine card status
        settings = self.controller.get_setting('notifications')
        card_icon = ft.Icons.NOTIFICATIONS
        card_icon_color = ft.Colors.ORANGE_600
        
        if self.validation_errors:
            card_icon = ft.Icons.ERROR_OUTLINE
            card_icon_color = ft.Colors.RED_600
        elif settings.get('enabled') and settings.get('email_enabled'):
            card_icon = ft.Icons.NOTIFICATIONS_ACTIVE
            card_icon_color = ft.Colors.GREEN_600
        elif not settings.get('enabled'):
            card_icon = ft.Icons.NOTIFICATIONS_OFF
            card_icon_color = ft.Colors.GREY_600
        
        return SettingsCard(
            title="Sistema Notifiche e Alert",
            description="Configura quando e come ricevere le notifiche per le scadenze imminenti",
            icon=card_icon,
            icon_color=card_icon_color,
            expanded=True,
            content=card_content,
            actions=card_actions,
            accent_color=ft.Colors.ORANGE_600
        ) 