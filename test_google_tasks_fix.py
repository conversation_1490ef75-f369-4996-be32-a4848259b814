#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to verify Google Tasks synchronization fixes
"""

import sys
import os
from pathlib import Path

# Add src directory to path
src_dir = Path(__file__).parent / "src"
sys.path.insert(0, str(src_dir))

from core.services.google_tasks_service import GoogleTasksService
from core import get_logger

logger = get_logger(__name__)

def test_google_tasks_authentication():
    """Test Google Tasks authentication status"""
    print("🔍 Testing Google Tasks Authentication Status...")

    try:
        # Initialize Google Tasks service with proper config directory (same as app)
        from core.config.app_config import AppConfig
        config = AppConfig()
        config_dir = os.path.join(config.data_dir, "config")
        service = GoogleTasksService(config_dir=config_dir)

        # Get detailed status
        status = service.get_authentication_status()

        print("\n📊 Google Tasks Status:")
        print(f"  • Google Libraries Available: {status['google_available']}")
        print(f"  • Service Enabled: {status['service_enabled']}")
        print(f"  • Service Object Created: {status['service_object']}")
        print(f"  • Token File Exists: {status['token_file_exists']}")
        print(f"  • Is Enabled: {status['is_enabled']}")
        print(f"  • Is Authenticated: {status['is_authenticated']}")

        if status['token_file_exists']:
            print(f"  • Token File Path: {service.token_file}")

        # If service shows as not enabled but token exists, try to re-check auth
        if status['token_file_exists'] and not status['service_enabled']:
            print("\n🔄 Token file exists but service not enabled, re-checking authentication...")
            auth_recheck = service._check_existing_auth()
            print(f"  • Re-check Authentication: {'✅ Success' if auth_recheck else '❌ Failed'}")

            # Get updated status
            status = service.get_authentication_status()
            print(f"  • Updated Service Enabled: {status['service_enabled']}")
            print(f"  • Updated Is Authenticated: {status['is_authenticated']}")

        # Test connection if authenticated
        if status['is_authenticated']:
            print("\n🔗 Testing Connection...")
            connection_test = service.test_connection()
            print(f"  • Connection Test: {'✅ Success' if connection_test else '❌ Failed'}")

            if connection_test:
                # Try to get task lists
                try:
                    task_lists = service.get_task_lists()
                    print(f"  • Available Task Lists: {len(task_lists)}")
                    for task_list in task_lists[:3]:  # Show first 3
                        print(f"    - {task_list.get('title', 'Unnamed')}")
                except Exception as e:
                    print(f"  • Error getting task lists: {e}")
        else:
            print("\n⚠️  Google Tasks is not authenticated")
            print("   To authenticate, go to Settings > Google Services and connect Google Tasks")

        return status['is_authenticated']

    except Exception as e:
        print(f"❌ Error testing Google Tasks: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_settings_integration():
    """Test settings integration"""
    print("\n🔍 Testing Settings Integration...")
    
    try:
        # This would normally be done by the app, but we'll simulate it
        from ui.views.settings import SettingsView
        from core.config.app_config import AppConfig
        
        # Create mock app instance
        class MockApp:
            def __init__(self):
                self.config = AppConfig()
        
        mock_app = MockApp()
        settings_view = SettingsView(mock_app)
        
        # Load Google services settings
        settings_view._load_google_services_settings()
        
        # Check the settings
        google_settings = settings_view.google_services_settings
        
        print("\n📊 Settings Status:")
        print(f"  • Tasks Enabled: {google_settings.get('tasks_enabled', False)}")
        print(f"  • Tasks Authenticated: {google_settings.get('tasks_authenticated', False)}")
        print(f"  • Tasks Auto Sync: {google_settings.get('tasks_auto_sync', True)}")
        
        return google_settings.get('tasks_authenticated', False)
        
    except Exception as e:
        print(f"❌ Error testing settings integration: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Google Tasks Synchronization Fix Test")
    print("=" * 50)
    
    # Test 1: Google Tasks Authentication
    auth_status = test_google_tasks_authentication()
    
    # Test 2: Settings Integration
    settings_status = test_settings_integration()
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 Test Summary:")
    print(f"  • Google Tasks Authentication: {'✅ Working' if auth_status else '❌ Not Working'}")
    print(f"  • Settings Integration: {'✅ Working' if settings_status else '❌ Not Working'}")
    
    if auth_status and settings_status:
        print("\n🎉 All tests passed! Google Tasks synchronization should work properly.")
        print("\n💡 Next steps:")
        print("   1. Run the application")
        print("   2. Go to Tasks view")
        print("   3. Create/edit/delete tasks to test auto-sync")
        print("   4. Use the sync button to manually sync all tasks")
    else:
        print("\n⚠️  Some tests failed. Check the issues above.")
        if not auth_status:
            print("   • Google Tasks needs to be authenticated in Settings")
        if not settings_status:
            print("   • Settings integration needs to be fixed")

if __name__ == "__main__":
    main()
