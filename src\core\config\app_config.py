#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configurazione dell'applicazione Agevolami PM
"""

import os
from pathlib import Path
from typing import Dict, Any
from dotenv import load_dotenv

# Carica variabili d'ambiente
load_dotenv()

class AppConfig:
    """
    Gestione configurazioni dell'applicazione
    """
    
    def __init__(self):
        self.app_name = "Agevolami PM"
        self.version = "1.0.0"
        self.company = "Agevolami S.r.l."
        
        # Percorsi applicazione
        self.base_dir = Path(__file__).parent.parent.parent.parent
        self.src_dir = self.base_dir / "src"
        self.data_dir = self.base_dir / "data"
        self.assets_dir = self.base_dir / "assets"
        self.logs_dir = self.base_dir / "logs"
        
        # Crea cartelle se non esistono
        self._create_directories()
        
        # Database
        self.database_path = self.data_dir / "agevolami_pm.db"
        
        # Configurazioni UI
        self.ui_config = {
            "primary_color": "#1565C0",  # Blue 800
            "secondary_color": "#0D47A1",  # Blue 900
            "accent_color": "#42A5F5",  # Blue 400
            "success_color": "#4CAF50",  # Green 500
            "warning_color": "#FF9800",  # Orange 500
            "error_color": "#F44336",  # Red 500
            "background_color": "#FAFAFA",  # Grey 50
            "surface_color": "#FFFFFF",  # White
            "text_primary": "#212121",  # Grey 900
            "text_secondary": "#757575",  # Grey 600
        }
        
        # Configurazioni email SMTP
        self.email_config = {
            "smtp_server": os.getenv("SMTP_SERVER", ""),
            "smtp_port": int(os.getenv("SMTP_PORT", "587")),
            "smtp_username": os.getenv("SMTP_USERNAME", ""),
            "smtp_password": os.getenv("SMTP_PASSWORD", ""),
            "smtp_use_tls": os.getenv("SMTP_USE_TLS", "true").lower() == "true",
            "from_email": os.getenv("FROM_EMAIL", "<EMAIL>"),
            "from_name": os.getenv("FROM_NAME", "Agevolami PM")
        }
        
        # Configurazioni alert
        self.alert_config = {
            "days_before_deadline": 15,
            "check_interval_hours": 24,
            "max_alerts_per_deadline": 10,
            "max_days_ahead": 30,
            "email_notifications_enabled": True,
            "desktop_notifications_enabled": True
        }
        
        # Configurazioni progetti
        self.project_types = {
            "accordi_innovazione": {
                "name": "Accordi per l'Innovazione",
                "description": "Progetti di ricerca e sviluppo",
                "max_duration_months": 36,
                "min_budget": 5000000,  # 5 milioni
                "requires_sal": True
            },
            "investimenti_sostenibili_40": {
                "name": "Investimenti Sostenibili 4.0",
                "description": "Incentivi per PMI del Mezzogiorno",
                "max_duration_months": 18,
                "min_budget": 750000,  # 750k
                "requires_sal": True
            },
            "contratti_sviluppo": {
                "name": "Contratti di Sviluppo",
                "description": "Programmi investimento grandi dimensioni",
                "max_duration_months": 36,
                "min_budget": 20000000,  # 20 milioni
                "requires_sal": True
            },
            "mini_contratti_sviluppo": {
                "name": "Mini Contratti di Sviluppo",
                "description": "Investimenti 5-20 milioni euro",
                "max_duration_months": 36,
                "min_budget": 5000000,  # 5 milioni
                "max_budget": 20000000,  # 20 milioni
                "requires_sal": True
            },
            "scadenze_fiscali": {
                "name": "Scadenze Fiscali",
                "description": "Adempimenti tributari e fiscali",
                "requires_sal": False
            }
        }
        
    def _create_directories(self):
        """
        Crea le cartelle necessarie se non esistono
        """
        directories = [self.data_dir, self.assets_dir, self.logs_dir]
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    def get_project_type_config(self, project_type: str) -> Dict[str, Any]:
        """
        Restituisce la configurazione per un tipo di progetto
        """
        return self.project_types.get(project_type, {})
    
    def is_email_configured(self) -> bool:
        """
        Verifica se la configurazione email è completa
        """
        required_fields = ["smtp_server", "smtp_username", "smtp_password"]
        return all(self.email_config.get(field) for field in required_fields)
    
    def get_database_url(self) -> str:
        """
        Restituisce l'URL del database SQLite
        """
        return f"sqlite:///{self.database_path}"
    
    def get_logs_path(self) -> Path:
        """
        Restituisce il percorso dei file di log
        """
        return self.logs_dir / "agevolami_pm.log"