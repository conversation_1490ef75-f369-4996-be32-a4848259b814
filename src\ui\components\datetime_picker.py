import flet as ft
from datetime import datetime, date, time
from typing import Optional, Callable


class DateTimePicker(ft.Container):
    """
    A combined date and time picker component for Flet applications.
    Provides both date selection (via calendar) and time selection (via dropdowns).
    """
    
    def __init__(
        self,
        label: str = "Seleziona Data e Ora",
        value: Optional[datetime] = None,
        on_change: Optional[Callable[[datetime], None]] = None,
        min_date: Optional[date] = None,
        max_date: Optional[date] = None,
        include_time: bool = True,
        page=None,
        **kwargs
    ):
        self.label = label
        self._value = value
        self.on_change = on_change
        self.min_date = min_date or date(2020, 1, 1)
        self.max_date = max_date or date(2030, 12, 31)
        self.include_time = include_time
        self.page = page
        
        # Stato del calendario
        if value:
            self.current_month = value.month
            self.current_year = value.year
        else:
            today = date.today()
            self.current_month = today.month
            self.current_year = today.year
            
        self.calendar_visible = False
        
        # Componenti
        self.date_field = None
        self.time_hour_dropdown = None
        self.time_minute_dropdown = None
        self.calendar_container = None
        self.calendar_dialog = None
        
        # Inizializza il contenuto
        content = self._build_content()
        super().__init__(content=content, **kwargs)
        
    @property
    def value(self) -> Optional[datetime]:
        return self._value
    
    @value.setter
    def value(self, new_value: Optional[datetime]):
        self._value = new_value
        self._update_display()

    def set_page(self, page):
        """Set the page reference for dialog functionality"""
        self.page = page

    def did_mount(self):
        """Called when the component is mounted to the page"""
        # Try to get page reference from the component hierarchy
        if not self.page and hasattr(self, 'page'):
            self.page = self.page
    
    def _update_display(self):
        """Aggiorna la visualizzazione dei campi"""
        if self.date_field:
            if self._value:
                self.date_field.value = self._value.strftime("%d/%m/%Y")
                if self.include_time and self.time_hour_dropdown and self.time_minute_dropdown:
                    self.time_hour_dropdown.value = str(self._value.hour).zfill(2)
                    # Handle minute values that aren't in the dropdown options
                    minute_str = str(self._value.minute).zfill(2)
                    valid_minutes = ["00", "15", "30", "45"]
                    self.time_minute_dropdown.value = minute_str if minute_str in valid_minutes else "00"
            else:
                self.date_field.value = ""
                if self.include_time and self.time_hour_dropdown and self.time_minute_dropdown:
                    self.time_hour_dropdown.value = "09"
                    self.time_minute_dropdown.value = "00"

            self.date_field.update()
            if self.include_time and self.time_hour_dropdown and self.time_minute_dropdown:
                self.time_hour_dropdown.update()
                self.time_minute_dropdown.update()
    
    def _build_content(self):
        # Campo di input per la data
        self.date_field = ft.TextField(
            label=self.label,
            value=self._value.strftime("%d/%m/%Y") if self._value else "",
            hint_text="DD/MM/YYYY",
            suffix_icon=ft.Icons.CALENDAR_MONTH,
            on_change=self._on_date_text_change,
            expand=True,
            read_only=False  # Allow manual input
        )
        
        # Controlli per l'ora (se abilitati)
        time_controls = []
        if self.include_time:
            # Dropdown per le ore (00-23)
            hours = [str(i).zfill(2) for i in range(24)]
            self.time_hour_dropdown = ft.Dropdown(
                label="Ora",
                options=[ft.dropdown.Option(h) for h in hours],
                value=str(self._value.hour).zfill(2) if self._value else "09",
                width=80,
                on_change=self._on_time_change
            )
            
            # Dropdown per i minuti (00, 15, 30, 45)
            minutes = ["00", "15", "30", "45"]
            current_minute = "00"
            if self._value:
                minute_str = str(self._value.minute).zfill(2)
                current_minute = minute_str if minute_str in minutes else "00"

            self.time_minute_dropdown = ft.Dropdown(
                label="Min",
                options=[ft.dropdown.Option(m) for m in minutes],
                value=current_minute,
                width=80,
                on_change=self._on_time_change
            )
            
            time_controls = [
                ft.Text("🕐", size=16),
                self.time_hour_dropdown,
                ft.Text(":", size=16),
                self.time_minute_dropdown
            ]
        
        # Contenitore principale
        main_row = [
            self.date_field,
            ft.IconButton(
                icon=ft.Icons.CALENDAR_TODAY,
                tooltip="Mostra/Nascondi Calendario",
                on_click=self._toggle_calendar
            )
        ]

        if time_controls:
            main_row.extend(time_controls)

        # Calendar container (initially hidden)
        self.calendar_container = ft.Container(
            content=self._build_calendar(),
            visible=False,
            margin=ft.margin.only(top=8),
            padding=ft.padding.all(8),
            bgcolor=ft.Colors.WHITE,
            border=ft.border.all(1, ft.Colors.GREY_300),
            border_radius=8,
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=4,
                color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
                offset=ft.Offset(0, 2)
            )
        )

        return ft.Column([
            ft.Row(main_row, alignment=ft.MainAxisAlignment.START),
            self.calendar_container
        ])
    
    def _on_date_text_change(self, e):
        """Gestisce il cambio di testo nel campo data"""
        try:
            if e.control.value:
                # Prova a parsare la data nel formato DD/MM/YYYY
                parsed_date = datetime.strptime(e.control.value, "%d/%m/%Y").date()
                
                # Verifica che sia nel range consentito
                if self.min_date <= parsed_date <= self.max_date:
                    # Mantieni l'ora esistente o usa 09:00 come default
                    if self._value:
                        new_datetime = datetime.combine(parsed_date, self._value.time())
                    else:
                        default_time = time(9, 0) if self.include_time else time(0, 0)
                        new_datetime = datetime.combine(parsed_date, default_time)
                    
                    self._value = new_datetime
                    if self.on_change:
                        self.on_change(self._value)
        except ValueError:
            pass  # Ignora errori di parsing
        
        e.control.update()
    
    def _on_time_change(self, e):
        """Gestisce il cambio di ora"""
        if not self._value:
            # Se non c'è una data, usa oggi
            today = date.today()
            self._value = datetime.combine(today, time(9, 0))
        
        try:
            hour = int(self.time_hour_dropdown.value) if self.time_hour_dropdown.value else 9
            minute = int(self.time_minute_dropdown.value) if self.time_minute_dropdown.value else 0
            
            new_time = time(hour, minute)
            self._value = datetime.combine(self._value.date(), new_time)
            
            if self.on_change:
                self.on_change(self._value)
                
        except (ValueError, AttributeError):
            pass  # Ignora errori
    
    def _toggle_calendar(self, e=None):
        """Mostra/nasconde il calendario inline"""
        if self.calendar_container:
            self.calendar_container.visible = not self.calendar_container.visible
            if self.calendar_container.visible:
                # Update calendar content when showing
                if self._value:
                    self.current_month = self._value.month
                    self.current_year = self._value.year
                self.calendar_container.content = self._build_calendar()
            self.calendar_container.update()
    
    def _select_today(self, e):
        """Seleziona la data di oggi"""
        today = date.today()
        if self.include_time:
            # Mantieni l'ora esistente o usa 09:00 come default
            if self._value:
                new_datetime = datetime.combine(today, self._value.time())
            else:
                new_datetime = datetime.combine(today, time(9, 0))
        else:
            new_datetime = datetime.combine(today, time(0, 0))

        self._value = new_datetime
        self._update_display()

        if self.on_change:
            self.on_change(self._value)

        # Hide calendar after selection
        if self.calendar_container:
            self.calendar_container.visible = False
            self.calendar_container.update()

    def _select_date(self, selected_date: date):
        """Seleziona una data specifica"""
        if self.include_time:
            # Mantieni l'ora esistente o usa 09:00 come default
            if self._value:
                new_datetime = datetime.combine(selected_date, self._value.time())
            else:
                new_datetime = datetime.combine(selected_date, time(9, 0))
        else:
            new_datetime = datetime.combine(selected_date, time(0, 0))

        self._value = new_datetime
        self._update_display()

        if self.on_change:
            self.on_change(self._value)

        # Hide calendar after selection
        if self.calendar_container:
            self.calendar_container.visible = False
            self.calendar_container.update()

    def _build_calendar(self):
        """Costruisce il calendario"""
        import calendar

        # Header con navigazione mese/anno
        month_names = [
            "Gennaio", "Febbraio", "Marzo", "Aprile", "Maggio", "Giugno",
            "Luglio", "Agosto", "Settembre", "Ottobre", "Novembre", "Dicembre"
        ]

        header = ft.Row([
            ft.IconButton(ft.Icons.CHEVRON_LEFT, on_click=self._prev_month),
            ft.Text(
                f"{month_names[self.current_month - 1]} {self.current_year}",
                size=16,
                weight=ft.FontWeight.BOLD,
                expand=True,
                text_align=ft.TextAlign.CENTER
            ),
            ft.IconButton(ft.Icons.CHEVRON_RIGHT, on_click=self._next_month),
        ])

        # Giorni della settimana
        weekdays = ft.Row([
            ft.Container(
                content=ft.Text(day, size=12, weight=ft.FontWeight.BOLD, text_align=ft.TextAlign.CENTER),
                width=40,
                height=30,
                alignment=ft.alignment.center
            )
            for day in ["Lun", "Mar", "Mer", "Gio", "Ven", "Sab", "Dom"]
        ])

        # Griglia dei giorni
        cal = calendar.monthcalendar(self.current_year, self.current_month)
        day_rows = []

        for week in cal:
            day_cells = []
            for day in week:
                if day == 0:
                    # Giorno vuoto
                    day_cells.append(ft.Container(width=40, height=40))
                else:
                    day_date = date(self.current_year, self.current_month, day)
                    is_today = day_date == date.today()
                    is_selected = self._value and day_date == self._value.date()
                    is_disabled = day_date < self.min_date or day_date > self.max_date

                    # Colori
                    if is_disabled:
                        bg_color = ft.Colors.GREY_200
                        text_color = ft.Colors.GREY_400
                    elif is_selected:
                        bg_color = ft.Colors.BLUE
                        text_color = ft.Colors.WHITE
                    elif is_today:
                        bg_color = ft.Colors.BLUE_100
                        text_color = ft.Colors.BLUE_800
                    else:
                        bg_color = ft.Colors.TRANSPARENT
                        text_color = ft.Colors.BLACK

                    day_button = ft.Container(
                        content=ft.Text(
                            str(day),
                            size=14,
                            color=text_color,
                            text_align=ft.TextAlign.CENTER
                        ),
                        width=40,
                        height=40,
                        bgcolor=bg_color,
                        border_radius=20,
                        alignment=ft.alignment.center,
                        on_click=None if is_disabled else lambda _, d=day_date: self._select_date(d),
                        ink=not is_disabled
                    )

                    day_cells.append(day_button)

            day_rows.append(ft.Row(day_cells))

        # Today button
        today_button = ft.ElevatedButton(
            text="Oggi",
            on_click=self._select_today,
            style=ft.ButtonStyle(
                bgcolor=ft.Colors.BLUE_600,
                color=ft.Colors.WHITE,
                padding=ft.padding.symmetric(horizontal=16, vertical=8),
                shape=ft.RoundedRectangleBorder(radius=6)
            ),
            height=32
        )

        return ft.Container(
            content=ft.Column([
                header,
                ft.Divider(),
                weekdays,
                ft.Column(day_rows),
                ft.Container(height=8),
                ft.Row([today_button], alignment=ft.MainAxisAlignment.CENTER)
            ]),
            width=300,
            padding=10
        )

    def _prev_month(self, e):
        """Mese precedente"""
        if self.current_month == 1:
            self.current_month = 12
            self.current_year -= 1
        else:
            self.current_month -= 1

        if self.calendar_container:
            self.calendar_container.content = self._build_calendar()
            self.calendar_container.update()

    def _next_month(self, e):
        """Mese successivo"""
        if self.current_month == 12:
            self.current_month = 1
            self.current_year += 1
        else:
            self.current_month += 1

        if self.calendar_container:
            self.calendar_container.content = self._build_calendar()
            self.calendar_container.update()
