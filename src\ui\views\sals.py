# -*- coding: utf-8 -*-
"""
Vista gestione SAL (Stati Avanzamento Lavori) per Agevolami PM
"""

import flet as ft
from datetime import datetime, date
from typing import Optional, List, Dict, Any
from uuid import UUID

from core.models import (
    SAL, Project, Client, Deadline, Document,
    ProjectStatus, SALType, SALMilestoneType, Priority
)
from core.services.sal_service import SALService
from core.utils import get_logger

logger = get_logger(__name__)

class SALsView:
    """Vista per la gestione completa dei SAL"""
    
    def __init__(self, app):
        self.app = app
        self.page = app.page
        self.db = app.db
        
        # Inizializza il servizio SAL
        self.sal_service = SALService(self.db)
        
        # Dati
        self.sals_data: List[SAL] = []
        self.projects_data: List[Project] = []
        self.clients_data: List[Client] = []
        self.filtered_sals: List[SAL] = []
        
        # Filtri
        self.selected_project_filter = None
        self.selected_status_filter = None
        self.search_query = ""
        
        # UI Components
        self.content = ft.Column()
        self.header_section = ft.Container()
        self.filters_section = ft.Container()
        self.sals_list_section = ft.Container()
        
        # Form components
        self.selected_sal: Optional[SAL] = None
        self.form_visible = False
        
    def build(self) -> ft.Control:
        """Costruisce la vista SAL"""
        try:
            self.refresh_data()
            self._build_header()
            self._build_filters()
            self._build_sals_list()
            
            self.content = ft.Column(
                controls=[
                    self.header_section,
                    ft.Container(height=12),
                    self.filters_section,
                    ft.Container(height=12),
                    self.sals_list_section
                ],
                spacing=0,
                expand=True,
                scroll=ft.ScrollMode.AUTO
            )
            
            return ft.Container(
                content=self.content,
                expand=True,
                bgcolor=ft.Colors.GREY_50,
                padding=12
            )
            
        except Exception as e:
            logger.error(f"Errore costruzione vista SAL: {e}")
            return ft.Container(
                content=ft.Text(f"Errore caricamento SAL: {e}", color=ft.Colors.RED),
                padding=20
            )
    
    def refresh_data(self):
        """Ricarica i dati SAL"""
        try:
            # Carica tutti i SAL
            all_sals = []
            self.projects_data = self.db.get_all_projects()
            
            for project in self.projects_data:
                project_sals = self.db.get_sals_by_project(project.id)
                all_sals.extend(project_sals)
            
            self.sals_data = all_sals
            self.clients_data = self.db.get_all_clients()
            
            # Applica filtri
            self._apply_filters()
            
            logger.info(f"Caricati {len(self.sals_data)} SAL")
            
        except Exception as e:
            logger.error(f"Errore caricamento dati SAL: {e}")
            self.sals_data = []
            self.filtered_sals = []
    
    def _build_header(self):
        """Costruisce la sezione header"""
        # Statistiche rapide
        total_sals = len(self.sals_data)
        completed_sals = len([sal for sal in self.sals_data if sal.status == ProjectStatus.COMPLETED])
        in_progress_sals = len([sal for sal in self.sals_data if sal.status == ProjectStatus.IN_PROGRESS])
        
        stats_cards = ft.Row(
            controls=[
                self._create_stat_card("Totali", str(total_sals), ft.Icons.TIMELINE, ft.Colors.BLUE_600),
                self._create_stat_card("Completati", str(completed_sals), ft.Icons.CHECK_CIRCLE, ft.Colors.GREEN_600),
                self._create_stat_card("In Corso", str(in_progress_sals), ft.Icons.PLAY_CIRCLE, ft.Colors.ORANGE_600),
                self._create_stat_card("Progetti con SAL", str(len(set(sal.project_id for sal in self.sals_data))), ft.Icons.FOLDER, ft.Colors.PURPLE_600)
            ],
            spacing=12,
            wrap=True
        )
        
        # Header con titolo e azioni
        header_content = ft.Column(
            controls=[
                ft.Row(
                    controls=[
                        ft.Text(
                            "Gestione SAL",
                            size=28,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.GREY_900
                        ),
                        ft.ElevatedButton(
                            "Nuovo SAL",
                            icon=ft.Icons.ADD_OUTLINED,
                            on_click=lambda _: self._show_sal_form(),
                            bgcolor=ft.Colors.BLUE_600,
                            color=ft.Colors.WHITE,
                            height=40
                        )
                    ],
                    alignment=ft.MainAxisAlignment.SPACE_BETWEEN
                ),
                ft.Container(height=16),
                stats_cards
            ],
            spacing=0
        )
        
        self.header_section = ft.Container(
            content=header_content,
            bgcolor=ft.Colors.WHITE,
            padding=20,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )
    
    def _create_stat_card(self, title: str, value: str, icon: str, color: str) -> ft.Card:
        """Crea una card statistica"""
        return ft.Card(
            content=ft.Container(
                content=ft.Column(
                    controls=[
                        ft.Row(
                            controls=[
                                ft.Container(
                                    content=ft.Icon(icon, color=ft.Colors.WHITE, size=16),
                                    bgcolor=color,
                                    padding=6,
                                    border_radius=6
                                ),
                                ft.Container(expand=True)
                            ]
                        ),
                        ft.Container(height=6),
                        ft.Text(
                            value,
                            size=18,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.GREY_900
                        ),
                        ft.Container(height=4),
                        ft.Text(
                            title,
                            size=11,
                            color=ft.Colors.GREY_700,
                            max_lines=2,
                            overflow=ft.TextOverflow.ELLIPSIS
                        )
                    ],
                    spacing=0,
                    horizontal_alignment=ft.CrossAxisAlignment.START
                ),
                padding=12,
                width=140,
                height=90
            ),
            elevation=1
        )
    
    def _build_filters(self):
        """Costruisce la sezione filtri"""
        # Dropdown progetti
        project_options = [ft.dropdown.Option(text="Tutti i progetti", key="")]
        for project in self.projects_data:
            if project.requires_sal:
                project_options.append(
                    ft.dropdown.Option(text=project.name, key=str(project.id))
                )
        
        project_filter = ft.Dropdown(
            label="Progetto",
            options=project_options,
            value=self.selected_project_filter or "",
            on_change=self._on_project_filter_change,
            width=250,
            bgcolor=ft.Colors.WHITE,
            border_color=ft.Colors.GREY_300
        )
        
        # Dropdown status
        status_filter = ft.Dropdown(
            label="Stato",
            options=[
                ft.dropdown.Option(text="Tutti gli stati", key=""),
                ft.dropdown.Option(text="Bozza", key=ProjectStatus.DRAFT),
                ft.dropdown.Option(text="In Corso", key=ProjectStatus.IN_PROGRESS),
                ft.dropdown.Option(text="Completato", key=ProjectStatus.COMPLETED),
                ft.dropdown.Option(text="Sospeso", key=ProjectStatus.SUSPENDED)
            ],
            value=self.selected_status_filter or "",
            on_change=self._on_status_filter_change,
            width=200,
            bgcolor=ft.Colors.WHITE,
            border_color=ft.Colors.GREY_300
        )
        
        # Campo ricerca
        search_field = ft.TextField(
            label="Cerca SAL...",
            prefix_icon=ft.Icons.SEARCH,
            value=self.search_query,
            on_change=self._on_search_change,
            width=300,
            bgcolor=ft.Colors.WHITE,
            border_color=ft.Colors.GREY_300
        )
        
        # Pulsante reset filtri
        reset_button = ft.OutlinedButton(
            "Reset Filtri",
            icon=ft.Icons.CLEAR,
            on_click=self._reset_filters,
            height=40
        )
        
        filters_row = ft.Row(
            controls=[
                project_filter,
                status_filter,
                search_field,
                reset_button
            ],
            spacing=12,
            wrap=True
        )
        
        self.filters_section = ft.Container(
            content=filters_row,
            bgcolor=ft.Colors.WHITE,
            padding=16,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )
    
    def _build_sals_list(self):
        """Costruisce la lista SAL"""
        if not self.filtered_sals:
            empty_content = ft.Container(
                content=ft.Column(
                    controls=[
                        ft.Icon(ft.Icons.TIMELINE_OUTLINED, size=64, color=ft.Colors.GREY_400),
                        ft.Text("Nessun SAL trovato", size=18, color=ft.Colors.GREY_600, weight=ft.FontWeight.BOLD),
                        ft.Text("Crea il primo SAL o modifica i filtri di ricerca", size=14, color=ft.Colors.GREY_400)
                    ],
                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                    spacing=12
                ),
                padding=40,
                alignment=ft.alignment.center
            )
            
            self.sals_list_section = ft.Container(
                content=empty_content,
                bgcolor=ft.Colors.WHITE,
                border_radius=12,
                border=ft.border.all(1, ft.Colors.GREY_200),
                height=300
            )
            return
        
        # Crea le card SAL
        sal_cards = []
        for sal in self.filtered_sals:
            sal_cards.append(self._create_sal_card(sal))
        
        sals_grid = ft.Column(
            controls=sal_cards,
            spacing=12,
            scroll=ft.ScrollMode.AUTO
        )
        
        self.sals_list_section = ft.Container(
            content=sals_grid,
            bgcolor=ft.Colors.WHITE,
            padding=16,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_200),
            expand=True
        )
    
    def _create_sal_card(self, sal: SAL) -> ft.Card:
        """Crea una card SAL moderna"""
        # Trova il progetto associato
        project = next((p for p in self.projects_data if p.id == sal.project_id), None)
        project_name = project.name if project else "Progetto non trovato"
        
        # Trova il cliente
        client = None
        if project:
            client = next((c for c in self.clients_data if c.id == project.client_id), None)
        client_name = client.name if client else "Cliente non trovato"
        
        # Status badge
        status_config = {
            ProjectStatus.DRAFT: ("Bozza", ft.Colors.GREY_600, ft.Icons.EDIT),
            ProjectStatus.IN_PROGRESS: ("In Corso", ft.Colors.BLUE_600, ft.Icons.PLAY_CIRCLE),
            ProjectStatus.COMPLETED: ("Completato", ft.Colors.GREEN_600, ft.Icons.CHECK_CIRCLE),
            ProjectStatus.SUSPENDED: ("Sospeso", ft.Colors.ORANGE_600, ft.Icons.PAUSE_CIRCLE)
        }
        
        status_text, status_color, status_icon = status_config.get(
            sal.status, ("Sconosciuto", ft.Colors.GREY_600, ft.Icons.HELP)
        )
        
        status_badge = ft.Container(
            content=ft.Row(
                controls=[
                    ft.Icon(status_icon, size=12, color=ft.Colors.WHITE),
                    ft.Text(status_text, size=10, color=ft.Colors.WHITE, weight=ft.FontWeight.BOLD)
                ],
                spacing=4,
                tight=True
            ),
            bgcolor=status_color,
            padding=ft.padding.symmetric(horizontal=8, vertical=4),
            border_radius=10
        )
        
        # Progress bar
        progress_color = ft.Colors.GREEN_600 if sal.percentage >= 80 else ft.Colors.BLUE_600 if sal.percentage >= 50 else ft.Colors.ORANGE_600
        
        return ft.Card(
            content=ft.Container(
                content=ft.Column(
                    controls=[
                        # Header con nome SAL e status
                        ft.Row(
                            controls=[
                                ft.Column(
                                    controls=[
                                        ft.Text(sal.name, size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_900),
                                        ft.Text(f"#{sal.sequence_number}", size=12, color=ft.Colors.GREY_500)
                                    ],
                                    spacing=2,
                                    expand=True
                                ),
                                status_badge
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_BETWEEN
                        ),
                        
                        # Informazioni progetto e cliente
                        ft.Column(
                            controls=[
                                ft.Row(
                                    controls=[
                                        ft.Icon(ft.Icons.FOLDER, size=14, color=ft.Colors.GREY_500),
                                        ft.Text(project_name, size=12, color=ft.Colors.GREY_700, expand=True, max_lines=1, overflow=ft.TextOverflow.ELLIPSIS)
                                    ],
                                    spacing=6
                                ),
                                ft.Row(
                                    controls=[
                                        ft.Icon(ft.Icons.BUSINESS, size=14, color=ft.Colors.GREY_500),
                                        ft.Text(client_name, size=12, color=ft.Colors.GREY_700, expand=True, max_lines=1, overflow=ft.TextOverflow.ELLIPSIS)
                                    ],
                                    spacing=6
                                )
                            ],
                            spacing=4
                        ),
                        
                        # Progress bar
                        ft.Column(
                            controls=[
                                ft.Row(
                                    controls=[
                                        ft.Text("Avanzamento", size=11, color=ft.Colors.GREY_600),
                                        ft.Text(f"{sal.percentage}%", size=11, weight=ft.FontWeight.BOLD, color=progress_color)
                                    ],
                                    alignment=ft.MainAxisAlignment.SPACE_BETWEEN
                                ),
                                ft.ProgressBar(
                                    value=sal.percentage / 100,
                                    color=progress_color,
                                    bgcolor=ft.Colors.GREY_200,
                                    height=6
                                )
                            ],
                            spacing=4
                        ),
                        
                        # Azioni
                        ft.Row(
                            controls=[
                                ft.TextButton(
                                    "Dettagli",
                                    icon=ft.Icons.VISIBILITY,
                                    on_click=lambda _, s=sal: self._view_sal_details(s)
                                ),
                                ft.TextButton(
                                    "Modifica",
                                    icon=ft.Icons.EDIT,
                                    on_click=lambda _, s=sal: self._edit_sal(s)
                                ),
                                ft.TextButton(
                                    "Elimina",
                                    icon=ft.Icons.DELETE,
                                    on_click=lambda _, s=sal: self._delete_sal(s),
                                    style=ft.ButtonStyle(color=ft.Colors.RED_600)
                                )
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_AROUND
                        )
                    ],
                    spacing=12
                ),
                padding=16
            ),
            elevation=2,
            surface_tint_color=ft.Colors.BLUE_50
        )
    
    def _apply_filters(self):
        """Applica i filtri ai SAL"""
        filtered = self.sals_data.copy()
        
        # Filtro progetto
        if self.selected_project_filter:
            filtered = [sal for sal in filtered if str(sal.project_id) == self.selected_project_filter]
        
        # Filtro status
        if self.selected_status_filter:
            filtered = [sal for sal in filtered if sal.status == self.selected_status_filter]
        
        # Filtro ricerca
        if self.search_query:
            query = self.search_query.lower()
            filtered = [
                sal for sal in filtered
                if query in sal.name.lower() or 
                   (sal.description and query in sal.description.lower())
            ]
        
        self.filtered_sals = filtered
    
    def _on_project_filter_change(self, e):
        """Gestisce il cambio filtro progetto"""
        self.selected_project_filter = e.control.value if e.control.value else None
        self._apply_filters()
        self._build_sals_list()
        self.page.update()
    
    def _on_status_filter_change(self, e):
        """Gestisce il cambio filtro status"""
        self.selected_status_filter = e.control.value if e.control.value else None
        self._apply_filters()
        self._build_sals_list()
        self.page.update()
    
    def _on_search_change(self, e):
        """Gestisce il cambio ricerca"""
        self.search_query = e.control.value
        self._apply_filters()
        self._build_sals_list()
        self.page.update()
    
    def _reset_filters(self, e):
        """Reset tutti i filtri"""
        self.selected_project_filter = None
        self.selected_status_filter = None
        self.search_query = ""
        self._apply_filters()
        self._build_filters()
        self._build_sals_list()
        self.page.update()
    
    def _show_sal_form(self, sal: Optional[SAL] = None):
        """Mostra il form SAL"""
        try:
            is_edit = sal is not None
            title = "Modifica SAL" if is_edit else "Nuovo SAL"

            # Get projects with SAL enabled for dropdown
            sal_projects = [p for p in self.projects_data if p.requires_sal]

            if not sal_projects:
                # Show error dialog if no projects with SAL enabled
                error_dialog = ft.AlertDialog(
                    modal=True,
                    title=ft.Text("Nessun Progetto Disponibile"),
                    content=ft.Text("Non ci sono progetti con SAL abilitati. Abilita i SAL in almeno un progetto prima di creare un SAL."),
                    actions=[
                        ft.TextButton("OK", on_click=lambda _: self.page.close(error_dialog))
                    ],
                    actions_alignment=ft.MainAxisAlignment.END,
                )
                self.page.open(error_dialog)
                return

            # Project dropdown
            project_options = [ft.dropdown.Option(text=p.name, key=str(p.id)) for p in sal_projects]
            project_dropdown = ft.Dropdown(
                label="Progetto *",
                options=project_options,
                value=str(sal.project_id) if is_edit else (self.selected_project_filter if self.selected_project_filter else None),
                expand=True
            )

            # Form fields
            name_field = ft.TextField(
                label="Nome SAL *",
                value=sal.name if is_edit else "",
                expand=True
            )

            description_field = ft.TextField(
                label="Descrizione",
                value=sal.description if is_edit else "",
                multiline=True,
                min_lines=2,
                max_lines=4
            )

            percentage_field = ft.TextField(
                label="Percentuale Completamento (0-100) *",
                value=str(sal.percentage) if is_edit else "",
                width=200
            )

            sequence_field = ft.TextField(
                label="Numero Sequenza *",
                value=str(sal.sequence_number) if is_edit else "",
                width=150
            )

            # Create form dialog
            form_dialog = ft.AlertDialog(
                modal=True,
                title=ft.Text(title),
                content=ft.Container(
                    content=ft.Column([
                        project_dropdown,
                        ft.Container(height=8),
                        name_field,
                        ft.Container(height=8),
                        description_field,
                        ft.Container(height=8),
                        ft.Row([
                            sequence_field,
                            percentage_field
                        ], spacing=16)
                    ], tight=True),
                    width=500,
                    height=300
                ),
                actions=[
                    ft.TextButton("Annulla", on_click=lambda _: self.page.close(form_dialog)),
                    ft.ElevatedButton(
                        "Salva SAL",
                        on_click=lambda _: self._save_sal_from_form(
                            form_dialog, sal, project_dropdown, name_field,
                            description_field, sequence_field, percentage_field
                        ),
                        bgcolor=ft.Colors.BLUE_600,
                        color=ft.Colors.WHITE
                    )
                ],
                actions_alignment=ft.MainAxisAlignment.END,
            )

            self.page.open(form_dialog)
            logger.info(f"Apertura form SAL: {'modifica' if is_edit else 'nuovo'}")

        except Exception as e:
            logger.error(f"Errore apertura form SAL: {e}")

    def _save_sal_from_form(self, dialog, sal, project_dropdown, name_field, description_field, sequence_field, percentage_field):
        """Salva il SAL dal form"""
        try:
            # Validation
            if not project_dropdown.value:
                self._show_error("Seleziona un progetto")
                return

            if not name_field.value or not name_field.value.strip():
                self._show_error("Inserisci il nome del SAL")
                return

            if not sequence_field.value or not sequence_field.value.strip():
                self._show_error("Inserisci il numero di sequenza")
                return

            if not percentage_field.value or not percentage_field.value.strip():
                self._show_error("Inserisci la percentuale di completamento")
                return

            try:
                sequence_number = int(sequence_field.value)
                if sequence_number < 1:
                    self._show_error("Il numero di sequenza deve essere maggiore di 0")
                    return
            except ValueError:
                self._show_error("Il numero di sequenza deve essere un numero intero")
                return

            try:
                percentage = float(percentage_field.value)
                if percentage < 0 or percentage > 100:
                    self._show_error("La percentuale deve essere tra 0 e 100")
                    return
            except ValueError:
                self._show_error("La percentuale deve essere un numero")
                return

            # Close dialog first
            self.page.close(dialog)

            # Create or update SAL
            if sal:  # Edit mode
                sal.project_id = UUID(project_dropdown.value)
                sal.name = name_field.value.strip()
                sal.description = description_field.value.strip() if description_field.value else ""
                sal.sequence_number = sequence_number
                sal.percentage = percentage
                sal.updated_at = datetime.now()

                self.sal_service.update_sal(sal)
                logger.info(f"SAL aggiornato: {sal.name}")
            else:  # Create mode
                new_sal = SAL(
                    project_id=UUID(project_dropdown.value),
                    name=name_field.value.strip(),
                    description=description_field.value.strip() if description_field.value else "",
                    sequence_number=sequence_number,
                    percentage=percentage
                )

                self.sal_service.create_sal(new_sal)
                logger.info(f"SAL creato: {new_sal.name}")

            # Refresh data and update UI
            self.refresh_data()
            self._build_sals_list()
            self.page.update()

        except Exception as e:
            logger.error(f"Errore salvataggio SAL: {e}")
            self._show_error(f"Errore durante il salvataggio: {str(e)}")

    def _show_error(self, message: str):
        """Mostra un messaggio di errore"""
        error_dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text("Errore"),
            content=ft.Text(message),
            actions=[
                ft.TextButton("OK", on_click=lambda _: self.page.close(error_dialog))
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        self.page.open(error_dialog)

    def _view_sal_details(self, sal: SAL):
        """Visualizza dettagli SAL"""
        try:
            # Use the proper navigation system to show detail view
            self.app.main_layout.navigate_to_detail("sal_detail", str(sal.id))
            logger.info(f"Visualizza dettagli SAL: {sal.name}")

        except Exception as e:
            logger.error(f"Errore apertura dettagli SAL: {e}")
            # Show error dialog
            error_dialog = ft.AlertDialog(
                modal=True,
                title=ft.Text("Errore"),
                content=ft.Text(f"Impossibile aprire i dettagli del SAL: {str(e)}"),
                actions=[
                    ft.TextButton("OK", on_click=lambda _: self.page.close(error_dialog))
                ],
                actions_alignment=ft.MainAxisAlignment.END,
            )
            self.page.open(error_dialog)

    def show_sal_detail(self, sal_id: str):
        """Mostra i dettagli di un SAL specifico - chiamato dal sistema di navigazione"""
        try:
            from .sal_detail import SALDetailView

            # Create the detail view
            detail_view = SALDetailView(self.app, UUID(sal_id))

            # Build the content
            detail_content = detail_view.build()

            # Update the main layout content area directly
            self.app.main_layout.content_area.content = detail_content

            # Force page update
            if hasattr(self.app.main_layout, 'page') and self.app.main_layout.page:
                self.app.main_layout.page.update()

            logger.info(f"Dettagli SAL caricati: {sal_id}")

        except Exception as e:
            logger.error(f"Errore caricamento dettagli SAL: {e}")
            # Show error dialog
            error_dialog = ft.AlertDialog(
                modal=True,
                title=ft.Text("Errore"),
                content=ft.Text(f"Errore durante il caricamento dei dettagli: {str(e)}"),
                actions=[
                    ft.TextButton("OK", on_click=lambda _: self.page.close(error_dialog))
                ],
                actions_alignment=ft.MainAxisAlignment.END,
            )
            self.page.open(error_dialog)
    
    def _edit_sal(self, sal: SAL):
        """Modifica SAL"""
        # TODO: Implementare modifica SAL
        logger.info(f"Modifica SAL: {sal.name}")
    
    def _delete_sal(self, sal: SAL):
        """Elimina SAL"""
        # TODO: Implementare eliminazione SAL
        logger.info(f"Elimina SAL: {sal.name}")
