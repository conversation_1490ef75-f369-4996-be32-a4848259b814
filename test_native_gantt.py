#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 Native Gantt Chart Test
Ultra-fast Flet-only implementation test
"""

import os
import sys
import flet as ft
from datetime import datetime, timedelta

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

print("🚀 Starting Native Gantt Chart Test...")
print("📊 Pure Flet performance - no external dependencies!")
print("✨ Ready for lightning-fast charts!")

def main(page: ft.Page):
    """Main test app"""
    
    # Page setup
    page.title = "🚀 Native Gantt Chart Test"
    page.theme_mode = ft.ThemeMode.LIGHT
    page.window.width = 1400
    page.window.height = 900
    page.padding = 0
    page.scroll = ft.ScrollMode.AUTO
    
    print("🎨 Initializing Native Gantt Chart...")
    
    try:
        # Import and setup  
        from core.models.base_models import Project, ProjectStatus, Deadline, DeadlineStatus, ProjectCategory, ProjectSubcategory, ProjectType, Priority
        from ui.views.native_gantt import NativeGanttChart
        from uuid import UUID
        
        # Mock app instance
        class MockApp:
            def __init__(self):
                self.page = page
                
        class MockDB:
            def get_all_projects(self):
                return test_projects
            def get_all_deadlines(self):
                return test_deadlines
        
        # Create test data first
        print("📊 Creating test data...")
        test_projects = [
            Project(
                id=UUID("12345678-1234-5678-9012-123456789011"),
                name="Website Redesign",
                description="Complete website overhaul",
                category=ProjectCategory.RICERCA_INNOVAZIONE,
                subcategory=ProjectSubcategory.RICERCA_INNOVAZIONE,
                project_type=ProjectType.ACCORDI_INNOVAZIONE,
                client_id=UUID("12345678-1234-5678-9012-123456789001"),
                start_date=datetime.now().date() - timedelta(days=10),
                end_date=datetime.now().date() + timedelta(days=20),
                status=ProjectStatus.IN_PROGRESS,
                priority=Priority.HIGH
            ),
            Project(
                id="proj2", 
                name="Mobile App Development",
                description="iOS and Android app",
                client="StartupXYZ",
                start_date=datetime.now() + timedelta(days=5),
                end_date=datetime.now() + timedelta(days=45),
                status=ProjectStatus.IN_PROGRESS,
                progress=25
            ),
            Project(
                id="proj3",
                name="Database Migration",
                description="Legacy system upgrade",
                client="BigCorp",
                start_date=datetime.now() - timedelta(days=20),
                end_date=datetime.now() - timedelta(days=5),
                status=ProjectStatus.COMPLETED,
                progress=100
            ),
            Project(
                id="proj4",
                name="Security Audit",
                description="Comprehensive security review",
                client="SecureBank",
                start_date=datetime.now() + timedelta(days=10),
                end_date=datetime.now() + timedelta(days=25),
                status=ProjectStatus.SUSPENDED,
                progress=15
            )
        ]
        
        # Test deadlines
        test_deadlines = [
            Deadline(
                id="dead1",
                title="Project Proposal Due",
                description="Submit final proposal",
                due_date=datetime.now() + timedelta(days=7),
                status=DeadlineStatus.PENDING,
                priority="HIGH",
                project_id="proj1"
            ),
            Deadline(
                id="dead2",
                title="Beta Release",
                description="Deploy beta version",
                due_date=datetime.now() + timedelta(days=30),
                status=DeadlineStatus.PENDING,
                priority="MEDIUM",
                project_id="proj2"
            ),
            Deadline(
                id="dead3",
                title="Final Documentation",
                description="Complete project docs",
                due_date=datetime.now() - timedelta(days=2),
                status=DeadlineStatus.COMPLETED,
                priority="LOW",
                project_id="proj3"
            ),
            Deadline(
                id="dead4",
                title="Security Report",
                description="Deliver security findings",
                due_date=datetime.now() - timedelta(days=1),
                status=DeadlineStatus.OVERDUE,
                priority="HIGH",
                project_id="proj4"
            )
        ]
        
        # Create mock app with database
        app = MockApp()
        app.db_manager = MockDB()
        
        # Create Native Gantt Chart
        gantt_chart = NativeGanttChart(app)
        
        # Build the chart
        chart_view = gantt_chart.build()
        
        # Add to page
        page.add(chart_view)
        
        print("✅ Native Gantt Chart loaded successfully!")
        print(f"📊 Displaying {len(test_projects)} projects and {len(test_deadlines)} deadlines")
        print("🎯 Chart should be lightning fast and responsive!")
        
    except Exception as e:
        print(f"❌ Error loading Native Gantt Chart: {e}")
        import traceback
        traceback.print_exc()
        
        # Fallback error display
        page.add(
            ft.Container(
                content=ft.Column([
                    ft.Icon(ft.Icons.ERROR, size=48, color=ft.Colors.RED),
                    ft.Text("❌ Error Loading Native Gantt Chart", size=24, weight=ft.FontWeight.BOLD),
                    ft.Text(f"Error: {str(e)}", size=14, color=ft.Colors.GREY_600),
                    ft.ElevatedButton(
                        "🔄 Retry",
                        on_click=lambda e: page.window.close(),
                        bgcolor=ft.Colors.BLUE_600,
                        color=ft.Colors.WHITE
                    )
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=15),
                alignment=ft.alignment.center,
                expand=True,
                padding=ft.padding.all(40),
                bgcolor=ft.Colors.RED_50
            )
        )

# Run test
if __name__ == "__main__":
    try:
        ft.app(target=main, port=8082)  # Different port to avoid conflicts
    except Exception as e:
        print(f"❌ Failed to start Native Gantt test: {e}")
        print("💡 Try closing other Flet apps or use a different port") 