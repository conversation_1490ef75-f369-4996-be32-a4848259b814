#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Modern Search Box Test - Flet 28.x+ Compatible
Test the new card-based search UI with clean overlay
"""

import flet as ft
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "src"))

from ui.components.header import ModernSearchBox

class MockDB:
    """Mock database for testing"""

    def get_all_clients(self):
        """Return mock clients"""
        from collections import namedtuple
        Client = namedtuple('Client', ['id', 'name', 'business_name', 'email'])

        return [
            Client(1, "<PERSON>", "Rossi S.r.l.", "<EMAIL>"),
            Client(2, "<PERSON> Verdi", "Verdi & Co.", "<EMAIL>"),
            Client(3, "Anna Bianchi", "Bianchi Studio", "<EMAIL>"),
            Client(4, "<PERSON>", "<PERSON>eri Consulting", "<EMAIL>"),
            Client(5, "<PERSON>", "Gialli Design", "<EMAIL>")
        ]

class MockApp:
    """Mock app instance for testing"""

    def __init__(self, page):
        self.page = page
        self.db = MockDB()

def main(page: ft.Page):
    """Test the modern search box"""
    page.title = "🔍 Modern Search Box Test - Flet 28.x+"
    page.window_width = 1400
    page.window_height = 900
    page.padding = 20
    page.bgcolor = ft.Colors.GREY_50

    # Create mock app
    app = MockApp(page)

    def handle_result_click(result):
        """Handle search result clicks"""
        page.open(
            ft.SnackBar(
                content=ft.Text(f"Clicked: {result['title']} ({result['type']})"),
                duration=2000
            )
        )

    # Create modern search box
    search_box = ModernSearchBox(
        app_instance=app,
        on_result_click=handle_result_click
    )
    
    # Create header section
    header_section = ft.Container(
        content=ft.Row([
            ft.Text(
                "🔍 Modern Search Box Test",
                size=28,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.BLUE_700
            ),
            ft.Container(expand=True),
            search_box.get_container()
        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
        padding=ft.padding.all(24),
        bgcolor=ft.Colors.WHITE,
        border_radius=16,
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=12,
            color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
            offset=ft.Offset(0, 4)
        )
    )

    # Test instructions
    instructions = ft.Card(
        content=ft.Container(
            content=ft.Column([
                ft.Text(
                    "✨ New Modern Search Features",
                    size=20,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.BLUE_700
                ),
                ft.Divider(color=ft.Colors.BLUE_200),
                ft.Column([
                    ft.Row([
                        ft.Icon(ft.Icons.SEARCH_ROUNDED, color=ft.Colors.GREEN_600, size=20),
                        ft.Text("1. Type to search - clear text visibility",
                               size=14, color=ft.Colors.GREY_700, expand=True)
                    ], spacing=8),
                    ft.Row([
                        ft.Icon(ft.Icons.CARD_MEMBERSHIP_ROUNDED, color=ft.Colors.GREEN_600, size=20),
                        ft.Text("2. Results appear as modern cards below search box",
                               size=14, color=ft.Colors.GREY_700, expand=True)
                    ], spacing=8),
                    ft.Row([
                        ft.Icon(ft.Icons.TOUCH_APP_ROUNDED, color=ft.Colors.GREEN_600, size=20),
                        ft.Text("3. Click cards to navigate (shows snackbar in test)",
                               size=14, color=ft.Colors.GREY_700, expand=True)
                    ], spacing=8),
                    ft.Row([
                        ft.Icon(ft.Icons.SPEED_ROUNDED, color=ft.Colors.GREEN_600, size=20),
                        ft.Text("4. No complex overlays - simple, clean UI",
                               size=14, color=ft.Colors.GREY_700, expand=True)
                    ], spacing=8)
                ], spacing=12),
                ft.Divider(color=ft.Colors.GREY_300),
                ft.Text(
                    "🎯 Try searching for: mario, luigi, anna, marco, sara",
                    size=14,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.BLUE_600,
                    text_align=ft.TextAlign.CENTER
                )
            ], spacing=16),
            padding=ft.padding.all(24)
        ),
        elevation=4
    )

    # Sample content cards
    sample_cards = ft.Row([
        ft.Card(
            content=ft.Container(
                content=ft.Column([
                    ft.Icon(ft.Icons.PERSON_ROUNDED, size=32, color=ft.Colors.BLUE_600),
                    ft.Text("Clients", size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_800),
                    ft.Text("5 total", size=12, color=ft.Colors.GREY_600)
                ], spacing=8, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=ft.padding.all(20),
                width=150
            ),
            elevation=2
        ),
        ft.Card(
            content=ft.Container(
                content=ft.Column([
                    ft.Icon(ft.Icons.FOLDER_ROUNDED, size=32, color=ft.Colors.GREEN_600),
                    ft.Text("Projects", size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_800),
                    ft.Text("Demo mode", size=12, color=ft.Colors.GREY_600)
                ], spacing=8, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=ft.padding.all(20),
                width=150
            ),
            elevation=2
        ),
        ft.Card(
            content=ft.Container(
                content=ft.Column([
                    ft.Icon(ft.Icons.SCHEDULE_ROUNDED, size=32, color=ft.Colors.ORANGE_600),
                    ft.Text("Deadlines", size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_800),
                    ft.Text("Demo mode", size=12, color=ft.Colors.GREY_600)
                ], spacing=8, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=ft.padding.all(20),
                width=150
            ),
            elevation=2
        )
    ], spacing=16, alignment=ft.MainAxisAlignment.CENTER)

    # Main layout
    page.add(
        ft.Column([
            header_section,
            ft.Container(height=20),  # Spacing
            instructions,
            ft.Container(height=20),  # Spacing
            sample_cards,
            ft.Container(height=40)   # Bottom spacing
        ], spacing=0, scroll=ft.ScrollMode.AUTO)
    )

if __name__ == "__main__":
    ft.app(target=main, view=ft.AppView.WEB_BROWSER, port=8090)