#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Componente Alert Panel per Agevolami PM
"""

import flet as ft
from typing import Callable, List, Dict, Any, Optional
from datetime import datetime, date

from core import get_logger
from core.models import Priority, AlertStatus

logger = get_logger(__name__)

class AlertPanel:
    """Pannello per visualizzare e gestire gli alert"""
    
    def __init__(self, app_instance, on_close: Callable[[], None]):
        self.app = app_instance
        self.on_close = on_close
        self.alerts_data = []
        self.filter_priority = None
        self.show_dismissed = False
        
        self._init_components()
    
    def _init_components(self):
        """Inizializza i componenti del pannello"""
        pass
    
    def _create_header(self) -> ft.Container:
        """Crea l'header del pannello"""
        return ft.Container(
            content=ft.Row([
                ft.Column([
                    ft.Text(
                        "Alert Attivi",
                        size=18,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.GREY_800
                    ),
                    ft.Text(
                        f"{len(self.alerts_data)} alert trovati",
                        size=12,
                        color=ft.Colors.GREY_500
                    )
                ], spacing=2),
                
                ft.Container(expand=True),
                
                ft.Row([
                    ft.IconButton(
                        icon=ft.Icons.REFRESH,
                        icon_size=20,
                        tooltip="Aggiorna",
                        on_click=lambda _: self.refresh_alerts()
                    ),
                    ft.IconButton(
                        icon=ft.Icons.CLOSE,
                        icon_size=20,
                        tooltip="Chiudi",
                        on_click=lambda _: self.on_close()
                    )
                ], spacing=0)
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            padding=ft.padding.all(16),
            border=ft.border.only(bottom=ft.BorderSide(1, ft.Colors.GREY_200))
        )
    
    def _create_filters(self) -> ft.Container:
        """Crea i filtri per gli alert"""
        
        # Filtro priorità
        priority_chips = []
        priorities = [
            ("Tutte", None, ft.Colors.GREY_600),
            ("Critica", Priority.CRITICAL, ft.Colors.RED_600),
            ("Alta", Priority.HIGH, ft.Colors.ORANGE_600),
            ("Media", Priority.MEDIUM, ft.Colors.BLUE_600),
            ("Bassa", Priority.LOW, ft.Colors.GREEN_600)
        ]
        
        for label, priority, color in priorities:
            is_selected = self.filter_priority == priority
            
            chip = ft.Container(
                content=ft.Text(
                    label,
                    size=11,
                    color=ft.Colors.WHITE if is_selected else color,
                    weight=ft.FontWeight.W_500
                ),
                padding=ft.padding.symmetric(horizontal=12, vertical=6),
                bgcolor=color if is_selected else ft.Colors.TRANSPARENT,
                border_radius=15,
                border=ft.border.all(1, color),
                on_click=lambda _, p=priority: self._set_priority_filter(p)
            )
            priority_chips.append(chip)
        
        return ft.Container(
            content=ft.Column([
                ft.Text(
                    "Filtra per priorità:",
                    size=12,
                    color=ft.Colors.GREY_600,
                    weight=ft.FontWeight.W_500
                ),
                ft.Row(
                    controls=priority_chips,
                    spacing=8,
                    wrap=True
                )
            ], spacing=8),
            padding=ft.padding.all(16),
            border=ft.border.only(bottom=ft.BorderSide(1, ft.Colors.GREY_100))
        )
    
    def _set_priority_filter(self, priority: Optional[Priority]):
        """Imposta il filtro per priorità"""
        self.filter_priority = priority
        self._update_alert_list()
    
    def _create_alert_item(self, alert_data: Dict[str, Any]) -> ft.Container:
        """Crea un elemento alert"""
        alert = alert_data["alert"]
        deadline = alert_data["deadline"]
        client = alert_data["client"]
        project = alert_data["project"]
        days_remaining = alert_data["days_remaining"]
        is_overdue = alert_data["is_overdue"]
        
        # Colori basati sulla priorità
        priority_colors = {
            Priority.CRITICAL: ft.Colors.RED_600,
            Priority.HIGH: ft.Colors.ORANGE_600,
            Priority.MEDIUM: ft.Colors.BLUE_600,
            Priority.LOW: ft.Colors.GREEN_600
        }
        
        priority_color = priority_colors.get(alert.priority, ft.Colors.GREY_600)
        
        # Icona basata sui giorni rimanenti
        if is_overdue:
            status_icon = ft.Icons.ERROR
            status_color = ft.Colors.RED_600
            status_text = "SCADUTO"
        elif days_remaining == 0:
            status_icon = ft.Icons.WARNING
            status_color = ft.Colors.ORANGE_600
            status_text = "OGGI"
        elif days_remaining <= 3:
            status_icon = ft.Icons.SCHEDULE
            status_color = ft.Colors.ORANGE_600
            status_text = f"{days_remaining}g"
        else:
            status_icon = ft.Icons.SCHEDULE
            status_color = ft.Colors.BLUE_600
            status_text = f"{days_remaining}g"
        
        # Informazioni aggiuntive
        subtitle_parts = []
        if client:
            subtitle_parts.append(client.name)
        if project:
            subtitle_parts.append(project.name)
        
        subtitle = " • ".join(subtitle_parts) if subtitle_parts else "Nessun progetto associato"
        
        return ft.Container(
            content=ft.Column([
                # Header dell'alert
                ft.Row([
                    # Indicatore priorità
                    ft.Container(
                        width=4,
                        height=40,
                        bgcolor=priority_color,
                        border_radius=2
                    ),
                    
                    # Contenuto principale
                    ft.Column([
                        ft.Row([
                            ft.Text(
                                alert.title,
                                size=13,
                                weight=ft.FontWeight.W_500,
                                color=ft.Colors.GREY_800,
                                expand=True
                            ),
                            
                            # Status badge
                            ft.Container(
                                content=ft.Row([
                                    ft.Icon(
                                        status_icon,
                                        size=12,
                                        color=status_color
                                    ),
                                    ft.Text(
                                        status_text,
                                        size=10,
                                        color=status_color,
                                        weight=ft.FontWeight.BOLD
                                    )
                                ], spacing=4),
                                padding=ft.padding.symmetric(horizontal=6, vertical=2),
                                bgcolor=ft.Colors.GREY_50,
                                border_radius=10,
                                border=ft.border.all(1, status_color)
                            )
                        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                        
                        ft.Text(
                            subtitle,
                            size=11,
                            color=ft.Colors.GREY_500
                        ),
                        
                        ft.Text(
                            alert.message,
                            size=11,
                            color=ft.Colors.GREY_600,
                            max_lines=2
                        )
                    ], spacing=4, expand=True)
                ], spacing=12),
                
                # Azioni
                ft.Row([
                    ft.TextButton(
                        text="Visualizza",
                        icon=ft.Icons.VISIBILITY,
                        on_click=lambda _, a=alert: self._view_alert_details(a)
                    ),
                    
                    ft.TextButton(
                        text="Ignora",
                        icon=ft.Icons.CLOSE,
                        on_click=lambda _, a=alert: self._dismiss_alert(a)
                    ),
                    
                    ft.Container(expand=True),
                    
                    ft.Text(
                        alert.created_at.strftime("%d/%m %H:%M"),
                        size=10,
                        color=ft.Colors.GREY_400
                    )
                ], alignment=ft.MainAxisAlignment.START)
            ], spacing=8),
            padding=ft.padding.all(16),
            margin=ft.margin.symmetric(horizontal=8, vertical=4),
            bgcolor=ft.Colors.WHITE,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.GREY_200),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=2,
                color=ft.Colors.BLACK12
            )
        )
    
    def _view_alert_details(self, alert):
        """Visualizza i dettagli di un alert"""
        # TODO: Implementare visualizzazione dettagli
        logger.info(f"Visualizzazione dettagli alert: {alert.id}")
    
    def _dismiss_alert(self, alert):
        """Ignora un alert"""
        try:
            success = self.app.alert_service.dismiss_alert(alert.id)
            if success:
                self.refresh_alerts()
                # Mostra notifica
                # TODO: Implementare notifica
                logger.info(f"Alert {alert.id} ignorato")
        except Exception as e:
            logger.error(f"Errore dismissing alert: {e}")
    
    def _create_alert_list(self) -> ft.Container:
        """Crea la lista degli alert"""
        if not self.alerts_data:
            return ft.Container(
                content=ft.Column([
                    ft.Icon(
                        ft.Icons.NOTIFICATIONS_OFF,
                        size=48,
                        color=ft.Colors.GREY_400
                    ),
                    ft.Text(
                        "Nessun alert attivo",
                        size=16,
                        color=ft.Colors.GREY_500,
                        weight=ft.FontWeight.W_500
                    ),
                    ft.Text(
                        "Tutti gli alert sono stati gestiti",
                        size=12,
                        color=ft.Colors.GREY_400
                    )
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=8),
                padding=ft.padding.all(40),
                alignment=ft.alignment.center
            )
        
        # Filtra gli alert
        filtered_alerts = self.alerts_data
        if self.filter_priority is not None:
            filtered_alerts = [
                alert_data for alert_data in filtered_alerts
                if alert_data["alert"].priority == self.filter_priority
            ]
        
        # Crea gli elementi
        alert_items = [self._create_alert_item(alert_data) for alert_data in filtered_alerts]
        
        return ft.Container(
            content=ft.Column(
                controls=alert_items,
                spacing=0,
                scroll=ft.ScrollMode.AUTO
            ),
            expand=True
        )
    
    def _update_alert_list(self):
        """Aggiorna la lista degli alert (placeholder per rebuild)"""
        # Questo metodo sarà chiamato quando serve ricostruire la UI
        pass
    
    def build(self) -> ft.Container:
        """Costruisce il pannello alert"""
        return ft.Container(
            content=ft.Column([
                # Header
                self._create_header(),
                
                # Filtri
                self._create_filters(),
                
                # Lista alert
                self._create_alert_list()
            ], spacing=0),
            width=400,
            bgcolor=ft.Colors.WHITE,
            border_radius=ft.border_radius.only(
                top_left=12,
                bottom_left=12
            ),
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=10,
                color=ft.Colors.BLACK26
            )
        )
    
    def refresh_alerts(self):
        """Aggiorna gli alert dal servizio"""
        try:
            self.alerts_data = self.app.alert_service.get_dashboard_alerts(limit=50)
            logger.info(f"Caricati {len(self.alerts_data)} alert")
        except Exception as e:
            logger.error(f"Errore caricamento alert: {e}")
            self.alerts_data = []
    
    def get_alert_count(self) -> int:
        """Restituisce il numero di alert attivi"""
        return len(self.alerts_data)