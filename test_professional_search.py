#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for the professional search functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import flet as ft
from src.ui.components.header import Header, ProfessionalSearchEngine
from src.core.database.database_manager import DatabaseManager
from src.core.models.client import Client
from src.core.models.project import Project
from src.core.models.deadline import Deadline
from datetime import date, datetime
import uuid

class MockApp:
    """Mock app for testing search functionality"""
    
    def __init__(self):
        self.db = DatabaseManager()
        self.page = None
        
        # Create test data
        self._create_test_data()
    
    def _create_test_data(self):
        """Create test data for search"""
        try:
            # Test clients
            client1 = Client(
                name="Acme Corporation",
                business_name="Acme Corp S.r.l.",
                email="<EMAIL>",
                vat_number="*************",
                tax_code="ACME123456"
            )
            
            client2 = Client(
                name="Beta Solutions",
                business_name="Beta Solutions S.p.A.",
                email="<EMAIL>",
                vat_number="*************",
                tax_code="BETA987654"
            )
            
            # Save clients
            client1_id = self.db.create_client(client1)
            client2_id = self.db.create_client(client2)
            
            # Test projects
            project1 = Project(
                name="Website Redesign",
                reference_code="WEB-2024-001",
                description="Complete website redesign for Acme Corporation",
                client_id=client1_id
            )
            
            project2 = Project(
                name="Mobile App Development",
                reference_code="APP-2024-002", 
                description="Native mobile app for Beta Solutions",
                client_id=client2_id
            )
            
            # Save projects
            project1_id = self.db.create_project(project1)
            project2_id = self.db.create_project(project2)
            
            # Test deadlines
            deadline1 = Deadline(
                title="Website Launch",
                description="Launch new website",
                due_date=date(2024, 12, 31),
                project_id=project1_id,
                client_id=client1_id
            )
            
            deadline2 = Deadline(
                title="App Store Submission",
                description="Submit app to stores",
                due_date=date(2024, 11, 15),
                project_id=project2_id,
                client_id=client2_id
            )
            
            # Save deadlines
            self.db.create_deadline(deadline1)
            self.db.create_deadline(deadline2)
            
            print("✅ Test data created successfully")
            
        except Exception as e:
            print(f"❌ Error creating test data: {e}")

def test_search_engine():
    """Test the professional search engine"""
    print("\n🔍 Testing Professional Search Engine...")
    
    app = MockApp()
    search_engine = ProfessionalSearchEngine(app)
    
    # Test searches
    test_queries = [
        "acme",
        "website",
        "beta",
        "app",
        "launch",
        "mobile"
    ]
    
    for query in test_queries:
        print(f"\n📝 Searching for: '{query}'")
        results = search_engine.search(query)
        
        if results:
            print(f"   Found {len(results)} results:")
            for i, result in enumerate(results[:3], 1):
                print(f"   {i}. {result['category']}: {result['title']} (score: {result['score']:.1f})")
        else:
            print("   No results found")

def main(page: ft.Page):
    """Main test application"""
    page.title = "Professional Search Test"
    page.window_width = 1200
    page.window_height = 800
    page.padding = 0
    
    # Create mock app
    app = MockApp()
    app.page = page
    
    # Create header with search
    header = Header(
        on_search=lambda query: print(f"Global search: {query}"),
        on_notification_click=lambda: print("Notifications clicked"),
        on_settings_click=lambda: print("Settings clicked"),
        app_instance=app
    )
    
    # Test content
    content = ft.Column([
        ft.Container(
            content=ft.Text(
                "🔍 Professional Search Test",
                size=24,
                weight=ft.FontWeight.BOLD,
                text_align=ft.TextAlign.CENTER
            ),
            padding=ft.padding.all(20),
            alignment=ft.alignment.center
        ),
        ft.Container(
            content=ft.Column([
                ft.Text("Test the search functionality:", size=16, weight=ft.FontWeight.W_500),
                ft.Text("• Type 'acme' to find Acme Corporation", size=14),
                ft.Text("• Type 'website' to find Website Redesign project", size=14),
                ft.Text("• Type 'beta' to find Beta Solutions", size=14),
                ft.Text("• Type 'app' to find Mobile App Development", size=14),
                ft.Text("• Type 'launch' to find Website Launch deadline", size=14),
                ft.Text("", size=14),
                ft.Text("Features:", size=16, weight=ft.FontWeight.W_500),
                ft.Text("✅ Real-time search with debouncing", size=14),
                ft.Text("✅ Relevance scoring and ranking", size=14),
                ft.Text("✅ Categorized results", size=14),
                ft.Text("✅ Professional UI with hover effects", size=14),
                ft.Text("✅ Keyboard navigation support", size=14),
                ft.Text("✅ Direct navigation to results", size=14),
                ft.Text("✅ Search caching for performance", size=14),
                ft.Text("✅ Fuzzy matching for typos", size=14),
            ], spacing=8),
            padding=ft.padding.all(20)
        )
    ], expand=True)
    
    # Main layout
    layout = ft.Column([
        header.build(),
        content
    ], spacing=0)
    
    page.add(layout)
    
    # Run search engine test
    test_search_engine()

if __name__ == "__main__":
    print("🚀 Starting Professional Search Test...")
    ft.app(target=main)
