#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Google Tasks Integration Service for Agevolami PM
"""

import os
import json
import tempfile
import threading
from datetime import datetime, date, timedelta
from typing import List, Dict, Optional, Any
from uuid import UUID
import pickle
import time

from ..utils import get_logger
from core.models.base_models import Task, TaskStatus, Deadline, Priority
from .cache_manager import get_google_api_cache

logger = get_logger(__name__)

try:
    from google.auth.transport.requests import Request
    from google.oauth2.credentials import Credentials
    from google_auth_oauthlib.flow import InstalledAppFlow
    from googleapiclient.discovery import build
    from googleapiclient.errors import HttpError
    GOOGLE_AVAILABLE = True
except ImportError:
    GOOGLE_AVAILABLE = False
    logger.warning("Google Tasks libraries not available. Install with: pip install google-api-python-client google-auth-httplib2 google-auth-oauthlib")

class GoogleTasksService:
    """Service for Google Tasks integration"""
    
    # Scopes needed for tasks access
    SCOPES = ['https://www.googleapis.com/auth/tasks']
    
    # Embedded client credentials for seamless authentication
    EMBEDDED_CREDENTIALS = {
        "installed": {
            "client_id": "************-n73v60k2put3es0ln1ke3qcsifatrlfk.apps.googleusercontent.com",
            "project_id": "agevolami-pm",
            "auth_uri": "https://accounts.google.com/o/oauth2/auth",
            "token_uri": "https://oauth2.googleapis.com/token",
            "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
            "client_secret": "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",
            "redirect_uris": ["http://localhost"]
        }
    }
    
    def __init__(self, config_dir: str = "config"):
        # Ensure consistent config directory structure
        if not config_dir.endswith("config"):
            self.config_dir = os.path.join(config_dir, "config")
        else:
            self.config_dir = config_dir
        os.makedirs(self.config_dir, exist_ok=True)

        self.token_file = os.path.join(self.config_dir, "google_tasks_token.pickle")
        self.service = None
        self.enabled = False
        self.task_lists = {}  # Cache for task lists {deadline_id: task_list_id}

        # Cache for API responses
        self.api_cache = get_google_api_cache()

        # Thread safety
        self._lock = threading.RLock()

        # FIXED: Use synchronous auth check like Google Drive to prevent race conditions
        if GOOGLE_AVAILABLE:
            self._check_existing_auth()
    
    def _check_existing_auth(self) -> bool:
        """Check if there's existing valid authentication without triggering new auth"""
        try:
            if not os.path.exists(self.token_file):
                return False

            with open(self.token_file, 'rb') as token:
                creds = pickle.load(token)

            # Check if credentials are valid
            if creds and creds.valid:
                self.service = build('tasks', 'v1', credentials=creds)
                self.enabled = True
                logger.info("Google Tasks: Existing authentication found and valid")
                return True
            elif creds and creds.expired and creds.refresh_token:
                try:
                    creds.refresh(Request())
                    with open(self.token_file, 'wb') as token:
                        pickle.dump(creds, token)

                    self.service = build('tasks', 'v1', credentials=creds)
                    self.enabled = True
                    logger.info("Google Tasks: Authentication refreshed successfully")
                    return True
                except Exception as e:
                    logger.warning(f"Google Tasks: Token refresh failed: {e}")
                    return False
            else:
                return False

        except Exception as e:
            logger.warning(f"Google Tasks: Error checking existing auth: {e}")
            return False
    
    def authenticate(self) -> bool:
        """Manually authenticate with Google Tasks API using embedded credentials"""
        try:
            creds = None
            
            # Create temporary credentials file from embedded data
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_file:
                json.dump(self.EMBEDDED_CREDENTIALS, temp_file)
                temp_credentials_file = temp_file.name
            
            try:
                # Use the temporary file for authentication
                flow = InstalledAppFlow.from_client_secrets_file(
                    temp_credentials_file, self.SCOPES)
                creds = flow.run_local_server(port=0)
            finally:
                # Clean up temporary file
                try:
                    os.unlink(temp_credentials_file)
                except:
                    pass
            
            # Save credentials for next run
            with open(self.token_file, 'wb') as token:
                pickle.dump(creds, token)
            
            # Build the service
            self.service = build('tasks', 'v1', credentials=creds)
            self.enabled = True
            
            logger.info("Google Tasks authentication successful")
            return True
            
        except Exception as e:
            logger.error(f"Google Tasks authentication failed: {e}")
            self.enabled = False
            return False
    
    def is_enabled(self) -> bool:
        """Check if Google Tasks integration is enabled"""
        return self.enabled and GOOGLE_AVAILABLE

    def is_authenticated(self) -> bool:
        """Check if user is authenticated with Google Tasks"""
        return self.is_enabled()

    def invalidate_cache(self) -> None:
        """Invalidate all cached tasks data"""
        self.api_cache.invalidate_tasks_cache()
        logger.info("Google Tasks cache invalidated")

    def get_authentication_status(self) -> dict:
        """Get detailed authentication status for debugging"""
        return {
            'google_available': GOOGLE_AVAILABLE,
            'service_enabled': self.enabled,
            'service_object': self.service is not None,
            'token_file_exists': os.path.exists(self.token_file),
            'is_enabled': self.is_enabled(),
            'is_authenticated': self.is_authenticated()
        }
    
    def create_task_list_for_deadline(self, deadline: Deadline) -> Optional[str]:
        """Create or find a Google Tasks list for a specific deadline with duplicate prevention"""
        if not self.is_enabled():
            return None
        
        try:
            # Create task list name
            list_name = f"[AGEV] {deadline.title}"
            if deadline.due_date:
                list_name += f" (Scad: {deadline.due_date.strftime('%d/%m/%Y')})"
            
            # First, check if we already have this list cached
            cached_list_id = self.task_lists.get(deadline.id)
            if cached_list_id:
                # Verify the list still exists on Google
                try:
                    self.service.tasklists().get(tasklist=cached_list_id).execute()
                    logger.info(f"Using cached Google Tasks list: {list_name} with ID: {cached_list_id}")
                    return cached_list_id
                except HttpError as e:
                    if e.resp.status == 404:
                        # List was deleted, remove from cache
                        logger.warning(f"Cached task list was deleted, will recreate: {list_name}")
                        del self.task_lists[deadline.id]
                    else:
                        raise e
            
            # Search for existing task list by name
            existing_list_id = self._find_existing_task_list_by_name(list_name)
            if existing_list_id:
                # Found existing list, cache it
                self.task_lists[deadline.id] = existing_list_id
                logger.info(f"Found existing Google Tasks list: {list_name} with ID: {existing_list_id}")
                return existing_list_id
            
            # Create new task list (no existing list found)
            task_list = {
                'title': list_name
            }
            
            result = self.service.tasklists().insert(body=task_list).execute()
            task_list_id = result['id']
            
            # Cache the task list ID
            self.task_lists[deadline.id] = task_list_id
            
            logger.info(f"Created new Google Tasks list: {list_name} with ID: {task_list_id}")
            return task_list_id
            
        except Exception as e:
            logger.error(f"Error creating Google Tasks list for deadline {deadline.title}: {e}")
            return None
    
    def _find_existing_task_list_by_name(self, list_name: str) -> Optional[str]:
        """Find an existing Google Tasks list by name to avoid duplicates"""
        try:
            # Get all task lists
            result = self.service.tasklists().list().execute()
            task_lists = result.get('items', [])
            
            # Search for list with matching name
            for task_list in task_lists:
                if task_list.get('title', '').strip() == list_name.strip():
                    logger.info(f"Found existing Google Tasks list with name: {list_name}")
                    return task_list['id']
            
            return None
            
        except Exception as e:
            logger.error(f"Error searching for existing task list: {e}")
            return None
    
    def _get_or_create_task_list(self, deadline: Deadline) -> Optional[str]:
        """Gets a task list ID from cache or creates a new one for the deadline."""
        if not deadline:
            # Fallback to a default list if no deadline is provided
            default_list_name = "Agevolami PM Tasks"
            if "default" in self.task_lists:
                return self.task_lists["default"]
            
            task_list_id = self._find_existing_task_list_by_name(default_list_name)
            if not task_list_id:
                logger.info(f"Creating default task list: {default_list_name}")
                new_list = self.service.tasklists().insert(body={'title': default_list_name}).execute()
                task_list_id = new_list['id']
            
            self.task_lists["default"] = task_list_id
            return task_list_id

        # Check local cache first
        if deadline.id in self.task_lists:
            return self.task_lists[deadline.id]
        
        # If not in cache, create it (which also handles finding existing ones on Google)
        task_list_id = self.create_task_list_for_deadline(deadline)
        if task_list_id:
            # Cache the ID for future use
            self.task_lists[deadline.id] = task_list_id
        
        return task_list_id

    def sync_task_to_google(self, task: Task, deadline: Deadline = None, parent_google_task_id: str = None) -> Optional[str]:
        """Sync a task to Google Tasks, creating or updating as needed with proper hierarchy support"""
        if not self.is_enabled():
            return None

        try:
            # 1. Get or create the task list
            task_list_id = self._get_or_create_task_list(deadline)
            if not task_list_id:
                logger.error("Could not get or create a task list for Google Tasks sync")
                return None

            # 2. Check if the task already exists in Google Tasks
            existing_task = self._find_existing_task_by_title(task_list_id, task.title)
            
            if existing_task:
                # Update existing task - using PATCH pattern for robustness
                logger.info(f"Updating existing Google Task: {task.title}")
                
                # FIX: Use patch with a minimal body for robust updates.
                # This avoids issues with read-only fields and is the correct
                # pattern for partial updates.
                update_body = {
                    'title': task.title,
                    'notes': task.description or '',
                    'status': 'completed' if task.status == TaskStatus.COMPLETED else 'needsAction'
                }
                if task.due_date:
                    # Extract date part from datetime if needed (Google Tasks API only supports dates)
                    due_date = task.due_date.date() if hasattr(task.due_date, 'date') else task.due_date
                    update_body['due'] = due_date.isoformat() + 'T00:00:00.000Z'
                else:
                    # To clear a due date, set it to null
                    update_body['due'] = None

                result = self.service.tasks().patch(
                    tasklist=task_list_id,
                    task=existing_task['id'],
                    body=update_body
                ).execute()
                task.google_task_id = result['id']
                
                # Handle parent relationship for existing task (if it's a subtask)
                if task.parent_task_id and parent_google_task_id:
                    self._set_task_parent(task_list_id, task.google_task_id, parent_google_task_id)
                
                logger.info(f"Task '{task.title}' updated in Google Tasks")

            else:
                # Create new task
                task_body = {
                    'title': task.title,
                    'notes': task.description or '',
                    'status': 'completed' if task.status == TaskStatus.COMPLETED else 'needsAction'
                }
                if task.due_date:
                    # Extract date part from datetime if needed (Google Tasks API only supports dates)
                    due_date = task.due_date.date() if hasattr(task.due_date, 'date') else task.due_date
                    task_body['due'] = due_date.isoformat() + 'T00:00:00.000Z'
                
                logger.info(f"Creating new Google Task: {task.title}")
                result = self.service.tasks().insert(
                    tasklist=task_list_id,
                    body=task_body
                ).execute()
                task.google_task_id = result['id']
                
                # Set parent relationship for new subtask
                if task.parent_task_id and parent_google_task_id:
                    self._set_task_parent(task_list_id, task.google_task_id, parent_google_task_id)
                    logger.info(f"Subtask '{task.title}' created under parent in Google Tasks")
                else:
                    logger.info(f"Parent task '{task.title}' created in Google Tasks")
            
            task.google_task_list_id = task_list_id
            # Invalidate cache for this task list
            self.api_cache.invalidate_tasks_cache(task_list_id)
            return task.google_task_id
        
        except HttpError as e:
            logger.error(f"Error syncing task to Google Tasks: {e}")
            return None
        except Exception as e:
            logger.error(f"An unexpected error occurred during Google Tasks sync: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def _find_existing_task_by_title(self, task_list_id: str, task_title: str) -> Optional[Dict]:
        """Find an existing Google Task by title to avoid duplicates"""
        try:
            # Get all tasks from the list
            result = self.service.tasks().list(tasklist=task_list_id).execute()
            tasks = result.get('items', [])
            
            # Search for task with matching title
            for google_task in tasks:
                if google_task.get('title', '').strip() == task_title.strip():
                    logger.info(f"Found existing Google Task with title: {task_title}")
                    return google_task
            
            return None
            
        except Exception as e:
            logger.error(f"Error searching for existing task: {e}")
            return None

    def _set_task_parent(self, task_list_id: str, task_id: str, parent_task_id: str) -> bool:
        """Set a task's parent using the Google Tasks move API"""
        try:
            # Use the move API to set parent relationship
            self.service.tasks().move(
                tasklist=task_list_id,
                task=task_id,
                parent=parent_task_id
            ).execute()
            logger.info(f"Set parent relationship for task {task_id} under {parent_task_id}")
            return True
        except HttpError as e:
            logger.error(f"Error setting task parent: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error setting task parent: {e}")
            return False

    def sync_task_hierarchy_to_google(self, tasks: List[Task], deadline: Deadline = None) -> Dict[str, Any]:
        """Sync an entire task hierarchy to Google Tasks with proper parent-child relationships"""
        if not self.is_enabled():
            return {"success": False, "error": "Google Tasks not enabled"}
        
        results = {
            "success": True,
            "synced": 0,
            "errors": [],
            "total": len(tasks),
            "task_mappings": {}  # local_task_id -> google_task_id
        }
        
        try:
            # 1. Separate parent tasks and subtasks
            parent_tasks = [t for t in tasks if not t.parent_task_id]
            subtasks = [t for t in tasks if t.parent_task_id]
            
            # 2. Sync parent tasks first
            for task in parent_tasks:
                try:
                    google_task_id = self.sync_task_to_google(task, deadline)
                    if google_task_id:
                        results["task_mappings"][str(task.id)] = google_task_id
                        results["synced"] += 1
                        logger.info(f"Synced parent task: {task.title}")
                    else:
                        results["errors"].append(f"Failed to sync parent task: {task.title}")
                except Exception as e:
                    results["errors"].append(f"Error syncing parent task {task.title}: {str(e)}")
            
            # 3. Sync subtasks with parent relationships
            for subtask in subtasks:
                try:
                    parent_google_task_id = results["task_mappings"].get(str(subtask.parent_task_id))
                    
                    if not parent_google_task_id:
                        results["errors"].append(f"Parent not found for subtask: {subtask.title}")
                        continue
                    
                    google_task_id = self.sync_task_to_google(
                        subtask, 
                        deadline, 
                        parent_google_task_id=parent_google_task_id
                    )
                    
                    if google_task_id:
                        results["task_mappings"][str(subtask.id)] = google_task_id
                        results["synced"] += 1
                        logger.info(f"Synced subtask: {subtask.title} under parent")
                    else:
                        results["errors"].append(f"Failed to sync subtask: {subtask.title}")
                        
                except Exception as e:
                    results["errors"].append(f"Error syncing subtask {subtask.title}: {str(e)}")
            
            logger.info(f"Hierarchy sync completed: {results['synced']}/{results['total']} tasks synced")
            return results
            
        except Exception as e:
            logger.error(f"Error during hierarchy sync: {e}")
            return {"success": False, "error": str(e)}
    
    def delete_task_from_google(self, task: Task) -> bool:
        """Delete a task from Google Tasks"""
        if not self.is_enabled() or not task.google_task_id or not task.google_task_list_id:
            return False
        
        try:
            self.service.tasks().delete(
                tasklist=task.google_task_list_id,
                task=task.google_task_id
            ).execute()
            logger.info(f"Deleted Google Task: {task.title}")
            return True
            
        except HttpError as e:
            if e.resp.status == 404:
                logger.warning(f"Google Task not found: {task.title}")
                return True  # Consider as success since it's already gone
            logger.error(f"Google Tasks API error: {e}")
            return False
        except Exception as e:
            logger.error(f"Error deleting Google Task: {e}")
            return False
    
    def sync_all_deadline_tasks(self, deadline: Deadline, tasks: List[Task]) -> Dict[str, Any]:
        """Sync all tasks for a deadline to Google Tasks"""
        if not self.is_enabled():
            return {"success": False, "error": "Google Tasks not enabled"}
        
        results = {
            "success": True,
            "synced": 0,
            "errors": [],
            "total": len(tasks),
            "task_list_id": None
        }
        
        # Create or get task list for deadline
        task_list_id = self.create_task_list_for_deadline(deadline)
        if not task_list_id:
            return {"success": False, "error": "Failed to create task list"}
        
        results["task_list_id"] = task_list_id
        
        # Sync each task
        for task in tasks:
            try:
                google_task_id = self.sync_task_to_google(task, deadline)
                if google_task_id:
                    results["synced"] += 1
                    # Store the Google task info in the task object
                    task.google_task_id = google_task_id
                    task.google_task_list_id = task_list_id
                else:
                    results["errors"].append(f"Failed to sync: {task.title}")
            except Exception as e:
                results["errors"].append(f"Error syncing {task.title}: {str(e)}")
        
        logger.info(f"Sync completed: {results['synced']}/{results['total']} tasks synced")
        return results
    
    def get_task_lists(self) -> List[Dict]:
        """Get all Google Tasks lists"""
        if not self.is_enabled():
            return []
        
        try:
            result = self.service.tasklists().list().execute()
            return result.get('items', [])
        except Exception as e:
            logger.error(f"Error getting task lists: {e}")
            return []
    
    def get_tasks_from_list(self, task_list_id: str) -> List[Dict]:
        """Get all tasks from a specific Google Tasks list"""
        if not self.is_enabled():
            return []
        
        try:
            result = self.service.tasks().list(tasklist=task_list_id).execute()
            return result.get('items', [])
        except Exception as e:
            logger.error(f"Error getting tasks from list: {e}")
            return []
    
    def _task_to_google_format(self, task: Task, deadline: Deadline) -> Dict:
        """Convert local task to Google Tasks format"""
        # Handle both enum and string values for status
        task_status = task.status.value if hasattr(task.status, 'value') else str(task.status)
        
        google_task = {
            'title': task.title,
            'notes': self._build_task_notes(task, deadline),
            'status': 'completed' if task_status == 'completato' else 'needsAction'
        }
        
        # Add due date if present
        if task.due_date:
            # Google Tasks expects RFC 3339 format for due date (date only, no time)
            # Extract date part from datetime if needed
            due_date = task.due_date.date() if hasattr(task.due_date, 'date') else task.due_date
            google_task['due'] = due_date.isoformat() + 'T00:00:00.000Z'
        
        return google_task
    
    def _build_task_notes(self, task: Task, deadline: Deadline) -> str:
        """Build comprehensive notes for Google Tasks"""
        notes = []
        
        if task.description:
            notes.append(f"[DESC] {task.description}")

        # Add deadline context
        notes.append(f"[DEADLINE] {deadline.title}")
        if deadline.due_date:
            notes.append(f"[DUE] Scadenza: {deadline.due_date.strftime('%d/%m/%Y')}")

        # Add priority - handle both enum and string values
        priority_labels = {
            'bassa': "[LOW]",
            'media': "[MED]",
            'alta': "[HIGH]",
            'critica': "[CRIT]"
        }
        
        # Get priority value safely
        priority_value = task.priority.value if hasattr(task.priority, 'value') else str(task.priority)
        label = priority_labels.get(priority_value.lower(), "[NORM]")
        notes.append(f"{label} Priorità: {priority_value.title()}")

        # Add time estimates
        if task.estimated_hours:
            notes.append(f"[TIME] Tempo stimato: {task.estimated_hours}h")

        if task.actual_hours:
            notes.append(f"[ACTUAL] Tempo effettivo: {task.actual_hours}h")

        # Add progress
        if task.progress_percentage > 0:
            notes.append(f"[PROGRESS] Progresso: {task.progress_percentage}%")

        # Add tags
        if task.tags:
            notes.append(f"[TAGS] Tag: {', '.join(task.tags)}")

        notes.append("\n[APP] Creato da Agevolami PM")
        
        return "\n".join(notes)
    
    def disconnect(self) -> bool:
        """Disconnect from Google Tasks"""
        try:
            if os.path.exists(self.token_file):
                os.remove(self.token_file)
            
            self.service = None
            self.enabled = False
            self.task_lists.clear()
            
            logger.info("Disconnected from Google Tasks")
            return True
            
        except Exception as e:
            logger.error(f"Error disconnecting from Google Tasks: {e}")
            return False
    
    def test_connection(self) -> bool:
        """Test the Google Tasks connection"""
        if not self.is_enabled():
            return False
        
        try:
            # Try to list task lists as a simple test
            self.service.tasklists().list().execute()
            logger.info("Google Tasks connection test successful")
            return True
        except Exception as e:
            logger.error(f"Google Tasks connection test failed: {e}")
            return False
    
    def cleanup_orphaned_tasks(self, valid_task_ids: List[str]) -> Dict[str, Any]:
        """Clean up Google Tasks that no longer exist locally"""
        if not self.is_enabled():
            return {"success": False, "error": "Google Tasks not enabled"}
        
        results = {
            "success": True,
            "cleaned": 0,
            "errors": [],
            "task_lists_cleaned": 0
        }
        
        try:
            # Get all task lists
            task_lists = self.get_task_lists()
            
            for task_list in task_lists:
                # Skip non-Agevolami task lists
                if not task_list['title'].startswith('[AGEV]'):
                    continue
                
                # Get tasks in this list
                google_tasks = self.get_tasks_from_list(task_list['id'])
                tasks_to_delete = []
                
                for google_task in google_tasks:
                    # Check if this task still exists locally
                    task_title = google_task.get('title', '')
                    # For now, we'll rely on manual cleanup since we don't have a direct mapping
                    # In a full implementation, you might store task IDs in the notes field
                    pass
                
                # If task list is empty after cleanup, consider removing it
                if len(google_tasks) == 0:
                    try:
                        self.service.tasklists().delete(tasklist=task_list['id']).execute()
                        results["task_lists_cleaned"] += 1
                        logger.info(f"Removed empty task list: {task_list['title']}")
                    except Exception as e:
                        results["errors"].append(f"Failed to remove empty task list: {str(e)}")
            
            logger.info(f"Cleanup completed: {results['cleaned']} tasks and {results['task_lists_cleaned']} lists cleaned")
            return results
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
            return {"success": False, "error": str(e)}
    
    def verify_sync_integrity(self, tasks: List[Task], deadlines: List[Deadline]) -> Dict[str, Any]:
        """Verify the integrity of Google Tasks sync"""
        if not self.is_enabled():
            return {"success": False, "error": "Google Tasks not enabled"}
        
        results = {
            "success": True,
            "verified": 0,
            "missing_on_google": [],
            "orphaned_on_google": [],
            "sync_conflicts": [],
            "total_checked": 0
        }
        
        try:
            # Check each task with Google IDs
            for task in tasks:
                results["total_checked"] += 1
                
                if task.google_task_id and task.google_task_list_id:
                    try:
                        # Verify task exists on Google
                        google_task = self.service.tasks().get(
                            tasklist=task.google_task_list_id,
                            task=task.google_task_id
                        ).execute()
                        
                        # Check for conflicts (title mismatch)
                        if google_task.get('title', '').strip() != task.title.strip():
                            results["sync_conflicts"].append({
                                "local_task": task.title,
                                "google_task": google_task.get('title', ''),
                                "task_id": str(task.id)
                            })
                        else:
                            results["verified"] += 1
                            
                    except HttpError as e:
                        if e.resp.status == 404:
                            results["missing_on_google"].append({
                                "title": task.title,
                                "task_id": str(task.id)
                            })
                        else:
                            logger.error(f"Error verifying task {task.title}: {e}")
            
            logger.info(f"Sync integrity check: {results['verified']}/{results['total_checked']} tasks verified")
            return results
            
        except Exception as e:
            logger.error(f"Error during sync integrity check: {e}")
            return {"success": False, "error": str(e)}
    
    def create_setup_instructions(self) -> str:
        """Create setup instructions for Google Tasks (simplified since credentials are embedded)"""
        return """
[MOBILE] GOOGLE TASKS - CONFIGURAZIONE AUTOMATICA

[OK] Le credenziali Google sono già integrate nell'applicazione!

[SETUP] PASSI PER L'ATTIVAZIONE:

1. Clicca su "Connetti Google Tasks" nelle impostazioni
2. Si aprirà automaticamente il browser per l'autorizzazione Google
3. Accedi con il tuo account Google
4. Autorizza l'accesso a Google Tasks per Agevolami PM
5. La configurazione sarà completata automaticamente!

[FEATURES] FUNZIONALITÀ:
- Creazione automatica di liste di task per ogni scadenza
- Sincronizzazione bidirezionale tra Agevolami PM e Google Tasks
- Accesso mobile tramite l'app Google Tasks
- Notifiche integrate Google

[WARNING] NOTA: Assicurati di avere un account Google attivo
"""