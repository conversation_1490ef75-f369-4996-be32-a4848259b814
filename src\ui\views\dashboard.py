#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vista Dashboard per Agevolami PM
"""

import flet as ft
from typing import List, Dict, Any, Optional
from datetime import datetime, date, timedelta

from core import get_logger
from core.models import ProjectStatus, DeadlineStatus, Priority

logger = get_logger(__name__)

class DashboardView:
    """Vista principale dashboard"""
    
    def __init__(self, app_instance):
        self.app = app_instance
        self.stats_data = {}
        self.recent_alerts = []
        self.upcoming_deadlines = []
        self.project_stats = []
        
        self._init_components()
    
    def _init_components(self):
        """Inizializza i componenti della dashboard"""
        pass
    
    def _create_stats_cards(self) -> ft.Row:
        """Crea le card delle statistiche principali"""
        
        # Card Clienti
        clients_card = self._create_stat_card(
            title="Clienti Attivi",
            value=str(self.stats_data.get('total_clients', 0)),
            icon=ft.Icons.PEOPLE,
            color=ft.Colors.BLUE_600,
            subtitle="Totale clienti"
        )
        
        # Card Progetti
        projects_card = self._create_stat_card(
            title="Progetti Attivi",
            value=str(self.stats_data.get('active_projects', 0)),
            icon=ft.Icons.WORK,
            color=ft.Colors.GREEN_600,
            subtitle=f"{self.stats_data.get('total_projects', 0)} totali"
        )
        
        # Card Scadenze
        deadlines_card = self._create_stat_card(
            title="Scadenze Prossime",
            value=str(self.stats_data.get('upcoming_deadlines', 0)),
            icon=ft.Icons.SCHEDULE,
            color=ft.Colors.ORANGE_600,
            subtitle="Prossimi 15 giorni"
        )
        
        # Card Alert
        alerts_card = self._create_stat_card(
            title="Alert Attivi",
            value=str(self.stats_data.get('active_alerts', 0)),
            icon=ft.Icons.NOTIFICATIONS_ACTIVE,
            color=ft.Colors.RED_600,
            subtitle="Richiedono attenzione"
        )
        
        return ft.Row([
            clients_card,
            projects_card, 
            deadlines_card,
            alerts_card
        ], spacing=16)
    
    def _create_stat_card(self, title: str, value: str, icon: str, color: str, subtitle: str = "") -> ft.Container:
        """Crea una singola card statistica"""
        return ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Container(
                        content=ft.Icon(
                            icon,
                            size=24,
                            color=ft.Colors.WHITE
                        ),
                        width=48,
                        height=48,
                        bgcolor=color,
                        border_radius=12,
                        alignment=ft.alignment.center
                    ),
                    
                    ft.Column([
                        ft.Text(
                            value,
                            size=24,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.GREY_800
                        ),
                        ft.Text(
                            title,
                            size=12,
                            color=ft.Colors.GREY_600,
                            weight=ft.FontWeight.W_500
                        )
                    ], spacing=0, expand=True)
                ], spacing=12),
                
                ft.Text(
                    subtitle,
                    size=10,
                    color=ft.Colors.GREY_500
                ) if subtitle else ft.Container(height=0)
            ], spacing=8),
            padding=ft.padding.all(20),
            bgcolor=ft.Colors.WHITE,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_200),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=4,
                color=ft.Colors.BLACK12
            ),
            expand=True
        )
    
    def _create_alerts_section(self) -> ft.Container:
        """Crea la sezione alert recenti"""
        
        if not self.recent_alerts:
            content = ft.Container(
                content=ft.Column([
                    ft.Icon(
                        ft.Icons.CHECK_CIRCLE,
                        size=32,
                        color=ft.Colors.GREEN_400
                    ),
                    ft.Text(
                        "Nessun alert attivo",
                        size=14,
                        color=ft.Colors.GREY_600,
                        weight=ft.FontWeight.W_500
                    ),
                    ft.Text(
                        "Tutto sotto controllo!",
                        size=12,
                        color=ft.Colors.GREY_400
                    )
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=8),
                padding=ft.padding.all(20),
                alignment=ft.alignment.center
            )
        else:
            alert_items = []
            for alert_data in self.recent_alerts[:5]:  # Mostra solo i primi 5
                alert_items.append(self._create_alert_item(alert_data))
            
            content = ft.Column(
                controls=alert_items,
                spacing=8
            )
        
        return ft.Container(
            content=ft.Column([
                # Header
                ft.Row([
                    ft.Text(
                        "Alert Recenti",
                        size=16,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.GREY_800
                    ),
                    ft.Container(expand=True),
                    ft.TextButton(
                        text="Vedi tutti",
                        icon=ft.Icons.ARROW_FORWARD,
                        on_click=lambda _: self._show_all_alerts()
                    )
                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                
                ft.Divider(height=1, color=ft.Colors.GREY_200),
                
                # Contenuto
                content
            ], spacing=12),
            padding=ft.padding.all(20),
            bgcolor=ft.Colors.WHITE,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_200),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=4,
                color=ft.Colors.BLACK12
            )
        )
    
    def _create_alert_item(self, alert_data: Dict[str, Any]) -> ft.Container:
        """Crea un elemento alert per la dashboard"""
        alert = alert_data["alert"]
        days_remaining = alert_data["days_remaining"]
        is_overdue = alert_data["is_overdue"]
        
        # Colori basati sulla priorità
        priority_colors = {
            Priority.CRITICAL: ft.Colors.RED_600,
            Priority.HIGH: ft.Colors.ORANGE_600,
            Priority.MEDIUM: ft.Colors.BLUE_600,
            Priority.LOW: ft.Colors.GREEN_600
        }
        
        priority_color = priority_colors.get(alert.priority, ft.Colors.GREY_600)
        
        # Status text
        if is_overdue:
            status_text = "SCADUTO"
            status_color = ft.Colors.RED_600
        elif days_remaining == 0:
            status_text = "OGGI"
            status_color = ft.Colors.ORANGE_600
        else:
            status_text = f"{days_remaining} giorni"
            status_color = ft.Colors.BLUE_600
        
        return ft.Container(
            content=ft.Row([
                # Indicatore priorità
                ft.Container(
                    width=3,
                    height=30,
                    bgcolor=priority_color,
                    border_radius=2
                ),
                
                # Contenuto
                ft.Column([
                    ft.Text(
                        alert.title,
                        size=12,
                        weight=ft.FontWeight.W_500,
                        color=ft.Colors.GREY_800,
                        max_lines=1
                    ),
                    ft.Text(
                        alert.message,
                        size=10,
                        color=ft.Colors.GREY_500,
                        max_lines=1
                    )
                ], spacing=2, expand=True),
                
                # Status
                ft.Text(
                    status_text,
                    size=10,
                    color=status_color,
                    weight=ft.FontWeight.BOLD
                )
            ], spacing=12),
            padding=ft.padding.symmetric(horizontal=12, vertical=8),
            border_radius=8,
            bgcolor=ft.Colors.GREY_50,
            border=ft.border.all(1, ft.Colors.GREY_100)
        )
    
    def _create_deadlines_section(self) -> ft.Container:
        """Crea la sezione scadenze imminenti"""
        
        if not self.upcoming_deadlines:
            content = ft.Container(
                content=ft.Column([
                    ft.Icon(
                        ft.Icons.EVENT_AVAILABLE,
                        size=32,
                        color=ft.Colors.GREEN_400
                    ),
                    ft.Text(
                        "Nessuna scadenza imminente",
                        size=14,
                        color=ft.Colors.GREY_600,
                        weight=ft.FontWeight.W_500
                    ),
                    ft.Text(
                        "Prossimi 15 giorni liberi",
                        size=12,
                        color=ft.Colors.GREY_400
                    )
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=8),
                padding=ft.padding.all(20),
                alignment=ft.alignment.center
            )
        else:
            deadline_items = []
            for deadline_data in self.upcoming_deadlines[:5]:  # Mostra solo i primi 5
                deadline_items.append(self._create_deadline_item(deadline_data))
            
            content = ft.Column(
                controls=deadline_items,
                spacing=8
            )
        
        return ft.Container(
            content=ft.Column([
                # Header
                ft.Row([
                    ft.Text(
                        "Scadenze Imminenti",
                        size=16,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.GREY_800
                    ),
                    ft.Container(expand=True),
                    ft.TextButton(
                        text="Vedi tutte",
                        icon=ft.Icons.ARROW_FORWARD,
                        on_click=lambda _: self._show_all_deadlines()
                    )
                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                
                ft.Divider(height=1, color=ft.Colors.GREY_200),
                
                # Contenuto
                content
            ], spacing=12),
            padding=ft.padding.all(20),
            bgcolor=ft.Colors.WHITE,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_200),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=4,
                color=ft.Colors.BLACK12
            )
        )
    
    def _create_deadline_item(self, deadline_data: Dict[str, Any]) -> ft.Container:
        """Crea un elemento scadenza per la dashboard"""
        deadline = deadline_data["deadline"]
        client = deadline_data.get("client")
        project = deadline_data.get("project")
        days_remaining = deadline_data["days_remaining"]
        is_overdue = deadline_data["is_overdue"]
        
        # Icona e colore basati sui giorni rimanenti
        if is_overdue:
            icon = ft.Icons.ERROR
            color = ft.Colors.RED_600
            status_text = "SCADUTO"
        elif days_remaining == 0:
            icon = ft.Icons.WARNING
            color = ft.Colors.ORANGE_600
            status_text = "OGGI"
        elif days_remaining <= 3:
            icon = ft.Icons.SCHEDULE
            color = ft.Colors.ORANGE_600
            status_text = f"{days_remaining}g"
        else:
            icon = ft.Icons.EVENT
            color = ft.Colors.BLUE_600
            status_text = f"{days_remaining}g"
        
        # Sottotitolo
        subtitle_parts = []
        if client:
            subtitle_parts.append(client.name)
        if project:
            subtitle_parts.append(project.name)
        
        subtitle = " • ".join(subtitle_parts) if subtitle_parts else "Scadenza generale"
        
        return ft.Container(
            content=ft.Row([
                # Icona status
                ft.Icon(
                    icon,
                    size=20,
                    color=color
                ),
                
                # Contenuto
                ft.Column([
                    ft.Text(
                        deadline.title,
                        size=12,
                        weight=ft.FontWeight.W_500,
                        color=ft.Colors.GREY_800,
                        max_lines=1
                    ),
                    ft.Text(
                        subtitle,
                        size=10,
                        color=ft.Colors.GREY_500,
                        max_lines=1
                    )
                ], spacing=2, expand=True),
                
                # Data e status
                ft.Column([
                    ft.Text(
                        deadline.due_date.strftime("%d/%m/%Y"),
                        size=10,
                        color=ft.Colors.GREY_600,
                        weight=ft.FontWeight.W_500
                    ),
                    ft.Text(
                        status_text,
                        size=9,
                        color=color,
                        weight=ft.FontWeight.BOLD
                    )
                ], spacing=2, horizontal_alignment=ft.CrossAxisAlignment.END)
            ], spacing=12),
            padding=ft.padding.symmetric(horizontal=12, vertical=8),
            border_radius=8,
            bgcolor=ft.Colors.GREY_50,
            border=ft.border.all(1, ft.Colors.GREY_100)
        )
    
    def _create_projects_chart(self) -> ft.Container:
        """Crea il grafico dei progetti per tipo"""
        
        if not self.project_stats:
            content = ft.Container(
                content=ft.Column([
                    ft.Icon(
                        ft.Icons.PIE_CHART,
                        size=32,
                        color=ft.Colors.GREY_400
                    ),
                    ft.Text(
                        "Nessun progetto attivo",
                        size=14,
                        color=ft.Colors.GREY_600,
                        weight=ft.FontWeight.W_500
                    )
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=8),
                padding=ft.padding.all(20),
                alignment=ft.alignment.center
            )
        else:
            # Crea una lista semplice dei progetti per tipo
            project_items = []
            colors = [ft.Colors.BLUE_600, ft.Colors.GREEN_600, ft.Colors.ORANGE_600, ft.Colors.PURPLE_600]
            
            for i, stat in enumerate(self.project_stats):
                color = colors[i % len(colors)]
                project_items.append(
                    ft.Row([
                        ft.Container(
                            width=12,
                            height=12,
                            bgcolor=color,
                            border_radius=6
                        ),
                        ft.Text(
                            stat["type"],
                            size=12,
                            color=ft.Colors.GREY_700,
                            expand=True
                        ),
                        ft.Text(
                            str(stat["count"]),
                            size=12,
                            color=ft.Colors.GREY_800,
                            weight=ft.FontWeight.BOLD
                        )
                    ], spacing=8)
                )
            
            content = ft.Column(
                controls=project_items,
                spacing=12
            )
        
        return ft.Container(
            content=ft.Column([
                # Header
                ft.Text(
                    "Progetti per Tipo",
                    size=16,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.GREY_800
                ),
                
                ft.Divider(height=1, color=ft.Colors.GREY_200),
                
                # Contenuto
                content
            ], spacing=12),
            padding=ft.padding.all(20),
            bgcolor=ft.Colors.WHITE,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_200),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=4,
                color=ft.Colors.BLACK12
            )
        )
    
    def _show_all_alerts(self):
        """Mostra tutti gli alert"""
        # TODO: Navigare alla vista alert o aprire il pannello
        logger.info("Navigazione a tutti gli alert")
    
    def _show_all_deadlines(self):
        """Mostra tutte le scadenze"""
        # TODO: Navigare alla vista scadenze
        logger.info("Navigazione a tutte le scadenze")
    
    def build(self) -> ft.Container:
        """Costruisce la vista dashboard"""
        return ft.Container(
            content=ft.Column([
                # Header
                ft.Container(
                    content=ft.Column([
                        ft.Text(
                            "Dashboard",
                            size=24,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.GREY_800
                        ),
                        ft.Text(
                            f"Aggiornato: {datetime.now().strftime('%d/%m/%Y alle %H:%M')}",
                            size=12,
                            color=ft.Colors.GREY_500
                        )
                    ], spacing=4),
                    padding=ft.padding.only(bottom=20)
                ),
                
                # Statistiche principali
                self._create_stats_cards(),
                
                # Sezioni principali
                ft.Row([
                    # Colonna sinistra
                    ft.Column([
                        self._create_alerts_section(),
                        self._create_deadlines_section()
                    ], spacing=20, expand=2),
                    
                    # Colonna destra
                    ft.Column([
                        self._create_projects_chart()
                    ], spacing=20, expand=1)
                ], spacing=20, alignment=ft.MainAxisAlignment.START)
            ], spacing=20, scroll=ft.ScrollMode.AUTO),
            padding=ft.padding.all(20),
            expand=True
        )
    
    def refresh_data(self):
        """Aggiorna i dati della dashboard"""
        try:
            # Carica statistiche
            self.stats_data = self._load_stats()
            
            # Carica alert recenti
            self.recent_alerts = self.app.alert_service.get_dashboard_alerts(limit=5)
            
            # Carica scadenze imminenti
            self.upcoming_deadlines = self._load_upcoming_deadlines()
            
            # Carica statistiche progetti
            self.project_stats = self._load_project_stats()
            
            logger.info("Dati dashboard aggiornati")
            
        except Exception as e:
            logger.error(f"Errore aggiornamento dashboard: {e}")
    
    def refresh(self):
        """Metodo di refresh chiamato dal layout principale"""
        self.refresh_data()
    
    def _load_stats(self) -> Dict[str, int]:
        """Carica le statistiche principali"""
        try:
            # Clienti
            clients = self.app.db.get_all_clients()
            total_clients = len(clients)
            
            # Progetti
            projects = self.app.db.get_all_projects()
            total_projects = len(projects)
            active_projects = len([p for p in projects if p.status == ProjectStatus.IN_PROGRESS])
            
            # Scadenze prossime (15 giorni)
            upcoming_date = date.today() + timedelta(days=15)
            upcoming_deadlines = len([
                d for d in self.app.db.get_all_deadlines()
                if d.due_date <= upcoming_date and d.status != DeadlineStatus.COMPLETED
            ])
            
            # Alert attivi
            active_alerts = len(self.app.alert_service.get_dashboard_alerts())
            
            return {
                'total_clients': total_clients,
                'total_projects': total_projects,
                'active_projects': active_projects,
                'upcoming_deadlines': upcoming_deadlines,
                'active_alerts': active_alerts
            }
            
        except Exception as e:
            logger.error(f"Errore caricamento statistiche: {e}")
            return {}
    
    def _load_upcoming_deadlines(self) -> List[Dict[str, Any]]:
        """Carica le scadenze imminenti"""
        try:
            deadlines = self.app.db.get_all_deadlines()
            upcoming = []
            
            for deadline in deadlines:
                if deadline.status == DeadlineStatus.COMPLETED:
                    continue
                
                days_remaining = (deadline.due_date - date.today()).days
                is_overdue = days_remaining < 0
                
                if is_overdue or days_remaining <= 15:
                    # Carica dati correlati
                    client = None
                    project = None
                    
                    if deadline.project_id:
                        project = self.app.db.get_project(deadline.project_id)
                        if project and project.client_id:
                            client = self.app.db.get_client(project.client_id)
                    
                    upcoming.append({
                        'deadline': deadline,
                        'client': client,
                        'project': project,
                        'days_remaining': abs(days_remaining),
                        'is_overdue': is_overdue
                    })
            
            # Ordina per urgenza
            upcoming.sort(key=lambda x: (x['is_overdue'], x['days_remaining']))
            
            return upcoming[:10]  # Limita a 10
            
        except Exception as e:
            logger.error(f"Errore caricamento scadenze: {e}")
            return []
    
    def _load_project_stats(self) -> List[Dict[str, Any]]:
        """Carica le statistiche dei progetti per tipo"""
        try:
            projects = self.app.db.get_all_projects()
            stats = {}
            
            for project in projects:
                if project.status == ProjectStatus.IN_PROGRESS:
                    project_type = project.project_type.value if hasattr(project.project_type, 'value') else str(project.project_type)
                    stats[project_type] = stats.get(project_type, 0) + 1
            
            return [
                {'type': type_name, 'count': count}
                for type_name, count in stats.items()
            ]
            
        except Exception as e:
            logger.error(f"Errore caricamento statistiche progetti: {e}")
            return []