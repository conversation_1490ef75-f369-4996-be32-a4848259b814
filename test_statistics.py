#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script per il servizio statistiche
"""

import sys
from pathlib import Path

# Aggiungi il percorso src al PYTHONPATH
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from core.config import AppConfig
from core.database import DatabaseManagerExtended
from core.services.statistics_service import StatisticsService
import json

def test_statistics_service():
    """Test del servizio statistiche"""
    try:
        print("=== TEST SERVIZIO STATISTICHE ===")
        
        # Inizializza configurazione e database
        config = AppConfig()
        db_manager = DatabaseManagerExtended(config.database_path)
        
        # Crea servizio statistiche
        stats_service = StatisticsService(db_manager, config)
        
        print("\n1. Test generazione report scadenze...")
        deadlines_report = stats_service.generate_deadlines_report()
        
        if deadlines_report:
            print(f"✓ Report scadenze generato con successo")
            print(f"  - Totale scadenze: {deadlines_report.get('summary', {}).get('total_deadlines', 0)}")
            print(f"  - Scadenze scadute: {deadlines_report.get('summary', {}).get('overdue', 0)}")
            print(f"  - Scadenze oggi: {deadlines_report.get('summary', {}).get('due_today', 0)}")
            print(f"  - Scadenze questa settimana: {deadlines_report.get('summary', {}).get('due_this_week', 0)}")
        else:
            print("✗ Errore nella generazione del report scadenze")
        
        print("\n2. Test generazione report completo...")
        full_report = stats_service.generate_full_report()
        
        if full_report:
            print(f"✓ Report completo generato con successo")
            summary = full_report.get('summary', {})
            print(f"  - Clienti totali: {summary.get('total_clients', 0)}")
            print(f"  - Progetti totali: {summary.get('total_projects', 0)}")
            print(f"  - Progetti attivi: {summary.get('active_projects', 0)}")
            print(f"  - Scadenze totali: {summary.get('total_deadlines', 0)}")
        else:
            print("✗ Errore nella generazione del report completo")
        
        print("\n3. Test configurazione email...")
        email_configured = config.is_email_configured()
        print(f"  - Email configurata: {'✓' if email_configured else '✗'}")
        
        if email_configured:
            print(f"  - Server SMTP: {config.email_config.get('smtp_server', 'N/A')}")
            print(f"  - Username: {config.email_config.get('smtp_username', 'N/A')}")
        else:
            print("  - Configura SMTP nelle impostazioni per testare l'invio email")
        
        print("\n4. Test HTML generation...")
        if deadlines_report:
            html_content = stats_service._generate_deadlines_html(deadlines_report)
            print(f"✓ HTML generato: {len(html_content)} caratteri")
        
        print("\n=== TEST COMPLETATO ===")
        return True
        
    except Exception as e:
        print(f"✗ Errore durante il test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_statistics_service()