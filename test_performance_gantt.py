#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Performance-Optimized Gantt Chart Test
Fast, responsive chart with simplified visuals for better UX
"""

import flet as ft
import sys
import os
from datetime import datetime, date, timedelta
from dataclasses import dataclass

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Simple mock classes for testing
class ProjectStatus:
    DRAFT = "bozza"
    SUBMITTED = "presentato"
    APPROVED = "approvato"
    IN_PROGRESS = "in_corso"
    COMPLETED = "completato"
    SUSPENDED = "sospeso"
    CANCELLED = "annullato"

class Priority:
    LOW = "bassa"
    MEDIUM = "media"
    HIGH = "alta"
    CRITICAL = "critica"

class DeadlineStatus:
    PENDING = "in_attesa"
    COMPLETED = "completato"
    OVERDUE = "scaduto"
    CANCELLED = "annullato"

@dataclass
class Project:
    id: str
    name: str
    description: str
    client_id: str
    status: str
    start_date: date
    end_date: date = None
    
@dataclass
class Deadline:
    id: str
    title: str
    description: str
    client_id: str
    due_date: date
    priority: str
    status: str

@dataclass
class Client:
    id: str
    name: str
    email: str

class MockDatabaseManager:
    def __init__(self):
        self.projects = []
        self.deadlines = []
        self.clients = []
        self._create_test_data()
    
    def _create_test_data(self):
        """Create minimal test data for performance testing"""
        today = date.today()
        
        # Fewer clients for simpler testing
        self.clients = [
            Client("1", "TechCorp", "<EMAIL>"),
            Client("2", "BuildCo", "<EMAIL>"),
            Client("3", "Restaurant", "<EMAIL>")
        ]
        
        # Fewer projects for better performance
        self.projects = [
            Project("p1", "Sistema CRM", "Sviluppo CRM", "1", 
                   ProjectStatus.IN_PROGRESS, today - timedelta(days=10), today + timedelta(days=20)),
            
            Project("p2", "Gestionale", "Software gestione", "2", 
                   ProjectStatus.APPROVED, today + timedelta(days=2), today + timedelta(days=30)),
            
            Project("p3", "App Mobile", "App prenotazioni", "3", 
                   ProjectStatus.SUBMITTED, today - timedelta(days=5), today + timedelta(days=15)),
            
            Project("p4", "Sito Web", "Nuovo sito", "1", 
                   ProjectStatus.COMPLETED, today - timedelta(days=30), today - timedelta(days=5))
        ]
        
        # Fewer deadlines for better performance
        self.deadlines = [
            Deadline("d1", "Documentazione", "Doc finale", "1", 
                    today + timedelta(days=7), Priority.HIGH, DeadlineStatus.PENDING),
            
            Deadline("d2", "Approvazione", "Approvazione progetto", "2", 
                    today + timedelta(days=12), Priority.CRITICAL, DeadlineStatus.PENDING),
            
            Deadline("d3", "Test", "Testing app", "3", 
                    today + timedelta(days=20), Priority.MEDIUM, DeadlineStatus.PENDING),
            
            # One completed (hidden by default)
            Deadline("d4", "Setup", "Setup iniziale", "1", 
                    today - timedelta(days=15), Priority.LOW, DeadlineStatus.COMPLETED)
        ]
    
    def get_all_projects(self):
        return self.projects
    
    def get_deadlines_by_date_range(self, start_date, end_date):
        return [d for d in self.deadlines if start_date <= d.due_date <= end_date]
    
    def get_all_clients(self):
        return self.clients

class MockApp:
    def __init__(self):
        self.db_manager = MockDatabaseManager()
        self.page = None
    
    def show_success(self, message: str):
        print(f"✅ {message}")
    
    def show_error(self, message: str):
        print(f"❌ {message}")

def main(page: ft.Page):
    """Main application function"""
    page.title = "🚀 Fast Gantt Chart - Performance Optimized"
    page.theme_mode = ft.ThemeMode.LIGHT
    page.padding = 0
    page.bgcolor = ft.Colors.GREY_50
    
    try:
        from ui.views.matplotlib_gantt import MatplotlibGanttView
        
        app = MockApp()
        app.page = page
        
        # Performance info card
        perf_card = ft.Container(
            content=ft.Column([
                ft.Text("🚀 Performance Optimized Gantt Chart", 
                       size=24, weight=ft.FontWeight.BOLD, color=ft.Colors.WHITE),
                ft.Text("✨ Simplified visuals, faster rendering, better UX", 
                       size=14, color=ft.Colors.WHITE70),
                ft.Row([
                    ft.Icon(ft.Icons.SPEED, color=ft.Colors.WHITE),
                    ft.Text("• Non-interactive backend • Reduced DPI • Simplified effects", 
                           size=12, color=ft.Colors.WHITE70)
                ], spacing=8)
            ], spacing=8),
            padding=ft.padding.all(20),
            margin=ft.margin.all(20),
            bgcolor=ft.Colors.GREEN_600,
            border_radius=12
        )
        
        gantt_view = MatplotlibGanttView(app)
        
        page.add(
            ft.Column([
                perf_card,
                gantt_view.build()
            ], spacing=0, scroll=ft.ScrollMode.AUTO)
        )
        
        print("✅ Performance-optimized Gantt Chart loaded successfully!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        
        page.add(
            ft.Container(
                content=ft.Column([
                    ft.Icon(ft.Icons.ERROR, size=64, color=ft.Colors.RED),
                    ft.Text(f"Error: {e}", size=16, color=ft.Colors.RED),
                    ft.Text("Check console for details", size=12)
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=ft.padding.all(40),
                alignment=ft.alignment.center
            )
        )

if __name__ == "__main__":
    print("🚀 Starting Performance-Optimized Gantt Chart...")
    print("⚡ Fast rendering with simplified visuals")
    print("🎯 Better UX and responsiveness")
    ft.app(target=main, view=ft.AppView.WEB_BROWSER, port=8082) 