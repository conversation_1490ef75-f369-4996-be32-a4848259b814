#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Google Calendar Integration Service for Agevolami PM
"""

import os
import json
import tempfile
import threading
from datetime import datetime, date, timedelta
from typing import List, Dict, Optional, Any
from uuid import UUID
import pickle
import time

from ..utils import get_logger
from core.models.base_models import Deadline, DeadlineStatus, Priority
from .cache_manager import get_google_api_cache

logger = get_logger(__name__)

try:
    from google.auth.transport.requests import Request
    from google.oauth2.credentials import Credentials
    from google_auth_oauthlib.flow import InstalledAppFlow
    from googleapiclient.discovery import build
    from googleapiclient.errors import HttpError
    GOOGLE_AVAILABLE = True
except ImportError:
    GOOGLE_AVAILABLE = False
    logger.warning("Google Calendar libraries not available. Install with: pip install google-api-python-client google-auth-httplib2 google-auth-oauthlib")

class GoogleCalendarService:
    """Service for Google Calendar integration"""
    
    # Scopes needed for calendar access
    SCOPES = ['https://www.googleapis.com/auth/calendar']
    
    # Embedded client credentials for seamless authentication
    EMBEDDED_CREDENTIALS = {
        "installed": {
            "client_id": "*********************************************.apps.googleusercontent.com",
            "project_id": "agevolami-pm",
            "auth_uri": "https://accounts.google.com/o/oauth2/auth",
            "token_uri": "https://oauth2.googleapis.com/token",
            "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
            "client_secret": "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",
            "redirect_uris": ["http://localhost"]
        }
    }
    
    def __init__(self, config_dir: str = "config"):
        # Ensure consistent config directory structure
        if not config_dir.endswith("config"):
            self.config_dir = os.path.join(config_dir, "config")
        else:
            self.config_dir = config_dir
        os.makedirs(self.config_dir, exist_ok=True)

        self.credentials_file = os.path.join(self.config_dir, "google_credentials.json")  # Keep for backward compatibility
        self.token_file = os.path.join(self.config_dir, "google_token.pickle")
        self.calendar_id = None  # Will be set to the Agevolami calendar ID
        self.service = None
        self.enabled = False

        # Cache for API responses
        self.api_cache = get_google_api_cache()

        # Thread safety
        self._lock = threading.RLock()

        # FIXED: Use synchronous auth check like Google Drive to prevent race conditions
        if GOOGLE_AVAILABLE:
            if self._check_existing_auth():
                # Setup calendar synchronously on successful auth
                self._setup_agevolami_calendar()
    
    def _check_existing_auth(self) -> bool:
        """Check if there's existing valid authentication without triggering new auth"""
        try:
            if not os.path.exists(self.token_file):
                return False

            with open(self.token_file, 'rb') as token:
                creds = pickle.load(token)

            # Check if credentials are valid
            if creds and creds.valid:
                self.service = build('calendar', 'v3', credentials=creds)
                self.enabled = True
                self._setup_agevolami_calendar()
                logger.info("Google Calendar: Existing authentication found and valid")
                return True
            elif creds and creds.expired and creds.refresh_token:
                try:
                    creds.refresh(Request())
                    with open(self.token_file, 'wb') as token:
                        pickle.dump(creds, token)

                    self.service = build('calendar', 'v3', credentials=creds)
                    self.enabled = True
                    self._setup_agevolami_calendar()
                    logger.info("Google Calendar: Authentication refreshed successfully")
                    return True
                except Exception as e:
                    logger.warning(f"Google Calendar: Token refresh failed: {e}")
                    return False
            else:
                return False

        except Exception as e:
            logger.warning(f"Google Calendar: Error checking existing auth: {e}")
            return False
    
    def _setup_agevolami_calendar(self):
        """Find or create the Agevolami calendar"""
        try:
            # List all calendars
            calendars_result = self.service.calendarList().list().execute()
            calendars = calendars_result.get('items', [])

            # Look for existing Agevolami calendar
            for calendar in calendars:
                if calendar.get('summary') == 'Agevolami PM':
                    self.calendar_id = calendar['id']
                    logger.info(f"Found existing Agevolami calendar: {self.calendar_id}")
                    return

            # Create new calendar if not found
            calendar = {
                'summary': 'Agevolami PM',
                'description': 'Scadenze e task dal sistema Agevolami PM',
                'timeZone': 'Europe/Rome'
            }

            created_calendar = self.service.calendars().insert(body=calendar).execute()
            self.calendar_id = created_calendar['id']
            logger.info(f"Created new Agevolami calendar: {self.calendar_id}")

        except Exception as e:
            logger.error(f"Error setting up Agevolami calendar: {e}")
            self.calendar_id = 'primary'  # Fallback to primary calendar
    
    def is_enabled(self) -> bool:
        """Check if Google Calendar integration is enabled"""
        return self.enabled and GOOGLE_AVAILABLE

    def invalidate_cache(self) -> None:
        """Invalidate all cached calendar data"""
        calendar_id = self.calendar_id or 'primary'
        self.api_cache.invalidate_calendar_cache(calendar_id)
        logger.info("Google Calendar cache invalidated")
    
    def sync_deadline_to_google(self, deadline: Deadline) -> Optional[str]:
        """Sync a deadline to Google Calendar with duplicate prevention"""
        if not self.is_enabled():
            return None
        
        try:
            # Convert deadline to Google Calendar event
            event = self._deadline_to_event(deadline)
            
            # Check if event already exists on Google
            if hasattr(deadline, 'google_event_id') and deadline.google_event_id:
                try:
                    # Verify the event still exists on Google
                    existing_event = self.service.events().get(
                        calendarId=self.calendar_id,
                        eventId=deadline.google_event_id
                    ).execute()
                    
                    # Event exists, update it
                    result = self.service.events().update(
                        calendarId=self.calendar_id,
                        eventId=deadline.google_event_id,
                        body=event
                    ).execute()
                    logger.info(f"Updated existing Google Calendar event: {deadline.title}")
                    return result['id']
                    
                except HttpError as e:
                    if e.resp.status == 404:
                        # Event was deleted from Google, clear the ID and create new
                        logger.warning(f"Google Calendar event was deleted, recreating: {deadline.title}")
                        deadline.google_event_id = None
                    else:
                        raise e
            
            # Search for existing event by title to avoid duplicates
            existing_google_event = self._find_existing_event_by_title(deadline.title, deadline.due_date)
            if existing_google_event:
                # Found existing event, update local deadline with Google ID
                deadline.google_event_id = existing_google_event['id']
                
                # Update the existing Google event
                result = self.service.events().update(
                    calendarId=self.calendar_id,
                    eventId=existing_google_event['id'],
                    body=event
                ).execute()
                logger.info(f"Found and updated existing Google Calendar event: {deadline.title}")
                return result['id']
            
            # Create new event (no existing event found)
            result = self.service.events().insert(
                calendarId=self.calendar_id,
                body=event
            ).execute()
            
            # Update local deadline with Google event ID
            deadline.google_event_id = result['id']

            # Invalidate cache since we added a new event
            self.invalidate_cache()

            logger.info(f"Created new Google Calendar event: {deadline.title}")
            return result['id']
                
        except HttpError as e:
            logger.error(f"Google Calendar API error: {e}")
            return None
        except Exception as e:
            logger.error(f"Error syncing deadline to Google Calendar: {e}")
            return None
    
    def _find_existing_event_by_title(self, title: str, due_date: date) -> Optional[Dict]:
        """Find an existing Google Calendar event by title and date to avoid duplicates"""
        try:
            # Search for events around the due date (±1 day to account for timezone differences)
            start_time = (due_date - timedelta(days=1)).isoformat() + 'T00:00:00Z'
            end_time = (due_date + timedelta(days=1)).isoformat() + 'T23:59:59Z'
            
            # Get events in the date range
            events_result = self.service.events().list(
                calendarId=self.calendar_id,
                timeMin=start_time,
                timeMax=end_time,
                singleEvents=True,
                orderBy='startTime'
            ).execute()
            
            events = events_result.get('items', [])
            
            # Search for event with matching title
            for google_event in events:
                if google_event.get('summary', '').strip() == title.strip():
                    logger.info(f"Found existing Google Calendar event with title: {title}")
                    return google_event
            
            return None
            
        except Exception as e:
            logger.error(f"Error searching for existing event: {e}")
            return None
    
    def delete_deadline_from_google(self, google_event_id: str) -> bool:
        """Delete a deadline from Google Calendar"""
        if not self.is_enabled() or not google_event_id:
            return False
        
        try:
            self.service.events().delete(
                calendarId=self.calendar_id,
                eventId=google_event_id
            ).execute()

            # Invalidate cache since we deleted an event
            self.invalidate_cache()

            logger.info(f"Deleted Google Calendar event: {google_event_id}")
            return True
            
        except HttpError as e:
            if e.resp.status == 404:
                logger.warning(f"Google Calendar event not found: {google_event_id}")
                return True  # Consider as success since it's already gone
            logger.error(f"Google Calendar API error: {e}")
            return False
        except Exception as e:
            logger.error(f"Error deleting from Google Calendar: {e}")
            return False
    
    def sync_all_deadlines(self, deadlines: List[Deadline]) -> Dict[str, Any]:
        """Sync all deadlines to Google Calendar"""
        if not self.is_enabled():
            return {"success": False, "error": "Google Calendar not enabled"}
        
        results = {
            "success": True,
            "synced": 0,
            "errors": [],
            "total": len(deadlines)
        }
        
        # Rate limiting: max 10 requests per second
        batch_size = 5
        delay_between_batches = 1.0  # seconds
        
        for i in range(0, len(deadlines), batch_size):
            batch = deadlines[i:i + batch_size]
            
            for deadline in batch:
                try:
                    event_id = self.sync_deadline_to_google(deadline)
                    if event_id:
                        results["synced"] += 1
                        # Update deadline with Google event ID (you'll need to add this field to your Deadline model)
                        # deadline.google_event_id = event_id
                    else:
                        results["errors"].append(f"Failed to sync: {deadline.title}")
                except Exception as e:
                    results["errors"].append(f"Error syncing {deadline.title}: {str(e)}")
            
            # Add delay between batches to respect rate limits
            if i + batch_size < len(deadlines):
                time.sleep(delay_between_batches)
        
        logger.info(f"Sync completed: {results['synced']}/{results['total']} deadlines synced")
        if results["errors"]:
            logger.warning(f"Sync errors: {len(results['errors'])} errors occurred")
        
        return results
    
    def get_google_events(self, start_date: date, end_date: date) -> List[Dict]:
        """Get events from Google Calendar for a date range with caching"""
        if not self.is_enabled():
            return []

        # Check cache first
        start_str = start_date.isoformat()
        end_str = end_date.isoformat()
        calendar_id = self.calendar_id or 'primary'

        cached_events = self.api_cache.get_calendar_events(calendar_id, start_str, end_str)
        if cached_events is not None:
            logger.debug(f"Retrieved {len(cached_events)} events from cache")
            return cached_events

        try:
            # Convert dates to RFC3339 format
            start_time = datetime.combine(start_date, datetime.min.time()).isoformat() + 'Z'
            end_time = datetime.combine(end_date, datetime.max.time()).isoformat() + 'Z'

            events_result = self.service.events().list(
                calendarId=calendar_id,
                timeMin=start_time,
                timeMax=end_time,
                singleEvents=True,
                orderBy='startTime'
            ).execute()

            events = events_result.get('items', [])

            # Cache the results
            self.api_cache.set_calendar_events(calendar_id, start_str, end_str, events)

            logger.info(f"Retrieved {len(events)} events from Google Calendar API")
            return events

        except Exception as e:
            logger.error(f"Error getting Google Calendar events: {e}")
            return []
    
    def _deadline_to_event(self, deadline: Deadline) -> Dict:
        """Convert a Deadline to Google Calendar event format"""
        # Import enums here to avoid circular imports
        from core.models.base_models import Priority, DeadlineStatus
        
        # Determine event color based on priority
        color_map = {
            Priority.LOW: '2',      # Green
            Priority.MEDIUM: '1',   # Blue
            Priority.HIGH: '6',     # Orange
            Priority.CRITICAL: '11' # Red
        }
        
        # Handle priority - could be enum or string
        priority_value = deadline.priority
        if hasattr(priority_value, 'value'):
            priority_str = priority_value.value.title()
            priority_enum = priority_value
        else:
            priority_str = str(priority_value).title()
            # Try to convert string to enum for color mapping
            try:
                priority_enum = Priority(priority_value.lower())
            except:
                priority_enum = Priority.MEDIUM  # Default
        
        # Handle status - could be enum or string  
        status_value = deadline.status
        if hasattr(status_value, 'value'):
            status_str = status_value.value.title()
        else:
            status_str = str(status_value).title()
        
        # Create event description
        description_parts = []
        if deadline.description:
            description_parts.append(deadline.description)
        
        description_parts.append(f"Priorità: {priority_str}")
        description_parts.append(f"Stato: {status_str}")
        description_parts.append(f"ID: {deadline.id}")
        description_parts.append("Creato da Agevolami PM")
        
        # Create the event
        event = {
            'summary': deadline.title,
            'description': '\n'.join(description_parts),
            'start': {
                'date': deadline.due_date.isoformat(),
                'timeZone': 'Europe/Rome',
            },
            'end': {
                'date': deadline.due_date.isoformat(),
                'timeZone': 'Europe/Rome',
            },
            'colorId': color_map.get(priority_enum, '1')
        }
        
        # Add reminders for deadlines
        if hasattr(deadline, 'alert_days_before') and deadline.alert_days_before and deadline.alert_days_before > 0:
            event['reminders'] = {
                'useDefault': False,
                'overrides': [
                    {'method': 'email', 'minutes': deadline.alert_days_before * 24 * 60},
                    {'method': 'popup', 'minutes': deadline.alert_days_before * 24 * 60}
                ]
            }
        
        return event
    
    def verify_sync_integrity(self, deadlines: List[Deadline]) -> Dict[str, Any]:
        """Verify the integrity of Google Calendar sync"""
        if not self.is_enabled():
            return {"success": False, "error": "Google Calendar not enabled"}
        
        results = {
            "success": True,
            "verified": 0,
            "missing_on_google": [],
            "sync_conflicts": [],
            "total_checked": 0
        }
        
        try:
            # Check each deadline with Google event ID
            for deadline in deadlines:
                results["total_checked"] += 1
                
                if hasattr(deadline, 'google_event_id') and deadline.google_event_id:
                    try:
                        # Verify event exists on Google
                        google_event = self.service.events().get(
                            calendarId=self.calendar_id,
                            eventId=deadline.google_event_id
                        ).execute()
                        
                        # Check for conflicts (title mismatch)
                        if google_event.get('summary', '').strip() != deadline.title.strip():
                            results["sync_conflicts"].append({
                                "local_deadline": deadline.title,
                                "google_event": google_event.get('summary', ''),
                                "deadline_id": str(deadline.id)
                            })
                        else:
                            results["verified"] += 1
                            
                    except HttpError as e:
                        if e.resp.status == 404:
                            results["missing_on_google"].append({
                                "title": deadline.title,
                                "deadline_id": str(deadline.id)
                            })
                        else:
                            logger.error(f"Error verifying deadline {deadline.title}: {e}")
            
            logger.info(f"Calendar sync integrity check: {results['verified']}/{results['total_checked']} deadlines verified")
            return results
            
        except Exception as e:
            logger.error(f"Error during calendar sync integrity check: {e}")
            return {"success": False, "error": str(e)}
    
    def cleanup_orphaned_events(self, valid_deadline_ids: List[str], start_date: date = None, end_date: date = None) -> Dict[str, Any]:
        """Clean up Google Calendar events that no longer exist locally"""
        if not self.is_enabled():
            return {"success": False, "error": "Google Calendar not enabled"}
        
        # Default to checking the last year and next year
        if not start_date:
            start_date = date.today() - timedelta(days=365)
        if not end_date:
            end_date = date.today() + timedelta(days=365)
        
        results = {
            "success": True,
            "cleaned": 0,
            "errors": [],
            "total_checked": 0
        }
        
        try:
            # Get all events in the date range
            events = self.get_google_events(start_date, end_date)
            
            for event in events:
                results["total_checked"] += 1
                
                # Check if this is an Agevolami PM event
                description = event.get('description', '')
                if 'Creato da Agevolami PM' not in description:
                    continue
                
                # Extract deadline ID from description
                deadline_id = None
                for line in description.split('\n'):
                    if line.startswith('ID: '):
                        deadline_id = line.replace('ID: ', '').strip()
                        break
                
                # If deadline ID not found locally, mark for deletion
                if deadline_id and deadline_id not in valid_deadline_ids:
                    try:
                        self.service.events().delete(
                            calendarId=self.calendar_id,
                            eventId=event['id']
                        ).execute()
                        results["cleaned"] += 1
                        logger.info(f"Removed orphaned calendar event: {event.get('summary', 'Unknown')}")
                    except Exception as e:
                        results["errors"].append(f"Failed to remove orphaned event: {str(e)}")
            
            logger.info(f"Calendar cleanup completed: {results['cleaned']}/{results['total_checked']} events cleaned")
            return results
            
        except Exception as e:
            logger.error(f"Error during calendar cleanup: {e}")
            return {"success": False, "error": str(e)}
    
    def create_setup_instructions(self) -> str:
        """Return setup instructions for Google Calendar integration"""
        return """
# Google Calendar Setup Instructions

## Step 1: Enable Google Calendar API
1. Go to https://console.developers.google.com/
2. Create a new project or select existing one
3. Enable the Google Calendar API
4. Go to "Credentials" and create OAuth 2.0 Client IDs
5. Download the credentials JSON file

## Step 2: Configure Agevolami PM
1. Place the downloaded JSON file in the config folder as 'google_credentials.json'
2. Restart the application
3. When prompted, authorize access to your Google Calendar

## Step 3: Features Available
- Automatic sync of deadlines to Google Calendar
- Color-coded events based on priority
- Email and popup reminders
- Bidirectional sync (future feature)

## Files needed:
- config/google_credentials.json (from Google Console)
- config/google_token.pickle (auto-generated)
"""

    def get_calendar_embed_url(self) -> Optional[str]:
        """Get the embed URL for the Agevolami calendar"""
        if not self.calendar_id:
            return None
        
        return f"https://calendar.google.com/calendar/embed?src={self.calendar_id}&ctz=Europe/Rome"
    
    def authenticate(self, credentials_content: str = None) -> bool:
        """Manually authenticate with Google Calendar using embedded or provided credentials"""
        try:
            credentials_data = None
            
            # Use embedded credentials by default
            if not credentials_content:
                credentials_data = self.EMBEDDED_CREDENTIALS
                logger.info("Using embedded Google Calendar credentials")
            else:
                # Use provided credentials (backward compatibility)
                with open(self.credentials_file, 'w') as f:
                    f.write(credentials_content)
                credentials_file_path = str(self.credentials_file)
            
            if credentials_data:
                # Create temporary credentials file from embedded data
                with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_file:
                    json.dump(credentials_data, temp_file)
                    credentials_file_path = temp_file.name
            
            try:
                # Esegue il flusso OAuth
                flow = InstalledAppFlow.from_client_secrets_file(
                    credentials_file_path, self.SCOPES)
                creds = flow.run_local_server(port=0)
                
                # Salva il token
                with open(self.token_file, 'wb') as token:
                    pickle.dump(creds, token)
                
                # Inizializza il servizio
                self.service = build('calendar', 'v3', credentials=creds)
                self.enabled = True
                
                # Crea cartella dell'app
                self._setup_agevolami_calendar()
                
                logger.info("✅ Google Calendar autenticato con successo")
                return True
                
            finally:
                # Clean up temporary file if we created one
                if credentials_data and os.path.exists(credentials_file_path):
                    try:
                        os.unlink(credentials_file_path)
                    except:
                        pass
            
        except Exception as e:
            logger.error(f"Errore autenticazione Google Calendar: {e}")
            return False
    
    def disconnect(self) -> bool:
        """Disconnect from Google Calendar"""
        try:
            # Remove token file
            if os.path.exists(self.token_file):
                os.remove(self.token_file)
            
            # Reset service state
            self.service = None
            self.enabled = False
            self.calendar_id = None
            
            logger.info("Disconnected from Google Calendar")
            return True
            
        except Exception as e:
            logger.error(f"Error disconnecting from Google Calendar: {e}")
            return False
    
    def test_connection(self) -> bool:
        """Test the Google Calendar connection"""
        try:
            if not self.service:
                return False
            
            # Try to list calendars as a connection test
            calendars_result = self.service.calendarList().list().execute()
            calendars = calendars_result.get('items', [])
            
            logger.info(f"Connection test successful, found {len(calendars)} calendars")
            return True
            
        except Exception as e:
            logger.error(f"Connection test failed: {e}")
            return False
    
    def is_authenticated(self) -> bool:
        """Check if service is authenticated"""
        return self.enabled and self.service is not None 