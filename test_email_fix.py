#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Email Fix
Quick test to verify the email fix works
"""

import json
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

def test_imports():
    """Test that all imports work correctly"""
    print("🔍 Testing imports...")
    
    try:
        from core.database import DatabaseManagerExtended
        print("  ✅ DatabaseManagerExtended import successful")
    except ImportError as e:
        print(f"  ❌ DatabaseManagerExtended import failed: {e}")
        return False
    
    try:
        from core.services.statistics_service import StatisticsService
        print("  ✅ StatisticsService import successful")
    except ImportError as e:
        print(f"  ❌ StatisticsService import failed: {e}")
        return False
    
    try:
        from core.config.app_config import AppConfig
        print("  ✅ AppConfig import successful")
    except ImportError as e:
        print(f"  ❌ AppConfig import failed: {e}")
        return False
    
    try:
        from core.services.email_service import EmailService
        print("  ✅ EmailService import successful")
    except ImportError as e:
        print(f"  ❌ EmailService import failed: {e}")
        return False
    
    return True

def test_service_initialization():
    """Test that services can be initialized"""
    print("\n🔧 Testing service initialization...")
    
    try:
        from core.database import DatabaseManagerExtended
        from core.services.statistics_service import StatisticsService
        from core.config.app_config import AppConfig
        from core.services.email_service import EmailService
        
        # Initialize config
        config = AppConfig()
        print(f"  ✅ AppConfig initialized")
        print(f"    Database path: {config.database_path}")
        
        # Initialize database manager
        db_manager = DatabaseManagerExtended(config.database_path)
        print(f"  ✅ DatabaseManagerExtended initialized")
        
        # Initialize statistics service
        stats_service = StatisticsService(db_manager, config)
        print(f"  ✅ StatisticsService initialized")
        
        # Check if email service exists
        if hasattr(stats_service, 'email_service'):
            print(f"  ✅ EmailService found in StatisticsService")
        else:
            print(f"  ❌ EmailService not found in StatisticsService")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ Service initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_email_configuration():
    """Test email configuration loading and updating"""
    print("\n📧 Testing email configuration...")
    
    try:
        # Load settings from JSON
        settings_file = Path('data/settings.json')
        if not settings_file.exists():
            print(f"  ❌ Settings file not found: {settings_file}")
            return False
        
        with open(settings_file, 'r', encoding='utf-8') as f:
            settings = json.load(f)
        
        email_settings = settings.get('email', {})
        print(f"  📧 Email settings loaded:")
        print(f"    Server: {email_settings.get('server', 'NOT SET')}")
        print(f"    Username: {email_settings.get('username', 'NOT SET')}")
        print(f"    Password: {'***' if email_settings.get('password') else 'NOT SET'}")
        
        # Test service initialization with email config update
        from core.database import DatabaseManagerExtended
        from core.services.statistics_service import StatisticsService
        from core.config.app_config import AppConfig
        
        config = AppConfig()
        db_manager = DatabaseManagerExtended(config.database_path)
        stats_service = StatisticsService(db_manager, config)
        
        # Update email service configuration
        stats_service.email_service.smtp_config.update({
            'smtp_server': email_settings.get('server', ''),
            'smtp_port': email_settings.get('port', 587),
            'smtp_username': email_settings.get('username', ''),
            'smtp_password': email_settings.get('password', ''),
            'smtp_use_tls': email_settings.get('use_tls', True),
            'from_name': email_settings.get('sender_name', 'Agevolami PM'),
            'from_email': email_settings.get('sender_email', ''),
            'enabled': bool(email_settings.get('server'))
        })
        
        print(f"  ✅ Email service configuration updated")
        print(f"    SMTP Server: {stats_service.email_service.smtp_config.get('smtp_server')}")
        print(f"    SMTP Enabled: {stats_service.email_service.smtp_config.get('enabled')}")
        
        # Test connection
        if stats_service.email_service.smtp_config.get('enabled'):
            print(f"  🔗 Testing SMTP connection...")
            try:
                connection_success = stats_service.email_service.test_connection()
                if connection_success:
                    print(f"    ✅ SMTP connection successful!")
                else:
                    print(f"    ❌ SMTP connection failed!")
            except Exception as e:
                print(f"    ❌ SMTP connection error: {e}")
        else:
            print(f"  ⚠️ SMTP not enabled, skipping connection test")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Email configuration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🚀 Email Fix Test")
    print("=" * 50)
    
    # Test imports
    if not test_imports():
        print("\n❌ Import tests failed!")
        return
    
    # Test service initialization
    if not test_service_initialization():
        print("\n❌ Service initialization tests failed!")
        return
    
    # Test email configuration
    if not test_email_configuration():
        print("\n❌ Email configuration tests failed!")
        return
    
    print("\n🎉 All tests passed!")
    print("The email fix should now work correctly.")
    print("\nNext steps:")
    print("1. Try the 'Test Email' button in Settings → Reports")
    print("2. Try sending a custom report")
    print("3. Check the recipient's email for messages")

if __name__ == "__main__":
    main()
