#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Essential Form Components for Settings System
Basic implementations to prevent import errors
"""

import flet as ft
from typing import Callable, Optional, List, Any

class FormField:
    """Form field factory class"""

    @staticmethod
    def create_text_field(label: str,
                         value: str = "",
                         hint_text: str = "",
                         password: bool = False,
                         required: bool = False,
                         validation_message: str = None,
                         on_change: Optional[Callable] = None,
                         width: Optional[int] = None,
                         expand: bool = False,
                         prefix_icon: str = None,
                         disabled: bool = False) -> ft.TextField:
        """Create a text field"""
        return ft.TextField(
            label=label + (" *" if required else ""),
            value=value,
            hint_text=hint_text,
            password=password,
            can_reveal_password=password,
            on_change=on_change,
            width=width,
            expand=expand,
            error_text=validation_message,
            prefix_icon=ft.Icon(prefix_icon) if prefix_icon else None,
            disabled=disabled
        )

    @staticmethod
    def create_number_field(label: str,
                           value: int = 0,
                           min_value: Optional[int] = None,
                           max_value: Optional[int] = None,
                           on_change: Optional[Callable] = None,
                           width: Optional[int] = None,
                           validation_message: str = None) -> ft.TextField:
        """Create a number field"""

        def handle_change(e):
            try:
                num_value = int(e.control.value) if e.control.value else 0
                if min_value is not None and num_value < min_value:
                    num_value = min_value
                elif max_value is not None and num_value > max_value:
                    num_value = max_value

                if on_change:
                    on_change(num_value)
            except ValueError:
                if on_change:
                    on_change(value)

        return ft.TextField(
            label=label,
            value=str(value),
            on_change=handle_change,
            width=width,
            input_filter=ft.NumbersOnlyInputFilter(),
            error_text=validation_message
        )

class SwitchField:
    """Switch field factory class"""

    @staticmethod
    def create(label: str,
               description: str = None,
               value: bool = False,
               on_change: Optional[Callable] = None,
               color: str = None,
               disabled: bool = False) -> ft.Container:
        """Create a switch field with optional description"""

        switch_content = [
            ft.Switch(
                label=label,
                value=value,
                on_change=on_change,
                active_color=color,
                disabled=disabled
            )
        ]

        if description:
            switch_content.append(
                ft.Text(
                    description,
                    size=11,
                    color=ft.Colors.GREY_600
                )
            )

        return ft.Container(
            content=ft.Column(switch_content, spacing=4),
            padding=ft.padding.symmetric(vertical=4)
        )

class DropdownField:
    """Dropdown field factory class"""

    @staticmethod
    def create(label: str,
               options: List,
               value: str = "",
               on_change: Optional[Callable] = None,
               width: Optional[int] = None,
               disabled: bool = False) -> ft.Dropdown:
        """Create a dropdown field"""
        dropdown_options = []
        for option in options:
            if isinstance(option, dict):
                dropdown_options.append(ft.dropdown.Option(
                    text=option.get('text', option.get('value', '')),
                    key=option.get('value', option.get('key', option.get('text', '')))
                ))
            else:
                dropdown_options.append(ft.dropdown.Option(text=str(option), key=str(option)))

        return ft.Dropdown(
            label=label,
            options=dropdown_options,
            value=value,
            on_change=on_change,
            width=width,
            disabled=disabled
        )

def DropdownFieldSimple(label: str,
                        options: List[str],
                        value: str = "",
                        on_change: Optional[Callable] = None,
                        width: Optional[int] = None) -> ft.Dropdown:
    """Create a simple dropdown field (legacy function)"""
    dropdown_options = []
    for option in options:
        if isinstance(option, dict):
            dropdown_options.append(ft.dropdown.Option(
                text=option.get('text', option.get('key', '')),
                key=option.get('key', option.get('text', ''))
            ))
        else:
            dropdown_options.append(ft.dropdown.Option(text=str(option), key=str(option)))

    return ft.Dropdown(
        label=label,
        options=dropdown_options,
        value=value,
        on_change=on_change,
        width=width
    )

class ButtonGroup:
    """Button group factory class"""

    @staticmethod
    def create_action_group(actions: List[dict], spacing: int = 8) -> ft.Row:
        """Create an action button group"""
        buttons = []

        for action in actions:
            style = action.get("style", "primary")

            if style == "primary":
                button = ft.ElevatedButton(
                    text=action["text"],
                    icon=action.get("icon"),
                    on_click=action.get("on_click"),
                    bgcolor=ft.Colors.BLUE_600,
                    color=ft.Colors.WHITE
                )
            else:
                button = ft.OutlinedButton(
                    text=action["text"],
                    icon=action.get("icon"),
                    on_click=action.get("on_click")
                )

            buttons.append(button)

        return ft.Row(buttons, spacing=spacing)

    @staticmethod
    def create_primary_secondary(primary_action: dict, 
                               secondary_action: dict = None, 
                               spacing: int = 8) -> ft.Row:
        """Create a primary-secondary button group"""
        buttons = []
        
        # Primary button
        primary_button = ft.ElevatedButton(
            text=primary_action["text"],
            icon=primary_action.get("icon"),
            on_click=primary_action.get("on_click"),
            bgcolor=ft.Colors.BLUE_600,
            color=ft.Colors.WHITE
        )
        buttons.append(primary_button)
        
        # Secondary button (if provided)
        if secondary_action:
            secondary_button = ft.OutlinedButton(
                text=secondary_action["text"],
                icon=secondary_action.get("icon"),
                on_click=secondary_action.get("on_click")
            )
            buttons.append(secondary_button)
        
        return ft.Row(buttons, spacing=spacing)

class InfoBox:
    """Info box factory class"""

    @staticmethod
    def create(title: str,
               message: str,
               type: str = "info",
               items: List[str] = None) -> ft.Container:
        """Create an info box"""

        colors = {
            "info": ft.Colors.BLUE_600,
            "success": ft.Colors.GREEN_600,
            "warning": ft.Colors.ORANGE_600,
            "error": ft.Colors.RED_600
        }

        icons = {
            "info": ft.Icons.INFO,
            "success": ft.Icons.CHECK_CIRCLE,
            "warning": ft.Icons.WARNING,
            "error": ft.Icons.ERROR
        }

        color = colors.get(type, ft.Colors.BLUE_600)
        icon = icons.get(type, ft.Icons.INFO)

        content = [
            ft.Row([
                ft.Icon(icon, color=color, size=20),
                ft.Text(title, weight=ft.FontWeight.BOLD, color=color)
            ], spacing=8),
            ft.Text(message, size=12, color=ft.Colors.GREY_700)
        ]

        if items:
            for item in items:
                content.append(
                    ft.Row([
                        ft.Text("•", color=color, size=12),
                        ft.Text(item, size=11, color=ft.Colors.GREY_600)
                    ], spacing=8)
                )

        return ft.Container(
            content=ft.Column(content, spacing=4),
            padding=ft.padding.all(12),
            bgcolor=ft.Colors.with_opacity(0.1, color),
            border_radius=8,
            border=ft.border.all(1, ft.Colors.with_opacity(0.3, color))
        )

class StatusIndicator:
    """Status indicator factory class"""

    @staticmethod
    def create(label: str,
               status: str,
               description: str = "",
               size: int = 16) -> ft.Container:
        """Create a status indicator"""

        colors = {
            "connected": ft.Colors.GREEN_600,
            "disconnected": ft.Colors.GREY_500,
            "error": ft.Colors.RED_600,
            "loading": ft.Colors.BLUE_600
        }

        icons = {
            "connected": ft.Icons.CHECK_CIRCLE,
            "disconnected": ft.Icons.RADIO_BUTTON_UNCHECKED,
            "error": ft.Icons.ERROR,
            "loading": ft.Icons.REFRESH
        }

        color = colors.get(status, ft.Colors.GREY_500)
        icon = icons.get(status, ft.Icons.HELP)

        content = [
            ft.Row([
                ft.Icon(icon, color=color, size=size),
                ft.Text(label, size=12, weight=ft.FontWeight.W_500)
            ], spacing=8)
        ]

        if description:
            content.append(
                ft.Text(description, size=10, color=ft.Colors.GREY_600)
            )

        return ft.Container(
            content=ft.Column(content, spacing=4),
            padding=ft.padding.symmetric(vertical=4)
        )

class CheckboxGroup:
    """Checkbox group factory class"""

    @staticmethod
    def create(title: str = None,
               description: str = None,
               options: List[dict] = None) -> ft.Container:
        """Create a checkbox group with title and description"""

        content = []

        # Title
        if title:
            content.append(
                ft.Text(
                    title,
                    size=14,
                    weight=ft.FontWeight.W_600,
                    color=ft.Colors.GREY_800
                )
            )

        # Description
        if description:
            content.append(
                ft.Text(
                    description,
                    size=11,
                    color=ft.Colors.GREY_600
                )
            )

        # Checkboxes
        if options:
            checkboxes = []
            for option in options:
                checkbox = ft.Checkbox(
                    label=option.get("label", ""),
                    value=option.get("checked", False),
                    on_change=option.get("on_change")
                )
                checkboxes.append(checkbox)

            content.append(ft.Column(checkboxes, spacing=4))

        return ft.Container(
            content=ft.Column(content, spacing=8),
            padding=ft.padding.symmetric(vertical=8)
        )

def CheckboxGroupSimple(options: List[str],
                        selected: List[str] = None,
                        on_change: Optional[Callable] = None) -> ft.Column:
    """Create a simple checkbox group (legacy function)"""

    if selected is None:
        selected = []

    checkboxes = []
    for option in options:
        checkbox = ft.Checkbox(
            label=option,
            value=option in selected,
            on_change=on_change
        )
        checkboxes.append(checkbox)

    return ft.Column(checkboxes, spacing=4)

def NumberField(label: str,
                value: int = 0,
                min_value: Optional[int] = None,
                max_value: Optional[int] = None,
                on_change: Optional[Callable] = None,
                width: Optional[int] = None) -> ft.TextField:
    """Create a number input field"""
    
    def handle_change(e):
        # Validate numeric input
        try:
            num_value = int(e.control.value) if e.control.value else 0
            if min_value is not None and num_value < min_value:
                e.control.value = str(min_value)
                num_value = min_value
            elif max_value is not None and num_value > max_value:
                e.control.value = str(max_value)
                num_value = max_value
            
            if on_change:
                on_change(e)
        except ValueError:
            # Reset to previous valid value
            e.control.value = str(value)
        
        e.control.update()
    
    return ft.TextField(
        label=label,
        value=str(value),
        on_change=handle_change,
        width=width,
        input_filter=ft.NumbersOnlyInputFilter()
    )

def TextArea(label: str,
             value: str = "",
             hint_text: str = "",
             max_lines: int = 3,
             on_change: Optional[Callable] = None) -> ft.TextField:
    """Create a text area field"""
    return ft.TextField(
        label=label,
        value=value,
        hint_text=hint_text,
        multiline=True,
        max_lines=max_lines,
        on_change=on_change,
        expand=True
    )

class FormComponent:
    """Base class for form components - simple wrapper"""
    
    def __init__(self, control: ft.Control):
        self.control = control
    
    def build(self) -> ft.Control:
        return self.control 