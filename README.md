# Agevolami PM - Sistema di Gestione Progetti e Scadenze

## Descrizione
Applicazione desktop Windows per la gestione di progetti, scadenze fiscali e incentivi finanziari per lo studio di consulenza Agevolami.it.

## Caratteristiche Principali

### 🎯 Funzionalità Core
- **Gestione Clienti**: Portafoglio clienti con informazioni complete
- **Gestione Progetti**: Progetti per incentivi come Accordi Innovazione, Investimenti Sostenibili 4.0
- **Gestione Scadenze**: Monitoraggio scadenze fiscali e progettuali
- **Sistema SAL**: Gestione Stati Avanzamento Lavori per progetti complessi
- **Sistema Alert**: Notifiche per scadenze imminenti (15 giorni)
- **Email SMTP**: Invio notifiche via email

### 📋 Progetti Supportati
- **Accordi per l'Innovazione**: Progetti R&S con durata max 36 mesi
- **Investimenti Sostenibili 4.0**: Incentivi per PMI del Mezzogiorno
- **Contratti di Sviluppo**: Programmi investimento grandi dimensioni
- **Mini Contratti di Sviluppo**: Investimenti 5-20 milioni euro
- **Scadenze Fiscali**: Gestione adempimenti tributari

### 🏗️ Architettura Feature-Based
```
src/
├── core/                    # Componenti condivisi
│   ├── database/           # Gestione database SQLite
│   ├── models/             # Modelli dati
│   ├── utils/              # Utilità comuni
│   └── config/             # Configurazioni
├── features/               # Funzionalità modulari
│   ├── clients/            # Gestione clienti
│   ├── projects/           # Gestione progetti
│   ├── deadlines/          # Gestione scadenze
│   ├── sal/                # Stati Avanzamento Lavori
│   ├── alerts/             # Sistema notifiche
│   ├── email/              # Gestione SMTP
│   └── dashboard/          # Dashboard principale
├── shared/                 # Componenti UI condivisi
│   ├── components/         # Widget riutilizzabili
│   ├── layouts/            # Layout comuni
│   └── styles/             # Stili e temi
└── main.py                 # Entry point applicazione
```

### 🎨 Design
- **Interfaccia Italiana**: Completamente localizzata
- **Design Professionale**: UI pulita e moderna
- **Tema Business**: Colori e layout adatti ambiente aziendale
- **Responsive**: Adattabile a diverse risoluzioni

### ⚡ Tecnologie
- **Framework**: Flet (Python + Flutter)
- **Database**: SQLite per persistenza locale
- **Email**: SMTP per notifiche
- **Packaging**: Flet build per distribuzione Windows

### 🚨 Sistema Alert
- **Monitoraggio Continuo**: Controllo scadenze ogni avvio
- **Alert 15 giorni**: Notifiche per scadenze imminenti
- **Persistenza Alert**: Gli alert rimangono fino a marcatura "Completato"
- **Multi-canale**: Notifiche in-app ed email

### 📊 Gestione SAL
Per progetti complessi suddivisi in Stati Avanzamento Lavori:
- Creazione SAL multipli per progetto
- Tracking avanzamento percentuale
- Scadenze specifiche per ogni SAL
- Documentazione allegata

### 🔧 Installazione e Uso

#### Requisiti
- Windows 10/11
- Python 3.9+
- Flet framework

#### Setup Sviluppo
```bash
# Clona repository
git clone <repository-url>
cd agevolami_pm_v2

# Installa dipendenze
pip install -r requirements.txt

# Avvia applicazione
flet run
```

#### Build Produzione
```bash
# Crea eseguibile Windows
flet build windows
```

### 📁 Struttura Database
- **clients**: Anagrafica clienti
- **projects**: Progetti e incentivi
- **deadlines**: Scadenze e adempimenti
- **sal**: Stati Avanzamento Lavori
- **alerts**: Sistema notifiche
- **documents**: Gestione documenti allegati

### 🎯 Roadmap
- [x] Architettura base feature-based
- [ ] Modulo gestione clienti
- [ ] Modulo gestione progetti
- [ ] Sistema scadenze e alert
- [ ] Integrazione SMTP
- [ ] Dashboard analytics
- [ ] Sistema backup
- [ ] Documentazione utente

### 📞 Supporto
Per supporto tecnico contattare il team di sviluppo.

---
*Sviluppato per Agevolami.it - Studio di Consulenza e Contabilità*