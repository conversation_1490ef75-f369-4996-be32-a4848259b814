#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for custom report with detailed information
"""

from src.core.services.statistics_service import StatisticsService
from src.core.database.database_manager import DatabaseManager
from src.core.config.app_config import AppConfig
from src.core.services.email_service import EmailService
import json

def test_custom_report():
    """Test custom report generation with detailed information"""
    try:
        # Initialize services
        config = AppConfig()
        db = DatabaseManager(config)
        email_service = EmailService(config)
        stats_service = StatisticsService(db, email_service)

        # Test custom report generation with detailed info
        filters = {
            'include_clients': True,
            'include_projects': True,
            'include_tasks': True,
            'include_deadlines': True,
            'days_ahead': 15
        }

        print('🔄 Generating custom report with detailed information...')
        report = stats_service.generate_custom_report(filters)

        if report:
            print('✅ Report generated successfully!')
            print(f'📊 Summary: {report.get("summary", {})}')
            
            # Check if detailed deadlines are included
            upcoming_deadlines = report.get('upcoming_deadlines_detailed', [])
            print(f'📅 Detailed upcoming deadlines: {len(upcoming_deadlines)}')
            
            # Check if detailed tasks are included
            tasks_detailed = report.get('tasks_detailed', {})
            print(f'📋 Detailed tasks info: {tasks_detailed}')
            
            if upcoming_deadlines:
                print('📅 First deadline details:')
                first_deadline = upcoming_deadlines[0]
                print(f'   Title: {first_deadline.get("title")}')
                print(f'   Days remaining: {first_deadline.get("days_remaining")}')
                print(f'   Project: {first_deadline.get("project_info", {}).get("name")}')
                print(f'   Client: {first_deadline.get("client_info", {}).get("name")}')
                print(f'   Related tasks: {len(first_deadline.get("related_tasks", []))}')
            
            # Test HTML generation
            from src.core.services.email_templates import EmailTemplates
            html = EmailTemplates.generate_custom_report_html(report)
            
            if 'Scadenze Dettagliate' in html:
                print('✅ HTML template includes detailed deadlines section')
            else:
                print('❌ HTML template missing detailed deadlines section')
                
            if 'Task in Scadenza' in html:
                print('✅ HTML template includes expiring tasks section')
            else:
                print('❌ HTML template missing expiring tasks section')
                
        else:
            print('❌ Failed to generate report')
            
    except Exception as e:
        print(f'❌ Error: {e}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_custom_report()
