#!/usr/bin/env python3
"""
Test script to verify the build process locally before pushing to GitHub
"""

import subprocess
import sys
from pathlib import Path

def run_command(command, check=True):
    """Run a shell command and return success status"""
    print(f"Running: {command}")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.stdout:
            print("STDOUT:", result.stdout.strip())
        if result.stderr:
            print("STDERR:", result.stderr.strip())
            
        if check and result.returncode != 0:
            print(f"❌ Command failed with exit code {result.returncode}")
            return False
        
        print("✅ Command completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error running command: {e}")
        return False

def test_dependencies():
    """Test that all dependencies can be installed"""
    print("\n🔍 Testing dependency installation...")
    
    commands = [
        "python -m pip install --upgrade pip",
        "pip install flet",
        "pip install -r requirements.txt"
    ]
    
    # Check if requirements-google.txt exists
    if Path("requirements-google.txt").exists():
        commands.append("pip install -r requirements-google.txt")
    
    for cmd in commands:
        if not run_command(cmd):
            return False
    
    return True

def test_flet_installation():
    """Test that Flet is properly installed"""
    print("\n🔍 Testing Flet installation...")
    
    commands = [
        "flet --version",
        "python -c \"import flet; print('Flet imported successfully')\""
    ]
    
    for cmd in commands:
        if not run_command(cmd):
            return False
    
    return True

def test_project_structure():
    """Verify project structure is correct for Flet build"""
    print("\n🔍 Testing project structure...")
    
    required_files = [
        "pyproject.toml",
        "src/main.py"
    ]
    
    for file_path in required_files:
        if not Path(file_path).exists():
            print(f"❌ Required file missing: {file_path}")
            return False
        else:
            print(f"✅ Found: {file_path}")
    
    return True

def test_build():
    """Test the actual build process"""
    print("\n🔍 Testing Flet build...")
    
    # Try to build (this might take a while)
    print("⚠️  This may take several minutes...")
    return run_command("flet build windows --verbose")

def main():
    """Main test function"""
    print("🧪 Testing Agevolami PM Build Process")
    print("=" * 50)
    
    tests = [
        ("Project Structure", test_project_structure),
        ("Dependencies", test_dependencies),
        ("Flet Installation", test_flet_installation),
        ("Build Process", test_build)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 Running test: {test_name}")
        print("-" * 30)
        
        try:
            success = test_func()
            results.append((test_name, success))
            
            if success:
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
                
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{test_name:20} {status}")
        if success:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Ready for GitHub Actions.")
        return 0
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Fix issues before pushing.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
