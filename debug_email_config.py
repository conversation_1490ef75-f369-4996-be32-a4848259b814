#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug Email Configuration
Quick script to test email configuration and sending
"""

import json
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from core.config.app_config import AppConfig
from core.services.email_service import EmailService
from core.utils import get_logger

logger = get_logger(__name__)

def load_settings_from_json():
    """Load settings from JSON file"""
    settings_file = Path('data/settings.json')
    if settings_file.exists():
        with open(settings_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    return {}

def test_email_configuration():
    """Test email configuration and sending"""
    print("🔍 Testing Email Configuration...")
    
    # Load settings from JSON
    settings = load_settings_from_json()
    email_settings = settings.get('email', {})
    reports_settings = settings.get('reports', {})
    
    print(f"\n📧 Email Settings from JSON:")
    print(f"  Server: {email_settings.get('server', 'NOT SET')}")
    print(f"  Port: {email_settings.get('port', 'NOT SET')}")
    print(f"  Username: {email_settings.get('username', 'NOT SET')}")
    print(f"  Password: {'***' if email_settings.get('password') else 'NOT SET'}")
    print(f"  Use TLS: {email_settings.get('use_tls', 'NOT SET')}")
    print(f"  Sender Name: {email_settings.get('sender_name', 'NOT SET')}")
    print(f"  Sender Email: {email_settings.get('sender_email', 'NOT SET')}")
    
    print(f"\n📊 Report Recipients:")
    recipients = reports_settings.get('recipients', [])
    for i, recipient in enumerate(recipients, 1):
        print(f"  {i}. {recipient}")
    
    if not recipients:
        print("  ❌ No recipients configured!")
        return False
    
    # Test with AppConfig (environment variables)
    print(f"\n🔧 Testing AppConfig (Environment Variables):")
    config = AppConfig()
    print(f"  SMTP Server: {config.email_config.get('smtp_server', 'NOT SET')}")
    print(f"  SMTP Username: {config.email_config.get('smtp_username', 'NOT SET')}")
    print(f"  Email Configured: {config.is_email_configured()}")
    
    # Create email service with AppConfig
    email_service = EmailService(config)
    
    # Update with JSON settings
    print(f"\n🔄 Updating EmailService with JSON settings...")
    email_service.smtp_config.update({
        'smtp_server': email_settings.get('server', ''),
        'smtp_port': email_settings.get('port', 587),
        'smtp_username': email_settings.get('username', ''),
        'smtp_password': email_settings.get('password', ''),
        'smtp_use_tls': email_settings.get('use_tls', True),
        'from_name': email_settings.get('sender_name', 'Agevolami PM'),
        'from_email': email_settings.get('sender_email', ''),
        'enabled': bool(email_settings.get('server'))
    })
    
    print(f"  Updated SMTP Config:")
    print(f"    Server: {email_service.smtp_config.get('smtp_server')}")
    print(f"    Port: {email_service.smtp_config.get('smtp_port')}")
    print(f"    Username: {email_service.smtp_config.get('smtp_username')}")
    print(f"    Enabled: {email_service.smtp_config.get('enabled')}")
    
    # Test connection
    print(f"\n🔗 Testing SMTP Connection...")
    try:
        connection_success = email_service.test_connection()
        if connection_success:
            print("  ✅ SMTP connection successful!")
        else:
            print("  ❌ SMTP connection failed!")
            return False
    except Exception as e:
        print(f"  ❌ SMTP connection error: {e}")
        return False
    
    # Send test email
    test_recipient = recipients[0]
    print(f"\n📧 Sending test email to: {test_recipient}")
    try:
        email_success = email_service.send_test_email(test_recipient)
        if email_success:
            print("  ✅ Test email sent successfully!")
            print(f"  📬 Check {test_recipient} for the test email")
            return True
        else:
            print("  ❌ Test email sending failed!")
            return False
    except Exception as e:
        print(f"  ❌ Test email error: {e}")
        return False

def test_custom_report():
    """Test custom report generation and sending"""
    print(f"\n📊 Testing Custom Report Generation...")
    
    try:
        from core.database import DatabaseManagerExtended
        from core.services.statistics_service import StatisticsService
        
        # Load settings
        settings = load_settings_from_json()
        email_settings = settings.get('email', {})
        reports_settings = settings.get('reports', {})
        recipients = reports_settings.get('recipients', [])
        
        if not recipients:
            print("  ❌ No recipients configured for reports!")
            return False
        
        # Initialize services
        config = AppConfig()
        db_manager = DatabaseManagerExtended()
        stats_service = StatisticsService(db_manager, config)
        
        # Update email service configuration
        stats_service.email_service.smtp_config.update({
            'smtp_server': email_settings.get('server', ''),
            'smtp_port': email_settings.get('port', 587),
            'smtp_username': email_settings.get('username', ''),
            'smtp_password': email_settings.get('password', ''),
            'smtp_use_tls': email_settings.get('use_tls', True),
            'from_name': email_settings.get('sender_name', 'Agevolami PM'),
            'from_email': email_settings.get('sender_email', ''),
            'enabled': bool(email_settings.get('server'))
        })
        
        # Create filters
        content_types = reports_settings.get('content_types', {})
        filters = {
            'include_clients': content_types.get('client_summary', True),
            'include_projects': content_types.get('project_completion', True),
            'include_tasks': content_types.get('incomplete_tasks', True),
            'include_deadlines': content_types.get('upcoming_deadlines', True),
            'include_expired_only': False,
            'days_ahead': reports_settings.get('days_ahead_filter', 7)
        }
        
        print(f"  📋 Report filters: {filters}")
        print(f"  📧 Recipients: {recipients}")
        
        # Send custom report
        success = stats_service.send_custom_report_email(recipients, filters)
        
        if success:
            print("  ✅ Custom report sent successfully!")
            print(f"  📬 Check {', '.join(recipients)} for the custom report")
            return True
        else:
            print("  ❌ Custom report sending failed!")
            return False
            
    except Exception as e:
        print(f"  ❌ Custom report error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Email Configuration Debug Tool")
    print("=" * 50)
    
    # Test basic email configuration
    email_test_success = test_email_configuration()
    
    if email_test_success:
        print(f"\n" + "=" * 50)
        # Test custom report if basic email works
        report_test_success = test_custom_report()
        
        if report_test_success:
            print(f"\n🎉 All tests passed! Email system is working correctly.")
        else:
            print(f"\n⚠️ Basic email works, but custom reports have issues.")
    else:
        print(f"\n❌ Basic email configuration has issues. Fix email settings first.")
    
    print(f"\n" + "=" * 50)
    print("Debug complete.")
