#!/usr/bin/env python3
"""
Test script to verify web scraping fixes
"""

import sys
import os
import json
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from core.services.enhanced_web_scraping_service import EnhancedWebScrapingService
from core.models.incentive_models import IncentiveSource

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(name)s:%(funcName)s:%(lineno)d | %(message)s'
)

def load_config():
    """Load incentives configuration"""
    config_path = Path("data/incentives_config.json")
    if not config_path.exists():
        print(f"Configuration file not found: {config_path}")
        return None

    with open(config_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def test_website_health():
    """Test website health checking"""
    print("\n=== Testing Website Health ===")

    config = load_config()
    if not config:
        return

    scraper = EnhancedWebScrapingService(config)

    # Test each configured website
    for website in config.get('websites', []):
        if not website.get('enabled', True):
            continue

        url = website['url']
        print(f"\nTesting {website['name']}: {url}")

        health = scraper.check_website_health(url)
        status = "✓ Healthy" if health.is_healthy else "✗ Unhealthy"
        print(f"  Status: {status}")
        print(f"  Response time: {health.response_time:.2f}s")
        print(f"  Status code: {health.status_code}")
        if health.error_message:
            print(f"  Error: {health.error_message}")
        if health.consecutive_failures > 0:
            print(f"  Consecutive failures: {health.consecutive_failures}")

def test_selector_validation():
    """Test selector validation"""
    print("\n=== Testing Selector Validation ===")

    config = load_config()
    if not config:
        return

    scraper = EnhancedWebScrapingService(config)

    # Test MIMIT selectors
    mimit_url = "https://www.mimit.gov.it/it/per-l-impresa"
    test_selectors = [
        'article.card', 'div.card', '.card-body',
        'li:has(a)', 'h2', 'h3', 'a[href*="/it/"]'
    ]

    print(f"\nTesting selectors on {mimit_url}")
    validations = scraper.validate_selectors(mimit_url, test_selectors)

    for validation in validations:
        status = "✓" if validation.is_valid else "✗"
        print(f"  {status} '{validation.selector}': {validation.elements_found} elements")
        if validation.error_message:
            print(f"    Error: {validation.error_message}")

def test_content_discovery():
    """Test automatic content discovery"""
    print("\n=== Testing Content Discovery ===")

    config = load_config()
    if not config:
        return

    scraper = EnhancedWebScrapingService(config)

    # Test on a working website
    test_url = "https://www.invitalia.it"
    print(f"\nTesting content discovery on {test_url}")

    soup = scraper.scrape_with_retry(test_url)
    if soup:
        discovered = scraper._discover_content_selectors(soup)
        print(f"Discovered {len(discovered)} potential selectors:")
        for selector in discovered[:5]:  # Show top 5
            print(f"  - {selector}")
    else:
        print("  Failed to scrape page for discovery")

def test_scraping_sample():
    """Test actual scraping with a small sample"""
    print("\n=== Testing Sample Scraping ===")

    config = load_config()
    if not config:
        return

    # Limit to small sample for testing
    test_config = config.copy()
    test_config['keywords'] = ['incentivi', 'finanziamenti']  # Limit keywords

    scraper = EnhancedWebScrapingService(test_config)

    print("Testing MIMIT scraping...")
    items = scraper._scrape_mimit_enhanced(['incentivi', 'finanziamenti'])
    print(f"Found {len(items)} items from MIMIT")

    if items:
        print("Sample item:")
        item = items[0]
        print(f"  Title: {item.title[:100]}...")
        print(f"  Source: {item.source}")
        print(f"  URL: {item.source_url}")

def main():
    """Run all tests"""
    print("Starting Web Scraping Fixes Test")
    print("=" * 50)

    try:
        test_website_health()
        test_selector_validation()
        test_content_discovery()
        test_scraping_sample()

        print("\n" + "=" * 50)
        print("Test completed successfully!")

    except Exception as e:
        print(f"\nTest failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
    
    # Create scraper config
    scraper_config = {
        'sources': [IncentiveSource.MIMIT],
        'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'request_delay_seconds': 1.0,
        'timeout_seconds': 15,
        'max_items_per_session': 50
    }
    
    # Initialize scraper
    scraper = WebScrapingService(scraper_config)
    
    # Sample HTML from MIMIT website structure
    sample_html = """
    <div>
        <h2><a href="/it/incentivi/piano-transizione-5-0">Piano Transizione 5.0</a></h2>
        <p>Investimenti per il sostegno alla trasformazione digitale ed energetica delle imprese</p>
    </div>
    <div>
        <h3><a href="/it/notizie-stampa/ddl-concorrenza-urso">Ddl Concorrenza: Urso, "più efficienza per servizi pubblici"</a></h3>
        <p>Il Consiglio dei ministri ha approvato il nuovo Disegno di legge annuale per il mercato e la concorrenza</p>
    </div>
    <div>
        <a href="/it/incentivi/autoproduzione-di-energia-da-fonti-rinnovabili-nelle-pmi-nuovo-sportello">
            Autoproduzione di energia da fonti rinnovabili
        </a>
        <p>Agevolazioni per impianti fotovoltaici e minieolici nelle piccole e medie imprese</p>
    </div>
    """
    
    soup = BeautifulSoup(sample_html, 'html.parser')
    keywords = ['incentivi', 'finanziamenti', 'agevolazioni', 'transizione', 'energia']
    
    print("Testing item extraction with sample HTML...")
    
    # Test extraction from div elements
    divs = soup.find_all('div')
    items_found = 0
    
    for div in divs:
        item = scraper._extract_item_from_element(div, IncentiveSource.MIMIT, "https://www.mimit.gov.it", keywords)
        if item:
            items_found += 1
            print(f"\nExtracted item {items_found}:")
            print(f"  Title: {item.title}")
            print(f"  Description: {item.description[:100]}...")
            print(f"  URL: {item.source_url}")
            print(f"  Keywords matched: {item.keywords_matched}")
    
    print(f"\nTotal items extracted: {items_found}")
    
    # Test with links directly
    print("\nTesting extraction from direct links...")
    links = soup.find_all('a')
    link_items_found = 0
    
    for link in links:
        item = scraper._extract_item_from_element(link, IncentiveSource.MIMIT, "https://www.mimit.gov.it", keywords)
        if item:
            link_items_found += 1
            print(f"\nExtracted link item {link_items_found}:")
            print(f"  Title: {item.title}")
            print(f"  URL: {item.source_url}")
            print(f"  Keywords matched: {item.keywords_matched}")
    
    print(f"\nTotal link items extracted: {link_items_found}")
    
    # Test connection test method (this might fail due to network)
    print("\nTesting connection methods...")
    try:
        result = scraper.test_connection(IncentiveSource.MIMIT)
        print(f"MIMIT connection test: {'SUCCESS' if result else 'FAILED'}")
    except Exception as e:
        print(f"MIMIT connection test: ERROR - {e}")
    
    # Clean up
    scraper.close()
    print("\nTest completed!")

if __name__ == "__main__":
    test_extraction_logic()
