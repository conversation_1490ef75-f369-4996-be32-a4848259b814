#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Modern Search Component using Flet's Native SearchBar
Designed for Flet 28.x+ with proper overlay and visibility handling
"""

import flet as ft
from typing import List, Dict, Any, Optional, Callable
import threading
import time
from core import get_logger

logger = get_logger(__name__)

class ModernSearchComponent:
    """
    Modern search component using Flet's native SearchBar control
    Provides proper overlay behavior and Material Design styling
    """
    
    def __init__(self, 
                 app_instance=None,
                 on_result_selected: Optional[Callable[[Dict[str, Any]], None]] = None,
                 placeholder: str = "🔍 Cerca clienti, progetti, scadenze..."):
        self.app = app_instance
        self.on_result_selected = on_result_selected
        self.placeholder = placeholder
        
        # Search state
        self.current_query = ""
        self.search_results: List[Dict[str, Any]] = []
        self.search_timer = None
        self.is_searching = False
        
        # UI Components
        self.search_bar: Optional[ft.SearchBar] = None
        self.result_tiles: List[ft.ListTile] = []
        
        # Initialize search engine
        try:
            from ui.components.header import ProfessionalSearchEngine
            self.search_engine = ProfessionalSearchEngine(app_instance) if app_instance else None
        except Exception as e:
            logger.error(f"Search engine initialization failed: {e}")
            self.search_engine = None
        
        self._init_search_bar()
    
    def _init_search_bar(self):
        """Initialize the native SearchBar control"""
        self.search_bar = ft.SearchBar(
            # Bar appearance when closed
            bar_hint_text=self.placeholder,
            bar_leading=ft.Icon(ft.Icons.SEARCH, color=ft.Colors.BLUE_600),
            bar_trailing=[
                ft.Icon(ft.Icons.MIC, color=ft.Colors.GREY_500),
                ft.IconButton(
                    icon=ft.Icons.CLOSE,
                    icon_color=ft.Colors.GREY_500,
                    on_click=self._clear_search,
                    tooltip="Cancella ricerca"
                )
            ],
            
            # Search view appearance when opened
            view_hint_text="Digita per cercare...",
            view_leading=ft.IconButton(
                icon=ft.Icons.ARROW_BACK,
                icon_color=ft.Colors.BLUE_600,
                tooltip="Chiudi ricerca"
            ),
            view_trailing=[
                ft.IconButton(
                    icon=ft.Icons.CLEAR,
                    icon_color=ft.Colors.GREY_500,
                    on_click=self._clear_search,
                    tooltip="Cancella tutto"
                )
            ],
            
            # Styling
            bar_bgcolor={
                ft.ControlState.DEFAULT: ft.Colors.GREY_50,
                ft.ControlState.FOCUSED: ft.Colors.WHITE,
                ft.ControlState.HOVERED: ft.Colors.GREY_100,
            },
            view_bgcolor=ft.Colors.WHITE,
            view_elevation=8,
            divider_color=ft.Colors.GREY_300,
            
            # Behavior
            full_screen=False,  # Keep compact for better UX
            autofocus=False,
            
            # Events
            on_change=self._handle_search_change,
            on_submit=self._handle_search_submit,
            on_tap=self._handle_search_tap,
            
            # Search results will be populated dynamically
            controls=[]
        )
    
    def _handle_search_tap(self, e):
        """Handle search bar tap - open the search view"""
        if self.search_bar:
            self.search_bar.open_view()
            logger.info("Search view opened")
    
    def _handle_search_change(self, e):
        """Handle search input changes with debouncing"""
        try:
            # Cancel previous timer
            if self.search_timer:
                self.search_timer.cancel()
            
            query = e.control.value.strip() if e.control and e.control.value else ""
            self.current_query = query
            
            logger.info(f"Search query changed: '{query}' (length: {len(query)})")
            
            if len(query) >= 2:
                # Show loading state immediately
                self._show_loading_state()
                
                # Debounce search to avoid excessive calls
                self.search_timer = threading.Timer(0.5, lambda: self._perform_search(query))
                self.search_timer.start()
            elif len(query) == 0:
                # Show popular searches when empty
                self._show_popular_searches()
            else:
                # Clear results for queries too short
                self._clear_results()
                
        except Exception as e:
            logger.error(f"Search change error: {e}")
            self._show_error_state()
    
    def _handle_search_submit(self, e):
        """Handle search submission (Enter key)"""
        query = e.control.value.strip() if e.control and e.control.value else ""
        if query and self.search_results:
            # Select first result
            first_result = self.search_results[0]
            self._handle_result_selection(first_result)
        logger.info(f"Search submitted: '{query}'")
    
    def _perform_search(self, query: str):
        """Perform the actual search operation"""
        if not self.search_engine:
            logger.error("Search engine not available")
            self._show_error_state()
            return
        
        try:
            logger.info(f"Performing search for: '{query}'")
            self.is_searching = True
            
            # Perform search using the existing search engine
            results = self.search_engine.search(query, max_results=8)
            
            self.search_results = results
            self.is_searching = False
            
            if results:
                self._update_search_results(results)
            else:
                self._show_no_results_state()
            
            logger.info(f"Search completed: {len(results)} results")
            
        except Exception as e:
            logger.error(f"Search error: {e}")
            self.is_searching = False
            self._show_error_state()
    
    def _update_search_results(self, results: List[Dict[str, Any]]):
        """Update search results in the SearchBar"""
        if not self.search_bar:
            return
        
        # Clear existing controls
        self.search_bar.controls.clear()
        self.result_tiles.clear()
        
        # Create result tiles
        for i, result in enumerate(results):
            tile = self._create_result_tile(result, i)
            self.result_tiles.append(tile)
            self.search_bar.controls.append(tile)
        
        # Update the SearchBar
        if self.app and hasattr(self.app, 'page'):
            self.app.page.update()
    
    def _create_result_tile(self, result: Dict[str, Any], index: int) -> ft.ListTile:
        """Create a modern result tile"""
        # Determine icon and colors based on result type
        icon_map = {
            'client': (ft.Icons.PERSON, ft.Colors.BLUE_600),
            'project': (ft.Icons.WORK, ft.Colors.GREEN_600),
            'deadline': (ft.Icons.SCHEDULE, ft.Colors.ORANGE_600),
            'task': (ft.Icons.TASK_ALT, ft.Colors.PURPLE_600),
        }
        
        result_type = result.get('type', 'other')
        icon, color = icon_map.get(result_type, (ft.Icons.SEARCH, ft.Colors.GREY_600))
        
        # Create relevance indicator
        score = result.get('score', 0)
        relevance_stars = "⭐" * min(3, max(1, int(score * 3))) if score > 0 else ""
        
        return ft.ListTile(
            leading=ft.Container(
                content=ft.Icon(icon, color=color, size=20),
                width=40,
                height=40,
                bgcolor=ft.Colors.with_opacity(0.1, color),
                border_radius=20,
                alignment=ft.alignment.center
            ),
            title=ft.Text(
                result.get('title', 'Untitled'),
                size=14,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.GREY_900,
                max_lines=1,
                overflow=ft.TextOverflow.ELLIPSIS
            ),
            subtitle=ft.Text(
                result.get('subtitle', ''),
                size=12,
                color=ft.Colors.GREY_600,
                max_lines=1,
                overflow=ft.TextOverflow.ELLIPSIS
            ),
            trailing=ft.Row([
                ft.Text(
                    relevance_stars,
                    size=10,
                    color=ft.Colors.AMBER_600
                ),
                ft.Icon(ft.Icons.ARROW_FORWARD_IOS, size=12, color=ft.Colors.GREY_400)
            ], tight=True, spacing=4),
            on_click=lambda _, r=result: self._handle_result_selection(r),
            hover_color=ft.Colors.BLUE_50,
            content_padding=ft.padding.symmetric(horizontal=16, vertical=8)
        )
    
    def _handle_result_selection(self, result: Dict[str, Any]):
        """Handle result selection"""
        logger.info(f"Result selected: {result.get('title', 'Unknown')}")
        
        # Close search view
        if self.search_bar:
            self.search_bar.close_view(result.get('title', ''))
        
        # Notify callback
        if self.on_result_selected:
            self.on_result_selected(result)
        
        # Navigate to result
        self._navigate_to_result(result)
    
    def _navigate_to_result(self, result: Dict[str, Any]):
        """Navigate to the selected result"""
        if not self.app or not hasattr(self.app, 'main_layout'):
            logger.error("Navigation not available")
            return
        
        try:
            result_type = result.get('type')
            result_id = result.get('id')
            
            if result_type == 'client':
                self.app.main_layout.navigate_to_detail("client_detail", result_id)
            elif result_type == 'project':
                self.app.main_layout.navigate_to_detail("project_detail", result_id)
            elif result_type == 'deadline':
                self.app.main_layout._navigate_to("deadlines")
            else:
                logger.warning(f"Unknown result type: {result_type}")
                
        except Exception as e:
            logger.error(f"Navigation error: {e}")
            self._show_error_message(f"Errore durante la navigazione: {str(e)}")
    
    def _show_loading_state(self):
        """Show loading state in search results"""
        if not self.search_bar:
            return
        
        self.search_bar.controls.clear()
        self.search_bar.controls.append(
            ft.ListTile(
                leading=ft.ProgressRing(width=20, height=20, stroke_width=2),
                title=ft.Text("Ricerca in corso...", color=ft.Colors.GREY_600),
                subtitle=ft.Text("Attendere prego", color=ft.Colors.GREY_400)
            )
        )
        
        if self.app and hasattr(self.app, 'page'):
            self.app.page.update()
    
    def _show_no_results_state(self):
        """Show no results state"""
        if not self.search_bar:
            return
        
        self.search_bar.controls.clear()
        self.search_bar.controls.extend([
            ft.ListTile(
                leading=ft.Icon(ft.Icons.SEARCH_OFF, color=ft.Colors.GREY_400),
                title=ft.Text("Nessun risultato trovato", color=ft.Colors.GREY_600),
                subtitle=ft.Text(f"per '{self.current_query}'", color=ft.Colors.GREY_400)
            ),
            ft.ListTile(
                leading=ft.Icon(ft.Icons.LIGHTBULB_OUTLINE, color=ft.Colors.BLUE_400),
                title=ft.Text("Suggerimenti:", color=ft.Colors.BLUE_600, weight=ft.FontWeight.BOLD),
                subtitle=ft.Text("Prova termini più generici o verifica l'ortografia", color=ft.Colors.GREY_500)
            )
        ])
        
        if self.app and hasattr(self.app, 'page'):
            self.app.page.update()
    
    def _show_error_state(self):
        """Show error state"""
        if not self.search_bar:
            return
        
        self.search_bar.controls.clear()
        self.search_bar.controls.append(
            ft.ListTile(
                leading=ft.Icon(ft.Icons.ERROR_OUTLINE, color=ft.Colors.RED_400),
                title=ft.Text("Errore durante la ricerca", color=ft.Colors.RED_600),
                subtitle=ft.Text("Riprova più tardi", color=ft.Colors.GREY_400),
                on_click=lambda _: self._retry_search()
            )
        )
        
        if self.app and hasattr(self.app, 'page'):
            self.app.page.update()
    
    def _show_popular_searches(self):
        """Show popular searches when query is empty"""
        if not self.search_bar:
            return
        
        popular_searches = [
            {"text": "📊 Dashboard", "query": "dashboard"},
            {"text": "👥 Clienti", "query": "client"},
            {"text": "💼 Progetti", "query": "project"},
            {"text": "⏰ Scadenze", "query": "deadline"},
            {"text": "📅 Calendario", "query": "calendar"},
            {"text": "⚙️ Impostazioni", "query": "settings"}
        ]
        
        self.search_bar.controls.clear()
        
        # Add header
        self.search_bar.controls.append(
            ft.ListTile(
                leading=ft.Icon(ft.Icons.TRENDING_UP, color=ft.Colors.ORANGE_600),
                title=ft.Text("🔥 Ricerche popolari", weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_800),
                subtitle=ft.Text("Tocca per cercare rapidamente", color=ft.Colors.GREY_500)
            )
        )
        
        # Add popular searches
        for search in popular_searches:
            self.search_bar.controls.append(
                ft.ListTile(
                    leading=ft.Icon(ft.Icons.SEARCH, color=ft.Colors.BLUE_400, size=16),
                    title=ft.Text(search["text"], color=ft.Colors.GREY_700),
                    on_click=lambda _, q=search["query"]: self._quick_search(q),
                    hover_color=ft.Colors.BLUE_50
                )
            )
        
        if self.app and hasattr(self.app, 'page'):
            self.app.page.update()
    
    def _quick_search(self, query: str):
        """Perform a quick search with predefined query"""
        if self.search_bar:
            self.search_bar.value = query
            self.current_query = query
            self._perform_search(query)
    
    def _retry_search(self):
        """Retry the last search"""
        if self.current_query:
            self._perform_search(self.current_query)
    
    def _clear_search(self, e=None):
        """Clear search and close view"""
        if self.search_bar:
            self.search_bar.close_view("")
        
        self.current_query = ""
        self.search_results.clear()
        
        # Cancel any pending search
        if self.search_timer:
            self.search_timer.cancel()
            self.search_timer = None
    
    def _clear_results(self):
        """Clear search results"""
        if self.search_bar:
            self.search_bar.controls.clear()
            if self.app and hasattr(self.app, 'page'):
                self.app.page.update()
    
    def _show_error_message(self, message: str):
        """Show error message to user"""
        if self.app and hasattr(self.app, 'page'):
            self.app.page.open(
                ft.SnackBar(
                    content=ft.Text(message),
                    bgcolor=ft.Colors.RED_400,
                    duration=3000
                )
            )
    
    def build(self) -> ft.SearchBar:
        """Build and return the SearchBar component"""
        return self.search_bar
    
    def set_query(self, query: str):
        """Programmatically set search query"""
        if self.search_bar:
            self.search_bar.value = query
            self.current_query = query
            if query:
                self._perform_search(query)
    
    def clear(self):
        """Clear the search"""
        self._clear_search()
    
    def get_current_query(self) -> str:
        """Get current search query"""
        return self.current_query
    
    def get_results(self) -> List[Dict[str, Any]]:
        """Get current search results"""
        return self.search_results.copy() 