#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Servizio di gestione alert per Agevolami PM
"""

from datetime import datetime, date, timedelta
from typing import List, Optional, Dict, Any
from uuid import UUID

from ..database import DatabaseManagerExtended
from ..models import (
    Alert, Deadline, Client, Project, 
    AlertStatus, DeadlineStatus, Priority
)
from ..config import AppConfig
from .email_service import EmailService
from ..utils import get_logger

logger = get_logger(__name__)

class AlertService:
    """Servizio per la gestione degli alert di scadenza"""
    
    def __init__(self, db_manager: DatabaseManagerExtended, config: AppConfig, windows_integration=None):
        self.db = db_manager
        self.config = config
        self.email_service = EmailService(config)
        self.windows_integration = windows_integration
    
    def check_and_create_alerts(self) -> List[Alert]:
        """Controlla le scadenze e crea nuovi alert se necessario"""
        logger.info("Controllo scadenze per creazione alert")
        
        # Recupera tutte le scadenze attive
        upcoming_deadlines = self.db.get_upcoming_deadlines(
            days_ahead=self.config.alert_config["max_days_ahead"]
        )
        
        new_alerts = []
        
        for deadline in upcoming_deadlines:
            if deadline.status != DeadlineStatus.PENDING:
                continue
            
            # Calcola giorni rimanenti
            days_remaining = (deadline.due_date - date.today()).days
            
            # Verifica se serve creare un alert
            if self._should_create_alert(deadline, days_remaining):
                alert = self._create_deadline_alert(deadline, days_remaining)
                if alert:
                    new_alerts.append(alert)
                    # Invia notifica desktop se abilitata
                    self._send_desktop_notification(alert, deadline, days_remaining)
        
        logger.info(f"Creati {len(new_alerts)} nuovi alert")
        return new_alerts
    
    def _should_create_alert(self, deadline: Deadline, days_remaining: int) -> bool:
        """Determina se creare un alert per una scadenza"""
        
        # Controlla se è nel periodo di alert
        if days_remaining > deadline.alert_days_before:
            return False
        
        # Verifica se esiste già un alert attivo per questa scadenza
        existing_alerts = self.db.get_alerts_by_deadline(deadline.id)
        active_alerts = [
            alert for alert in existing_alerts 
            if alert.status == AlertStatus.ACTIVE and alert.days_before_deadline == days_remaining
        ]
        
        return len(active_alerts) == 0
    
    def _create_deadline_alert(self, deadline: Deadline, days_remaining: int) -> Optional[Alert]:
        """Crea un alert per una scadenza"""
        try:
            # Determina priorità dell'alert
            alert_priority = self._calculate_alert_priority(deadline, days_remaining)
            
            # Crea messaggio alert
            title, message = self._create_alert_message(deadline, days_remaining)
            
            alert = Alert(
                deadline_id=deadline.id,
                title=title,
                message=message,
                status=AlertStatus.ACTIVE,
                priority=alert_priority,
                days_before_deadline=days_remaining,
                email_sent=False,
                metadata={
                    "deadline_title": deadline.title,
                    "deadline_date": deadline.due_date.isoformat(),
                    "client_id": str(deadline.client_id) if deadline.client_id else None,
                    "project_id": str(deadline.project_id) if deadline.project_id else None
                }
            )
            
            # Salva nel database
            created_alert = self.db.create_alert(alert)
            
            logger.info(f"Alert creato per scadenza: {deadline.title}")
            return created_alert
            
        except Exception as e:
            logger.error(f"Errore creazione alert per scadenza {deadline.id}: {e}")
            return None
    
    def _calculate_alert_priority(self, deadline: Deadline, days_remaining: int) -> Priority:
        """Calcola la priorità dell'alert basata sui giorni rimanenti e priorità scadenza"""
        
        # Priorità base dalla scadenza
        base_priority = deadline.priority
        
        # Aumenta priorità in base ai giorni rimanenti
        if days_remaining < 0:  # Scaduto
            return Priority.CRITICAL
        elif days_remaining == 0:  # Oggi
            return Priority.CRITICAL
        elif days_remaining <= 1:  # Domani
            return Priority.HIGH if base_priority != Priority.CRITICAL else Priority.CRITICAL
        elif days_remaining <= 3:  # Entro 3 giorni
            return Priority.HIGH if base_priority == Priority.LOW else base_priority
        elif days_remaining <= 7:  # Entro una settimana
            return Priority.MEDIUM if base_priority == Priority.LOW else base_priority
        else:
            return base_priority
    
    def _create_alert_message(self, deadline: Deadline, days_remaining: int) -> tuple[str, str]:
        """Crea titolo e messaggio per l'alert"""
        
        if days_remaining < 0:
            title = f"🚨 SCADENZA SUPERATA: {deadline.title}"
            message = f"La scadenza '{deadline.title}' è stata superata di {abs(days_remaining)} giorni."
        elif days_remaining == 0:
            title = f"⚠️ SCADENZA OGGI: {deadline.title}"
            message = f"La scadenza '{deadline.title}' è prevista per oggi ({deadline.due_date.strftime('%d/%m/%Y')})."
        elif days_remaining == 1:
            title = f"🔴 SCADENZA DOMANI: {deadline.title}"
            message = f"La scadenza '{deadline.title}' è prevista per domani ({deadline.due_date.strftime('%d/%m/%Y')})."
        elif days_remaining <= 3:
            title = f"🟡 SCADENZA URGENTE: {deadline.title}"
            message = f"La scadenza '{deadline.title}' è prevista tra {days_remaining} giorni ({deadline.due_date.strftime('%d/%m/%Y')})."
        elif days_remaining <= 7:
            title = f"🟠 SCADENZA IMMINENTE: {deadline.title}"
            message = f"La scadenza '{deadline.title}' è prevista tra {days_remaining} giorni ({deadline.due_date.strftime('%d/%m/%Y')})."
        else:
            title = f"📅 PROMEMORIA: {deadline.title}"
            message = f"La scadenza '{deadline.title}' è prevista tra {days_remaining} giorni ({deadline.due_date.strftime('%d/%m/%Y')})."
        
        if deadline.description:
            message += f"\n\nDescrizione: {deadline.description}"
        
        return title, message
    
    def send_email_alerts(self) -> int:
        """Invia email per gli alert attivi che non hanno ancora ricevuto email"""
        if not self.config.smtp_config["enabled"]:
            logger.info("SMTP non abilitato, email alert non inviate")
            return 0
        
        # Recupera alert attivi senza email inviata
        active_alerts = self.db.get_active_alerts()
        pending_email_alerts = [
            alert for alert in active_alerts 
            if not alert.email_sent and alert.status == AlertStatus.ACTIVE
        ]
        
        sent_count = 0
        
        for alert in pending_email_alerts:
            if self._send_alert_email(alert):
                # Marca come email inviata
                self.db.mark_email_sent(alert.id)
                sent_count += 1
        
        logger.info(f"Inviate {sent_count} email di alert")
        return sent_count
    
    def _send_alert_email(self, alert: Alert) -> bool:
        """Invia email per un singolo alert"""
        try:
            # Recupera dati correlati
            deadline = self.db.get_deadline(alert.deadline_id)
            if not deadline:
                logger.warning(f"Scadenza non trovata per alert {alert.id}")
                return False
            
            client = None
            if deadline.client_id:
                client = self.db.get_client(deadline.client_id)
            
            project = None
            if deadline.project_id:
                project = self.db.get_project(deadline.project_id)
            
            # Verifica che il cliente abbia email
            if not client or not client.email:
                logger.warning(f"Cliente senza email per alert {alert.id}")
                return False
            
            # Verifica se le email sono abilitate per questa scadenza
            if not deadline.email_notifications:
                logger.info(f"Email disabilitate per scadenza {deadline.id}")
                return False
            
            # Invia email
            success = self.email_service.send_deadline_alert(
                deadline=deadline,
                client=client,
                project=project
            )
            
            if success:
                logger.info(f"Email alert inviata per scadenza: {deadline.title}")
            else:
                logger.error(f"Errore invio email alert per scadenza: {deadline.title}")
            
            return success
            
        except Exception as e:
            logger.error(f"Errore invio email alert {alert.id}: {e}")
            return False
    
    def dismiss_alert(self, alert_id: UUID, dismissed_by: str = "Utente") -> bool:
        """Ignora un alert"""
        try:
            success = self.db.dismiss_alert(alert_id, dismissed_by)
            if success:
                logger.info(f"Alert {alert_id} ignorato da {dismissed_by}")
            return success
        except Exception as e:
            logger.error(f"Errore dismissing alert {alert_id}: {e}")
            return False
    
    def complete_deadline_alerts(self, deadline_id: UUID) -> int:
        """Completa tutti gli alert di una scadenza quando viene completata"""
        try:
            alerts = self.db.get_alerts_by_deadline(deadline_id)
            completed_count = 0
            
            for alert in alerts:
                if alert.status == AlertStatus.ACTIVE:
                    alert.status = AlertStatus.COMPLETED
                    self.db.update_alert(alert)
                    completed_count += 1
            
            logger.info(f"Completati {completed_count} alert per scadenza {deadline_id}")
            return completed_count
            
        except Exception as e:
            logger.error(f"Errore completamento alert per scadenza {deadline_id}: {e}")
            return 0
    
    def get_dashboard_alerts(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Recupera alert per la dashboard con informazioni aggiuntive"""
        try:
            active_alerts = self.db.get_active_alerts()
            
            # Ordina per priorità e data creazione
            priority_order = {
                Priority.CRITICAL: 0,
                Priority.HIGH: 1,
                Priority.MEDIUM: 2,
                Priority.LOW: 3
            }
            
            sorted_alerts = sorted(
                active_alerts,
                key=lambda x: (priority_order.get(x.priority, 4), x.created_at),
                reverse=True
            )[:limit]
            
            dashboard_alerts = []
            
            for alert in sorted_alerts:
                # Recupera informazioni aggiuntive
                deadline = self.db.get_deadline(alert.deadline_id)
                client = None
                project = None
                
                if deadline:
                    if deadline.client_id:
                        client = self.db.get_client(deadline.client_id)
                    if deadline.project_id:
                        project = self.db.get_project(deadline.project_id)
                
                dashboard_alert = {
                    "alert": alert,
                    "deadline": deadline,
                    "client": client,
                    "project": project,
                    "days_remaining": alert.days_before_deadline,
                    "is_overdue": alert.days_before_deadline < 0
                }
                
                dashboard_alerts.append(dashboard_alert)
            
            return dashboard_alerts
            
        except Exception as e:
            logger.error(f"Errore recupero alert dashboard: {e}")
            return []
    
    def get_alert_statistics(self) -> Dict[str, Any]:
        """Recupera statistiche sugli alert"""
        try:
            all_alerts = self.db.get_active_alerts()
            
            stats = {
                "total_active": len(all_alerts),
                "by_priority": {
                    "critical": len([a for a in all_alerts if a.priority == Priority.CRITICAL]),
                    "high": len([a for a in all_alerts if a.priority == Priority.HIGH]),
                    "medium": len([a for a in all_alerts if a.priority == Priority.MEDIUM]),
                    "low": len([a for a in all_alerts if a.priority == Priority.LOW])
                },
                "overdue": len([a for a in all_alerts if a.days_before_deadline < 0]),
                "today": len([a for a in all_alerts if a.days_before_deadline == 0]),
                "this_week": len([a for a in all_alerts if 0 <= a.days_before_deadline <= 7]),
                "email_sent": len([a for a in all_alerts if a.email_sent]),
                "email_pending": len([a for a in all_alerts if not a.email_sent])
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"Errore calcolo statistiche alert: {e}")
            return {}
    
    def run_alert_check_cycle(self) -> Dict[str, Any]:
        """Esegue un ciclo completo di controllo alert"""
        logger.info("Avvio ciclo controllo alert")
        
        try:
            # 1. Crea nuovi alert
            new_alerts = self.check_and_create_alerts()
            
            # 2. Invia email per alert pendenti
            emails_sent = self.send_email_alerts()
            
            # 3. Aggiorna alert per scadenze completate
            self._update_completed_deadline_alerts()
            
            # 4. Calcola statistiche
            stats = self.get_alert_statistics()
            
            result = {
                "success": True,
                "new_alerts_created": len(new_alerts),
                "emails_sent": emails_sent,
                "statistics": stats,
                "timestamp": datetime.now().isoformat()
            }
            
            logger.info(f"Ciclo alert completato: {len(new_alerts)} nuovi, {emails_sent} email inviate")
            return result
            
        except Exception as e:
            logger.error(f"Errore ciclo controllo alert: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def _update_completed_deadline_alerts(self):
        """Aggiorna gli alert per scadenze che sono state completate"""
        try:
            # Recupera tutte le scadenze completate che hanno alert attivi
            active_alerts = self.db.get_active_alerts()
            
            for alert in active_alerts:
                deadline = self.db.get_deadline(alert.deadline_id)
                if deadline and deadline.status == DeadlineStatus.COMPLETED:
                    self.complete_deadline_alerts(deadline.id)
                    
        except Exception as e:
            logger.error(f"Errore aggiornamento alert scadenze completate: {e}")
    
    def _send_desktop_notification(self, alert: Alert, deadline: Deadline, days_remaining: int):
        """Invia notifica desktop per l'alert"""
        try:
            if not self.windows_integration:
                return
            
            # Verifica le impostazioni delle notifiche
            windows_config = getattr(self.config, 'windows_config', {})
            if not windows_config.get('notifications_enabled', True):
                return
            
            # Verifica filtro di priorità
            priority_filter = windows_config.get('notification_priority_filter', 'normal')
            alert_priority_value = self._get_priority_value(alert.priority)
            filter_priority_value = self._get_priority_filter_value(priority_filter)
            
            if alert_priority_value < filter_priority_value:
                return
            
            # Verifica orari silenziosi
            if self._is_quiet_hours(windows_config):
                # Per alert critici, ignora orari silenziosi
                if alert.priority != Priority.CRITICAL:
                    return
            
            # Recupera informazioni progetto se disponibile
            project_name = None
            if deadline.project_id:
                project = self.db.get_project(deadline.project_id)
                if project:
                    project_name = project.name
            
            # Invia notifica con priorità basata su giorni rimanenti
            notification_priority = self._map_alert_priority_to_notification(alert.priority, days_remaining)
            
            success = self.windows_integration.send_deadline_alert(
                deadline.title,
                days_remaining,
                project_name
            )
            
            if success:
                logger.info(f"Notifica desktop inviata per alert {alert.id}")
            else:
                logger.warning(f"Fallimento invio notifica desktop per alert {alert.id}")
                
        except Exception as e:
            logger.error(f"Errore invio notifica desktop: {e}")
    
    def _get_priority_value(self, priority: Priority) -> int:
        """Converte Priority enum in valore numerico"""
        priority_values = {
            Priority.LOW: 1,
            Priority.MEDIUM: 2,
            Priority.HIGH: 3,
            Priority.CRITICAL: 4
        }
        return priority_values.get(priority, 2)
    
    def _get_priority_filter_value(self, filter_setting: str) -> int:
        """Converte il filtro di priorità in valore numerico"""
        filter_values = {
            'all': 1,
            'normal': 2,
            'high': 3,
            'urgent': 4
        }
        return filter_values.get(filter_setting, 2)
    
    def _is_quiet_hours(self, windows_config: dict) -> bool:
        """Verifica se siamo negli orari silenziosi"""
        try:
            if not windows_config.get('notification_quiet_hours_enabled', False):
                return False
            
            now = datetime.now().time()
            start_time_str = windows_config.get('notification_quiet_start', '22:00')
            end_time_str = windows_config.get('notification_quiet_end', '08:00')
            
            # Parse dei tempi
            start_time = datetime.strptime(start_time_str, '%H:%M').time()
            end_time = datetime.strptime(end_time_str, '%H:%M').time()
            
            # Gestisce il caso in cui l'orario attraversa la mezzanotte
            if start_time <= end_time:
                return start_time <= now <= end_time
            else:
                return now >= start_time or now <= end_time
                
        except Exception as e:
            logger.error(f"Errore controllo orari silenziosi: {e}")
            return False
    
    def _map_alert_priority_to_notification(self, alert_priority: Priority, days_remaining: int) -> str:
        """Mappa la priorità alert a priorità notifica"""
        if days_remaining <= 0:
            return "urgent"
        elif days_remaining <= 1:
            return "urgent"
        elif days_remaining <= 3:
            return "high"
        elif alert_priority == Priority.CRITICAL:
            return "high"
        elif alert_priority == Priority.HIGH:
            return "normal"
        else:
            return "normal"