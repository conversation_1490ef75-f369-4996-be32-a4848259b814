#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple Test of Flet's Native SearchBar
Demonstrates proper overlay and visibility behavior
"""

import flet as ft

def main(page: ft.Page):
    """Simple SearchBar test"""
    page.title = "Native SearchBar Test"
    page.window_width = 1000
    page.window_height = 600
    
    # Sample search results
    def create_search_results():
        return [
            ft.ListTile(
                leading=ft.Icon(ft.Icons.PERSON, color=ft.Colors.BLUE_600),
                title=ft.Text("👤 Cliente: <PERSON>"),
                subtitle=ft.Text("Email: <EMAIL>"),
                on_click=lambda _: print("Selected: <PERSON>")
            ),
            ft.ListTile(
                leading=ft.Icon(ft.Icons.WORK, color=ft.Colors.GREEN_600),
                title=ft.Text("💼 Progetto: Website Aziendale"),
                subtitle=ft.Text("Cliente: Acme Corp - Scadenza: 15/02/2025"),
                on_click=lambda _: print("Selected: Website Aziendale")
            ),
            ft.ListTile(
                leading=ft.Icon(ft.Icons.SCHEDULE, color=ft.Colors.ORANGE_600),
                title=ft.Text("⏰ Scadenza: Presentazione Report"),
                subtitle=ft.Text("Data: 10/02/2025 - Priorità: Alta"),
                on_click=lambda _: print("Selected: Presentazione Report")
            )
        ]
    
    # Native SearchBar with proper overlay behavior
    search_bar = ft.SearchBar(
        bar_hint_text="🔍 Cerca clienti, progetti, scadenze...",
        view_hint_text="Digita per cercare...",
        
        # Styling for better visibility
        bar_bgcolor={
            ft.ControlState.DEFAULT: ft.Colors.WHITE,
            ft.ControlState.FOCUSED: ft.Colors.WHITE,
            ft.ControlState.HOVERED: ft.Colors.GREY_50,
        },
        view_bgcolor=ft.Colors.WHITE,
        view_elevation=12,
        
        # Icons
        bar_leading=ft.Icon(ft.Icons.SEARCH, color=ft.Colors.BLUE_600),
        bar_trailing=[
            ft.IconButton(
                icon=ft.Icons.MIC,
                icon_color=ft.Colors.GREY_500,
                tooltip="Voice search"
            )
        ],
        
        # Search behavior
        on_change=lambda e: update_search_results(e.control.value),
        on_tap=lambda _: search_bar.open_view(),
        
        # Initial results (popular searches)
        controls=[
            ft.ListTile(
                leading=ft.Icon(ft.Icons.TRENDING_UP, color=ft.Colors.ORANGE_600),
                title=ft.Text("🔥 Ricerche Popolari", weight=ft.FontWeight.BOLD),
                subtitle=ft.Text("Tocca per cercare rapidamente")
            ),
            ft.ListTile(
                leading=ft.Icon(ft.Icons.DASHBOARD, color=ft.Colors.BLUE_400, size=16),
                title=ft.Text("📊 Dashboard"),
                on_click=lambda _: print("Go to Dashboard")
            ),
            ft.ListTile(
                leading=ft.Icon(ft.Icons.PEOPLE, color=ft.Colors.GREEN_400, size=16),
                title=ft.Text("👥 Clienti"),
                on_click=lambda _: print("Go to Clients")
            ),
            ft.ListTile(
                leading=ft.Icon(ft.Icons.WORK, color=ft.Colors.PURPLE_400, size=16),
                title=ft.Text("💼 Progetti"),
                on_click=lambda _: print("Go to Projects")
            )
        ]
    )
    
    def update_search_results(query: str):
        """Update search results based on query"""
        if not query or len(query) < 2:
            # Show popular searches
            search_bar.controls = [
                ft.ListTile(
                    leading=ft.Icon(ft.Icons.TRENDING_UP, color=ft.Colors.ORANGE_600),
                    title=ft.Text("🔥 Ricerche Popolari", weight=ft.FontWeight.BOLD),
                    subtitle=ft.Text("Tocca per cercare rapidamente")
                ),
                ft.ListTile(
                    leading=ft.Icon(ft.Icons.DASHBOARD, color=ft.Colors.BLUE_400, size=16),
                    title=ft.Text("📊 Dashboard"),
                    on_click=lambda _: print("Go to Dashboard")
                ),
                ft.ListTile(
                    leading=ft.Icon(ft.Icons.PEOPLE, color=ft.Colors.GREEN_400, size=16),
                    title=ft.Text("👥 Clienti"),
                    on_click=lambda _: print("Go to Clients")
                )
            ]
        else:
            # Show search results
            search_bar.controls = [
                ft.ListTile(
                    leading=ft.Icon(ft.Icons.SEARCH, color=ft.Colors.BLUE_600),
                    title=ft.Text(f"🔍 Risultati per '{query}'", weight=ft.FontWeight.BOLD),
                    subtitle=ft.Text("3 risultati trovati")
                ),
                *create_search_results()
            ]
        
        page.update()
    
    # Header layout
    header = ft.Container(
        content=ft.Row([
            # Title
            ft.Column([
                ft.Text("Agevolami PM", size=24, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_800),
                ft.Text("Dashboard", size=12, color=ft.Colors.GREY_500)
            ], spacing=2),
            
            # Spacer
            ft.Container(expand=True),
            
            # Search bar (this is the key - native component!)
            ft.Container(
                content=search_bar,
                width=400
            ),
            
            # Spacer
            ft.Container(expand=True),
            
            # Actions
            ft.Row([
                ft.IconButton(ft.Icons.NOTIFICATIONS, icon_color=ft.Colors.GREY_600),
                ft.IconButton(ft.Icons.SETTINGS, icon_color=ft.Colors.GREY_600)
            ])
        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
        height=80,
        padding=ft.padding.symmetric(horizontal=20, vertical=16),
        bgcolor=ft.Colors.WHITE,
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=4,
            color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
            offset=ft.Offset(0, 2)
        )
    )
    
    # Main content to test overlay
    content = ft.Container(
        content=ft.Column([
            ft.Text(
                "✨ Native SearchBar Test",
                size=28,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.BLUE_600,
                text_align=ft.TextAlign.CENTER
            ),
            ft.Text(
                "This demonstrates Flet's native SearchBar component",
                size=16,
                color=ft.Colors.GREY_600,
                text_align=ft.TextAlign.CENTER
            ),
            
            ft.Container(height=20),
            
            ft.Row([
                ft.Container(
                    content=ft.Column([
                        ft.Icon(ft.Icons.CHECK_CIRCLE, color=ft.Colors.GREEN_600, size=32),
                        ft.Text("✅ Text Visibility", weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN_600),
                        ft.Text("Search text is always visible", size=12, color=ft.Colors.GREY_600, text_align=ft.TextAlign.CENTER)
                    ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                    padding=ft.padding.all(20),
                    bgcolor=ft.Colors.GREEN_50,
                    border_radius=8,
                    expand=True
                ),
                ft.Container(
                    content=ft.Column([
                        ft.Icon(ft.Icons.LAYERS, color=ft.Colors.BLUE_600, size=32),
                        ft.Text("✅ Proper Overlay", weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_600),
                        ft.Text("Results appear above content", size=12, color=ft.Colors.GREY_600, text_align=ft.TextAlign.CENTER)
                    ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                    padding=ft.padding.all(20),
                    bgcolor=ft.Colors.BLUE_50,
                    border_radius=8,
                    expand=True
                ),
                ft.Container(
                    content=ft.Column([
                        ft.Icon(ft.Icons.DESIGN_SERVICES, color=ft.Colors.PURPLE_600, size=32),
                        ft.Text("✅ Material Design", weight=ft.FontWeight.BOLD, color=ft.Colors.PURPLE_600),
                        ft.Text("Native Material styling", size=12, color=ft.Colors.GREY_600, text_align=ft.TextAlign.CENTER)
                    ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                    padding=ft.padding.all(20),
                    bgcolor=ft.Colors.PURPLE_50,
                    border_radius=8,
                    expand=True
                )
            ], spacing=16),
            
            ft.Container(height=30),
            
            ft.Container(
                content=ft.Column([
                    ft.Text("🧪 Test Steps:", size=18, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_800),
                    ft.Text("1. Click the search bar above", size=14, color=ft.Colors.GREY_700),
                    ft.Text("2. See the search view open with popular searches", size=14, color=ft.Colors.GREY_700),
                    ft.Text("3. Type 'mario' or 'progetto' to see results", size=14, color=ft.Colors.GREY_700),
                    ft.Text("4. Notice perfect visibility and overlay behavior", size=14, color=ft.Colors.GREY_700),
                ], spacing=8),
                padding=ft.padding.all(20),
                bgcolor=ft.Colors.WHITE,
                border_radius=8,
                border=ft.border.all(1, ft.Colors.GREY_200)
            )
        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=20),
        padding=ft.padding.all(40),
        expand=True,
        bgcolor=ft.Colors.GREY_50
    )
    
    # Main layout
    page.add(
        ft.Column([
            header,
            content
        ], spacing=0, expand=True)
    )

if __name__ == "__main__":
    ft.app(target=main, view=ft.AppView.WEB_BROWSER, port=8081) 