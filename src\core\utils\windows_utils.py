#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Utilità Windows per Agevolami PM
Gestisce startup automatico e notifiche Windows
"""

import os
import sys
import platform
import winreg
from pathlib import Path
from typing import Optional, Tuple
import logging

logger = logging.getLogger(__name__)

class WindowsStartupManager:
    """Gestore per l'avvio automatico su Windows"""
    
    APP_NAME = "AgevolamiPM"
    REGISTRY_KEY = r"Software\Microsoft\Windows\CurrentVersion\Run"
    
    @classmethod
    def is_windows(cls) -> bool:
        """Verifica se siamo su Windows"""
        return platform.system() == "Windows"
    
    @classmethod
    def get_executable_path(cls) -> str:
        """Ottiene il percorso dell'eseguibile corrente"""
        if getattr(sys, 'frozen', False):
            # Se l'app è compilata (exe)
            return sys.executable
        else:
            # Se l'app è in sviluppo
            # Usa python + script principale
            main_script = Path(__file__).parent.parent.parent.parent / "main.py"
            if main_script.exists():
                return f'"{sys.executable}" "{main_script}"'
            else:
                return f'"{sys.executable}" -m src.main'
    
    @classmethod
    def is_startup_enabled(cls) -> bool:
        """Verifica se l'avvio automatico è abilitato"""
        if not cls.is_windows():
            return False
        
        try:
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, cls.REGISTRY_KEY, 0, winreg.KEY_READ) as key:
                try:
                    value, _ = winreg.QueryValueEx(key, cls.APP_NAME)
                    return bool(value)
                except FileNotFoundError:
                    return False
        except Exception as e:
            logger.error(f"Errore controllo startup: {e}")
            return False
    
    @classmethod
    def enable_startup(cls) -> bool:
        """Abilita l'avvio automatico"""
        if not cls.is_windows():
            logger.warning("Avvio automatico supportato solo su Windows")
            return False
        
        try:
            executable_path = cls.get_executable_path()
            
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, cls.REGISTRY_KEY, 0, winreg.KEY_WRITE) as key:
                winreg.SetValueEx(key, cls.APP_NAME, 0, winreg.REG_SZ, executable_path)
            
            logger.info(f"Avvio automatico abilitato: {executable_path}")
            return True
            
        except Exception as e:
            logger.error(f"Errore abilitazione startup: {e}")
            return False
    
    @classmethod
    def disable_startup(cls) -> bool:
        """Disabilita l'avvio automatico"""
        if not cls.is_windows():
            return False
        
        try:
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, cls.REGISTRY_KEY, 0, winreg.KEY_WRITE) as key:
                try:
                    winreg.DeleteValue(key, cls.APP_NAME)
                    logger.info("Avvio automatico disabilitato")
                    return True
                except FileNotFoundError:
                    # Già disabilitato
                    return True
                    
        except Exception as e:
            logger.error(f"Errore disabilitazione startup: {e}")
            return False


class WindowsNotificationManager:
    """Gestore per le notifiche Windows"""
    
    def __init__(self, app_name: str = "Agevolami PM"):
        self.app_name = app_name
        self._toaster = None
        self._init_toaster()
    
    def _init_toaster(self):
        """Inizializza il sistema di toast notifications"""
        try:
            if platform.system() == "Windows":
                # Use plyer as primary notification system to avoid win10toast WNDPROC issues
                # win10toast has known issues with WNDPROC return values in some Windows versions
                logger.info("Using plyer for Windows notifications to avoid WNDPROC issues")
                self._toaster = None  # Will use plyer fallback
            else:
                self._toaster = None
        except Exception as e:
            logger.error(f"Error initializing toast notifications: {e}")
            self._toaster = None
    
    def show_toast(
        self, 
        title: str, 
        message: str, 
        duration: int = 5,
        icon_path: Optional[str] = None,
        threaded: bool = True
    ) -> bool:
        """
        Mostra una toast notification Windows
        
        Args:
            title: Titolo della notifica
            message: Messaggio della notifica
            duration: Durata in secondi
            icon_path: Percorso icona (opzionale)
            threaded: Se eseguire in thread separato
        
        Returns:
            True se la notifica è stata mostrata con successo
        """
        try:
            if not platform.system() == "Windows":
                return self._fallback_notification(title, message)
            
            if self._toaster:
                self._toaster.show_toast(
                    title=title,
                    msg=message,
                    duration=duration,
                    icon_path=icon_path,
                    threaded=threaded
                )
                return True
            else:
                return self._fallback_notification(title, message)
                
        except Exception as e:
            logger.error(f"Errore toast notification: {e}")
            return self._fallback_notification(title, message)
    
    def show_notification_with_plyer(
        self,
        title: str,
        message: str,
        timeout: int = 5,
        app_icon: Optional[str] = None
    ) -> bool:
        """
        Mostra notifica usando plyer (cross-platform)

        Args:
            title: Titolo della notifica
            message: Messaggio della notifica
            timeout: Timeout in secondi
            app_icon: Percorso icona app

        Returns:
            True se la notifica è stata mostrata
        """
        try:
            from plyer import notification

            # Ensure timeout is reasonable (plyer can be sensitive to very long timeouts)
            timeout = max(1, min(timeout, 30))

            # Clean up title and message to avoid encoding issues
            title = str(title).strip()[:100]  # Limit title length
            message = str(message).strip()[:500]  # Limit message length

            notification.notify(
                title=title,
                message=message,
                timeout=timeout,
                app_icon=app_icon or None,
                app_name=self.app_name
            )

            logger.debug(f"Plyer notification sent: {title}")
            return True

        except ImportError:
            logger.warning("plyer non disponibile")
            return self._fallback_notification(title, message)
        except Exception as e:
            logger.error(f"Errore plyer notification: {e}")
            return self._fallback_notification(title, message)
    
    def show_priority_notification(
        self,
        title: str,
        message: str,
        priority: str = "normal",
        persistent: bool = False
    ) -> bool:
        """
        Mostra notifica con priorità specifica
        
        Args:
            title: Titolo
            message: Messaggio
            priority: "low", "normal", "high", "urgent"
            persistent: Se la notifica deve rimanere fino a dismissal
        
        Returns:
            True se mostrata con successo
        """
        # Durata basata su priorità
        durations = {
            "low": 3,
            "normal": 5,
            "high": 10,
            "urgent": 15
        }
        
        duration = durations.get(priority, 5)
        
        # Prefisso per urgenza
        if priority == "urgent":
            title = f"[URGENT] {title}"
        elif priority == "high":
            title = f"[HIGH] {title}"
        
        # Use plyer directly for better stability (win10toast has WNDPROC issues)
        if self.show_notification_with_plyer(title, message, timeout=duration):
            return True
        
        # Fallback a plyer
        return self.show_notification_with_plyer(title, message, timeout=duration)
    
    def _fallback_notification(self, title: str, message: str) -> bool:
        """Notifica di fallback (console log)"""
        logger.info(f"NOTIFICA: {title} - {message}")
        return False


class WindowsSystemIntegration:
    """Integrazione completa con il sistema Windows"""
    
    def __init__(self, app_name: str = "Agevolami PM"):
        self.app_name = app_name
        self.startup_manager = WindowsStartupManager()
        self.notification_manager = WindowsNotificationManager(app_name)
    
    def configure_startup(self, enabled: bool) -> bool:
        """Configura l'avvio automatico"""
        if enabled:
            return self.startup_manager.enable_startup()
        else:
            return self.startup_manager.disable_startup()
    
    def is_startup_enabled(self) -> bool:
        """Verifica se l'avvio automatico è abilitato"""
        return self.startup_manager.is_startup_enabled()
    
    def send_notification(
        self,
        title: str,
        message: str,
        priority: str = "normal",
        persistent: bool = False
    ) -> bool:
        """Invia una notifica sistema"""
        return self.notification_manager.show_priority_notification(
            title, message, priority, persistent
        )
    
    def send_deadline_alert(
        self,
        deadline_title: str,
        days_remaining: int,
        project_name: Optional[str] = None
    ) -> bool:
        """Invia alert specifico per scadenze"""
        if days_remaining <= 0:
            priority = "urgent"
            title = "Scadenza SCADUTA!"
            icon = "[URGENT]"
        elif days_remaining <= 1:
            priority = "urgent"
            title = "Scadenza IMMINENTE!"
            icon = "[WARNING]"
        elif days_remaining <= 3:
            priority = "high"
            title = "Scadenza Ravvicinata"
            icon = "[ALERT]"
        else:
            priority = "normal"
            title = "Promemoria Scadenza"
            icon = "[REMINDER]"
        
        message = f"{icon} {deadline_title}"
        if project_name:
            message += f"\nProgetto: {project_name}"
        
        if days_remaining > 0:
            message += f"\nScade tra {days_remaining} giorni"
        else:
            message += f"\nScaduta da {abs(days_remaining)} giorni"
        
        return self.send_notification(title, message, priority)
    
    def send_project_notification(
        self,
        project_name: str,
        event_type: str,
        details: str = ""
    ) -> bool:
        """Invia notifica relativa a progetti"""
        title = f"Progetto: {project_name}"
        
        if event_type == "created":
            message = f"[OK] Nuovo progetto creato\n{details}"
        elif event_type == "completed":
            message = f"[COMPLETED] Progetto completato\n{details}"
        elif event_type == "updated":
            message = f"[UPDATED] Progetto aggiornato\n{details}"
        elif event_type == "deadline_added":
            message = f"[DEADLINE] Nuova scadenza aggiunta\n{details}"
        else:
            message = f"[INFO] {event_type}\n{details}"
        
        return self.send_notification(title, message, "normal")
    
    def test_notification_system(self) -> Tuple[bool, str]:
        """Testa il sistema di notifiche"""
        try:
            success = self.send_notification(
                "Test Notifiche",
                "Sistema di notifiche Agevolami PM funzionante!",
                "normal"
            )
            
            if success:
                return True, "Sistema notifiche funzionante"
            else:
                return False, "Notifiche disabilitate o non supportate"
                
        except Exception as e:
            return False, f"Errore sistema notifiche: {e}" 