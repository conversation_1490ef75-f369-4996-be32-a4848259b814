#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script per verificare i miglioramenti al sistema di backup Google Drive
"""

import sys
import os
from pathlib import Path

# Aggiungi il percorso src al PYTHONPATH
sys.path.insert(0, str(Path(__file__).parent / "src"))

from core.services.google_drive_service import GoogleDriveService
from core.config import AppConfig

def test_backup_improvements():
    """Test delle nuove funzionalità di backup"""
    print("🧪 Test miglioramenti backup Google Drive")
    print("=" * 50)
    
    # Inizializza configurazione
    config = AppConfig()
    
    # Inizializza servizio Google Drive
    drive_service = GoogleDriveService(config.data_dir / "config")
    
    print(f"📁 Directory config: {config.data_dir / 'config'}")
    print(f"🔐 Autenticato: {drive_service.is_authenticated}")
    
    if not drive_service.is_authenticated:
        print("❌ Google Drive non autenticato. Esegui prima l'autenticazione dall'app.")
        return False
    
    print("\n1️⃣ Test connessione...")
    if drive_service.test_connection():
        print("✅ Connessione OK")
    else:
        print("❌ Connessione fallita")
        return False
    
    print("\n2️⃣ Test informazioni storage...")
    storage_info = drive_service.get_storage_info()
    if storage_info:
        print(f"📊 Storage info:")
        print(f"   • Email: {storage_info.get('user_email', 'N/A')}")
        print(f"   • Spazio totale: {storage_info.get('total_space', 0) / (1024**3):.1f} GB")
        print(f"   • Spazio usato: {storage_info.get('used_space', 0) / (1024**3):.1f} GB")
        print(f"   • Spazio disponibile: {storage_info.get('available_space', 0) / (1024**3):.1f} GB")
    else:
        print("❌ Impossibile ottenere info storage")
    
    print("\n3️⃣ Test lista backup...")
    backups = drive_service.list_backups()
    print(f"📋 Trovati {len(backups)} backup")
    
    for i, backup in enumerate(backups[:5]):  # Mostra primi 5
        print(f"   {i+1}. {backup['name']}")
        print(f"      • Dimensione: {backup['size_mb']} MB")
        print(f"      • Età: {backup['age_days']} giorni")
        print(f"      • Recente: {backup['is_recent']}")
        print(f"      • Vecchio: {backup['is_old']}")
    
    print("\n4️⃣ Test statistiche backup...")
    stats = drive_service.get_backup_statistics()
    if stats.get('success'):
        print(f"📊 Statistiche:")
        print(f"   • Totale backup: {stats['total_backups']}")
        print(f"   • Spazio totale: {stats['total_size_mb']} MB")
        print(f"   • Backup recenti: {stats['recent_backups']}")
        print(f"   • Backup vecchi: {stats['old_backups']}")
        print(f"   • Dimensione media: {stats['average_size_mb']} MB")
    else:
        print(f"❌ Errore statistiche: {stats.get('error', 'Unknown')}")
    
    print("\n5️⃣ Test verifica integrità backup locale...")
    # Crea un backup di test
    database_path = config.data_dir / "agevolami.db"
    if database_path.exists():
        test_backup = drive_service._create_local_backup(database_path, config.data_dir)
        if test_backup:
            integrity_ok = drive_service._verify_backup_integrity(test_backup)
            print(f"✅ Integrità backup: {'OK' if integrity_ok else 'FALLITA'}")
            
            # Pulisci file di test
            os.unlink(test_backup)
        else:
            print("❌ Impossibile creare backup di test")
    else:
        print("⚠️ Database non trovato, skip test integrità")
    
    print("\n6️⃣ Test simulazione cleanup (DRY RUN)...")
    # Non eseguiamo cleanup reale, solo simulazione
    print("   (Simulazione - nessun backup verrà eliminato)")
    if stats.get('success') and stats['old_backups'] > 0:
        print(f"   • Backup candidati per eliminazione: {stats['old_backups']}")
        print("   • Per eseguire cleanup reale, usa l'interfaccia dell'app")
    else:
        print("   • Nessun backup vecchio da eliminare")
    
    print("\n✅ Test completati con successo!")
    print("\n🎯 Nuove funzionalità disponibili:")
    print("   • ✅ Verifica integrità backup automatica")
    print("   • ✅ Cleanup automatico backup vecchi")
    print("   • ✅ Statistiche dettagliate")
    print("   • ✅ Metadata migliorati per i backup")
    print("   • ✅ Controllo spazio storage")
    print("   • ✅ UI migliorata con indicatori di stato")
    
    return True

if __name__ == "__main__":
    try:
        success = test_backup_improvements()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Errore durante il test: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
