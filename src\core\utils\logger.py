#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Sistema di logging per Agevolami PM
"""

import sys
import os
from pathlib import Path
from loguru import logger
from typing import Optional

def setup_logger(log_level: str = "INFO", log_file: Optional[Path] = None) -> logger:
    """
    Configura il sistema di logging dell'applicazione

    Args:
        log_level: Livello di logging (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Percorso del file di log (opzionale)

    Returns:
        Logger configurato
    """

    # Rimuovi handler predefiniti
    logger.remove()

    # Set UTF-8 encoding for Windows built applications
    if sys.platform == "win32":
        os.environ.setdefault('PYTHONIOENCODING', 'utf-8')

    # Formato log (senza colori per built applications per evitare problemi di encoding)
    is_built_app = getattr(sys, 'frozen', False)

    if is_built_app:
        # Formato semplificato per applicazioni built
        log_format = (
            "{time:YYYY-MM-DD HH:mm:ss} | "
            "{level: <8} | "
            "{name}:{function}:{line} | "
            "{message}"
        )
        colorize = False
    else:
        # Formato colorato per sviluppo
        log_format = (
            "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
            "<level>{message}</level>"
        )
        colorize = True

    # Handler per console con gestione encoding sicura
    try:
        logger.add(
            sys.stdout,
            format=log_format,
            level=log_level,
            colorize=colorize,
            backtrace=True,
            diagnose=True,
            encoding="utf-8",
            errors="replace"  # Replace problematic characters instead of crashing
        )
    except Exception:
        # Fallback semplice senza encoding parameter
        try:
            logger.add(
                sys.stdout,
                format=log_format,
                level=log_level,
                colorize=colorize,
                backtrace=True,
                diagnose=True
            )
        except Exception:
            # Ultimate fallback - logging di base senza formattazione speciale
            logger.add(
                sys.stdout,
                format="{time} | {level} | {message}",
                level=log_level,
                colorize=False,
                backtrace=False,
                diagnose=False
            )
    
    # Handler per file se specificato
    if log_file:
        # Crea directory se non esiste
        log_file.parent.mkdir(parents=True, exist_ok=True)

        try:
            logger.add(
                str(log_file),
                format=log_format,
                level=log_level,
                rotation="10 MB",
                retention="30 days",
                compression="zip",
                backtrace=True,
                diagnose=True,
                encoding="utf-8",
                errors="replace"
            )
        except Exception:
            # Fallback per sistemi che non supportano encoding parameter
            logger.add(
                str(log_file),
                format=log_format,
                level=log_level,
                rotation="10 MB",
                retention="30 days",
                compression="zip",
                backtrace=True,
                diagnose=True
            )
    
    return logger

def get_logger(name: str) -> logger:
    """
    Restituisce un logger con il nome specificato
    
    Args:
        name: Nome del logger
    
    Returns:
        Logger configurato
    """
    return logger.bind(name=name)