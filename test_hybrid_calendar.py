#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for Hybrid Calendar View
Run this to see the new hybrid calendar in action
"""

import flet as ft
from datetime import date, timedelta
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from ui.views.hybrid_calendar import HybridCalendarView
from core.models.base_models import Deadline, Priority, DeadlineStatus

class MockDB:
    """Mock database for testing"""
    def __init__(self):
        # Create some sample deadlines
        today = date.today()
        self.deadlines = [
            MockDeadline(
                id="1",
                title="Riunione con cliente",
                description="Discussione requisiti progetto",
                due_date=today,
                priority=Priority.HIGH,
                status=DeadlineStatus.PENDING
            ),
            MockDeadline(
                id="2",
                title="Consegna progetto",
                description="Consegna finale del progetto X",
                due_date=today + timedelta(days=2),
                priority=Priority.CRITICAL,
                status=DeadlineStatus.PENDING
            ),
            MockDeadline(
                id="3",
                title="Review codice",
                description="Revisione del codice sviluppato",
                due_date=today + timedelta(days=5),
                priority=Priority.MEDIUM,
                status=DeadlineStatus.PENDING
            ),
            MockDeadline(
                id="4",
                title="Meeting team",
                description="Riunione settimanale del team",
                due_date=today - timedelta(days=1),
                priority=Priority.LOW,
                status=DeadlineStatus.COMPLETED
            )
        ]
    
    def get_deadlines_by_date_range(self, start_date, end_date):
        """Get deadlines in date range"""
        return [d for d in self.deadlines if start_date <= d.due_date <= end_date]
    
    def get_all_projects(self):
        """Get all projects"""
        return []
    
    def get_all_clients(self):
        """Get all clients"""
        return []
    
    def update_deadline(self, deadline):
        """Update deadline"""
        for i, d in enumerate(self.deadlines):
            if d.id == deadline.id:
                self.deadlines[i] = deadline
                break

class MockDeadline:
    """Mock deadline for testing"""
    def __init__(self, id, title, description, due_date, priority, status):
        self.id = id
        self.title = title
        self.description = description
        self.due_date = due_date
        self.priority = priority
        self.status = status
        self.completed_date = None

class MockApp:
    """Mock app for testing"""
    def __init__(self):
        self.db_manager = MockDB()
        self.page = None
        self.main_layout = None

def main(page: ft.Page):
    page.title = "Test Hybrid Calendar"
    page.theme_mode = ft.ThemeMode.LIGHT
    page.window.width = 1200
    page.window.height = 800
    
    # Create mock app
    app = MockApp()
    app.page = page
    
    # Create hybrid calendar
    calendar_view = HybridCalendarView(app)
    
    # Add to page
    page.add(
        ft.Container(
            content=ft.Column([
                ft.Text(
                    "🗓️ Hybrid Calendar Test",
                    size=24,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.BLUE_700
                ),
                ft.Text(
                    "This is the new hybrid calendar with native date picker + custom event display",
                    size=14,
                    color=ft.Colors.GREY_600
                ),
                ft.Divider(),
                calendar_view.build()
            ], spacing=16),
            padding=ft.padding.all(20),
            expand=True
        )
    )

if __name__ == "__main__":
    print("🚀 Starting Hybrid Calendar Test...")
    print("📋 Features included:")
    print("   ✅ Native date picker for better UX")
    print("   ✅ Mini calendar with event indicators")
    print("   ✅ Modern event cards with priority colors")
    print("   ✅ Quick navigation (prev/next/today)")
    print("   ✅ Multiple view modes (month/week/agenda)")
    print("   ✅ Event status toggle")
    print("")
    
    ft.app(target=main) 