#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test per la vista Gantt migliorata con filtri progetti e supporto tasks
"""

import flet as ft
from datetime import datetime, date, timedelta
from uuid import uuid4
from dataclasses import dataclass
from typing import List

# Mock models per il test
@dataclass
class Project:
    id: str
    name: str
    client_id: str
    status: str
    start_date: date
    end_date: date = None

@dataclass
class Deadline:
    id: str
    title: str
    client_id: str
    project_id: str
    due_date: date
    priority: str
    status: str

@dataclass
class Task:
    id: str
    title: str
    project_id: str
    status: str
    priority: str
    start_date: date = None
    due_date: datetime = None
    progress_percentage: int = 0
    created_at: datetime = None

@dataclass
class Client:
    id: str
    name: str

# Mock database manager
class MockDatabaseManager:
    def __init__(self):
        self.projects = []
        self.deadlines = []
        self.tasks = []
        self.clients = []
        self._create_test_data()
    
    def _create_test_data(self):
        today = date.today()
        
        # Clients
        self.clients = [
            Client("1", "Azienda Alpha S.r.l."),
            Client("2", "Beta Corporation"),
            Client("3", "Gamma Industries")
        ]
        
        # Projects
        self.projects = [
            Project("p1", "Incentivo Industria 4.0", "1", "in_corso", 
                   today - timedelta(days=30), today + timedelta(days=60)),
            Project("p2", "Credito R&S 2024", "2", "approvato", 
                   today - timedelta(days=15), today + timedelta(days=90)),
            Project("p3", "Finanziamento Export", "3", "presentato", 
                   today + timedelta(days=10), today + timedelta(days=120))
        ]
        
        # Deadlines
        self.deadlines = [
            Deadline("d1", "Presentazione Domanda", "1", "p1", today + timedelta(days=15), "alta", "in_attesa"),
            Deadline("d2", "Documentazione Tecnica", "2", "p2", today + timedelta(days=30), "media", "in_attesa"),
            Deadline("d3", "Revisione Finale", "3", "p3", today + timedelta(days=45), "critica", "in_attesa")
        ]
        
        # Tasks
        self.tasks = [
            Task("t1", "Preparazione documenti", "p1", "in_corso", "alta", 
                today - timedelta(days=5), datetime.combine(today + timedelta(days=10), datetime.min.time()), 
                60, datetime.now()),
            Task("t2", "Analisi tecnica", "p1", "in_attesa", "media", 
                today + timedelta(days=5), datetime.combine(today + timedelta(days=20), datetime.min.time()), 
                0, datetime.now()),
            Task("t3", "Validazione budget", "p2", "completato", "alta", 
                today - timedelta(days=10), datetime.combine(today - timedelta(days=2), datetime.min.time()), 
                100, datetime.now()),
            Task("t4", "Studio fattibilità", "p3", "in_corso", "critica", 
                today, datetime.combine(today + timedelta(days=15), datetime.min.time()), 
                30, datetime.now())
        ]
    
    def get_all_projects(self):
        return self.projects
    
    def get_deadlines_by_date_range(self, start_date, end_date):
        return [d for d in self.deadlines if start_date <= d.due_date <= end_date]
    
    def get_tasks_by_date_range(self, start_date, end_date):
        return [t for t in self.tasks if self._task_in_range(t, start_date, end_date)]
    
    def _task_in_range(self, task, start_date, end_date):
        # Check if task overlaps with date range
        task_start = task.start_date if task.start_date else task.created_at.date()
        task_end = task.due_date.date() if task.due_date else task_start
        
        return not (task_end < start_date or task_start > end_date)
    
    def get_all_clients(self):
        return self.clients

# Mock app instance
class MockApp:
    def __init__(self):
        self.db_manager = MockDatabaseManager()
        self.page = None

def main(page: ft.Page):
    page.title = "Test Gantt Migliorato"
    page.window_width = 1400
    page.window_height = 900

    # Mock app
    app = MockApp()
    app.page = page

    # Add current directory to path for imports
    import sys
    import os
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

    try:
        # Import the enhanced Gantt view
        from src.ui.views.gantt import GanttView

        # Create Gantt view
        gantt_view = GanttView(app)

        # Build and add to page
        page.add(gantt_view.build())

        # Show success message
        page.snack_bar = ft.SnackBar(
            content=ft.Text("✅ Gantt migliorato caricato con successo! Prova i nuovi filtri e il toggle Tasks."),
            bgcolor=ft.Colors.GREEN_600
        )
        page.snack_bar.open = True
        page.update()

    except Exception as e:
        # Show error message
        page.add(ft.Text(f"❌ Errore caricamento Gantt: {str(e)}", color=ft.Colors.RED))
        page.update()

if __name__ == "__main__":
    print("🚀 Avvio test Gantt migliorato...")
    print("📋 Funzionalità aggiunte:")
    print("   • Filtro per progetti specifici")
    print("   • Toggle per visualizzare/nascondere tasks")
    print("   • Export PNG/PDF")
    print("   • Visualizzazione tasks con barre di progresso")
    print("   • Filtri migliorati per client e progetti")
    
    ft.app(target=main)
