#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Google Authentication Fixes - Enterprise Level Verification
"""

import sys
import os
from pathlib import Path
import time

# Add src directory to path
src_dir = Path(__file__).parent / "src"
sys.path.insert(0, str(src_dir))

from core.services.google_tasks_service import GoogleTasksService
from core.services.google_calendar_service import GoogleCalendarService
from core.services.google_drive_service import GoogleDriveService
from core import get_logger

logger = get_logger(__name__)

def test_authentication_consistency():
    """Test that all Google services show consistent authentication status"""
    print("🔍 Testing Google Services Authentication Consistency...")
    
    try:
        # Initialize all services with proper config directory
        from core.config.app_config import AppConfig
        config = AppConfig()
        config_dir = os.path.join(config.data_dir, "config")
        
        print(f"📁 Using config directory: {config_dir}")
        
        # Initialize services
        tasks_service = GoogleTasksService(config_dir=config_dir)
        calendar_service = GoogleCalendarService(config_dir=config_dir)
        drive_service = GoogleDriveService(config_dir=Path(config_dir).parent)
        
        services = {
            "Google Tasks": tasks_service,
            "Google Calendar": calendar_service,
            "Google Drive": drive_service
        }
        
        print("\n📊 Authentication Status:")
        print("-" * 50)
        
        all_authenticated = True
        for name, service in services.items():
            if hasattr(service, 'is_authenticated'):
                # Handle both callable and property attributes
                if callable(service.is_authenticated):
                    auth_status = service.is_authenticated()
                else:
                    auth_status = service.is_authenticated
            elif hasattr(service, 'is_enabled'):
                if callable(service.is_enabled):
                    auth_status = service.is_enabled()
                else:
                    auth_status = service.is_enabled
            else:
                auth_status = False
                
            status_icon = "✅" if auth_status else "❌"
            print(f"{status_icon} {name}: {'Authenticated' if auth_status else 'Not Authenticated'}")
            
            if not auth_status:
                all_authenticated = False
                
            # Show token file status
            if hasattr(service, 'token_file'):
                token_exists = os.path.exists(service.token_file)
                token_icon = "📄" if token_exists else "📭"
                print(f"   {token_icon} Token File: {'Exists' if token_exists else 'Missing'}")
                if token_exists:
                    print(f"   📍 Path: {service.token_file}")
        
        print("-" * 50)
        
        if all_authenticated:
            print("🎉 ALL SERVICES AUTHENTICATED SUCCESSFULLY!")
            
            # Test actual API connections
            print("\n🔗 Testing API Connections...")
            
            # Test Google Tasks connection
            if hasattr(tasks_service, 'test_connection'):
                tasks_conn = tasks_service.test_connection()
                print(f"📝 Google Tasks API: {'✅ Connected' if tasks_conn else '❌ Failed'}")
            
            # Test Google Calendar connection
            if hasattr(calendar_service, 'test_connection'):
                calendar_conn = calendar_service.test_connection()
                print(f"📅 Google Calendar API: {'✅ Connected' if calendar_conn else '❌ Failed'}")
            
            # Test Google Drive connection
            if hasattr(drive_service, 'test_connection'):
                drive_conn = drive_service.test_connection()
                print(f"💾 Google Drive API: {'✅ Connected' if drive_conn else '❌ Failed'}")
                
        else:
            print("⚠️  Some services are not authenticated")
            print("   Go to Settings > Google Services to authenticate")
        
        return all_authenticated
        
    except Exception as e:
        print(f"❌ Error testing authentication: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_startup_speed():
    """Test how quickly services initialize (should be fast with sync init)"""
    print("\n⚡ Testing Service Initialization Speed...")
    
    try:
        from core.config.app_config import AppConfig
        config = AppConfig()
        config_dir = os.path.join(config.data_dir, "config")
        
        # Time the initialization
        start_time = time.time()
        
        tasks_service = GoogleTasksService(config_dir=config_dir)
        calendar_service = GoogleCalendarService(config_dir=config_dir)
        drive_service = GoogleDriveService(config_dir=Path(config_dir).parent)
        
        end_time = time.time()
        init_time = end_time - start_time
        
        print(f"🕐 Initialization Time: {init_time:.3f} seconds")
        
        if init_time < 1.0:
            print("✅ FAST STARTUP - No UI blocking detected")
        elif init_time < 3.0:
            print("⚠️  MODERATE STARTUP - May cause brief UI delay")
        else:
            print("❌ SLOW STARTUP - Will cause UI freezing")
            
        return init_time < 3.0
        
    except Exception as e:
        print(f"❌ Error testing startup speed: {e}")
        return False

def test_settings_sync():
    """Test that settings stay in sync with service state"""
    print("\n⚙️  Testing Settings Synchronization...")
    
    try:
        # Load settings
        settings_file = Path("data/settings.json")
        if not settings_file.exists():
            print("❌ Settings file not found")
            return False
            
        import json
        with open(settings_file, 'r') as f:
            settings = json.load(f)
            
        google_settings = settings.get('google_services_config', {})
        
        print("📋 Current Settings:")
        print(f"   📅 Calendar Enabled: {google_settings.get('calendar_enabled', False)}")
        print(f"   📅 Calendar Authenticated: {google_settings.get('calendar_authenticated', False)}")
        print(f"   📝 Tasks Enabled: {google_settings.get('tasks_enabled', False)}")
        print(f"   📝 Tasks Authenticated: {google_settings.get('tasks_authenticated', False)}")
        print(f"   💾 Drive Enabled: {google_settings.get('drive_enabled', False)}")
        print(f"   💾 Drive Authenticated: {google_settings.get('drive_authenticated', False)}")
        
        # Check if settings match actual service state
        from core.config.app_config import AppConfig
        config = AppConfig()
        config_dir = os.path.join(config.data_dir, "config")
        
        tasks_service = GoogleTasksService(config_dir=config_dir)
        calendar_service = GoogleCalendarService(config_dir=config_dir)
        
        tasks_actual = tasks_service.is_enabled()
        calendar_actual = calendar_service.is_enabled()
        
        tasks_settings = google_settings.get('tasks_authenticated', False)
        calendar_settings = google_settings.get('calendar_authenticated', False)
        
        tasks_sync = (tasks_actual == tasks_settings)
        calendar_sync = (calendar_actual == calendar_settings)
        
        print(f"\n🔄 Settings vs Service State:")
        print(f"   📝 Tasks Sync: {'✅ Match' if tasks_sync else '❌ Mismatch'}")
        print(f"   📅 Calendar Sync: {'✅ Match' if calendar_sync else '❌ Mismatch'}")
        
        return tasks_sync and calendar_sync
        
    except Exception as e:
        print(f"❌ Error testing settings sync: {e}")
        return False

def main():
    """Run all authentication tests"""
    print("🚀 Google Authentication Enterprise-Level Test Suite")
    print("=" * 60)
    
    tests = [
        ("Authentication Consistency", test_authentication_consistency),
        ("Startup Speed", test_startup_speed),
        ("Settings Synchronization", test_settings_sync)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        try:
            result = test_func()
            if result:
                print(f"✅ PASSED: {test_name}")
                passed += 1
            else:
                print(f"❌ FAILED: {test_name}")
        except Exception as e:
            print(f"💥 ERROR in {test_name}: {e}")
    
    print("\n" + "=" * 60)
    print(f"🏆 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED - Enterprise-level authentication achieved!")
    else:
        print("⚠️  Some tests failed - authentication needs improvement")
    
    print("\n💡 RECOMMENDATIONS:")
    if passed == total:
        print("   ✅ Authentication is now enterprise-ready")
        print("   ✅ No more logout issues expected")
        print("   ✅ UI should be smooth and responsive")
    else:
        print("   🔧 Check token files in data/config/")
        print("   🔧 Re-authenticate services in Settings")
        print("   🔧 Restart the application to test fixes")

if __name__ == "__main__":
    main() 