#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cache Manager for Agevolami PM
Provides in-memory caching for frequently accessed data and API responses
"""

import threading
import time
from datetime import datetime, timedelta
from typing import Any, Dict, Optional, Callable, List
from dataclasses import dataclass
from ..utils import get_logger

logger = get_logger(__name__)

@dataclass
class CacheEntry:
    """Represents a cached item with expiration"""
    value: Any
    expires_at: datetime
    created_at: datetime
    access_count: int = 0
    last_accessed: Optional[datetime] = None

class CacheManager:
    """Thread-safe cache manager with TTL support"""
    
    def __init__(self, default_ttl: int = 300):  # 5 minutes default TTL
        self.default_ttl = default_ttl
        self._cache: Dict[str, CacheEntry] = {}
        self._lock = threading.RLock()
        self._cleanup_thread = None
        self._running = True
        self._start_cleanup_thread()
    
    def _start_cleanup_thread(self):
        """Start background thread for cache cleanup"""
        def cleanup_worker():
            while self._running:
                try:
                    self._cleanup_expired()
                    time.sleep(60)  # Cleanup every minute
                except Exception as e:
                    logger.error(f"Cache cleanup error: {e}")
                    time.sleep(60)
        
        self._cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        self._cleanup_thread.start()
        logger.info("Cache cleanup thread started")
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        with self._lock:
            entry = self._cache.get(key)
            if entry is None:
                return None
            
            # Check if expired
            if datetime.now() > entry.expires_at:
                del self._cache[key]
                return None
            
            # Update access statistics
            entry.access_count += 1
            entry.last_accessed = datetime.now()
            
            return entry.value
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set value in cache with TTL"""
        if ttl is None:
            ttl = self.default_ttl
        
        with self._lock:
            now = datetime.now()
            entry = CacheEntry(
                value=value,
                expires_at=now + timedelta(seconds=ttl),
                created_at=now,
                access_count=0,
                last_accessed=None
            )
            self._cache[key] = entry
    
    def delete(self, key: str) -> bool:
        """Delete key from cache"""
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                return True
            return False
    
    def clear(self) -> None:
        """Clear all cache entries"""
        with self._lock:
            self._cache.clear()
            logger.info("Cache cleared")
    
    def get_or_set(self, key: str, factory: Callable[[], Any], ttl: Optional[int] = None) -> Any:
        """Get value from cache or set it using factory function"""
        value = self.get(key)
        if value is not None:
            return value
        
        # Generate value using factory
        value = factory()
        self.set(key, value, ttl)
        return value
    
    def _cleanup_expired(self):
        """Remove expired entries from cache"""
        with self._lock:
            now = datetime.now()
            expired_keys = [
                key for key, entry in self._cache.items()
                if now > entry.expires_at
            ]
            
            for key in expired_keys:
                del self._cache[key]
            
            if expired_keys:
                logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        with self._lock:
            total_entries = len(self._cache)
            total_access_count = sum(entry.access_count for entry in self._cache.values())
            
            return {
                "total_entries": total_entries,
                "total_access_count": total_access_count,
                "average_access_count": total_access_count / total_entries if total_entries > 0 else 0,
                "cache_keys": list(self._cache.keys())
            }
    
    def shutdown(self):
        """Shutdown cache manager"""
        self._running = False
        if self._cleanup_thread and self._cleanup_thread.is_alive():
            self._cleanup_thread.join(timeout=5)
        logger.info("Cache manager shutdown")

# Global cache instance
_global_cache = None
_cache_lock = threading.Lock()

def get_cache() -> CacheManager:
    """Get global cache instance (singleton)"""
    global _global_cache
    if _global_cache is None:
        with _cache_lock:
            if _global_cache is None:
                _global_cache = CacheManager()
    return _global_cache

class GoogleAPICache:
    """Specialized cache for Google API responses"""
    
    def __init__(self, cache_manager: CacheManager):
        self.cache = cache_manager
        self.google_calendar_ttl = 300  # 5 minutes for calendar events
        self.google_tasks_ttl = 180     # 3 minutes for tasks
        self.auth_status_ttl = 60       # 1 minute for auth status
    
    def get_calendar_events(self, calendar_id: str, start_date: str, end_date: str) -> Optional[List[Dict]]:
        """Get cached calendar events"""
        key = f"google_calendar_events:{calendar_id}:{start_date}:{end_date}"
        return self.cache.get(key)
    
    def set_calendar_events(self, calendar_id: str, start_date: str, end_date: str, events: List[Dict]) -> None:
        """Cache calendar events"""
        key = f"google_calendar_events:{calendar_id}:{start_date}:{end_date}"
        self.cache.set(key, events, self.google_calendar_ttl)
    
    def get_task_lists(self) -> Optional[List[Dict]]:
        """Get cached task lists"""
        return self.cache.get("google_task_lists")
    
    def set_task_lists(self, task_lists: List[Dict]) -> None:
        """Cache task lists"""
        self.cache.set("google_task_lists", task_lists, self.google_tasks_ttl)
    
    def get_tasks(self, task_list_id: str) -> Optional[List[Dict]]:
        """Get cached tasks for a task list"""
        key = f"google_tasks:{task_list_id}"
        return self.cache.get(key)
    
    def set_tasks(self, task_list_id: str, tasks: List[Dict]) -> None:
        """Cache tasks for a task list"""
        key = f"google_tasks:{task_list_id}"
        self.cache.set(key, tasks, self.google_tasks_ttl)
    
    def get_auth_status(self, service_type: str) -> Optional[bool]:
        """Get cached authentication status"""
        key = f"google_auth_status:{service_type}"
        return self.cache.get(key)
    
    def set_auth_status(self, service_type: str, is_authenticated: bool) -> None:
        """Cache authentication status"""
        key = f"google_auth_status:{service_type}"
        self.cache.set(key, is_authenticated, self.auth_status_ttl)
    
    def invalidate_calendar_cache(self, calendar_id: str = None) -> None:
        """Invalidate calendar-related cache entries"""
        if calendar_id:
            # Invalidate specific calendar
            keys_to_delete = [key for key in self.cache._cache.keys() 
                            if key.startswith(f"google_calendar_events:{calendar_id}:")]
        else:
            # Invalidate all calendar cache
            keys_to_delete = [key for key in self.cache._cache.keys() 
                            if key.startswith("google_calendar_events:")]
        
        for key in keys_to_delete:
            self.cache.delete(key)
    
    def invalidate_tasks_cache(self, task_list_id: str = None) -> None:
        """Invalidate tasks-related cache entries"""
        if task_list_id:
            self.cache.delete(f"google_tasks:{task_list_id}")
        else:
            # Invalidate all tasks cache
            keys_to_delete = [key for key in self.cache._cache.keys() 
                            if key.startswith("google_tasks:")]
            for key in keys_to_delete:
                self.cache.delete(key)
            
            # Also invalidate task lists
            self.cache.delete("google_task_lists")

# Global Google API cache instance
_google_api_cache = None

def get_google_api_cache() -> GoogleAPICache:
    """Get global Google API cache instance"""
    global _google_api_cache
    if _google_api_cache is None:
        _google_api_cache = GoogleAPICache(get_cache())
    return _google_api_cache
