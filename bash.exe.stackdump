Stack trace:
Frame         Function      Args
0007FFFF5840  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF4740) msys-2.0.dll+0x1FE8E
0007FFFF5840  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF5B18) msys-2.0.dll+0x67F9
0007FFFF5840  000210046832 (000210286019, 0007FFFF56F8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF5840  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF5840  000210068E24 (0007FFFF5850, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF5B20  00021006A225 (0007FFFF5850, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFC725F0000 ntdll.dll
7FFC72210000 KERNEL32.DLL
7FFC6FA60000 KERNELBASE.dll
7FFC722E0000 USER32.dll
000210040000 msys-2.0.dll
7FFC6FA30000 win32u.dll
7FFC71C10000 GDI32.dll
7FFC6F7E0000 gdi32full.dll
7FFC6FE40000 msvcp_win.dll
7FFC6F910000 ucrtbase.dll
7FFC70C20000 advapi32.dll
7FFC711C0000 msvcrt.dll
7FFC70AE0000 sechost.dll
7FFC6FEE0000 bcrypt.dll
7FFC70FE0000 RPCRT4.dll
7FFC6EDF0000 CRYPTBASE.DLL
7FFC6F6E0000 bcryptPrimitives.dll
7FFC721D0000 IMM32.DLL
