#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vista Gantt per Agevolami PM
"""

import flet as ft
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Any, Tuple
from uuid import UUID
import math
import time

from core import get_logger
from core.models.base_models import Project, Deadline, Client, Task, ProjectStatus, DeadlineStatus, Priority, TaskStatus

logger = get_logger(__name__)

class GanttView:
    """Vista Gantt per visualizzare progetti e scadenze in formato timeline"""
    
    def __init__(self, app_instance):
        self.app = app_instance
        self.db = app_instance.db_manager
        
        # Configurazione vista
        self.start_date = None
        self.end_date = None
        self.time_range = "3months"  # 1month, 3months, 6months, 1year
        self.zoom_level = 1.0
        self.filter_client = None
        self.filter_status = None
        self.filter_project = None  # Nuovo filtro per progetti
        self.show_deadlines = True
        self.show_projects = True
        self.show_tasks = False  # Nuovo toggle per tasks

        # Dati
        self.projects = []
        self.deadlines = []
        self.tasks = []  # Nuovo array per tasks
        self.clients = []
        
        # Dimensioni layout
        self.row_height = 40
        self.timeline_height = 30
        self.day_width = 30
        self.header_width = 300
        
        # Colori
        self.project_colors = {
            ProjectStatus.DRAFT: ft.Colors.GREY,
            ProjectStatus.SUBMITTED: ft.Colors.ORANGE,
            ProjectStatus.APPROVED: ft.Colors.BLUE,
            ProjectStatus.IN_PROGRESS: ft.Colors.GREEN,
            ProjectStatus.COMPLETED: ft.Colors.BLUE_GREY,
            ProjectStatus.SUSPENDED: ft.Colors.RED_300,
            ProjectStatus.CANCELLED: ft.Colors.RED
        }
        
        self.priority_colors = {
            Priority.LOW: ft.Colors.GREEN,
            Priority.MEDIUM: ft.Colors.BLUE,
            Priority.HIGH: ft.Colors.ORANGE,
            Priority.CRITICAL: ft.Colors.RED
        }

        self.task_colors = {
            TaskStatus.PENDING: ft.Colors.ORANGE_300,
            TaskStatus.IN_PROGRESS: ft.Colors.BLUE_300,
            TaskStatus.WAITING: ft.Colors.YELLOW_300,
            TaskStatus.COMPLETED: ft.Colors.GREEN_300,
            TaskStatus.CANCELLED: ft.Colors.RED_300
        }
        
        self._calculate_date_range()
        self._load_data()
    
    def _calculate_date_range(self):
        """Calcola il range di date da visualizzare"""
        today = date.today()
        
        if self.time_range == "1month":
            self.start_date = today.replace(day=1)
            next_month = today.replace(day=28) + timedelta(days=4)
            self.end_date = next_month - timedelta(days=next_month.day)
        elif self.time_range == "3months":
            self.start_date = today.replace(day=1) - timedelta(days=30)
            self.end_date = today + timedelta(days=90)
        elif self.time_range == "6months":
            self.start_date = today.replace(day=1) - timedelta(days=60)
            self.end_date = today + timedelta(days=180)
        elif self.time_range == "1year":
            self.start_date = today.replace(day=1, month=1)
            self.end_date = today.replace(day=31, month=12)
        
        # Calcola larghezza timeline
        days_span = (self.end_date - self.start_date).days
        self.timeline_width = days_span * self.day_width * self.zoom_level
    
    def _load_data(self):
        """Carica i dati necessari"""
        try:
            # Carica progetti, scadenze e tasks nel range di date
            self.projects = self.db.get_all_projects()
            self.deadlines = self.db.get_deadlines_by_date_range(self.start_date, self.end_date)
            self.tasks = self.db.get_tasks_by_date_range(self.start_date, self.end_date)
            self.clients = self.db.get_all_clients()

            # Filtra per date
            self.projects = [
                p for p in self.projects
                if self._project_in_date_range(p)
            ]

            logger.info(f"Caricati {len(self.projects)} progetti, {len(self.deadlines)} scadenze e {len(self.tasks)} tasks per Gantt")

        except Exception as e:
            logger.error(f"Errore caricamento dati Gantt: {e}")
            self.projects = []
            self.deadlines = []
            self.tasks = []
            self.clients = []
    
    def _project_in_date_range(self, project: Project) -> bool:
        """Verifica se un progetto è nel range di date"""
        if project.start_date and project.start_date > self.end_date:
            return False
        if project.end_date and project.end_date < self.start_date:
            return False
        return True
    
    def _create_header(self) -> ft.Container:
        """Crea l'header del Gantt"""
        # Selettore range temporale
        time_range_dropdown = ft.Dropdown(
            label="Periodo",
            options=[
                ft.dropdown.Option(text="1 Mese", key="1month"),
                ft.dropdown.Option(text="3 Mesi", key="3months"),
                ft.dropdown.Option(text="6 Mesi", key="6months"),
                ft.dropdown.Option(text="1 Anno", key="1year")
            ],
            value=self.time_range,
            width=120,
            on_change=lambda e: self._change_time_range(e.control.value)
        )
        
        # Zoom controls
        zoom_out_btn = ft.IconButton(
            icon=ft.Icons.ZOOM_OUT,
            tooltip="Zoom Out",
            on_click=lambda _: self._change_zoom(-0.2)
        )
        
        zoom_in_btn = ft.IconButton(
            icon=ft.Icons.ZOOM_IN,
            tooltip="Zoom In",
            on_click=lambda _: self._change_zoom(0.2)
        )
        
        zoom_reset_btn = ft.IconButton(
            icon=ft.Icons.CENTER_FOCUS_STRONG,
            tooltip="Reset Zoom",
            on_click=lambda _: self._reset_zoom()
        )
        
        # Filtri
        filter_client = ft.Dropdown(
            label="Cliente",
            options=[ft.dropdown.Option(text="Tutti", key=None)] + [
                ft.dropdown.Option(text=client.name, key=str(client.id))
                for client in self.clients
            ],
            value=str(self.filter_client) if self.filter_client else None,
            width=180,
            on_change=lambda e: self._update_filter('client', e.control.value)
        )

        filter_project = ft.Dropdown(
            label="Progetto",
            options=[ft.dropdown.Option(text="Tutti", key=None)] + [
                ft.dropdown.Option(text=project.name[:30] + ("..." if len(project.name) > 30 else ""), key=str(project.id))
                for project in self.projects
            ],
            value=str(self.filter_project) if self.filter_project else None,
            width=200,
            on_change=lambda e: self._update_filter('project', e.control.value)
        )

        filter_status = ft.Dropdown(
            label="Stato",
            options=[
                ft.dropdown.Option(text="Tutti", key=None),
                ft.dropdown.Option(text="Bozza", key="bozza"),
                ft.dropdown.Option(text="Presentato", key="presentato"),
                ft.dropdown.Option(text="Approvato", key="approvato"),
                ft.dropdown.Option(text="In corso", key="in_corso"),
                ft.dropdown.Option(text="Completato", key="completato")
            ],
            value=self.filter_status,
            width=140,
            on_change=lambda e: self._update_filter('status', e.control.value)
        )
        
        # Toggle visualizzazione
        show_projects_switch = ft.Switch(
            label="Progetti",
            value=self.show_projects,
            on_change=lambda e: self._toggle_view('projects', e.control.value)
        )

        show_deadlines_switch = ft.Switch(
            label="Scadenze",
            value=self.show_deadlines,
            on_change=lambda e: self._toggle_view('deadlines', e.control.value)
        )

        show_tasks_switch = ft.Switch(
            label="Tasks",
            value=self.show_tasks,
            on_change=lambda e: self._toggle_view('tasks', e.control.value)
        )
        
        # Export buttons
        export_png_btn = ft.IconButton(
            icon=ft.Icons.IMAGE,
            tooltip="Esporta PNG",
            on_click=lambda _: self._export_chart("PNG")
        )

        export_pdf_btn = ft.IconButton(
            icon=ft.Icons.PICTURE_AS_PDF,
            tooltip="Esporta PDF",
            on_click=lambda _: self._export_chart("PDF")
        )

        # Info periodo
        period_info = ft.Text(
            f"Dal {self.start_date.strftime('%d/%m/%Y')} al {self.end_date.strftime('%d/%m/%Y')}",
            size=12,
            color=ft.Colors.GREY_600
        )
        
        return ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Text(
                        "Diagramma di Gantt",
                        size=24,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.GREY_800
                    ),
                    ft.Container(expand=True),
                    time_range_dropdown
                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                
                ft.Row([
                    # Controlli zoom
                    ft.Row([
                        ft.Text("Zoom:", size=12, color=ft.Colors.GREY_600),
                        zoom_out_btn,
                        ft.Text(f"{int(self.zoom_level * 100)}%", size=12, color=ft.Colors.GREY_600),
                        zoom_in_btn,
                        zoom_reset_btn
                    ], spacing=4),

                    # Filtri
                    filter_client,
                    filter_project,
                    filter_status,

                    # Toggle
                    show_projects_switch,
                    show_deadlines_switch,
                    show_tasks_switch,

                    # Export
                    ft.Row([
                        ft.Text("Export:", size=12, color=ft.Colors.GREY_600),
                        export_png_btn,
                        export_pdf_btn
                    ], spacing=4),

                    ft.Container(expand=True),

                    period_info
                ], spacing=12, alignment=ft.MainAxisAlignment.SPACE_BETWEEN)
            ], spacing=12),
            padding=ft.padding.all(20),
            bgcolor=ft.Colors.WHITE,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )
    
    def _create_timeline_header(self) -> ft.Container:
        """Crea l'header della timeline con le date"""
        # Calcola i mesi da visualizzare
        current_date = self.start_date
        date_headers = []
        
        while current_date <= self.end_date:
            # Header del mese
            month_start = current_date.replace(day=1)
            if current_date.month == 12:
                month_end = date(current_date.year + 1, 1, 1) - timedelta(days=1)
            else:
                month_end = date(current_date.year, current_date.month + 1, 1) - timedelta(days=1)
            
            # Calcola larghezza del mese
            visible_start = max(month_start, self.start_date)
            visible_end = min(month_end, self.end_date)
            month_days = (visible_end - visible_start).days + 1
            month_width = month_days * self.day_width * self.zoom_level
            
            if month_width > 0:
                date_headers.append(
                    ft.Container(
                        content=ft.Text(
                            current_date.strftime("%B %Y"),
                            size=12,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.GREY_700,
                            text_align=ft.TextAlign.CENTER
                        ),
                        width=month_width,
                        height=self.timeline_height,
                        bgcolor=ft.Colors.GREY_100,
                        border=ft.border.all(1, ft.Colors.GREY_300),
                        alignment=ft.alignment.center
                    )
                )
            
            # Vai al mese successivo
            if current_date.month == 12:
                current_date = current_date.replace(year=current_date.year + 1, month=1)
            else:
                current_date = current_date.replace(month=current_date.month + 1)
        
        return ft.Container(
            content=ft.Row([
                # Spazio per la colonna dei nomi
                ft.Container(width=self.header_width, height=self.timeline_height),
                # Headers delle date
                ft.Row(date_headers, spacing=0)
            ], spacing=0),
            height=self.timeline_height
        )
    
    def _create_gantt_chart(self) -> ft.Container:
        """Crea il diagramma di Gantt principale"""
        rows = []

        # Progetti filtrati
        filtered_projects = [
            p for p in self.projects
            if self._passes_project_filters(p)
        ] if self.show_projects else []

        # Scadenze filtrate
        filtered_deadlines = [
            d for d in self.deadlines
            if self._passes_deadline_filters(d)
        ] if self.show_deadlines else []

        # Tasks filtrate
        filtered_tasks = [
            t for t in self.tasks
            if self._passes_task_filters(t)
        ] if self.show_tasks else []

        # Combina progetti, scadenze e tasks
        all_items = []

        for project in filtered_projects:
            all_items.append(("project", project))

        for deadline in filtered_deadlines:
            all_items.append(("deadline", deadline))

        for task in filtered_tasks:
            all_items.append(("task", task))

        # Ordina per data di inizio
        all_items.sort(key=lambda x: self._get_item_start_date(x))

        # Crea righe
        for item_type, item in all_items:
            if item_type == "project":
                rows.append(self._create_project_row(item))
            elif item_type == "deadline":
                rows.append(self._create_deadline_row(item))
            else:  # task
                rows.append(self._create_task_row(item))

        if not rows:
            rows.append(
                ft.Container(
                    content=ft.Text(
                        "Nessun elemento da visualizzare nel periodo selezionato",
                        size=14,
                        color=ft.Colors.GREY_600,
                        text_align=ft.TextAlign.CENTER
                    ),
                    height=60,
                    alignment=ft.alignment.center,
                    bgcolor=ft.Colors.GREY_50
                )
            )

        return ft.Container(
            content=ft.Column(rows, spacing=1, scroll=ft.ScrollMode.AUTO),
            expand=True
        )
    
    def _create_project_row(self, project: Project) -> ft.Container:
        """Crea una riga per un progetto"""
        # Trova il cliente
        client_name = "N/A"
        if project.client_id:
            client = next((c for c in self.clients if c.id == project.client_id), None)
            if client:
                client_name = client.name
        
        # Informazioni progetto
        project_info = ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Icon(ft.Icons.FOLDER, size=16, color=ft.Colors.BLUE_600),
                    ft.Text(
                        project.name[:25] + ("..." if len(project.name) > 25 else ""),
                        size=12,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.GREY_800
                    )
                ], spacing=4),
                ft.Text(
                    client_name,
                    size=10,
                    color=ft.Colors.GREY_600
                ),
                ft.Container(
                    content=ft.Text(
                        project.status,
                        size=9,
                        color=ft.Colors.WHITE,
                        weight=ft.FontWeight.BOLD
                    ),
                    padding=ft.padding.symmetric(horizontal=6, vertical=2),
                    bgcolor=self.project_colors.get(project.status, ft.Colors.GREY),
                    border_radius=8
                )
            ], spacing=2),
            width=self.header_width - 10,
            height=self.row_height - 2,
            padding=ft.padding.all(5),
            bgcolor=ft.Colors.WHITE,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )
        
        # Barra del progetto
        project_bar = self._create_project_bar(project)
        
        return ft.Container(
            content=ft.Row([
                project_info,
                project_bar
            ], spacing=0),
            height=self.row_height
        )
    
    def _create_deadline_row(self, deadline: Deadline) -> ft.Container:
        """Crea una riga per una scadenza"""
        # Trova il cliente e il progetto
        client_name = "N/A"
        project_name = "N/A"
        
        if deadline.client_id:
            client = next((c for c in self.clients if c.id == deadline.client_id), None)
            if client:
                client_name = client.name
        
        if deadline.project_id:
            project = next((p for p in self.projects if p.id == deadline.project_id), None)
            if project:
                project_name = project.name[:15] + ("..." if len(project.name) > 15 else "")
        
        # Informazioni scadenza
        deadline_info = ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Icon(ft.Icons.EVENT, size=16, color=ft.Colors.ORANGE_600),
                    ft.Text(
                        deadline.title[:25] + ("..." if len(deadline.title) > 25 else ""),
                        size=12,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.GREY_800
                    )
                ], spacing=4),
                ft.Text(
                    f"{client_name} - {project_name}",
                    size=10,
                    color=ft.Colors.GREY_600
                ),
                ft.Row([
                    ft.Container(
                        content=ft.Text(
                            deadline.priority,
                            size=9,
                            color=ft.Colors.WHITE,
                            weight=ft.FontWeight.BOLD
                        ),
                        padding=ft.padding.symmetric(horizontal=6, vertical=2),
                        bgcolor=self.priority_colors.get(deadline.priority, ft.Colors.GREY),
                        border_radius=8
                    ),
                    ft.Text(
                        deadline.due_date.strftime("%d/%m"),
                        size=9,
                        color=ft.Colors.GREY_600
                    )
                ], spacing=4)
            ], spacing=2),
            width=self.header_width - 10,
            height=self.row_height - 2,
            padding=ft.padding.all(5),
            bgcolor=ft.Colors.WHITE,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )
        
        # Marcatore della scadenza
        deadline_marker = self._create_deadline_marker(deadline)
        
        return ft.Container(
            content=ft.Row([
                deadline_info,
                deadline_marker
            ], spacing=0),
            height=self.row_height
        )
    
    def _create_project_bar(self, project: Project) -> ft.Container:
        """Crea la barra del progetto nella timeline"""
        start_date = project.start_date or self.start_date
        end_date = project.end_date or self.end_date
        
        # Calcola posizione e dimensioni
        days_from_start = (start_date - self.start_date).days
        project_duration = (end_date - start_date).days + 1
        
        x_position = max(0, days_from_start * self.day_width * self.zoom_level)
        bar_width = max(5, project_duration * self.day_width * self.zoom_level)
        
        # Limita la barra ai confini visibili
        if days_from_start < 0:
            bar_width += days_from_start * self.day_width * self.zoom_level
            x_position = 0
        
        max_width = self.timeline_width
        if x_position + bar_width > max_width:
            bar_width = max_width - x_position
        
        if bar_width <= 0:
            return ft.Container(width=self.timeline_width, height=self.row_height)
        
        # Colore della barra
        bar_color = self.project_colors.get(project.status, ft.Colors.GREY)
        
        # Calcola percentuale completamento (mockup)
        completion_percentage = 0
        if project.status == ProjectStatus.COMPLETED:
            completion_percentage = 100
        elif project.status == ProjectStatus.IN_PROGRESS:
            completion_percentage = 60  # Valore esempio
        elif project.status == ProjectStatus.APPROVED:
            completion_percentage = 20
        
        # Barra di completamento
        completion_bar = None
        if completion_percentage > 0:
            completion_width = (bar_width * completion_percentage) / 100
            completion_bar = ft.Container(
                width=completion_width,
                height=self.row_height - 10,
                bgcolor=bar_color,
                border_radius=2
            )
        
        # Container della timeline
        timeline_container = ft.Container(
            width=self.timeline_width,
            height=self.row_height,
            bgcolor=ft.Colors.GREY_50,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )
        
        # Barra del progetto
        project_bar = ft.Container(
            content=ft.Stack([
                # Barra di sfondo
                ft.Container(
                    width=bar_width,
                    height=self.row_height - 10,
                    bgcolor=f"{bar_color}40",  # Colore trasparente
                    border=ft.border.all(2, bar_color),
                    border_radius=4
                ),
                # Barra di completamento
                completion_bar if completion_bar else ft.Container(),
                # Testo del progetto
                ft.Container(
                    content=ft.Text(
                        f"{project.name[:15]}{'...' if len(project.name) > 15 else ''} ({completion_percentage}%)",
                        size=9,
                        color=ft.Colors.GREY_800,
                        weight=ft.FontWeight.BOLD
                    ),
                    padding=ft.padding.symmetric(horizontal=4, vertical=2),
                    alignment=ft.alignment.center_left
                ) if bar_width > 60 else ft.Container()
            ]),
            margin=ft.margin.only(left=x_position, top=5, bottom=5),
            on_click=lambda e: self._show_project_detail(project)
        )
        
        return ft.Stack([
            timeline_container,
            project_bar
        ])
    
    def _create_deadline_marker(self, deadline: Deadline) -> ft.Container:
        """Crea il marcatore della scadenza nella timeline"""
        # Calcola posizione
        days_from_start = (deadline.due_date - self.start_date).days
        x_position = days_from_start * self.day_width * self.zoom_level
        
        # Se fuori dalla vista, non mostrare
        if x_position < 0 or x_position > self.timeline_width:
            return ft.Container(width=self.timeline_width, height=self.row_height)
        
        # Colore del marcatore
        marker_color = self.priority_colors.get(deadline.priority, ft.Colors.BLUE)
        
        # Container della timeline
        timeline_container = ft.Container(
            width=self.timeline_width,
            height=self.row_height,
            bgcolor=ft.Colors.GREY_50,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )
        
        # Marcatore della scadenza
        marker = ft.Container(
            content=ft.Column([
                # Linea verticale
                ft.Container(
                    width=2,
                    height=self.row_height - 15,
                    bgcolor=marker_color
                ),
                # Punto
                ft.Container(
                    width=8,
                    height=8,
                    bgcolor=marker_color,
                    border_radius=4,
                    border=ft.border.all(2, ft.Colors.WHITE)
                )
            ], spacing=0, alignment=ft.MainAxisAlignment.CENTER),
            margin=ft.margin.only(left=x_position - 4, top=2),
            tooltip=f"{deadline.title}\nScadenza: {deadline.due_date.strftime('%d/%m/%Y')}\nPriorità: {deadline.priority}",
            on_click=lambda e: self._show_deadline_detail(deadline)
        )
        
        return ft.Stack([
            timeline_container,
            marker
        ])

    def _create_task_row(self, task: Task) -> ft.Container:
        """Crea una riga per una task"""
        # Trova il cliente e il progetto
        client_name = "N/A"
        project_name = "N/A"

        if task.project_id:
            project = next((p for p in self.projects if p.id == task.project_id), None)
            if project:
                project_name = project.name[:15] + ("..." if len(project.name) > 15 else "")
                if project.client_id:
                    client = next((c for c in self.clients if c.id == project.client_id), None)
                    if client:
                        client_name = client.name

        # Informazioni task
        task_info = ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Icon(ft.Icons.TASK_ALT, size=16, color=ft.Colors.PURPLE_600),
                    ft.Text(
                        task.title[:25] + ("..." if len(task.title) > 25 else ""),
                        size=12,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.GREY_800
                    )
                ], spacing=4),
                ft.Text(
                    f"{client_name} - {project_name}",
                    size=10,
                    color=ft.Colors.GREY_600
                ),
                ft.Row([
                    ft.Container(
                        content=ft.Text(
                            task.status.value if hasattr(task.status, 'value') else str(task.status),
                            size=9,
                            color=ft.Colors.WHITE,
                            weight=ft.FontWeight.BOLD
                        ),
                        padding=ft.padding.symmetric(horizontal=6, vertical=2),
                        bgcolor=self.task_colors.get(task.status, ft.Colors.GREY),
                        border_radius=8
                    ),
                    ft.Container(
                        content=ft.Text(
                            f"{task.progress_percentage}%",
                            size=9,
                            color=ft.Colors.WHITE,
                            weight=ft.FontWeight.BOLD
                        ),
                        padding=ft.padding.symmetric(horizontal=6, vertical=2),
                        bgcolor=ft.Colors.BLUE_600,
                        border_radius=8
                    )
                ], spacing=4)
            ], spacing=2),
            width=self.header_width - 10,
            height=self.row_height - 2,
            padding=ft.padding.all(5),
            bgcolor=ft.Colors.WHITE,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )

        # Barra della task
        task_bar = self._create_task_bar(task)

        return ft.Container(
            content=ft.Row([
                task_info,
                task_bar
            ], spacing=0),
            height=self.row_height
        )

    def _create_task_bar(self, task: Task) -> ft.Container:
        """Crea la barra della task nella timeline"""
        # Determina le date della task
        start_date = None
        end_date = None

        if task.start_date:
            start_date = task.start_date
        elif task.due_date:
            # Se non c'è start_date, usa due_date come punto singolo
            start_date = task.due_date.date() if hasattr(task.due_date, 'date') else task.due_date
        else:
            # Usa created_at come fallback
            start_date = task.created_at.date() if hasattr(task.created_at, 'date') else self.start_date

        if task.due_date:
            end_date = task.due_date.date() if hasattr(task.due_date, 'date') else task.due_date
        else:
            # Se non c'è due_date, mostra come milestone
            end_date = start_date

        # Calcola posizione e dimensioni
        days_from_start = (start_date - self.start_date).days
        task_duration = max(1, (end_date - start_date).days + 1)

        x_position = max(0, days_from_start * self.day_width * self.zoom_level)
        bar_width = max(5, task_duration * self.day_width * self.zoom_level)

        # Limita la barra ai confini visibili
        if days_from_start < 0:
            bar_width += days_from_start * self.day_width * self.zoom_level
            x_position = 0

        max_width = self.timeline_width
        if x_position + bar_width > max_width:
            bar_width = max_width - x_position

        if bar_width <= 0:
            return ft.Container(width=self.timeline_width, height=self.row_height)

        # Colore della barra
        bar_color = self.task_colors.get(task.status, ft.Colors.GREY)

        # Barra di completamento
        completion_percentage = task.progress_percentage
        completion_bar = None
        if completion_percentage > 0:
            completion_width = (bar_width * completion_percentage) / 100
            completion_bar = ft.Container(
                width=completion_width,
                height=self.row_height - 10,
                bgcolor=bar_color,
                border_radius=2
            )

        # Container della timeline
        timeline_container = ft.Container(
            width=self.timeline_width,
            height=self.row_height,
            bgcolor=ft.Colors.GREY_50,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )

        # Barra della task
        task_bar = ft.Container(
            content=ft.Stack([
                # Barra di sfondo
                ft.Container(
                    width=bar_width,
                    height=self.row_height - 10,
                    bgcolor=f"{bar_color}40",  # Colore trasparente
                    border=ft.border.all(2, bar_color),
                    border_radius=4
                ),
                # Barra di completamento
                completion_bar if completion_bar else ft.Container(),
                # Testo della task
                ft.Container(
                    content=ft.Text(
                        f"{task.title[:15]}{'...' if len(task.title) > 15 else ''} ({completion_percentage}%)",
                        size=9,
                        color=ft.Colors.GREY_800,
                        weight=ft.FontWeight.BOLD
                    ),
                    padding=ft.padding.symmetric(horizontal=4, vertical=2),
                    alignment=ft.alignment.center_left
                ) if bar_width > 60 else ft.Container()
            ]),
            margin=ft.margin.only(left=x_position, top=5, bottom=5),
            on_click=lambda e: self._show_task_detail(task)
        )

        return ft.Stack([
            timeline_container,
            task_bar
        ])
    
    def _get_item_start_date(self, item_tuple: Tuple[str, Any]) -> date:
        """Ottiene la data di inizio di un elemento per l'ordinamento"""
        item_type, item = item_tuple
        if item_type == "project":
            return item.start_date or self.start_date
        elif item_type == "deadline":
            return item.due_date
        else:  # task
            if item.start_date:
                return item.start_date
            elif item.due_date:
                return item.due_date.date() if hasattr(item.due_date, 'date') else item.due_date
            else:
                return item.created_at.date() if hasattr(item.created_at, 'date') else self.start_date
    
    def _passes_project_filters(self, project: Project) -> bool:
        """Verifica se un progetto passa i filtri"""
        if self.filter_client and str(project.client_id) != str(self.filter_client):
            return False
        
        if self.filter_status and project.status != self.filter_status:
            return False
        
        return True
    
    def _passes_deadline_filters(self, deadline: Deadline) -> bool:
        """Verifica se una scadenza passa i filtri"""
        if self.filter_client and deadline.client_id and str(deadline.client_id) != str(self.filter_client):
            return False

        if self.filter_project and deadline.project_id and str(deadline.project_id) != str(self.filter_project):
            return False

        return True

    def _passes_task_filters(self, task: Task) -> bool:
        """Verifica se una task passa i filtri"""
        if self.filter_client and task.project_id:
            # Trova il progetto della task per verificare il cliente
            project = next((p for p in self.projects if p.id == task.project_id), None)
            if project and project.client_id and str(project.client_id) != str(self.filter_client):
                return False

        if self.filter_project and task.project_id and str(task.project_id) != str(self.filter_project):
            return False

        return True
    
    def _change_time_range(self, time_range: str):
        """Cambia il range temporale"""
        self.time_range = time_range
        self._calculate_date_range()
        self._load_data()
        self._update_view()
    
    def _change_zoom(self, delta: float):
        """Cambia il livello di zoom"""
        self.zoom_level = max(0.2, min(3.0, self.zoom_level + delta))
        self._calculate_date_range()
        self._update_view()
    
    def _reset_zoom(self):
        """Reset del zoom al livello 1.0"""
        self.zoom_level = 1.0
        self._calculate_date_range()
        self._update_view()
    
    def _update_filter(self, filter_type: str, value: Any):
        """Aggiorna un filtro"""
        if filter_type == "client":
            self.filter_client = UUID(value) if value and value != "None" else None
        elif filter_type == "project":
            self.filter_project = UUID(value) if value and value != "None" else None
        elif filter_type == "status":
            self.filter_status = value if value != "None" else None

        self._update_view()
    
    def _toggle_view(self, view_type: str, value: bool):
        """Toggle visualizzazione progetti/scadenze/tasks"""
        if view_type == "projects":
            self.show_projects = value
        elif view_type == "deadlines":
            self.show_deadlines = value
        elif view_type == "tasks":
            self.show_tasks = value

        self._update_view()
    
    def _update_view(self):
        """Aggiorna la vista corrente"""
        try:
            # Avoid recursive updates
            if hasattr(self, '_updating') and self._updating:
                return
            
            self._updating = True
            
            # Throttle updates - don't update more than once per 100ms
            current_time = time.time()
            if hasattr(self, '_last_update_time'):
                if current_time - self._last_update_time < 0.1:  # 100ms throttle
                    return
            
            self._last_update_time = current_time
            
            # Just trigger a refresh of the current view instead of rebuilding everything
            if hasattr(self.app, 'main_layout') and self.app.main_layout:
                self.app.main_layout.refresh_current_view()
            
        except Exception as e:
            logger.error(f"Errore aggiornamento vista Gantt: {e}")
        finally:
            # Reset the updating flag
            if hasattr(self, '_updating'):
                self._updating = False
    
    def _show_project_detail(self, project: Project):
        """Mostra i dettagli di un progetto"""
        logger.info(f"Mostra dettagli progetto: {project.name}")
        # TODO: Navigare alla vista dettaglio progetto
    
    def _show_deadline_detail(self, deadline: Deadline):
        """Mostra i dettagli di una scadenza"""
        logger.info(f"Mostra dettagli scadenza: {deadline.title}")
        # TODO: Navigare alla vista dettaglio scadenza

    def _show_task_detail(self, task: Task):
        """Mostra i dettagli di una task"""
        logger.info(f"Mostra dettagli task: {task.title}")
        # TODO: Navigare alla vista dettaglio task
    
    def build(self) -> ft.Container:
        """Costruisce la vista Gantt"""
        return ft.Container(
            content=ft.Column([
                self._create_header(),
                ft.Container(height=16),  # Spaziatura
                self._create_timeline_header(),
                self._create_gantt_chart()
            ], spacing=0),
            padding=ft.padding.all(20),
            expand=True
        )
    
    def _export_chart(self, format_type: str):
        """Esporta il diagramma di Gantt"""
        try:
            import os
            from datetime import datetime
            from PIL import Image, ImageDraw, ImageFont

            # Create export directory
            export_dir = "data/exports"
            if not os.path.exists(export_dir):
                os.makedirs(export_dir)

            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{export_dir}/gantt_chart_{timestamp}.{format_type.lower()}"

            # Create image
            self._create_image_export(filename, format_type)

            # Show success message
            if hasattr(self.app, 'page'):
                snack = ft.SnackBar(
                    content=ft.Text(f"📊 Gantt esportato come {format_type}: {filename}"),
                    bgcolor=ft.Colors.GREEN_600
                )
                self.app.page.snack_bar = snack
                snack.open = True
                self.app.page.update()

        except Exception as e:
            logger.error(f"Errore esportazione Gantt: {e}")
            if hasattr(self.app, 'page'):
                snack = ft.SnackBar(
                    content=ft.Text(f"❌ Errore esportazione: {str(e)}"),
                    bgcolor=ft.Colors.RED_600
                )
                self.app.page.snack_bar = snack
                snack.open = True
                self.app.page.update()

    def _create_image_export(self, filename: str, format_type: str):
        """Crea l'immagine di export del Gantt"""
        try:
            from PIL import Image, ImageDraw, ImageFont

            # Dimensioni immagine
            img_width = 1200
            img_height = 800

            # Crea immagine
            image = Image.new('RGB', (img_width, img_height), 'white')
            draw = ImageDraw.Draw(image)

            # Font
            try:
                font_title = ImageFont.truetype("arial.ttf", 16)
                font_normal = ImageFont.truetype("arial.ttf", 12)
                font_small = ImageFont.truetype("arial.ttf", 10)
            except:
                font_title = ImageFont.load_default()
                font_normal = ImageFont.load_default()
                font_small = ImageFont.load_default()

            # Titolo
            title = f"Diagramma di Gantt - {self.start_date.strftime('%d/%m/%Y')} - {self.end_date.strftime('%d/%m/%Y')}"
            draw.text((20, 20), title, fill='black', font=font_title)

            # Legenda
            y_pos = 60
            if self.show_projects:
                draw.rectangle([20, y_pos, 40, y_pos + 15], fill='blue')
                draw.text((50, y_pos), "Progetti", fill='black', font=font_small)
                y_pos += 25

            if self.show_deadlines:
                draw.rectangle([20, y_pos, 40, y_pos + 15], fill='orange')
                draw.text((50, y_pos), "Scadenze", fill='black', font=font_small)
                y_pos += 25

            if self.show_tasks:
                draw.rectangle([20, y_pos, 40, y_pos + 15], fill='purple')
                draw.text((50, y_pos), "Tasks", fill='black', font=font_small)
                y_pos += 25

            # Messaggio semplificato per l'export
            draw.text((20, y_pos + 20), "Export del diagramma di Gantt generato con successo", fill='black', font=font_normal)
            draw.text((20, y_pos + 40), f"Elementi visualizzati: {len(self.projects)} progetti, {len(self.deadlines)} scadenze, {len(self.tasks)} tasks", fill='black', font=font_small)

            # Salva immagine
            if format_type == "PDF":
                image.save(filename, "PDF", resolution=100.0)
            else:
                image.save(filename, format_type)

            logger.info(f"✅ {format_type} export creato: {filename}")

        except Exception as e:
            logger.error(f"❌ Errore creazione {format_type} export: {e}")
            raise e

    def refresh_data(self):
        """Aggiorna i dati del Gantt"""
        self._load_data()
        # Don't call _update_view here to avoid loops - let the main layout handle UI updates