#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Componente Sidebar per Agevolami PM
"""

import flet as ft
from typing import Callable, Optional
from datetime import datetime
from core import get_logger

class Sidebar:
    """Componente sidebar per la navigazione"""
    
    def __init__(self, on_menu_click: Callable[[str], None], on_alert_click: Callable[[], None]):
        self.on_menu_click = on_menu_click
        self.on_alert_click = on_alert_click
        self.active_menu = "dashboard"
        self.alert_count = 0
        
        # Menu items
        self.menu_items = [
            {
                "key": "dashboard",
                "title": "Dashboard",
                "icon": ft.Icons.DASHBOARD,
                "description": "Panoramica generale"
            },
            {
                "key": "calendar",
                "title": "Calendario",
                "icon": ft.Icons.CALENDAR_MONTH,
                "description": "Vista calendario scadenze"
            },
            {
                "key": "gantt",
                "title": "Gantt",
                "icon": ft.Icons.TIMELINE,
                "description": "Diagramma temporale progetti"
            },
            {
                "key": "clients",
                "title": "Clienti",
                "icon": ft.Icons.PEOPLE,
                "description": "Gestione clienti"
            },
            {
                "key": "projects",
                "title": "Progetti",
                "icon": ft.Icons.WORK,
                "description": "Gestione progetti"
            },
            {
                "key": "sals",
                "title": "SAL",
                "icon": ft.Icons.TIMELINE,
                "description": "Stati Avanzamento Lavori"
            },
            {
                "key": "tasks",
                "title": "Attività",
                "icon": ft.Icons.TASK_ALT,
                "description": "Gestione attività"
            },
            {
                "key": "deadlines",
                "title": "Scadenze",
                "icon": ft.Icons.SCHEDULE,
                "description": "Gestione scadenze"
            },
            {
                "key": "incentives",
                "title": "Incentivi",
                "icon": ft.Icons.TRENDING_UP,
                "description": "Monitoraggio incentivi finanziari"
            }
        ]
    
    def _create_menu_item(self, item: dict) -> ft.Container:
        """Crea un elemento del menu"""
        is_active = item["key"] == self.active_menu
        
        # Colori basati sullo stato
        if is_active:
            bg_color = ft.Colors.BLUE_100
            text_color = ft.Colors.BLUE_800
            icon_color = ft.Colors.BLUE_600
        else:
            bg_color = ft.Colors.TRANSPARENT
            text_color = ft.Colors.GREY_700
            icon_color = ft.Colors.GREY_600
        
        # Badge per le scadenze
        badge = None
        if item["key"] == "deadlines" and self.alert_count > 0:
            badge = ft.Container(
                content=ft.Text(
                    str(self.alert_count) if self.alert_count < 100 else "99+",
                    size=10,
                    color=ft.Colors.WHITE,
                    weight=ft.FontWeight.BOLD
                ),
                bgcolor=ft.Colors.RED,
                border_radius=10,
                padding=ft.padding.symmetric(horizontal=6, vertical=2),
                margin=ft.margin.only(left=5)
            )
        
        # Contenuto del menu item
        content = ft.Row([
            ft.Icon(
                item["icon"],
                color=icon_color,
                size=20
            ),
            ft.Column([
                ft.Row([
                    ft.Text(
                        item["title"],
                        size=14,
                        weight=ft.FontWeight.W_500,
                        color=text_color
                    ),
                    badge if badge else ft.Container()
                ], spacing=0),
                ft.Text(
                    item["description"],
                    size=11,
                    color=ft.Colors.GREY_500
                )
            ], spacing=2, expand=True)
        ], spacing=12, alignment=ft.MainAxisAlignment.START)
        
        return ft.Container(
            content=content,
            padding=ft.padding.symmetric(horizontal=16, vertical=12),
            margin=ft.margin.symmetric(horizontal=8, vertical=2),
            bgcolor=bg_color,
            border_radius=8,
            on_click=lambda _: self._handle_menu_click(item["key"]),
            animate=ft.Animation(200, ft.AnimationCurve.EASE_OUT)
        )
    
    def _handle_menu_click(self, menu_key: str):
        """Gestisce il click su un menu item"""
        if menu_key != self.active_menu:
            self.set_active_menu(menu_key)
            self.on_menu_click(menu_key)
    
    def _create_alert_button(self) -> ft.Container:
        """Crea il pulsante per gli alert"""
        # Badge per il contatore
        badge = None
        if self.alert_count > 0:
            badge = ft.Container(
                content=ft.Text(
                    str(self.alert_count) if self.alert_count < 100 else "99+",
                    size=10,
                    color=ft.Colors.WHITE,
                    weight=ft.FontWeight.BOLD
                ),
                bgcolor=ft.Colors.RED,
                border_radius=10,
                padding=ft.padding.symmetric(horizontal=6, vertical=2),
                right=-5,
                top=-5
            )
        
        button_content = ft.Stack([
            ft.Icon(
                ft.Icons.NOTIFICATIONS,
                color=ft.Colors.ORANGE_600,
                size=24
            ),
            badge if badge else ft.Container()
        ])
        
        return ft.Container(
            content=button_content,
            padding=12,
            margin=ft.margin.symmetric(horizontal=8, vertical=4),
            bgcolor=ft.Colors.ORANGE_50,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.ORANGE_200),
            on_click=lambda _: self.on_alert_click(),
            tooltip="Visualizza alert attivi"
        )
    
    def _create_logo_section(self) -> ft.Container:
        """Crea la sezione logo/titolo"""
        return ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Image(
                        src="assets/logo_v2.png",
                        width=40,
                        height=40,
                        fit=ft.ImageFit.CONTAIN
                    ),
                    ft.Column([
                        ft.Text(
                            "Agevolami",
                            size=18,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.BLUE_800
                        ),
                        ft.Text(
                            "Project Manager",
                            size=11,
                            color=ft.Colors.GREY_600
                        )
                    ], spacing=0)
                ], spacing=12),
                
                ft.Divider(color=ft.Colors.GREY_300, height=1)
            ], spacing=16),
            padding=ft.padding.all(16)
        )
    
    def _create_footer_section(self) -> ft.Container:
        """Crea la sezione footer"""
        return ft.Container(
            content=ft.Column([
                ft.Divider(color=ft.Colors.GREY_300, height=1),
                
                ft.Row([
                    ft.Icon(
                        ft.Icons.INFO_OUTLINE,
                        color=ft.Colors.GREY_500,
                        size=16
                    ),
                    ft.Text(
                        "v2.0.0",
                        size=11,
                        color=ft.Colors.GREY_500
                    )
                ], spacing=8),
                
                ft.Text(
                    f"© {datetime.now().year} Agevolami.it",
                    size=10,
                    color=ft.Colors.GREY_400
                )
            ], spacing=8),
            padding=ft.padding.all(16)
        )
    
    def build(self) -> ft.Container:
        """Costruisce la sidebar"""
        # Menu items
        menu_controls = [self._create_menu_item(item) for item in self.menu_items]
        
        return ft.Container(
            content=ft.Column([
                # Logo/Titolo
                self._create_logo_section(),
                
                # Alert button
                self._create_alert_button(),
                
                # Spazio
                ft.Container(height=8),
                
                # Scrollable menu items
                ft.Container(
                    content=ft.Column(
                        controls=menu_controls,
                        spacing=0,
                        scroll=ft.ScrollMode.AUTO
                    ),
                    expand=True
                ),
                
                # Footer
                self._create_footer_section()
            ], spacing=0),
            width=280,
            bgcolor=ft.Colors.WHITE,
            border=ft.border.only(right=ft.BorderSide(1, ft.Colors.GREY_200)),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=4,
                color=ft.Colors.BLACK12
            )
        )
    
    def set_active_menu(self, menu_key: str):
        """Imposta il menu attivo"""
        self.active_menu = menu_key
    
    def update_alert_count(self, count: int):
        """Aggiorna il contatore degli alert"""
        self.alert_count = count
    
    def get_active_menu(self) -> str:
        """Restituisce il menu attivo"""
        return self.active_menu