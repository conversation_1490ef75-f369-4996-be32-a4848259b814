#!/usr/bin/env python3
"""
Windows build script for GitHub Actions
Avoids console encoding issues by using Python subprocess
"""

import os
import sys
import subprocess
import locale

def set_utf8_encoding():
    """Set UTF-8 encoding for the environment"""
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'
    os.environ['PYTHONLEGACYWINDOWSSTDIO'] = '1'
    
    # Try to set console to UTF-8
    try:
        if sys.platform == 'win32':
            subprocess.run(['chcp', '65001'], shell=True, capture_output=True)
    except:
        pass

def run_flet_build():
    """Run flet build with proper encoding handling"""
    try:
        # Set encoding
        set_utf8_encoding()
        
        print("Starting Flet build process...")
        print(f"Python version: {sys.version}")
        print(f"Platform: {sys.platform}")
        print(f"Locale: {locale.getpreferredencoding()}")
        
        # Verify flet is installed and accessible
        try:
            import flet
            try:
                print(f"Flet version: {flet.__version__}")
            except AttributeError:
                print("Flet module loaded successfully (version info not available)")
        except ImportError:
            print("❌ Flet not found! Installing...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'flet'], check=True)
            import flet
            try:
                print(f"Flet version: {flet.__version__}")
            except AttributeError:
                print("Flet module loaded successfully (version info not available)")
        
        # Check if flet command is available
        try:
            result = subprocess.run(['flet', '--version'], 
                                 capture_output=True, text=True, check=True)
            print(f"Flet CLI available: {result.stdout.strip()}")
        except subprocess.CalledProcessError as e:
            print(f"❌ Flet CLI test failed: {e}")
            return False
        except FileNotFoundError:
            print("❌ Flet command not found in PATH")
            return False
        
        # Run the build command using flet directly
        cmd = ['flet', 'build', 'windows']
        
        print(f"Running command: {' '.join(cmd)}")
        
        # Use subprocess with proper encoding handling
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            encoding='utf-8',
            errors='replace',
            bufsize=1,
            universal_newlines=True
        )
        
        # Stream output in real-time
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                print(output.strip())
        
        # Get the return code
        return_code = process.poll()
        
        if return_code == 0:
            print("✅ Build completed successfully!")
            return True
        else:
            print(f"❌ Build failed with return code: {return_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error during build: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    print("🔨 Windows Build Script for GitHub Actions")
    print("=" * 50)
    
    success = run_flet_build()
    
    if success:
        print("\n🎉 Build process completed successfully!")
        
        # Check for build output directories
        build_found = False

        # Check for build/windows directory (default Flet output)
        if os.path.exists('build/windows'):
            print("📁 Build artifacts found in 'build/windows' directory:")
            for root, dirs, files in os.walk('build/windows'):
                for file in files:
                    file_path = os.path.join(root, file)
                    size = os.path.getsize(file_path)
                    print(f"  - {file_path} ({size:,} bytes)")
            build_found = True

        # Check for dist directory (alternative output)
        if os.path.exists('dist'):
            print("📁 Build artifacts found in 'dist' directory:")
            for root, dirs, files in os.walk('dist'):
                for file in files:
                    file_path = os.path.join(root, file)
                    size = os.path.getsize(file_path)
                    print(f"  - {file_path} ({size:,} bytes)")
            build_found = True

        # Check for any other build-related directories
        if not build_found:
            print("⚠️  Warning: No build output found in expected directories")
            print("🔍 Searching for build-related directories...")
            for item in os.listdir('.'):
                if os.path.isdir(item) and ('build' in item.lower() or 'dist' in item.lower() or 'output' in item.lower()):
                    print(f"  Found directory: {item}")
                    for root, dirs, files in os.walk(item):
                        for file in files:
                            file_path = os.path.join(root, file)
                            size = os.path.getsize(file_path)
                            print(f"    - {file_path} ({size:,} bytes)")
        
        sys.exit(0)
    else:
        print("\n❌ Build process failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
