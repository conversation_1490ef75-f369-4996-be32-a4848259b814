#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LLM Service for Incentives Analysis
Uses OpenRouter API to analyze and summarize Italian financial incentives
"""

import openai
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import json
import time

from core.utils.logger import get_logger
from core.models.incentive_models import IncentiveItem, IncentivePriority

logger = get_logger(__name__)

class LLMService:
    """Service for LLM-powered incentives analysis using OpenRouter"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.api_key = config.get('openrouter_api_key', '')
        self.model = config.get('llm_model', 'gpt-3.5-turbo')
        self.api_url = config.get('llm_api_url', 'https://openrouter.ai/api/v1')
        self.enabled = config.get('llm_enabled', True) and bool(self.api_key)

        if self.enabled:
            # Configure OpenAI client for OpenRouter (or custom API)
            self.client = openai.OpenAI(
                api_key=self.api_key,
                base_url=self.api_url
            )
            logger.info(f"LLM service initialized with model: {self.model}, API: {self.api_url}")
        else:
            self.client = None
            logger.warning("LLM service disabled - no API key provided")
    
    def analyze_incentive(self, item: IncentiveItem) -> Tuple[str, float, IncentivePriority]:
        """Analyze a single incentive item and return summary, relevance score, and priority"""
        logger.debug(f"Starting LLM analysis for: {item.title[:50]}...")
        logger.debug(f"LLM service enabled: {self.enabled}")
        logger.debug(f"API key present: {bool(self.api_key)}")
        logger.debug(f"Model: {self.model}")

        if not self.enabled:
            logger.warning("LLM analysis not available - service disabled")
            return "LLM analysis not available", 0.0, IncentivePriority.MEDIUM

        try:
            # Prepare content for analysis
            content = f"""
            Titolo: {item.title}
            Descrizione: {item.description}
            Fonte: {item.source.value}
            URL: {item.source_url}
            Contenuto completo: {item.raw_content[:1000]}...
            """

            logger.debug(f"Content prepared for analysis: {len(content)} characters")

            # Create analysis prompt
            prompt = self._create_analysis_prompt(content)
            logger.debug(f"Analysis prompt created: {len(prompt)} characters")

            # Call LLM
            logger.info(f"Calling LLM API with model: {self.model}")
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": self._get_system_prompt()},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=500
            )

            logger.debug(f"LLM API response received")

            # Parse response
            analysis_text = response.choices[0].message.content
            logger.debug(f"Analysis text received: {len(analysis_text)} characters")

            summary, relevance_score, priority = self._parse_analysis_response(analysis_text)

            logger.info(f"LLM analysis completed for: {item.title[:50]}... (score: {relevance_score}, priority: {priority.value})")
            return summary, relevance_score, priority

        except Exception as e:
            logger.error(f"Error in LLM analysis for '{item.title[:50]}...': {e}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            return f"Errore nell'analisi: {str(e)}", 0.0, IncentivePriority.MEDIUM
    
    def analyze_multiple_incentives(self, items: List[IncentiveItem]) -> List[Tuple[str, float, IncentivePriority]]:
        """Analyze multiple incentives with rate limiting"""
        results = []
        
        for i, item in enumerate(items):
            try:
                summary, score, priority = self.analyze_incentive(item)
                results.append((summary, score, priority))
                
                # Rate limiting - delay between requests
                if i < len(items) - 1:  # Don't delay after the last item
                    time.sleep(1.0)  # 1 second delay
                    
            except Exception as e:
                logger.error(f"Error analyzing item {i}: {e}")
                results.append(("Errore nell'analisi", 0.0, IncentivePriority.MEDIUM))
        
        return results
    
    def generate_summary_report(self, items: List[IncentiveItem]) -> str:
        """Generate a comprehensive summary report of all incentives"""
        if not self.enabled or not items:
            return "Report non disponibile"
        
        try:
            # Prepare items summary
            items_summary = []
            for item in items[:10]:  # Limit to first 10 items
                items_summary.append(f"- {item.title} ({item.source.value}): {item.description[:100]}...")
            
            content = "\n".join(items_summary)
            
            prompt = f"""
            Analizza questi incentivi finanziari italiani e crea un report riassuntivo professionale:

            INCENTIVI TROVATI:
            {content}

            Crea un report che includa:
            1. Panoramica generale degli incentivi trovati
            2. Categorie principali identificate
            3. Opportunità più interessanti
            4. Raccomandazioni per le aziende
            5. Scadenze importanti da monitorare

            Il report deve essere professionale, conciso e utile per consulenti aziendali.
            """
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "Sei un esperto consulente in incentivi finanziari italiani. Crea report professionali e utili."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.4,
                max_tokens=1000
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"Error generating summary report: {e}")
            return f"Errore nella generazione del report: {str(e)}"
    
    def _get_system_prompt(self) -> str:
        """Get the system prompt for incentive analysis"""
        return """
        Sei un esperto analista di incentivi finanziari italiani. Il tuo compito è analizzare incentivi, bandi e finanziamenti per le imprese italiane.

        Per ogni incentivo che analizzi, devi:
        1. Creare un riassunto chiaro e professionale (max 300 parole)
        2. Estrarre tutte le date importanti (apertura, scadenza, etc.)
        3. Identificare importi e percentuali di finanziamento
        4. Assegnare un punteggio di rilevanza da 0.0 a 1.0 (dove 1.0 = molto rilevante per le PMI italiane)
        5. Determinare la priorità: HIGH (scadenza imminente/molto importante), MEDIUM (interessante), LOW (poco rilevante)

        Concentrati su:
        - Beneficiari target (PMI, startup, grandi imprese, settori specifici)
        - Importi disponibili e percentuali di copertura
        - Date di apertura e scadenza bandi
        - Settori interessati
        - Requisiti principali e criteri di ammissibilità
        - Tipologia di spese ammissibili
        - Modalità di erogazione (contributo a fondo perduto, finanziamento agevolato, etc.)

        Rispondi sempre in italiano e in formato JSON:
        {
            "summary": "riassunto professionale dettagliato",
            "relevance_score": 0.8,
            "priority": "HIGH|MEDIUM|LOW",
            "target_beneficiaries": "PMI, startup, etc.",
            "amount": "importo disponibile o percentuale",
            "opening_date": "data apertura (YYYY-MM-DD o null)",
            "deadline": "data scadenza (YYYY-MM-DD o null)",
            "sectors": ["settore1", "settore2"],
            "requirements": ["requisito1", "requisito2"],
            "eligible_expenses": ["spesa1", "spesa2"],
            "funding_type": "contributo a fondo perduto/finanziamento agevolato/altro",
            "coverage_percentage": "percentuale di copertura",
            "key_points": ["punto chiave 1", "punto chiave 2"]
        }
        """
    
    def _create_analysis_prompt(self, content: str) -> str:
        """Create analysis prompt for a specific incentive"""
        return f"""
        Analizza questo incentivo finanziario italiano:

        {content}

        Fornisci un'analisi completa seguendo il formato JSON richiesto.
        """
    
    def _parse_analysis_response(self, response_text: str) -> Tuple[str, float, IncentivePriority]:
        """Parse LLM response and extract summary, score, and priority"""
        try:
            # Try to parse as JSON first
            if '{' in response_text and '}' in response_text:
                json_start = response_text.find('{')
                json_end = response_text.rfind('}') + 1
                json_text = response_text[json_start:json_end]
                
                data = json.loads(json_text)

                # Build enhanced summary with all extracted information
                base_summary = data.get('summary', response_text[:200])

                # Add structured information to summary
                enhanced_summary = base_summary

                if data.get('deadline'):
                    enhanced_summary += f"\n📅 Scadenza: {data.get('deadline')}"
                if data.get('opening_date'):
                    enhanced_summary += f"\n🚀 Apertura: {data.get('opening_date')}"
                if data.get('amount'):
                    enhanced_summary += f"\n💰 Importo: {data.get('amount')}"
                if data.get('coverage_percentage'):
                    enhanced_summary += f"\n📊 Copertura: {data.get('coverage_percentage')}"
                if data.get('target_beneficiaries'):
                    enhanced_summary += f"\n🎯 Destinatari: {data.get('target_beneficiaries')}"
                if data.get('funding_type'):
                    enhanced_summary += f"\n🏦 Tipo: {data.get('funding_type')}"

                relevance_score = float(data.get('relevance_score', 0.5))
                priority_str = data.get('priority', 'MEDIUM').upper()

                # Convert priority string to enum
                if priority_str == 'HIGH':
                    priority = IncentivePriority.HIGH
                elif priority_str == 'LOW':
                    priority = IncentivePriority.LOW
                else:
                    priority = IncentivePriority.MEDIUM

                return enhanced_summary, relevance_score, priority
            
        except (json.JSONDecodeError, KeyError, ValueError) as e:
            logger.warning(f"Could not parse JSON response, using fallback: {e}")
        
        # Fallback parsing
        summary = response_text[:300] if response_text else "Analisi non disponibile"
        
        # Simple heuristics for score and priority
        text_lower = response_text.lower()
        if any(word in text_lower for word in ['urgente', 'scadenza', 'importante', 'alto']):
            priority = IncentivePriority.HIGH
            relevance_score = 0.8
        elif any(word in text_lower for word in ['interessante', 'medio', 'buono']):
            priority = IncentivePriority.MEDIUM
            relevance_score = 0.6
        else:
            priority = IncentivePriority.LOW
            relevance_score = 0.4
        
        return summary, relevance_score, priority
    
    def test_connection(self) -> bool:
        """Test connection to OpenRouter API"""
        if not self.enabled:
            return False
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "user", "content": "Test connection. Rispondi solo 'OK'."}
                ],
                max_tokens=10
            )
            
            return "ok" in response.choices[0].message.content.lower()
            
        except Exception as e:
            logger.error(f"LLM connection test failed: {e}")
            return False
    
    def get_available_models(self) -> List[str]:
        """Get list of available models from OpenRouter"""
        try:
            # This would require a separate API call to OpenRouter's models endpoint
            # For now, return common models
            return [
                "gpt-4",
                "gpt-3.5-turbo",
                "claude-3-sonnet",
                "claude-3-haiku"
            ]
        except Exception as e:
            logger.error(f"Error getting available models: {e}")
            return ["gpt-3.5-turbo"]
