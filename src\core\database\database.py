#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gestione database SQLite per Agevolami PM
"""

import sqlite3
import json
from datetime import datetime, date
from decimal import Decimal
from pathlib import Path
from typing import Optional, List, Dict, Any, Union
from uuid import UUID, uuid4
from contextlib import contextmanager

from ..models import (
    Client, Project, SAL, Deadline, Alert, Document, EmailTemplate,
    ProjectStatus, DeadlineStatus, AlertStatus, ProjectCategory, ProjectSubcategory, ProjectType, Priority
)
from ..utils import get_logger

logger = get_logger(__name__)

class DatabaseManager:
    """Gestore del database SQLite"""
    
    def __init__(self, db_path: Path):
        self.db_path = db_path
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self._init_database()
    
    def _init_database(self):
        """Inizializza il database e crea le tabelle"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # Abilita foreign keys
            cursor.execute("PRAGMA foreign_keys = ON")
            
            # Tabella clienti
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS clients (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    business_name TEXT,
                    vat_number TEXT,
                    tax_code TEXT,
                    email TEXT,
                    phone TEXT,
                    address TEXT,
                    city TEXT,
                    province TEXT,
                    postal_code TEXT,
                    notes TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TEXT NOT NULL,
                    updated_at TEXT
                )
            """)
            
            # Tabella progetti
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS projects (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    category TEXT NOT NULL DEFAULT 'incentivi_pubblici',
                    subcategory TEXT NOT NULL DEFAULT 'ricerca_innovazione',
                    project_type TEXT NOT NULL,
                    client_id TEXT NOT NULL,
                    status TEXT DEFAULT 'bozza',
                    priority TEXT DEFAULT 'media',
                    start_date TEXT,
                    end_date TEXT,
                    submission_date TEXT,
                    approval_date TEXT,
                    budget REAL,
                    approved_amount REAL,
                    requires_sal BOOLEAN DEFAULT 0,
                    sal_count INTEGER DEFAULT 0,
                    notes TEXT,
                    tags TEXT,
                    created_at TEXT NOT NULL,
                    updated_at TEXT,
                    FOREIGN KEY (client_id) REFERENCES clients (id)
                )
            """)
            
            # Tabella SAL
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS sals (
                    id TEXT PRIMARY KEY,
                    project_id TEXT NOT NULL,
                    name TEXT NOT NULL,
                    description TEXT,
                    sequence_number INTEGER NOT NULL,
                    sal_type TEXT DEFAULT 'milestone',
                    milestone_type TEXT,
                    percentage REAL NOT NULL,
                    status TEXT DEFAULT 'bozza',
                    planned_start_date TEXT,
                    planned_end_date TEXT,
                    actual_start_date TEXT,
                    actual_end_date TEXT,
                    budget REAL,
                    actual_cost REAL,
                    template_id TEXT,
                    auto_generated BOOLEAN DEFAULT 0,
                    requires_approval BOOLEAN DEFAULT 0,
                    approved_by TEXT,
                    approved_at TEXT,
                    notes TEXT,
                    created_at TEXT NOT NULL,
                    updated_at TEXT,
                    FOREIGN KEY (project_id) REFERENCES projects (id)
                )
            """)
            
            # Tabella scadenze
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS deadlines (
                    id TEXT PRIMARY KEY,
                    title TEXT NOT NULL,
                    description TEXT,
                    due_date TEXT NOT NULL,
                    status TEXT DEFAULT 'in_attesa',
                    priority TEXT DEFAULT 'media',
                    client_id TEXT,
                    project_id TEXT,
                    sal_id TEXT,
                    alert_days_before INTEGER DEFAULT 15,
                    email_notifications BOOLEAN DEFAULT 1,
                    completed_date TEXT,
                    completed_by TEXT,
                    notes TEXT,
                    recurrence_type TEXT DEFAULT 'nessuna',
                    recurrence_interval INTEGER DEFAULT 1,
                    recurrence_end_date TEXT,
                    parent_deadline_id TEXT,
                    is_recurring BOOLEAN DEFAULT 0,
                    created_at TEXT NOT NULL,
                    updated_at TEXT,
                    FOREIGN KEY (client_id) REFERENCES clients (id),
                    FOREIGN KEY (project_id) REFERENCES projects (id),
                    FOREIGN KEY (sal_id) REFERENCES sals (id)
                )
            """)
            
            # Tabella alert
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS alerts (
                    id TEXT PRIMARY KEY,
                    deadline_id TEXT NOT NULL,
                    title TEXT NOT NULL,
                    message TEXT NOT NULL,
                    status TEXT DEFAULT 'attivo',
                    priority TEXT DEFAULT 'media',
                    days_before_deadline INTEGER NOT NULL,
                    email_sent BOOLEAN DEFAULT 0,
                    email_sent_at TEXT,
                    dismissed_at TEXT,
                    dismissed_by TEXT,
                    metadata TEXT,
                    created_at TEXT NOT NULL,
                    updated_at TEXT,
                    FOREIGN KEY (deadline_id) REFERENCES deadlines (id)
                )
            """)
            
            # Tabella documenti
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS documents (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    file_path TEXT NOT NULL,
                    file_size INTEGER NOT NULL,
                    mime_type TEXT,
                    client_id TEXT,
                    project_id TEXT,
                    sal_id TEXT,
                    deadline_id TEXT,
                    tags TEXT,
                    is_public BOOLEAN DEFAULT 0,
                    created_at TEXT NOT NULL,
                    updated_at TEXT,
                    FOREIGN KEY (client_id) REFERENCES clients (id),
                    FOREIGN KEY (project_id) REFERENCES projects (id),
                    FOREIGN KEY (sal_id) REFERENCES sals (id),
                    FOREIGN KEY (deadline_id) REFERENCES deadlines (id)
                )
            """)
            
            # Tabella tasks
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS tasks (
                    id TEXT PRIMARY KEY,
                    title TEXT NOT NULL,
                    description TEXT,
                    deadline_id TEXT,  -- Nullable - task can be attached directly to project
                    project_id TEXT,   -- Nullable - allows standalone tasks
                    parent_task_id TEXT,  -- Nullable - for subtasks
                    assigned_to TEXT,
                    status TEXT DEFAULT 'in_attesa',
                    priority TEXT DEFAULT 'media',
                    estimated_hours REAL,
                    actual_hours REAL,
                    due_date TEXT,
                    start_date TEXT,
                    completed_date TEXT,
                    progress_percentage INTEGER DEFAULT 0,
                    depends_on_tasks TEXT,
                    google_task_id TEXT,
                    google_task_list_id TEXT,
                    sync_to_google_tasks BOOLEAN DEFAULT 1,
                    notes TEXT,
                    tags TEXT,
                    created_at TEXT NOT NULL,
                    updated_at TEXT,
                    FOREIGN KEY (deadline_id) REFERENCES deadlines (id),
                    FOREIGN KEY (project_id) REFERENCES projects (id),
                    FOREIGN KEY (parent_task_id) REFERENCES tasks (id)
                )
            """)

            # Tabella incentivi
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS incentives (
                    id TEXT PRIMARY KEY,
                    title TEXT NOT NULL,
                    description TEXT,
                    source TEXT NOT NULL,
                    source_url TEXT,
                    content_hash TEXT UNIQUE,
                    found_date TEXT NOT NULL,
                    publication_date TEXT,
                    deadline_date TEXT,
                    status TEXT DEFAULT 'new',
                    priority TEXT DEFAULT 'medium',
                    tags TEXT,
                    keywords_matched TEXT,
                    llm_summary TEXT,
                    llm_relevance_score REAL DEFAULT 0.0,
                    llm_analysis_date TEXT,
                    raw_content TEXT,
                    metadata TEXT,
                    user_notes TEXT,
                    is_favorite BOOLEAN DEFAULT 0,
                    created_at TEXT NOT NULL,
                    updated_at TEXT
                )
            """)

            # Tabella sessioni di monitoraggio
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS monitoring_sessions (
                    id TEXT PRIMARY KEY,
                    start_time TEXT NOT NULL,
                    end_time TEXT,
                    sources_checked TEXT,
                    keywords_used TEXT,
                    items_found INTEGER DEFAULT 0,
                    new_items INTEGER DEFAULT 0,
                    errors_encountered TEXT,
                    status TEXT DEFAULT 'running',
                    success BOOLEAN DEFAULT 0
                )
            """)
            
            # Tabella template email
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS email_templates (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    subject TEXT NOT NULL,
                    body TEXT NOT NULL,
                    template_type TEXT NOT NULL,
                    available_variables TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TEXT NOT NULL,
                    updated_at TEXT
                )
            """)
            
            # Aggiungi colonne ricorrenza se non esistono (per database esistenti)
            try:
                cursor.execute("ALTER TABLE deadlines ADD COLUMN recurrence_type TEXT DEFAULT 'nessuna'")
            except sqlite3.OperationalError:
                pass  # Colonna già esistente
            
            try:
                cursor.execute("ALTER TABLE deadlines ADD COLUMN recurrence_interval INTEGER DEFAULT 1")
            except sqlite3.OperationalError:
                pass  # Colonna già esistente
            
            try:
                cursor.execute("ALTER TABLE deadlines ADD COLUMN is_recurring BOOLEAN DEFAULT 0")
            except sqlite3.OperationalError:
                pass  # Colonna già esistente
            
            try:
                cursor.execute("ALTER TABLE deadlines ADD COLUMN recurrence_end_date TEXT")
            except sqlite3.OperationalError:
                pass  # Colonna già esistente
            
            try:
                cursor.execute("ALTER TABLE deadlines ADD COLUMN parent_deadline_id TEXT")
            except sqlite3.OperationalError:
                pass  # Colonna già esistente
            
            # Aggiungi colonne categoria e sottocategoria se non esistono (per database esistenti)
            try:
                cursor.execute("ALTER TABLE projects ADD COLUMN category TEXT DEFAULT 'incentivi_pubblici'")
            except sqlite3.OperationalError:
                pass  # Colonna già esistente
            
            try:
                cursor.execute("ALTER TABLE projects ADD COLUMN subcategory TEXT DEFAULT 'ricerca_innovazione'")
            except sqlite3.OperationalError:
                pass  # Colonna già esistente
            
            # Modifica tasks table per rendere deadline_id e project_id opzionali (supporto task standalone)
            try:
                # Check if we need to migrate the tasks table
                cursor.execute("PRAGMA table_info(tasks)")
                columns = cursor.fetchall()
                deadline_id_column = next((col for col in columns if col[1] == 'deadline_id'), None)
                project_id_column = next((col for col in columns if col[1] == 'project_id'), None)
                
                needs_migration = (
                    (deadline_id_column and deadline_id_column[3] == 1) or  # deadline_id is NOT NULL
                    (project_id_column and project_id_column[3] == 1)       # project_id is NOT NULL
                )
                
                if needs_migration:
                    logger.info("Migrating tasks table to allow nullable deadline_id and project_id...")
                    
                    # Create new table with nullable deadline_id, project_id and parent_task_id
                    cursor.execute("""
                        CREATE TABLE tasks_new (
                            id TEXT PRIMARY KEY,
                            title TEXT NOT NULL,
                            description TEXT,
                            deadline_id TEXT,  -- Now nullable
                            project_id TEXT,   -- Now nullable for standalone tasks
                            parent_task_id TEXT,  -- Now nullable for subtasks
                            assigned_to TEXT,
                            status TEXT DEFAULT 'in_attesa',
                            priority TEXT DEFAULT 'media',
                            estimated_hours REAL,
                            actual_hours REAL,
                            due_date TEXT,
                            start_date TEXT,
                            completed_date TEXT,
                            progress_percentage INTEGER DEFAULT 0,
                            depends_on_tasks TEXT,
                            google_task_id TEXT,
                            google_task_list_id TEXT,
                            sync_to_google_tasks BOOLEAN DEFAULT 1,
                            notes TEXT,
                            tags TEXT,
                            created_at TEXT NOT NULL,
                            updated_at TEXT,
                            FOREIGN KEY (deadline_id) REFERENCES deadlines (id),
                            FOREIGN KEY (project_id) REFERENCES projects (id),
                            FOREIGN KEY (parent_task_id) REFERENCES tasks (id)
                        )
                    """)
                    
                    # Copy data from old table (with NULL for new parent_task_id column)
                    cursor.execute("""
                        INSERT INTO tasks_new (
                            id, title, description, deadline_id, project_id, parent_task_id,
                            assigned_to, status, priority, estimated_hours, actual_hours,
                            due_date, start_date, completed_date, progress_percentage,
                            depends_on_tasks, google_task_id, google_task_list_id,
                            sync_to_google_tasks, notes, tags, created_at, updated_at
                        )
                        SELECT 
                            id, title, description, deadline_id, project_id, NULL,
                            assigned_to, status, priority, estimated_hours, actual_hours,
                            due_date, start_date, completed_date, progress_percentage,
                            depends_on_tasks, google_task_id, google_task_list_id,
                            sync_to_google_tasks, notes, tags, created_at, updated_at
                        FROM tasks
                    """)
                    
                    # Drop old table and rename new one
                    cursor.execute("DROP TABLE tasks")
                    cursor.execute("ALTER TABLE tasks_new RENAME TO tasks")
                    
                    logger.info("Tasks table migration completed successfully")
                    
            except sqlite3.OperationalError as e:
                logger.warning(f"Could not migrate tasks table: {e}")
                pass
            
            # Add parent_task_id column for existing tasks tables (subtasks support)
            try:
                cursor.execute("ALTER TABLE tasks ADD COLUMN parent_task_id TEXT")
                logger.info("Added parent_task_id column for subtasks support")
            except sqlite3.OperationalError:
                pass  # Column already exists
            
            # Aggiungi nuovi campi per i clienti (indirizzo separato, attività, rappresentante legale)
            try:
                cursor.execute("ALTER TABLE clients ADD COLUMN street TEXT")
            except sqlite3.OperationalError:
                pass  # Colonna già esistente
            
            try:
                cursor.execute("ALTER TABLE clients ADD COLUMN civic_number TEXT")
            except sqlite3.OperationalError:
                pass  # Colonna già esistente
            
            try:
                cursor.execute("ALTER TABLE clients ADD COLUMN commune TEXT")
            except sqlite3.OperationalError:
                pass  # Colonna già esistente
            
            try:
                cursor.execute("ALTER TABLE clients ADD COLUMN province_code TEXT")
            except sqlite3.OperationalError:
                pass  # Colonna già esistente
            
            try:
                cursor.execute("ALTER TABLE clients ADD COLUMN activity_code TEXT")
            except sqlite3.OperationalError:
                pass  # Colonna già esistente
            
            try:
                cursor.execute("ALTER TABLE clients ADD COLUMN activity_description TEXT")
            except sqlite3.OperationalError:
                pass  # Colonna già esistente
            
            try:
                cursor.execute("ALTER TABLE clients ADD COLUMN legal_rep_name TEXT")
            except sqlite3.OperationalError:
                pass  # Colonna già esistente
            
            try:
                cursor.execute("ALTER TABLE clients ADD COLUMN legal_rep_surname TEXT")
            except sqlite3.OperationalError:
                pass  # Colonna già esistente
            
            try:
                cursor.execute("ALTER TABLE clients ADD COLUMN legal_rep_tax_code TEXT")
            except sqlite3.OperationalError:
                pass  # Colonna già esistente
            
            try:
                cursor.execute("ALTER TABLE clients ADD COLUMN legal_rep_birth_date TEXT")
            except sqlite3.OperationalError:
                pass  # Colonna già esistente
            
            try:
                cursor.execute("ALTER TABLE clients ADD COLUMN legal_rep_birth_place TEXT")
            except sqlite3.OperationalError:
                pass  # Colonna già esistente
            
            try:
                cursor.execute("ALTER TABLE clients ADD COLUMN legal_rep_residence_street TEXT")
            except sqlite3.OperationalError:
                pass  # Colonna già esistente
            
            try:
                cursor.execute("ALTER TABLE clients ADD COLUMN legal_rep_residence_civic TEXT")
            except sqlite3.OperationalError:
                pass  # Colonna già esistente
            
            try:
                cursor.execute("ALTER TABLE clients ADD COLUMN legal_rep_residence_city TEXT")
            except sqlite3.OperationalError:
                pass  # Colonna già esistente
            
            try:
                cursor.execute("ALTER TABLE clients ADD COLUMN legal_rep_residence_province TEXT")
            except sqlite3.OperationalError:
                pass  # Colonna già esistente
            
            try:
                cursor.execute("ALTER TABLE clients ADD COLUMN legal_rep_residence_postal_code TEXT")
            except sqlite3.OperationalError:
                pass  # Colonna già esistente
            
            try:
                cursor.execute("ALTER TABLE clients ADD COLUMN legal_rep_email TEXT")
            except sqlite3.OperationalError:
                pass  # Colonna già esistente
            
            try:
                cursor.execute("ALTER TABLE clients ADD COLUMN legal_rep_phone TEXT")
            except sqlite3.OperationalError:
                pass  # Colonna già esistente
            
            try:
                cursor.execute("ALTER TABLE clients ADD COLUMN legal_rep_role TEXT")
            except sqlite3.OperationalError:
                pass  # Colonna già esistente
            
            # Add Google Calendar integration columns to deadlines table
            try:
                cursor.execute("ALTER TABLE deadlines ADD COLUMN google_event_id TEXT")
            except sqlite3.OperationalError:
                pass  # Colonna già esistente

            try:
                cursor.execute("ALTER TABLE deadlines ADD COLUMN sync_to_google BOOLEAN DEFAULT 1")
            except sqlite3.OperationalError:
                pass  # Colonna già esistente

            # Add enhanced SAL columns for existing databases
            try:
                cursor.execute("ALTER TABLE sals ADD COLUMN sal_type TEXT DEFAULT 'milestone'")
            except sqlite3.OperationalError:
                pass  # Colonna già esistente

            try:
                cursor.execute("ALTER TABLE sals ADD COLUMN milestone_type TEXT")
            except sqlite3.OperationalError:
                pass  # Colonna già esistente

            try:
                cursor.execute("ALTER TABLE sals ADD COLUMN template_id TEXT")
            except sqlite3.OperationalError:
                pass  # Colonna già esistente

            try:
                cursor.execute("ALTER TABLE sals ADD COLUMN auto_generated BOOLEAN DEFAULT 0")
            except sqlite3.OperationalError:
                pass  # Colonna già esistente

            try:
                cursor.execute("ALTER TABLE sals ADD COLUMN requires_approval BOOLEAN DEFAULT 0")
            except sqlite3.OperationalError:
                pass  # Colonna già esistente

            try:
                cursor.execute("ALTER TABLE sals ADD COLUMN approved_by TEXT")
            except sqlite3.OperationalError:
                pass  # Colonna già esistente

            try:
                cursor.execute("ALTER TABLE sals ADD COLUMN approved_at TEXT")
            except sqlite3.OperationalError:
                pass  # Colonna già esistente
            
            # Indici per performance
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_projects_client_id ON projects (client_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_sals_project_id ON sals (project_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_deadlines_due_date ON deadlines (due_date)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_deadlines_status ON deadlines (status)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_alerts_deadline_id ON alerts (deadline_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_alerts_status ON alerts (status)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_tasks_deadline_id ON tasks (deadline_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_tasks_project_id ON tasks (project_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks (status)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_tasks_due_date ON tasks (due_date)")

            # Indici per incentivi
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_incentives_status ON incentives (status)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_incentives_source ON incentives (source)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_incentives_found_date ON incentives (found_date)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_incentives_content_hash ON incentives (content_hash)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_incentives_priority ON incentives (priority)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_incentives_relevance_score ON incentives (llm_relevance_score)")

            # Indici per sessioni di monitoraggio
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_monitoring_sessions_start_time ON monitoring_sessions (start_time)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_monitoring_sessions_status ON monitoring_sessions (status)")

            conn.commit()
            logger.info("Database inizializzato correttamente")
    
    @contextmanager
    def get_connection(self):
        """Context manager per le connessioni al database"""
        conn = sqlite3.connect(str(self.db_path))
        conn.row_factory = sqlite3.Row
        try:
            yield conn
        except Exception as e:
            conn.rollback()
            logger.error(f"Errore database: {e}")
            raise
        finally:
            conn.close()
    
    def _serialize_value(self, value: Any) -> Any:
        """Serializza valori per il database"""
        if isinstance(value, (datetime, date)):
            return value.isoformat()
        elif isinstance(value, UUID):
            return str(value)
        elif isinstance(value, list):
            return json.dumps(value)
        elif isinstance(value, dict):
            return json.dumps(value)
        elif isinstance(value, Decimal):
            # Convert Decimal to float for SQLite compatibility
            return float(value)
        return value
    
    def _deserialize_value(self, value: Any, field_type: type) -> Any:
        """Deserializza valori dal database"""
        if value is None:
            return None

        if field_type == datetime:
            return datetime.fromisoformat(value)
        elif field_type == date:
            return date.fromisoformat(value)
        elif field_type == UUID:
            return UUID(value)
        elif field_type == list:
            return json.loads(value) if value else []
        elif field_type == dict:
            return json.loads(value) if value else {}
        elif hasattr(field_type, '__origin__'):
            # Handle generic types like List[str], Dict[str, Any], etc.
            if field_type.__origin__ == list:
                return json.loads(value) if value else []
            elif field_type.__origin__ == dict:
                return json.loads(value) if value else {}
        elif hasattr(field_type, '__bases__') and any(base.__name__ == 'Enum' for base in field_type.__bases__):
            # Handle enum types
            try:
                return field_type(value)
            except (ValueError, TypeError) as e:
                # Log the error for debugging but don't fail completely
                logger.warning(f"Failed to convert '{value}' to enum {field_type.__name__}: {e}")
                # Return the original string value instead of None to preserve data
                return value
        elif field_type in [ProjectCategory, ProjectSubcategory, ProjectStatus, ProjectType, Priority]:
            # Handle specific enum types
            try:
                return field_type(value)
            except (ValueError, TypeError) as e:
                # Log the error for debugging but don't fail completely
                logger.warning(f"Failed to convert '{value}' to enum {field_type.__name__}: {e}")
                # Return the original string value instead of None to preserve data
                return value

        return value
    
    def _model_to_dict(self, model: Any) -> Dict[str, Any]:
        """Converte un modello Pydantic in dizionario per il database"""
        # Pydantic v2 compatibility
        if hasattr(model, 'model_dump'):
            # Pydantic v2
            data = model.model_dump()
        else:
            # Pydantic v1 fallback
            data = model.dict()
        
        serialized = {}
        
        for key, value in data.items():
            serialized[key] = self._serialize_value(value)
        
        return serialized
    
    def _dict_to_model(self, data: Dict[str, Any], model_class: type) -> Any:
        """Converte un dizionario dal database in modello Pydantic"""
        if not data:
            return None
        
        # Deserializza i campi speciali
        deserialized = {}
        for key, value in data.items():
            # Pydantic v2 compatibility
            if hasattr(model_class, 'model_fields'):
                # Pydantic v2
                field_info = model_class.model_fields.get(key)
                if field_info:
                    field_type = field_info.annotation
                    deserialized[key] = self._deserialize_value(value, field_type)
                else:
                    deserialized[key] = value
            else:
                # Pydantic v1 fallback
                field_info = getattr(model_class, '__fields__', {}).get(key)
                if field_info:
                    field_type = getattr(field_info, 'type_', type(value))
                    deserialized[key] = self._deserialize_value(value, field_type)
                else:
                    deserialized[key] = value
        
        return model_class(**deserialized)
    
    # CRUD Operations per Client
    def create_client(self, client: Client) -> Client:
        """Crea un nuovo cliente"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            data = self._model_to_dict(client)
            
            columns = ', '.join(data.keys())
            placeholders = ', '.join(['?' for _ in data])
            
            cursor.execute(
                f"INSERT INTO clients ({columns}) VALUES ({placeholders})",
                list(data.values())
            )
            conn.commit()
            
            logger.info(f"Cliente creato: {client.name}")
            return client
    
    def get_client(self, client_id: UUID) -> Optional[Client]:
        """Recupera un cliente per ID"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM clients WHERE id = ?", (str(client_id),))
            row = cursor.fetchone()
            
            if row:
                return self._dict_to_model(dict(row), Client)
            return None
    
    def get_all_clients(self, active_only: bool = True) -> List[Client]:
        """Recupera tutti i clienti"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            query = "SELECT * FROM clients"
            if active_only:
                query += " WHERE is_active = 1"
            query += " ORDER BY name"
            
            cursor.execute(query)
            rows = cursor.fetchall()
            
            return [self._dict_to_model(dict(row), Client) for row in rows]
    
    def update_client(self, client: Client) -> Client:
        """Aggiorna un cliente"""
        client.updated_at = datetime.now()
        
        with self.get_connection() as conn:
            cursor = conn.cursor()
            data = self._model_to_dict(client)
            
            set_clause = ', '.join([f"{key} = ?" for key in data.keys() if key != 'id'])
            values = [value for key, value in data.items() if key != 'id']
            values.append(str(client.id))
            
            cursor.execute(
                f"UPDATE clients SET {set_clause} WHERE id = ?",
                values
            )
            conn.commit()
            
            logger.info(f"Cliente aggiornato: {client.name}")
            return client
    
    def check_client_exists(self, fiscal_code: str = None, vat_number: str = None, exclude_id: UUID = None) -> bool:
        """Controlla se esiste già un cliente con lo stesso codice fiscale o partita IVA"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            conditions = []
            params = []
            
            if fiscal_code:
                conditions.append("tax_code = ?")
                params.append(fiscal_code)
            
            if vat_number:
                conditions.append("vat_number = ?")
                params.append(vat_number)
            
            if not conditions:
                return False
            
            query = f"SELECT COUNT(*) FROM clients WHERE is_active = 1 AND ({' OR '.join(conditions)})"
            
            if exclude_id:
                query += " AND id != ?"
                params.append(str(exclude_id))
            
            cursor.execute(query, params)
            count = cursor.fetchone()[0]
            
            return count > 0
    
    def delete_client(self, client_id: UUID) -> bool:
        """Elimina un cliente (soft delete)"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "UPDATE clients SET is_active = 0, updated_at = ? WHERE id = ?",
                (datetime.now().isoformat(), str(client_id))
            )
            conn.commit()
            
            logger.info(f"Cliente eliminato: {client_id}")
            return cursor.rowcount > 0
    
    # CRUD Operations per Project
    def create_project(self, project: Project) -> Project:
        """Crea un nuovo progetto"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            data = self._model_to_dict(project)
            
            columns = ', '.join(data.keys())
            placeholders = ', '.join(['?' for _ in data])
            
            cursor.execute(
                f"INSERT INTO projects ({columns}) VALUES ({placeholders})",
                list(data.values())
            )
            conn.commit()
            
            logger.info(f"Progetto creato: {project.name}")
            return project
    
    def get_project(self, project_id: UUID) -> Optional[Project]:
        """Recupera un progetto per ID"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM projects WHERE id = ?", (str(project_id),))
            row = cursor.fetchone()
            
            if row:
                return self._dict_to_model(dict(row), Project)
            return None
    
    def get_projects_by_client(self, client_id: UUID) -> List[Project]:
        """Recupera tutti i progetti di un cliente"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT * FROM projects WHERE client_id = ? ORDER BY created_at DESC",
                (str(client_id),)
            )
            rows = cursor.fetchall()
            
            return [self._dict_to_model(dict(row), Project) for row in rows]
    
    def get_all_projects(self) -> List[Project]:
        """Recupera tutti i progetti"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM projects ORDER BY created_at DESC")
            rows = cursor.fetchall()
            
            return [self._dict_to_model(dict(row), Project) for row in rows]
    
    def update_project(self, project: Project) -> Project:
        """Aggiorna un progetto"""
        project.updated_at = datetime.now()
        
        with self.get_connection() as conn:
            cursor = conn.cursor()
            data = self._model_to_dict(project)
            
            set_clause = ', '.join([f"{key} = ?" for key in data.keys() if key != 'id'])
            values = [value for key, value in data.items() if key != 'id']
            values.append(str(project.id))
            
            cursor.execute(
                f"UPDATE projects SET {set_clause} WHERE id = ?",
                values
            )
            conn.commit()
            
            logger.info(f"Progetto aggiornato: {project.name}")
            return project
    
    def delete_project(self, project_id: UUID) -> bool:
        """Elimina un progetto"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM projects WHERE id = ?", (str(project_id),))
            conn.commit()

            logger.info(f"Progetto eliminato: {project_id}")
            return cursor.rowcount > 0

    # SAL CRUD Operations
    def create_sal(self, sal: 'SAL') -> 'SAL':
        """Crea un nuovo SAL"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO sals (id, project_id, name, description, sequence_number,
                                percentage, status, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                str(sal.id),
                str(sal.project_id),
                sal.name,
                sal.description,
                sal.sequence_number,
                sal.percentage,
                sal.status.value if hasattr(sal.status, 'value') else str(sal.status),
                sal.created_at.isoformat(),
                sal.updated_at.isoformat()
            ))
            conn.commit()

            logger.info(f"SAL creato: {sal.name}")
            return sal

    def get_sal_by_id(self, sal_id: UUID) -> Optional['SAL']:
        """Recupera un SAL per ID"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM sals WHERE id = ?", (str(sal_id),))
            row = cursor.fetchone()

            if row:
                from ..models import SAL
                return self._dict_to_model(dict(row), SAL)
            return None

    def get_sals_by_project(self, project_id: UUID) -> List['SAL']:
        """Recupera tutti i SAL di un progetto"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT * FROM sals WHERE project_id = ? ORDER BY sequence_number",
                (str(project_id),)
            )
            rows = cursor.fetchall()

            from ..models import SAL
            return [self._dict_to_model(dict(row), SAL) for row in rows]

    def get_all_sals(self) -> List['SAL']:
        """Recupera tutti i SAL"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM sals ORDER BY created_at DESC")
            rows = cursor.fetchall()

            from ..models import SAL
            return [self._dict_to_model(dict(row), SAL) for row in rows]

    def update_sal(self, sal: 'SAL') -> 'SAL':
        """Aggiorna un SAL esistente"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # Build dynamic update query
            fields = []
            values = []

            if hasattr(sal, 'name') and sal.name is not None:
                fields.append("name = ?")
                values.append(sal.name)

            if hasattr(sal, 'description'):
                fields.append("description = ?")
                values.append(sal.description)

            if hasattr(sal, 'sequence_number') and sal.sequence_number is not None:
                fields.append("sequence_number = ?")
                values.append(sal.sequence_number)

            if hasattr(sal, 'percentage') and sal.percentage is not None:
                fields.append("percentage = ?")
                values.append(sal.percentage)

            if hasattr(sal, 'status') and sal.status is not None:
                fields.append("status = ?")
                values.append(sal.status.value if hasattr(sal.status, 'value') else str(sal.status))

            if hasattr(sal, 'updated_at') and sal.updated_at is not None:
                fields.append("updated_at = ?")
                values.append(sal.updated_at.isoformat())

            if not fields:
                return sal

            set_clause = ", ".join(fields)
            values.append(str(sal.id))

            cursor.execute(
                f"UPDATE sals SET {set_clause} WHERE id = ?",
                values
            )
            conn.commit()

            logger.info(f"SAL aggiornato: {sal.name}")
            return sal

    def delete_sal(self, sal_id: UUID) -> bool:
        """Elimina un SAL"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM sals WHERE id = ?", (str(sal_id),))
            conn.commit()

            logger.info(f"SAL eliminato: {sal_id}")
            return cursor.rowcount > 0