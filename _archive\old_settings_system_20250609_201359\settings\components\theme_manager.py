#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Theme Management System
Provides consistent styling and theming across the settings interface
"""

import flet as ft
from typing import Dict, Any, Optional
from enum import Enum
from core import get_logger

logger = get_logger(__name__)

class ThemeMode(Enum):
    """Available theme modes"""
    LIGHT = "light"
    DARK = "dark"
    SYSTEM = "system"

class ColorPalette:
    """Color palette definitions for different themes"""
    
    LIGHT = {
        # Primary colors
        'primary': ft.Colors.BLUE_600,
        'primary_light': ft.Colors.BLUE_100,
        'primary_dark': ft.Colors.BLUE_800,
        
        # Secondary colors
        'secondary': ft.Colors.GREY_600,
        'secondary_light': ft.Colors.GREY_100,
        'secondary_dark': ft.Colors.GREY_800,
        
        # Surface colors
        'surface': ft.Colors.WHITE,
        'surface_variant': ft.Colors.GREY_50,
        'background': ft.Colors.GREY_50,
        
        # Text colors
        'text_primary': ft.Colors.GREY_900,
        'text_secondary': ft.Colors.GREY_600,
        'text_disabled': ft.Colors.GREY_400,
        
        # Status colors
        'success': ft.Colors.GREEN_600,
        'warning': ft.Colors.ORANGE_600,
        'error': ft.Colors.RED_600,
        'info': ft.Colors.BLUE_600,
        
        # Border and divider
        'border': ft.Colors.GREY_300,
        'divider': ft.Colors.GREY_200,
        
        # Interactive states
        'hover': ft.Colors.GREY_100,
        'pressed': ft.Colors.GREY_200,
        'selected': ft.Colors.BLUE_50,
    }
    
    DARK = {
        # Primary colors
        'primary': ft.Colors.BLUE_400,
        'primary_light': ft.Colors.BLUE_300,
        'primary_dark': ft.Colors.BLUE_600,
        
        # Secondary colors
        'secondary': ft.Colors.GREY_400,
        'secondary_light': ft.Colors.GREY_300,
        'secondary_dark': ft.Colors.GREY_600,
        
        # Surface colors
        'surface': ft.Colors.GREY_800,
        'surface_variant': ft.Colors.GREY_900,
        'background': ft.Colors.BLACK,
        
        # Text colors
        'text_primary': ft.Colors.WHITE,
        'text_secondary': ft.Colors.GREY_300,
        'text_disabled': ft.Colors.GREY_600,
        
        # Status colors
        'success': ft.Colors.GREEN_400,
        'warning': ft.Colors.ORANGE_400,
        'error': ft.Colors.RED_400,
        'info': ft.Colors.BLUE_400,
        
        # Border and divider
        'border': ft.Colors.GREY_700,
        'divider': ft.Colors.GREY_800,
        
        # Interactive states
        'hover': ft.Colors.GREY_700,
        'pressed': ft.Colors.GREY_600,
        'selected': ft.Colors.BLUE_900,
    }

class Typography:
    """Typography definitions"""
    
    STYLES = {
        'heading_large': {
            'size': 28,
            'weight': ft.FontWeight.BOLD,
        },
        'heading_medium': {
            'size': 20,
            'weight': ft.FontWeight.BOLD,
        },
        'heading_small': {
            'size': 16,
            'weight': ft.FontWeight.BOLD,
        },
        'body_large': {
            'size': 16,
            'weight': ft.FontWeight.NORMAL,
        },
        'body_medium': {
            'size': 14,
            'weight': ft.FontWeight.NORMAL,
        },
        'body_small': {
            'size': 12,
            'weight': ft.FontWeight.NORMAL,
        },
        'caption': {
            'size': 11,
            'weight': ft.FontWeight.NORMAL,
        },
        'label': {
            'size': 13,
            'weight': ft.FontWeight.W_500,
        }
    }

class Spacing:
    """Spacing constants"""
    
    XS = 4
    SM = 8
    MD = 12
    LG = 16
    XL = 20
    XXL = 24
    XXXL = 32

class Elevation:
    """Elevation and shadow definitions"""
    
    NONE = None
    
    LEVEL_1 = ft.BoxShadow(
        spread_radius=0,
        blur_radius=2,
        color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
        offset=ft.Offset(0, 1)
    )
    
    LEVEL_2 = ft.BoxShadow(
        spread_radius=0,
        blur_radius=4,
        color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
        offset=ft.Offset(0, 2)
    )
    
    LEVEL_3 = ft.BoxShadow(
        spread_radius=0,
        blur_radius=8,
        color=ft.Colors.with_opacity(0.15, ft.Colors.BLACK),
        offset=ft.Offset(0, 4)
    )
    
    LEVEL_4 = ft.BoxShadow(
        spread_radius=0,
        blur_radius=12,
        color=ft.Colors.with_opacity(0.2, ft.Colors.BLACK),
        offset=ft.Offset(0, 6)
    )

class SettingsThemeManager:
    """
    Manages theming for the settings interface
    """
    
    def __init__(self, initial_mode: ThemeMode = ThemeMode.LIGHT):
        self.current_mode = initial_mode
        self.color_palette = self._get_color_palette()
        
        # Theme change callbacks
        self.theme_callbacks = []
        
        logger.info(f"Settings Theme Manager initialized with {initial_mode.value} mode")
    
    def set_theme_mode(self, mode: ThemeMode):
        """Change the current theme mode"""
        if mode != self.current_mode:
            old_mode = self.current_mode
            self.current_mode = mode
            self.color_palette = self._get_color_palette()
            
            logger.info(f"Theme changed from {old_mode.value} to {mode.value}")
            
            # Notify all callbacks
            for callback in self.theme_callbacks:
                try:
                    callback(mode, old_mode)
                except Exception as e:
                    logger.error(f"Error in theme callback: {e}")
    
    def register_theme_callback(self, callback):
        """Register callback for theme changes"""
        self.theme_callbacks.append(callback)
    
    def _get_color_palette(self) -> Dict[str, Any]:
        """Get color palette for current theme"""
        if self.current_mode == ThemeMode.DARK:
            return ColorPalette.DARK
        else:
            return ColorPalette.LIGHT  # Default to light for system mode too
    
    def get_color(self, color_key: str) -> str:
        """Get color from current palette"""
        return self.color_palette.get(color_key, ft.Colors.GREY_500)
    
    def create_card_style(self, variant: str = "default") -> Dict[str, Any]:
        """Create card styling"""
        base_style = {
            'bgcolor': self.get_color('surface'),
            'border_radius': 12,
            'border': ft.border.all(1, self.get_color('border')),
            'shadow': Elevation.LEVEL_2
        }
        
        if variant == "elevated":
            base_style['shadow'] = Elevation.LEVEL_3
        elif variant == "outlined":
            base_style['shadow'] = Elevation.NONE
            base_style['border'] = ft.border.all(2, self.get_color('border'))
        elif variant == "filled":
            base_style['bgcolor'] = self.get_color('surface_variant')
            base_style['border'] = ft.border.all(1, self.get_color('border'))
        
        return base_style
    
    def create_button_style(self, variant: str = "primary") -> Dict[str, Any]:
        """Create button styling"""
        if variant == "primary":
            return {
                'bgcolor': self.get_color('primary'),
                'color': ft.Colors.WHITE,
                'overlay_color': {
                    ft.MaterialState.HOVERED: self.get_color('primary_dark'),
                    ft.MaterialState.PRESSED: self.get_color('primary_dark'),
                }
            }
        elif variant == "secondary":
            return {
                'bgcolor': self.get_color('surface'),
                'color': self.get_color('primary'),
                'overlay_color': {
                    ft.MaterialState.HOVERED: self.get_color('hover'),
                    ft.MaterialState.PRESSED: self.get_color('pressed'),
                },
                'side': ft.BorderSide(1, self.get_color('border'))
            }
        elif variant == "text":
            return {
                'bgcolor': ft.Colors.TRANSPARENT,
                'color': self.get_color('primary'),
                'overlay_color': {
                    ft.MaterialState.HOVERED: self.get_color('hover'),
                    ft.MaterialState.PRESSED: self.get_color('pressed'),
                }
            }
        
        return {}
    
    def create_text_style(self, style_name: str, color_key: str = 'text_primary') -> Dict[str, Any]:
        """Create text styling"""
        base_style = Typography.STYLES.get(style_name, Typography.STYLES['body_medium']).copy()
        base_style['color'] = self.get_color(color_key)
        return base_style
    
    def create_input_style(self) -> Dict[str, Any]:
        """Create input field styling"""
        return {
            'bgcolor': self.get_color('surface'),
            'border_color': self.get_color('border'),
            'focused_border_color': self.get_color('primary'),
            'cursor_color': self.get_color('primary'),
            'selection_color': self.get_color('selected'),
            'color': self.get_color('text_primary'),
            'content_padding': ft.padding.symmetric(horizontal=Spacing.MD, vertical=Spacing.SM)
        }
    
    def create_status_style(self, status: str) -> Dict[str, Any]:
        """Create status indicator styling"""
        status_colors = {
            'success': self.get_color('success'),
            'warning': self.get_color('warning'),
            'error': self.get_color('error'),
            'info': self.get_color('info')
        }
        
        color = status_colors.get(status, self.get_color('secondary'))
        
        return {
            'color': color,
            'bgcolor': ft.Colors.with_opacity(0.1, color),
            'border': ft.border.all(1, ft.Colors.with_opacity(0.3, color)),
            'border_radius': 6,
            'padding': ft.padding.symmetric(horizontal=Spacing.SM, vertical=Spacing.XS)
        }

class ThemedComponents:
    """
    Factory for creating themed components
    """
    
    def __init__(self, theme_manager: SettingsThemeManager):
        self.theme = theme_manager
    
    def create_card(self, content: ft.Control, title: Optional[str] = None, variant: str = "default") -> ft.Container:
        """Create a themed card"""
        card_style = self.theme.create_card_style(variant)
        
        card_content = []
        
        if title:
            card_content.append(
                ft.Text(
                    title,
                    **self.theme.create_text_style('heading_small', 'text_primary')
                )
            )
            card_content.append(ft.Divider(height=1, color=self.theme.get_color('divider')))
        
        card_content.append(content)
        
        return ft.Container(
            content=ft.Column(card_content, spacing=Spacing.MD),
            padding=ft.padding.all(Spacing.LG),
            **card_style
        )
    
    def create_button(self, text: str, variant: str = "primary", icon: Optional[str] = None, 
                     on_click: Optional[callable] = None) -> ft.ElevatedButton:
        """Create a themed button"""
        button_style = self.theme.create_button_style(variant)
        
        if variant == "text":
            return ft.TextButton(
                text=text,
                icon=icon,
                on_click=on_click,
                style=ft.ButtonStyle(**button_style)
            )
        elif variant == "secondary":
            return ft.OutlinedButton(
                text=text,
                icon=icon,
                on_click=on_click,
                style=ft.ButtonStyle(**button_style)
            )
        else:
            return ft.ElevatedButton(
                text=text,
                icon=icon,
                on_click=on_click,
                style=ft.ButtonStyle(**button_style)
            )
    
    def create_input(self, label: str, hint_text: Optional[str] = None, 
                    value: Optional[str] = None, **kwargs) -> ft.TextField:
        """Create a themed input field"""
        input_style = self.theme.create_input_style()
        
        return ft.TextField(
            label=label,
            hint_text=hint_text,
            value=value,
            **input_style,
            **kwargs
        )
    
    def create_switch(self, label: str, value: bool = False, 
                     on_change: Optional[callable] = None) -> ft.Row:
        """Create a themed switch with label"""
        return ft.Row([
            ft.Switch(
                value=value,
                on_change=on_change,
                active_color=self.theme.get_color('primary')
            ),
            ft.Text(
                label,
                **self.theme.create_text_style('body_medium', 'text_primary')
            )
        ], spacing=Spacing.SM)
    
    def create_status_badge(self, text: str, status: str = "info") -> ft.Container:
        """Create a themed status badge"""
        status_style = self.theme.create_status_style(status)
        
        return ft.Container(
            content=ft.Text(
                text,
                size=10,
                weight=ft.FontWeight.W_500,
                color=status_style['color']
            ),
            **{k: v for k, v in status_style.items() if k != 'color'}
        )
    
    def create_section_header(self, title: str, subtitle: Optional[str] = None, 
                            icon: Optional[str] = None) -> ft.Container:
        """Create a themed section header"""
        header_content = []
        
        # Title row with optional icon
        title_row = []
        if icon:
            title_row.append(
                ft.Icon(icon, size=24, color=self.theme.get_color('primary'))
            )
        
        title_row.append(
            ft.Text(
                title,
                **self.theme.create_text_style('heading_medium', 'text_primary')
            )
        )
        
        header_content.append(
            ft.Row(title_row, spacing=Spacing.SM)
        )
        
        # Optional subtitle
        if subtitle:
            header_content.append(
                ft.Text(
                    subtitle,
                    **self.theme.create_text_style('body_small', 'text_secondary')
                )
            )
        
        return ft.Container(
            content=ft.Column(header_content, spacing=Spacing.XS),
            padding=ft.padding.only(bottom=Spacing.MD)
        )

# Global theme manager instance
_global_theme_manager: Optional[SettingsThemeManager] = None

def get_theme_manager() -> SettingsThemeManager:
    """Get or create global theme manager instance"""
    global _global_theme_manager
    if _global_theme_manager is None:
        _global_theme_manager = SettingsThemeManager()
    return _global_theme_manager

def get_themed_components() -> ThemedComponents:
    """Get themed components factory"""
    return ThemedComponents(get_theme_manager()) 