"""
Update dialog for Agevolami PM
Shows available updates and handles the update process
"""

import flet as ft
from typing import Dict, Any, Callable
import threading
import time

class UpdateDialog:
    """Dialog for handling application updates"""
    
    def __init__(self, page: ft.Page, update_info: Dict[str, Any], updater):
        self.page = page
        self.update_info = update_info
        self.updater = updater
        self.dialog = None
        self.progress_bar = None
        self.status_text = None
        
    def show_update_available_dialog(self, on_update_callback: Callable = None):
        """Show dialog when update is available"""
        
        # Format file size
        size_mb = self.update_info.get('size', 0) / (1024 * 1024)
        size_text = f"{size_mb:.1f} MB" if size_mb > 0 else "Dimensione sconosciuta"
        
        # Create dialog content
        content = ft.Column([
            ft.Row([
                ft.Icon(ft.icons.SYSTEM_UPDATE, color=ft.colors.BLUE, size=40),
                ft.Column([
                    ft.Text("Aggiornamento Disponibile", 
                           size=20, weight=ft.FontWeight.BOLD),
                    ft.Text(f"Versione {self.update_info['latest_version']} disponibile",
                           size=14, color=ft.colors.GREY_700)
                ], expand=True)
            ]),
            
            ft.Divider(),
            
            ft.Column([
                ft.Text("Dettagli:", weight=ft.FontWeight.BOLD),
                ft.Text(f"• Versione attuale: {self.update_info['current_version']}"),
                ft.Text(f"• Nuova versione: {self.update_info['latest_version']}"),
                ft.Text(f"• Dimensione download: {size_text}"),
                ft.Text(f"• Data rilascio: {self._format_date(self.update_info.get('release_date', ''))}"),
            ], spacing=5),
            
            ft.Divider(),
            
            ft.Container(
                content=ft.Column([
                    ft.Text("Note di rilascio:", weight=ft.FontWeight.BOLD),
                    ft.Text(
                        self.update_info.get('release_notes', 'Nessuna nota disponibile'),
                        selectable=True
                    )
                ]),
                bgcolor=ft.colors.GREY_100,
                padding=10,
                border_radius=5,
                height=150
            )
        ], spacing=15, width=500)
        
        # Create dialog
        self.dialog = ft.AlertDialog(
            title=ft.Text("Aggiornamento Agevolami PM"),
            content=content,
            actions=[
                ft.TextButton(
                    "Più tardi",
                    on_click=self._close_dialog
                ),
                ft.ElevatedButton(
                    "Aggiorna ora",
                    icon=ft.icons.DOWNLOAD,
                    on_click=lambda _: self._start_update(on_update_callback)
                )
            ],
            actions_alignment=ft.MainAxisAlignment.SPACE_BETWEEN
        )
        
        self.page.dialog = self.dialog
        self.dialog.open = True
        self.page.update()
    
    def show_update_progress_dialog(self):
        """Show progress dialog during update"""
        
        self.progress_bar = ft.ProgressBar(width=400)
        self.status_text = ft.Text("Preparazione download...", size=14)
        
        content = ft.Column([
            ft.Row([
                ft.Icon(ft.icons.DOWNLOAD, color=ft.colors.BLUE, size=30),
                ft.Text("Aggiornamento in corso...", size=18, weight=ft.FontWeight.BOLD)
            ]),
            
            ft.Divider(),
            
            self.status_text,
            self.progress_bar,
            
            ft.Text(
                "Non chiudere l'applicazione durante l'aggiornamento.",
                size=12,
                color=ft.colors.ORANGE,
                italic=True
            )
        ], spacing=15, width=450)
        
        self.dialog = ft.AlertDialog(
            title=ft.Text("Aggiornamento Agevolami PM"),
            content=content,
            modal=True
        )
        
        self.page.dialog = self.dialog
        self.dialog.open = True
        self.page.update()
    
    def show_update_success_dialog(self):
        """Show success dialog after update"""
        
        content = ft.Column([
            ft.Row([
                ft.Icon(ft.icons.CHECK_CIRCLE, color=ft.colors.GREEN, size=40),
                ft.Column([
                    ft.Text("Aggiornamento Completato!", 
                           size=20, weight=ft.FontWeight.BOLD, color=ft.colors.GREEN),
                    ft.Text("L'applicazione verrà riavviata automaticamente.",
                           size=14, color=ft.colors.GREY_700)
                ], expand=True)
            ]),
            
            ft.Divider(),
            
            ft.Text(
                f"Agevolami PM è stato aggiornato alla versione {self.update_info['latest_version']}.\n"
                "L'applicazione si riavvierà tra pochi secondi.",
                text_align=ft.TextAlign.CENTER
            )
        ], spacing=15, width=400)
        
        self.dialog = ft.AlertDialog(
            title=ft.Text("Aggiornamento Completato"),
            content=content,
            actions=[
                ft.ElevatedButton(
                    "Riavvia ora",
                    icon=ft.icons.RESTART_ALT,
                    on_click=self._restart_app
                )
            ],
            actions_alignment=ft.MainAxisAlignment.CENTER
        )
        
        self.page.dialog = self.dialog
        self.dialog.open = True
        self.page.update()
        
        # Auto-restart after 5 seconds
        threading.Timer(5.0, self._restart_app).start()
    
    def show_update_error_dialog(self, error_message: str):
        """Show error dialog if update fails"""
        
        content = ft.Column([
            ft.Row([
                ft.Icon(ft.icons.ERROR, color=ft.colors.RED, size=40),
                ft.Column([
                    ft.Text("Errore Aggiornamento", 
                           size=20, weight=ft.FontWeight.BOLD, color=ft.colors.RED),
                    ft.Text("Si è verificato un errore durante l'aggiornamento.",
                           size=14, color=ft.colors.GREY_700)
                ], expand=True)
            ]),
            
            ft.Divider(),
            
            ft.Container(
                content=ft.Text(error_message, selectable=True),
                bgcolor=ft.colors.RED_50,
                padding=10,
                border_radius=5
            ),
            
            ft.Text(
                "Puoi riprovare più tardi o scaricare manualmente l'aggiornamento dal sito web.",
                size=12,
                color=ft.colors.GREY_600,
                italic=True
            )
        ], spacing=15, width=450)
        
        self.dialog = ft.AlertDialog(
            title=ft.Text("Errore Aggiornamento"),
            content=content,
            actions=[
                ft.TextButton("Chiudi", on_click=self._close_dialog)
            ]
        )
        
        self.page.dialog = self.dialog
        self.dialog.open = True
        self.page.update()
    
    def _start_update(self, callback: Callable = None):
        """Start the update process"""
        self._close_dialog()
        self.show_update_progress_dialog()
        
        def update_thread():
            try:
                # Update progress
                self._update_progress("Download in corso...", 0.2)
                time.sleep(1)
                
                # Download and install
                download_url = self.update_info.get('download_url')
                if not download_url:
                    raise Exception("URL di download non disponibile")
                
                self._update_progress("Installazione...", 0.7)
                success = self.updater.download_and_install_update(download_url)
                
                if success:
                    self._update_progress("Completato!", 1.0)
                    time.sleep(1)
                    self.page.run_thread(self.show_update_success_dialog)
                    
                    if callback:
                        callback()
                else:
                    raise Exception("Installazione fallita")
                    
            except Exception as e:
                self.page.run_thread(
                    lambda: self.show_update_error_dialog(str(e))
                )
        
        threading.Thread(target=update_thread, daemon=True).start()
    
    def _update_progress(self, status: str, progress: float):
        """Update progress bar and status text"""
        def update_ui():
            if self.status_text:
                self.status_text.value = status
            if self.progress_bar:
                self.progress_bar.value = progress
            self.page.update()
        
        self.page.run_thread(update_ui)
    
    def _format_date(self, date_str: str) -> str:
        """Format release date"""
        try:
            from datetime import datetime
            dt = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
            return dt.strftime('%d/%m/%Y')
        except:
            return date_str
    
    def _close_dialog(self, e=None):
        """Close the dialog"""
        if self.dialog:
            self.dialog.open = False
            self.page.update()
    
    def _restart_app(self, e=None):
        """Restart the application"""
        self._close_dialog()
        self.updater.restart_application()
