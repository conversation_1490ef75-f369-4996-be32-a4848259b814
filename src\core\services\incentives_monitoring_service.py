#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Main Incentives Monitoring Service
Orchestrates web scraping, LLM analysis, and data management for Italian financial incentives
"""

import schedule
import threading
import time
from typing import List, Dict, Any, Optional
from datetime import datetime, date, timedelta
from pathlib import Path

from core.utils.logger import get_logger
from core.models.incentive_models import (
    IncentiveItem, MonitoringSession, IncentiveReport, MonitoringConfig,
    IncentiveSource, IncentiveStatus, IncentivePriority,
    incentive_item_to_dict, dict_to_incentive_item
)
# Try to import enhanced service, fallback to original if not available
try:
    from core.services.enhanced_web_scraping_service import EnhancedWebScrapingService
    ENHANCED_SCRAPING_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Enhanced web scraping not available: {e}")
    from core.services.web_scraping_service import WebScrapingService
    ENHANCED_SCRAPING_AVAILABLE = False
from core.services.llm_service import LLMService
from core.services.email_service import EmailService

logger = get_logger(__name__)

class IncentivesMonitoringService:
    """Main service for monitoring Italian financial incentives"""
    
    def __init__(self, db_manager, config: Dict[str, Any], email_service: EmailService):
        self.db = db_manager

        # Initialize config with error handling for unknown fields
        incentives_config = config.get('incentives', {})
        try:
            self.config = MonitoringConfig(**incentives_config)
        except TypeError as e:
            # Handle unknown fields by filtering them out
            logger.warning(f"Unknown configuration fields detected: {e}")

            # Get valid fields from MonitoringConfig
            from dataclasses import fields
            valid_fields = {f.name for f in fields(MonitoringConfig)}

            # Filter config to only include valid fields
            filtered_config = {k: v for k, v in incentives_config.items() if k in valid_fields}

            # Log filtered out fields
            filtered_out = set(incentives_config.keys()) - valid_fields
            if filtered_out:
                logger.info(f"Filtered out unknown config fields: {filtered_out}")

            self.config = MonitoringConfig(**filtered_config)

        self.email_service = email_service

        # Store the full config for websites configuration
        self.full_config = config.get('incentives', {})

        # Initialize sub-services with fallback
        try:
            if ENHANCED_SCRAPING_AVAILABLE:
                self.web_scraper = EnhancedWebScrapingService(self._get_enhanced_scraper_config())
                logger.info("Using enhanced web scraping service")
            else:
                self.web_scraper = WebScrapingService(self._get_scraper_config())
                logger.info("Using standard web scraping service")
        except Exception as e:
            logger.error(f"Error initializing web scraper: {e}")
            # Fallback to standard service
            self.web_scraper = WebScrapingService(self._get_scraper_config())
            logger.info("Fallback to standard web scraping service")
        self.llm_service = LLMService(self._get_llm_config())
        
        # Monitoring state
        self.is_monitoring = False
        self.monitoring_thread = None
        self.last_run = None
        
        # Initialize database tables
        self._init_database()
        
        # Setup scheduled monitoring
        if self.config.enabled:
            self._setup_scheduled_monitoring()
    
    def _init_database(self):
        """Initialize database tables for incentives"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                
                # Incentives table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS incentives (
                        id TEXT PRIMARY KEY,
                        title TEXT NOT NULL,
                        description TEXT,
                        source TEXT NOT NULL,
                        source_url TEXT,
                        content_hash TEXT UNIQUE,
                        found_date TEXT NOT NULL,
                        publication_date TEXT,
                        deadline_date TEXT,
                        status TEXT DEFAULT 'new',
                        priority TEXT DEFAULT 'medium',
                        tags TEXT,
                        keywords_matched TEXT,
                        llm_summary TEXT,
                        llm_relevance_score REAL DEFAULT 0.0,
                        llm_analysis_date TEXT,
                        raw_content TEXT,
                        metadata TEXT,
                        user_notes TEXT,
                        is_favorite BOOLEAN DEFAULT 0,
                        created_at TEXT NOT NULL,
                        updated_at TEXT
                    )
                """)
                
                # Monitoring sessions table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS monitoring_sessions (
                        id TEXT PRIMARY KEY,
                        start_time TEXT NOT NULL,
                        end_time TEXT,
                        sources_checked TEXT,
                        keywords_used TEXT,
                        items_found INTEGER DEFAULT 0,
                        new_items INTEGER DEFAULT 0,
                        errors_encountered TEXT,
                        status TEXT DEFAULT 'running',
                        success BOOLEAN DEFAULT 0
                    )
                """)
                
                # Create indexes
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_incentives_status ON incentives (status)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_incentives_source ON incentives (source)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_incentives_found_date ON incentives (found_date)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_incentives_content_hash ON incentives (content_hash)")
                
                conn.commit()
                logger.info("Incentives database tables initialized")
                
        except Exception as e:
            logger.error(f"Error initializing incentives database: {e}")
    
    def start_monitoring(self) -> bool:
        """Start automated monitoring"""
        try:
            if self.is_monitoring:
                logger.warning("Monitoring already running")
                return False
            
            self.is_monitoring = True
            logger.info("Starting incentives monitoring...")
            
            # Run initial scan
            self.run_monitoring_session()
            
            return True
            
        except Exception as e:
            logger.error(f"Error starting monitoring: {e}")
            self.is_monitoring = False
            return False
    
    def stop_monitoring(self):
        """Stop automated monitoring"""
        self.is_monitoring = False
        schedule.clear('incentives')
        logger.info("Incentives monitoring stopped")
    
    def run_monitoring_session(self) -> MonitoringSession:
        """Run a single monitoring session"""
        session = MonitoringSession(
            sources_checked=list(self.config.sources),
            keywords_used=self.config.keywords
        )
        
        try:
            logger.info("Starting monitoring session...")
            
            # Scrape websites
            found_items = self.web_scraper.scrape_all_sources(self.config.keywords)
            session.items_found = len(found_items)
            
            # Filter out duplicates
            new_items = self._filter_new_items(found_items)
            session.new_items = len(new_items)
            
            # Analyze with LLM if enabled
            if self.config.llm_enabled and new_items:
                self._analyze_items_with_llm(new_items)
            
            # Save new items to database
            for item in new_items:
                self._save_incentive_item(item)
            
            # Send notifications if configured
            if self.config.email_notifications and new_items:
                self._send_notification_email(new_items)
            
            # Update session
            session.end_time = datetime.now()
            session.status = "completed"
            session.success = True
            self.last_run = session.end_time
            
            logger.info(f"Monitoring session completed: {session.new_items} new items found")
            
        except Exception as e:
            session.end_time = datetime.now()
            session.status = "failed"
            session.errors_encountered.append(str(e))
            logger.error(f"Monitoring session failed: {e}")
        
        # Save session to database
        self._save_monitoring_session(session)
        
        return session
    
    def _filter_new_items(self, items: List[IncentiveItem]) -> List[IncentiveItem]:
        """Filter out items that already exist in database"""
        new_items = []
        
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                
                for item in items:
                    # Check if item already exists by content hash
                    cursor.execute(
                        "SELECT id FROM incentives WHERE content_hash = ?",
                        (item.content_hash,)
                    )
                    
                    if not cursor.fetchone():
                        new_items.append(item)
                    
        except Exception as e:
            logger.error(f"Error filtering new items: {e}")
            return items  # Return all items if filtering fails
        
        return new_items
    
    def _analyze_items_with_llm(self, items: List[IncentiveItem]):
        """Analyze items with LLM service"""
        try:
            logger.info(f"Analyzing {len(items)} items with LLM...")
            logger.info(f"LLM service enabled: {self.llm_service.enabled}")
            logger.info(f"LLM config: llm_enabled={self.config.llm_enabled}, api_key_present={bool(self.config.openrouter_api_key)}")

            if not self.llm_service.enabled:
                logger.warning("LLM service is not enabled - skipping analysis")
                return

            analyzed_count = 0
            for i, item in enumerate(items):
                try:
                    logger.info(f"Analyzing item {i+1}/{len(items)}: {item.title[:50]}...")
                    summary, relevance_score, priority = self.llm_service.analyze_incentive(item)

                    # Update item with LLM analysis
                    item.llm_summary = summary
                    item.llm_relevance_score = relevance_score
                    item.priority = priority
                    item.llm_analysis_date = datetime.now()

                    # Auto-filter based on relevance score
                    if hasattr(self.config, 'min_relevance_score') and relevance_score < self.config.min_relevance_score:
                        item.status = IncentiveStatus.NOT_RELEVANT

                    analyzed_count += 1
                    logger.info(f"Analysis completed for item {i+1}: score={relevance_score}, priority={priority.value}")

                    # Add small delay to avoid rate limiting
                    if i < len(items) - 1:
                        time.sleep(0.5)

                except Exception as item_error:
                    logger.error(f"Error analyzing individual item {i+1}: {item_error}")
                    # Continue with next item
                    continue

            logger.info(f"LLM analysis completed: {analyzed_count}/{len(items)} items analyzed successfully")

        except Exception as e:
            logger.error(f"Error in LLM analysis: {e}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
    
    def _save_incentive_item(self, item: IncentiveItem):
        """Save incentive item to database"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                data = incentive_item_to_dict(item)
                
                columns = ', '.join(data.keys())
                placeholders = ', '.join(['?' for _ in data])
                
                cursor.execute(
                    f"INSERT OR REPLACE INTO incentives ({columns}) VALUES ({placeholders})",
                    list(data.values())
                )
                conn.commit()
                
        except Exception as e:
            logger.error(f"Error saving incentive item: {e}")
    
    def _save_monitoring_session(self, session: MonitoringSession):
        """Save monitoring session to database"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT OR REPLACE INTO monitoring_sessions 
                    (id, start_time, end_time, sources_checked, keywords_used, 
                     items_found, new_items, errors_encountered, status, success)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    str(session.id),
                    session.start_time.isoformat(),
                    session.end_time.isoformat() if session.end_time else None,
                    ','.join([s.value for s in session.sources_checked]),
                    ','.join(session.keywords_used),
                    session.items_found,
                    session.new_items,
                    ','.join(session.errors_encountered),
                    session.status,
                    session.success
                ))
                conn.commit()
                
        except Exception as e:
            logger.error(f"Error saving monitoring session: {e}")
    
    def _send_notification_email(self, items: List[IncentiveItem]):
        """Send email notification about new incentives"""
        try:
            if not self.config.notification_email:
                logger.warning("No notification email configured")
                return
            
            # Generate email content
            subject = f"🎯 Nuovi Incentivi Trovati - {len(items)} opportunità"
            
            # Create HTML email body
            html_body = self._create_notification_email_html(items)
            
            # Send email
            success = self.email_service.send_email(
                to_email=self.config.notification_email,
                subject=subject,
                body="",  # Plain text version
                html_body=html_body
            )
            
            if success:
                logger.info(f"Notification email sent to {self.config.notification_email}")
            else:
                logger.error("Failed to send notification email")
                
        except Exception as e:
            logger.error(f"Error sending notification email: {e}")
    
    def _create_notification_email_html(self, items: List[IncentiveItem]) -> str:
        """Create HTML email body for notifications"""
        items_html = ""
        for item in items[:10]:  # Limit to first 10 items
            priority_color = {
                IncentivePriority.HIGH: "#dc3545",
                IncentivePriority.MEDIUM: "#ffc107", 
                IncentivePriority.LOW: "#28a745"
            }.get(item.priority, "#6c757d")
            
            items_html += f"""
            <div style="border: 1px solid #ddd; border-radius: 8px; padding: 16px; margin: 12px 0;">
                <h3 style="color: #333; margin: 0 0 8px 0;">{item.title}</h3>
                <p style="color: #666; margin: 0 0 8px 0;">{item.description[:200]}...</p>
                <div style="display: flex; gap: 12px; align-items: center;">
                    <span style="background: {priority_color}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                        {item.priority.value.upper()}
                    </span>
                    <span style="color: #666; font-size: 14px;">Fonte: {item.source.value}</span>
                    {f'<span style="color: #666; font-size: 14px;">Score: {item.llm_relevance_score:.1f}</span>' if item.llm_relevance_score > 0 else ''}
                </div>
                {f'<a href="{item.source_url}" style="color: #007bff; text-decoration: none;">Visualizza dettagli →</a>' if item.source_url else ''}
            </div>
            """
        
        return f"""
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <h1 style="color: #007bff;">🎯 Nuovi Incentivi Finanziari Trovati</h1>
            <p>Il sistema di monitoraggio ha trovato <strong>{len(items)} nuovi incentivi</strong> che potrebbero interessarti:</p>
            
            {items_html}
            
            <hr style="margin: 24px 0;">
            <p style="color: #666; font-size: 14px;">
                Questo è un messaggio automatico dal sistema di monitoraggio incentivi di Agevolami PM.<br>
                Data scansione: {datetime.now().strftime('%d/%m/%Y alle %H:%M')}
            </p>
        </body>
        </html>
        """
    
    def _setup_scheduled_monitoring(self):
        """Setup scheduled monitoring based on configuration"""
        try:
            schedule.clear('incentives')
            
            if self.config.frequency == 'daily':
                schedule.every().day.at("09:00").do(self.run_monitoring_session).tag('incentives')
            elif self.config.frequency == 'weekly':
                schedule.every().monday.at("09:00").do(self.run_monitoring_session).tag('incentives')
            elif self.config.frequency == 'monthly':
                schedule.every().month.do(self.run_monitoring_session).tag('incentives')
            
            logger.info(f"Scheduled monitoring setup: {self.config.frequency}")
            
        except Exception as e:
            logger.error(f"Error setting up scheduled monitoring: {e}")
    
    def _get_scraper_config(self) -> Dict[str, Any]:
        """Get configuration for standard web scraper"""
        return {
            'sources': self.config.sources,
            'user_agent': self.config.user_agent,
            'request_delay_seconds': self.config.request_delay_seconds,
            'max_items_per_session': self.config.max_items_per_session,
            'websites': self.full_config.get('websites', [])
        }

    def _get_enhanced_scraper_config(self) -> Dict[str, Any]:
        """Get configuration for enhanced web scraper"""
        base_config = self._get_scraper_config()

        # Add enhanced configuration options
        enhanced_config = {
            **base_config,
            'scraping_method': self.full_config.get('scraping_method', 'requests'),
            'timeout_seconds': self.full_config.get('timeout_seconds', 30),
            'validation': self.full_config.get('validation', {
                'min_title_length': 10,
                'max_title_length': 200,
                'min_description_length': 20,
                'max_description_length': 1000,
                'forbidden_keywords': ['cookie', 'privacy', 'login'],
                'max_duplicate_similarity': 0.85
            }),
            'retry': self.full_config.get('retry', {
                'max_retries': 3,
                'base_delay': 1.0,
                'max_delay': 60.0,
                'exponential_base': 2.0,
                'jitter': True
            })
        }

        return enhanced_config
    
    def _get_llm_config(self) -> Dict[str, Any]:
        """Get configuration for LLM service"""
        return {
            'openrouter_api_key': self.config.openrouter_api_key,
            'llm_model': self.config.llm_model,
            'llm_enabled': self.config.llm_enabled,
            'llm_api_url': getattr(self.config, 'llm_api_url', 'https://openrouter.ai/api/v1')
        }

    def delete_all_incentives(self) -> bool:
        """Delete all incentive records from database"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM incentives")
                deleted_count = cursor.rowcount
                conn.commit()

                logger.info(f"Deleted {deleted_count} incentive records")
                return True

        except Exception as e:
            logger.error(f"Error deleting all incentives: {e}")
            return False

    def reanalyze_incentive(self, incentive_id: str) -> bool:
        """Re-scrape and re-analyze a specific incentive"""
        try:
            # Get the incentive from database
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM incentives WHERE id = ?", (incentive_id,))
                row = cursor.fetchone()

                if not row:
                    logger.error(f"Incentive not found: {incentive_id}")
                    return False

                incentive_data = dict(row)
                source_url = incentive_data.get('source_url')

                if not source_url:
                    logger.error(f"No source URL for incentive: {incentive_id}")
                    return False

                # Re-scrape the specific URL (use enhanced method if available)
                if hasattr(self.web_scraper, 'scrape_single_url_enhanced'):
                    updated_content = self.web_scraper.scrape_single_url_enhanced(source_url)
                else:
                    updated_content = self.web_scraper.scrape_single_url(source_url)

                if updated_content:
                    # Create updated incentive item
                    from core.models.incentive_models import dict_to_incentive_item
                    incentive_item = dict_to_incentive_item(incentive_data)

                    # Update with new content
                    incentive_item.raw_content = updated_content
                    incentive_item.updated_at = datetime.now()

                    # Re-analyze with LLM if enabled
                    if self.config.llm_enabled:
                        summary, relevance_score, priority = self.llm_service.analyze_incentive(incentive_item)
                        incentive_item.llm_summary = summary
                        incentive_item.llm_relevance_score = relevance_score
                        incentive_item.priority = priority
                        incentive_item.llm_analysis_date = datetime.now()

                    # Save updated incentive
                    self._save_incentive_item(incentive_item)

                    logger.info(f"Successfully re-analyzed incentive: {incentive_id}")
                    return True
                else:
                    logger.error(f"Failed to re-scrape URL: {source_url}")
                    return False

        except Exception as e:
            logger.error(f"Error re-analyzing incentive {incentive_id}: {e}")
            return False

    def get_monitoring_statistics(self) -> Dict[str, Any]:
        """Get monitoring statistics"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()

                # Total incentives
                cursor.execute("SELECT COUNT(*) FROM incentives")
                total_incentives = cursor.fetchone()[0]

                # New incentives (last 30 days)
                cursor.execute("""
                    SELECT COUNT(*) FROM incentives
                    WHERE found_date >= date('now', '-30 days')
                """)
                new_incentives = cursor.fetchone()[0]

                # High priority incentives
                cursor.execute("""
                    SELECT COUNT(*) FROM incentives
                    WHERE priority = 'high'
                """)
                high_priority = cursor.fetchone()[0]

                # Average relevance score
                cursor.execute("SELECT AVG(llm_relevance_score) FROM incentives")
                avg_relevance = cursor.fetchone()[0] or 0.0

                return {
                    'total_incentives': total_incentives,
                    'new_incentives': new_incentives,
                    'high_priority': high_priority,
                    'avg_relevance_score': round(avg_relevance, 2)
                }

        except Exception as e:
            logger.error(f"Error getting monitoring statistics: {e}")
            return {
                'total_incentives': 0,
                'new_incentives': 0,
                'high_priority': 0,
                'avg_relevance_score': 0.0
            }

    def get_health_report(self) -> Dict[str, Any]:
        """Get website health monitoring report"""
        try:
            if hasattr(self.web_scraper, 'get_health_report'):
                return self.web_scraper.get_health_report()
            else:
                # Fallback for standard scraper
                return {
                    'message': 'Health monitoring requires enhanced web scraping service',
                    'enhanced_available': ENHANCED_SCRAPING_AVAILABLE
                }
        except Exception as e:
            logger.error(f"Error getting health report: {e}")
            return {}

    def get_selector_report(self) -> Dict[str, Any]:
        """Get selector validation report"""
        try:
            if hasattr(self.web_scraper, 'get_selector_report'):
                return self.web_scraper.get_selector_report()
            else:
                # Fallback for standard scraper
                return {
                    'message': 'Selector validation requires enhanced web scraping service',
                    'enhanced_available': ENHANCED_SCRAPING_AVAILABLE
                }
        except Exception as e:
            logger.error(f"Error getting selector report: {e}")
            return {}

    def run_health_check(self) -> Dict[str, Any]:
        """Run comprehensive health check on all configured websites"""
        try:
            logger.info("Running comprehensive health check...")

            health_results = {}
            for website in self.full_config.get('websites', []):
                if not website.get('enabled', True):
                    continue

                website_name = website.get('name', '')
                base_url = website.get('url', '')

                if not base_url:
                    continue

                logger.info(f"Checking health for {website_name}...")
                if hasattr(self.web_scraper, 'check_website_health'):
                    health = self.web_scraper.check_website_health(base_url)
                else:
                    # Fallback health check for standard scraper
                    try:
                        import requests
                        import time
                        start_time = time.time()
                        response = requests.head(base_url, timeout=10)
                        response_time = time.time() - start_time

                        # Create a simple health object
                        from types import SimpleNamespace
                        health = SimpleNamespace(
                            url=base_url,
                            is_healthy=response.status_code < 400,
                            response_time=response_time,
                            status_code=response.status_code,
                            error_message="",
                            consecutive_failures=0,
                            last_check=datetime.now()
                        )
                    except Exception as ex:
                        health = SimpleNamespace(
                            url=base_url,
                            is_healthy=False,
                            response_time=0,
                            status_code=0,
                            error_message=str(ex),
                            consecutive_failures=1,
                            last_check=datetime.now()
                        )

                health_results[website_name] = {
                    'url': health.url,
                    'is_healthy': health.is_healthy,
                    'response_time': health.response_time,
                    'status_code': health.status_code,
                    'error_message': health.error_message,
                    'consecutive_failures': health.consecutive_failures,
                    'last_check': health.last_check.isoformat()
                }

            # Get overall health report
            overall_report = self.web_scraper.get_health_report()

            return {
                'individual_sites': health_results,
                'overall_report': overall_report,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error running health check: {e}")
            return {'error': str(e)}

    def run_selector_validation(self) -> Dict[str, Any]:
        """Run selector validation on all configured websites"""
        try:
            logger.info("Running selector validation...")

            validation_results = {}

            # Common selectors to test
            test_selectors = [
                'article', '.news-item', '.content-item', '.card',
                'h1', 'h2', 'h3', 'a[href]', '.title', '.headline'
            ]

            for website in self.full_config.get('websites', []):
                if not website.get('enabled', True):
                    continue

                website_name = website.get('name', '')
                base_url = website.get('url', '')
                search_paths = website.get('search_paths', ['/'])

                if not base_url:
                    continue

                logger.info(f"Validating selectors for {website_name}...")

                # Test selectors on first search path
                test_url = f"{base_url}{search_paths[0]}" if search_paths else base_url

                if hasattr(self.web_scraper, 'validate_selectors'):
                    validations = self.web_scraper.validate_selectors(test_url, test_selectors)
                else:
                    # Fallback selector validation for standard scraper
                    validations = []
                    try:
                        import requests
                        from bs4 import BeautifulSoup
                        response = requests.get(test_url, timeout=10)
                        response.raise_for_status()
                        soup = BeautifulSoup(response.content, 'html.parser')

                        from types import SimpleNamespace
                        for selector in test_selectors:
                            try:
                                elements = soup.select(selector)
                                validation = SimpleNamespace(
                                    selector=selector,
                                    is_valid=len(elements) > 0,
                                    elements_found=len(elements),
                                    error_message=""
                                )
                            except Exception as ex:
                                validation = SimpleNamespace(
                                    selector=selector,
                                    is_valid=False,
                                    elements_found=0,
                                    error_message=str(ex)
                                )
                            validations.append(validation)
                    except Exception as ex:
                        logger.error(f"Error validating selectors for {test_url}: {ex}")
                        validations = [SimpleNamespace(
                            selector=s,
                            is_valid=False,
                            elements_found=0,
                            error_message=f"Page load error: {ex}"
                        ) for s in test_selectors]

                validation_results[website_name] = {
                    'url': test_url,
                    'validations': [
                        {
                            'selector': v.selector,
                            'is_valid': v.is_valid,
                            'elements_found': v.elements_found,
                            'error_message': v.error_message
                        }
                        for v in validations
                    ],
                    'working_selectors': len([v for v in validations if v.is_valid]),
                    'total_selectors': len(validations)
                }

            # Get overall selector report
            overall_report = self.web_scraper.get_selector_report()

            return {
                'individual_sites': validation_results,
                'overall_report': overall_report,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error running selector validation: {e}")
            return {'error': str(e)}

    def cleanup_resources(self):
        """Cleanup monitoring service resources"""
        try:
            if hasattr(self.web_scraper, 'cleanup'):
                self.web_scraper.cleanup()
            elif hasattr(self.web_scraper, 'session'):
                # Cleanup standard scraper session
                self.web_scraper.session.close()
            logger.info("Monitoring service resources cleaned up")
        except Exception as e:
            logger.error(f"Error cleaning up resources: {e}")
