#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Enhanced Web Scraping Service for Italian Financial Incentives
Includes Selenium support, content validation, health monitoring, 
selector validation, advanced deduplication, and retry logic
"""

import requests
import time
import hashlib
import re
import json
from typing import List, Dict, Any, Optional, Tuple, Set
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import difflib
from pathlib import Path

# Selenium imports (optional)
try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options as ChromeOptions
    from selenium.webdriver.firefox.options import Options as FirefoxOptions
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException, WebDriverException
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

from core.utils.logger import get_logger
from core.models.incentive_models import IncentiveItem, IncentiveSource, IncentiveStatus, IncentivePriority

logger = get_logger(__name__)

class ScrapingMethod(Enum):
    """Scraping method options"""
    REQUESTS = "requests"
    SELENIUM = "selenium"
    HYBRID = "hybrid"

@dataclass
class ContentValidationConfig:
    """Configuration for content validation"""
    min_title_length: int = 10
    max_title_length: int = 200
    min_description_length: int = 20
    max_description_length: int = 1000
    required_keywords: List[str] = field(default_factory=list)
    forbidden_keywords: List[str] = field(default_factory=list)
    min_relevance_score: float = 0.3
    max_duplicate_similarity: float = 0.85

@dataclass
class WebsiteHealth:
    """Website health status"""
    url: str
    is_healthy: bool
    last_check: datetime
    response_time: float
    status_code: int
    error_message: str = ""
    consecutive_failures: int = 0

@dataclass
class SelectorValidation:
    """Selector validation result"""
    selector: str
    is_valid: bool
    elements_found: int
    last_validated: datetime
    error_message: str = ""

@dataclass
class RetryConfig:
    """Retry configuration"""
    max_retries: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    exponential_base: float = 2.0
    jitter: bool = True

class EnhancedWebScrapingService:
    """Enhanced service for scraping Italian financial incentives websites"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.session = requests.Session()
        
        # Enhanced configurations
        self.validation_config = ContentValidationConfig(**config.get('validation', {}))
        self.retry_config = RetryConfig(**config.get('retry', {}))
        self.scraping_method = ScrapingMethod(config.get('scraping_method', 'requests'))
        
        # Health monitoring
        self.website_health: Dict[str, WebsiteHealth] = {}
        self.selector_validations: Dict[str, List[SelectorValidation]] = {}
        
        # Deduplication
        self.content_fingerprints: Set[str] = set()
        self.similarity_cache: Dict[str, List[str]] = {}
        
        # Selenium driver (if available)
        self.selenium_driver = None
        
        # Configure enhanced retry strategy
        retry_strategy = Retry(
            total=self.retry_config.max_retries,
            backoff_factor=self.retry_config.base_delay,
            status_forcelist=[429, 500, 502, 503, 504, 520, 521, 522, 523, 524],
            allowed_methods=["HEAD", "GET", "OPTIONS"],
            raise_on_status=False
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)

        # Enhanced headers with rotation capability - more realistic user agents
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/20100101 Firefox/121.0',
            # Add more realistic browser variations
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36 Edg/118.0.2088.76',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
            'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:121.0) Gecko/20100101 Firefox/121.0'
        ]

        self.request_delay = config.get('request_delay_seconds', 2.0)
        self.timeout = config.get('timeout_seconds', 30)
        self.websites_config = config.get('websites', [])
        self.current_user_agent_index = 0

        # Set initial headers - will be updated per request
        self._update_session_headers()
        
        # Initialize Selenium if available and configured
        if SELENIUM_AVAILABLE and self.scraping_method in [ScrapingMethod.SELENIUM, ScrapingMethod.HYBRID]:
            self._init_selenium()
        
        # Load health monitoring data
        self._load_health_data()
        
        logger.info(f"Enhanced web scraping service initialized with method: {self.scraping_method.value}")

    def _update_session_headers(self):
        """Update session headers with realistic browser headers"""
        import random

        # Rotate user agent
        user_agent = self.user_agents[self.current_user_agent_index]

        # Generate realistic headers based on user agent
        headers = {
            'User-Agent': user_agent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'it-IT,it;q=0.9,en-US;q=0.8,en;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0',
            'DNT': '1'
        }

        # Add browser-specific headers
        if 'Chrome' in user_agent:
            headers.update({
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-User': '?1',
                'Sec-CH-UA': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                'Sec-CH-UA-Mobile': '?0',
                'Sec-CH-UA-Platform': '"Windows"'
            })
        elif 'Firefox' in user_agent:
            headers.update({
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-User': '?1'
            })
        elif 'Safari' in user_agent and 'Chrome' not in user_agent:
            # Safari-specific headers
            headers['Accept'] = 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'

        # Add some randomization to avoid fingerprinting
        if random.random() < 0.5:
            headers['Sec-GPC'] = '1'

        self.session.headers.update(headers)

    def _rotate_user_agent(self):
        """Rotate to next user agent to avoid detection"""
        self.current_user_agent_index = (self.current_user_agent_index + 1) % len(self.user_agents)
        self._update_session_headers()
        logger.debug(f"Rotated to user agent: {self.user_agents[self.current_user_agent_index][:50]}...")

    def _init_selenium(self):
        """Initialize Selenium WebDriver"""
        try:
            chrome_options = ChromeOptions()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            
            self.selenium_driver = webdriver.Chrome(options=chrome_options)
            self.selenium_driver.set_page_load_timeout(self.timeout)
            logger.info("Selenium WebDriver initialized successfully")
            
        except Exception as e:
            logger.warning(f"Failed to initialize Selenium WebDriver: {e}")
            self.selenium_driver = None
            if self.scraping_method == ScrapingMethod.SELENIUM:
                self.scraping_method = ScrapingMethod.REQUESTS
                logger.info("Falling back to requests-only scraping")

    def _load_health_data(self):
        """Load website health monitoring data"""
        try:
            health_file = Path("data/website_health.json")
            if health_file.exists():
                with open(health_file, 'r', encoding='utf-8') as f:
                    health_data = json.load(f)
                    for url, data in health_data.items():
                        self.website_health[url] = WebsiteHealth(
                            url=url,
                            is_healthy=data['is_healthy'],
                            last_check=datetime.fromisoformat(data['last_check']),
                            response_time=data['response_time'],
                            status_code=data['status_code'],
                            error_message=data.get('error_message', ''),
                            consecutive_failures=data.get('consecutive_failures', 0)
                        )
                logger.info(f"Loaded health data for {len(self.website_health)} websites")
        except Exception as e:
            logger.warning(f"Could not load health data: {e}")

    def _save_health_data(self):
        """Save website health monitoring data"""
        try:
            health_data = {}
            for url, health in self.website_health.items():
                health_data[url] = {
                    'is_healthy': health.is_healthy,
                    'last_check': health.last_check.isoformat(),
                    'response_time': health.response_time,
                    'status_code': health.status_code,
                    'error_message': health.error_message,
                    'consecutive_failures': health.consecutive_failures
                }
            
            health_file = Path("data/website_health.json")
            health_file.parent.mkdir(exist_ok=True)
            with open(health_file, 'w', encoding='utf-8') as f:
                json.dump(health_data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"Could not save health data: {e}")

    def check_website_health(self, url: str) -> WebsiteHealth:
        """Check health of a specific website"""
        start_time = time.time()
        
        try:
            response = self.session.head(url, timeout=self.timeout)
            response_time = time.time() - start_time
            
            is_healthy = response.status_code < 400
            health = WebsiteHealth(
                url=url,
                is_healthy=is_healthy,
                last_check=datetime.now(),
                response_time=response_time,
                status_code=response.status_code,
                consecutive_failures=0 if is_healthy else self.website_health.get(url, WebsiteHealth(url, False, datetime.now(), 0, 0)).consecutive_failures + 1
            )
            
        except Exception as e:
            response_time = time.time() - start_time
            previous_health = self.website_health.get(url, WebsiteHealth(url, False, datetime.now(), 0, 0))
            
            health = WebsiteHealth(
                url=url,
                is_healthy=False,
                last_check=datetime.now(),
                response_time=response_time,
                status_code=0,
                error_message=str(e),
                consecutive_failures=previous_health.consecutive_failures + 1
            )
        
        self.website_health[url] = health
        self._save_health_data()
        
        logger.info(f"Health check for {url}: {'✓' if health.is_healthy else '✗'} ({health.response_time:.2f}s)")
        return health

    def validate_selectors(self, url: str, selectors: List[str]) -> List[SelectorValidation]:
        """Validate CSS selectors on a webpage"""
        validations = []

        try:
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            soup = BeautifulSoup(response.content, 'html.parser')

            for selector in selectors:
                try:
                    elements = soup.select(selector)
                    validation = SelectorValidation(
                        selector=selector,
                        is_valid=len(elements) > 0,
                        elements_found=len(elements),
                        last_validated=datetime.now()
                    )
                except Exception as e:
                    validation = SelectorValidation(
                        selector=selector,
                        is_valid=False,
                        elements_found=0,
                        last_validated=datetime.now(),
                        error_message=str(e)
                    )

                validations.append(validation)
                logger.debug(f"Selector '{selector}' on {url}: {'✓' if validation.is_valid else '✗'} ({validation.elements_found} elements)")

            # Store validations
            self.selector_validations[url] = validations

        except Exception as e:
            logger.error(f"Error validating selectors for {url}: {e}")
            for selector in selectors:
                validations.append(SelectorValidation(
                    selector=selector,
                    is_valid=False,
                    elements_found=0,
                    last_validated=datetime.now(),
                    error_message=f"Page load error: {e}"
                ))

        return validations

    def validate_content(self, item: IncentiveItem) -> bool:
        """Validate content quality based on configuration"""
        config = self.validation_config

        # Check title length
        if len(item.title) < config.min_title_length or len(item.title) > config.max_title_length:
            logger.debug(f"Content validation failed: title length {len(item.title)} not in range [{config.min_title_length}, {config.max_title_length}]")
            return False

        # Check description length
        if len(item.description) < config.min_description_length or len(item.description) > config.max_description_length:
            logger.debug(f"Content validation failed: description length {len(item.description)} not in range [{config.min_description_length}, {config.max_description_length}]")
            return False

        # Check required keywords
        content_text = f"{item.title} {item.description}".lower()
        if config.required_keywords:
            has_required = any(keyword.lower() in content_text for keyword in config.required_keywords)
            if not has_required:
                logger.debug(f"Content validation failed: no required keywords found")
                return False

        # Check forbidden keywords
        if config.forbidden_keywords:
            has_forbidden = any(keyword.lower() in content_text for keyword in config.forbidden_keywords)
            if has_forbidden:
                logger.debug(f"Content validation failed: forbidden keywords found")
                return False

        return True

    def is_duplicate_content(self, item: IncentiveItem) -> bool:
        """Advanced duplicate detection using similarity comparison"""
        content_text = f"{item.title} {item.description}".lower()
        content_hash = hashlib.md5(content_text.encode()).hexdigest()

        # Check exact hash match
        if content_hash in self.content_fingerprints:
            logger.debug(f"Exact duplicate found for: {item.title[:50]}...")
            return True

        # Check similarity with existing content
        for existing_content in self.similarity_cache.get(item.source.value, []):
            similarity = difflib.SequenceMatcher(None, content_text, existing_content).ratio()
            if similarity > self.validation_config.max_duplicate_similarity:
                logger.debug(f"Similar content found (similarity: {similarity:.2f}): {item.title[:50]}...")
                return True

        # Add to caches
        self.content_fingerprints.add(content_hash)
        if item.source.value not in self.similarity_cache:
            self.similarity_cache[item.source.value] = []
        self.similarity_cache[item.source.value].append(content_text)

        # Limit cache size
        if len(self.similarity_cache[item.source.value]) > 1000:
            self.similarity_cache[item.source.value] = self.similarity_cache[item.source.value][-500:]

        return False

    def scrape_with_retry(self, url: str, method: ScrapingMethod = None) -> Optional[BeautifulSoup]:
        """Scrape URL with retry logic"""
        if method is None:
            method = self.scraping_method

        config = self.retry_config

        for attempt in range(config.max_retries + 1):
            try:
                if method == ScrapingMethod.SELENIUM and self.selenium_driver:
                    return self._scrape_with_selenium(url)
                else:
                    return self._scrape_with_requests(url)

            except Exception as e:
                if attempt == config.max_retries:
                    logger.error(f"Failed to scrape {url} after {config.max_retries + 1} attempts: {e}")
                    return None

                # Handle specific error types differently
                if isinstance(e, requests.exceptions.HTTPError):
                    status_code = e.response.status_code if e.response else 0
                    if status_code == 403:
                        # For 403 errors, rotate user agent and use longer delay
                        self._rotate_user_agent()
                        delay = config.base_delay * 3  # Longer delay for 403
                    elif status_code == 429:
                        # Rate limiting - use much longer delay
                        delay = config.base_delay * 5
                    elif status_code >= 500:
                        # Server errors - shorter delay, might be temporary
                        delay = config.base_delay
                    else:
                        # Other HTTP errors
                        delay = config.base_delay * 2
                else:
                    # Calculate delay with exponential backoff for other errors
                    delay = min(
                        config.base_delay * (config.exponential_base ** attempt),
                        config.max_delay
                    )

                # Add jitter if configured
                if config.jitter:
                    import random
                    delay *= (0.5 + random.random() * 0.5)

                logger.warning(f"Attempt {attempt + 1} failed for {url}: {e}. Retrying in {delay:.1f}s...")
                time.sleep(delay)

        return None

    def _establish_session(self, base_url: str) -> bool:
        """Establish a session with the website to avoid bot detection"""
        try:
            import random
            from urllib.parse import urljoin

            # First, visit the homepage to establish a session
            logger.debug(f"Establishing session with {base_url}")

            # Update headers for this domain
            self._update_session_headers()

            # Add a referer for subsequent requests
            self.session.headers['Referer'] = base_url

            # Visit homepage first
            response = self.session.get(base_url, timeout=self.timeout)
            if response.status_code == 200:
                # Small delay to mimic human behavior
                time.sleep(random.uniform(1, 3))
                return True
            else:
                logger.warning(f"Failed to establish session with {base_url}: {response.status_code}")
                return False

        except Exception as e:
            logger.warning(f"Error establishing session with {base_url}: {e}")
            return False

    def _scrape_with_requests(self, url: str) -> BeautifulSoup:
        """Scrape using requests library with enhanced error handling"""
        try:
            import random
            from urllib.parse import urlparse

            # Extract base URL for session establishment
            parsed_url = urlparse(url)
            base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"

            # Establish session if this is a new domain
            if not hasattr(self, '_established_sessions'):
                self._established_sessions = set()

            if base_url not in self._established_sessions:
                if self._establish_session(base_url):
                    self._established_sessions.add(base_url)

            # Add random delay to avoid rate limiting
            delay = self.request_delay + random.uniform(0, 2)
            time.sleep(delay)

            # Set referer to make request look more natural
            if 'Referer' not in self.session.headers:
                self.session.headers['Referer'] = base_url

            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            return BeautifulSoup(response.content, 'html.parser')

        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 403:
                # Try with different user agent on 403
                logger.warning(f"403 Forbidden for {url}, trying with different user agent")
                self._rotate_user_agent()
                time.sleep(random.uniform(5, 10))  # Longer delay after 403

                response = self.session.get(url, timeout=self.timeout)
                response.raise_for_status()
                return BeautifulSoup(response.content, 'html.parser')
            else:
                raise

    def _scrape_with_selenium(self, url: str) -> BeautifulSoup:
        """Scrape using Selenium WebDriver"""
        if not self.selenium_driver:
            raise Exception("Selenium driver not available")

        self.selenium_driver.get(url)

        # Wait for page to load
        WebDriverWait(self.selenium_driver, 10).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )

        # Wait for dynamic content (if any)
        time.sleep(2)

        return BeautifulSoup(self.selenium_driver.page_source, 'html.parser')

    def scrape_all_sources(self, keywords: List[str]) -> List[IncentiveItem]:
        """Enhanced scraping of all configured sources"""
        all_items = []

        # Check website health first
        logger.info("Checking website health...")
        unhealthy_sites = []
        for website in self.websites_config:
            if not website.get('enabled', True):
                continue

            health = self.check_website_health(website['url'])
            if not health.is_healthy:
                unhealthy_sites.append(website['name'])
                logger.warning(f"Website {website['name']} is unhealthy: {health.error_message}")

        if unhealthy_sites:
            logger.warning(f"Unhealthy websites detected: {', '.join(unhealthy_sites)}")

        # Scrape sources with parallel processing
        sources = self.config.get('sources', [])

        with ThreadPoolExecutor(max_workers=3) as executor:
            future_to_source = {
                executor.submit(self._scrape_source_enhanced, source, keywords): source
                for source in sources
            }

            for future in as_completed(future_to_source):
                source = future_to_source[future]
                try:
                    items = future.result()
                    all_items.extend(items)
                    logger.info(f"Found {len(items)} items from {source.value}")
                except Exception as e:
                    logger.error(f"Error scraping {source.value}: {e}")

        # Scrape custom websites
        try:
            logger.info("Scraping custom websites...")
            custom_items = self._scrape_custom_websites_enhanced(keywords)
            all_items.extend(custom_items)
            logger.info(f"Found {len(custom_items)} items from custom websites")
        except Exception as e:
            logger.error(f"Error scraping custom websites: {e}")

        # Apply content validation and deduplication
        validated_items = []
        for item in all_items:
            if self.validate_content(item) and not self.is_duplicate_content(item):
                validated_items.append(item)
            else:
                logger.debug(f"Item filtered out: {item.title[:50]}...")

        logger.info(f"After validation and deduplication: {len(validated_items)}/{len(all_items)} items")
        return validated_items

    def _scrape_source_enhanced(self, source: IncentiveSource, keywords: List[str]) -> List[IncentiveItem]:
        """Enhanced scraping of a specific source"""
        if source == IncentiveSource.MIMIT:
            return self._scrape_mimit_enhanced(keywords)
        elif source == IncentiveSource.INVITALIA:
            return self._scrape_invitalia_enhanced(keywords)
        elif source == IncentiveSource.SIMEST:
            return self._scrape_simest_enhanced(keywords)
        elif source == IncentiveSource.OTHER:
            return self._scrape_custom_websites_enhanced(keywords)
        else:
            logger.warning(f"Unknown source: {source}")
            return []

    def _scrape_mimit_enhanced(self, keywords: List[str]) -> List[IncentiveItem]:
        """Enhanced MIMIT scraping with selector validation"""
        items = []

        mimit_config = self._get_website_config("MIMIT")
        if not mimit_config:
            logger.warning("MIMIT configuration not found")
            return items

        base_url = mimit_config['url']
        search_paths = mimit_config.get('search_paths', [])

        # Enhanced selectors with balanced approach
        content_selectors = [
            # Primary MIMIT content selectors
            'article.card', 'div.card', '.card-body', '.card-wrapper',
            'li:has(a)', 'li:has(img)', 'li:has(h3)', 'li:has(h4)',
            # News and content containers
            'article', 'section', '.news-item', '.content-item', '.list-item',
            # Links to relevant content
            'a[href*="/notizie-stampa/"]', 'a[href*="/incentivi/"]', 'a[href*="/bandi/"]',
            'a[href*="agevolaz"]', 'a[href*="finanziament"]',
            # Headers and content
            'h2', 'h3', 'h4', 'h5',
            # Generic content containers
            '.row .col', '.container .row', '.list-group-item',
            '[class*="card"]', '[class*="item"]', '[class*="news"]'
        ]

        urls_to_check = [f"{base_url}{path}" for path in search_paths] if search_paths else [
            f"{base_url}/it/incentivi-mise",
            f"{base_url}/it/notizie-stampa",
            f"{base_url}/it/per-l-impresa"
        ]

        for url in urls_to_check:
            # Check if URL is healthy
            health = self.website_health.get(url)
            if health and not health.is_healthy and health.consecutive_failures > 3:
                logger.warning(f"Skipping unhealthy URL: {url}")
                continue

            # Validate selectors with more comprehensive testing
            validations = self.validate_selectors(url, content_selectors[:10])  # Test first 10 selectors
            working_selectors = [v.selector for v in validations if v.is_valid and v.elements_found > 0]

            if not working_selectors:
                logger.warning(f"No working selectors found for {url}")
                # Try to discover content automatically
                soup_test = self.scrape_with_retry(url)
                if soup_test:
                    discovered_selectors = self._discover_content_selectors(soup_test)
                    logger.info(f"Discovered {len(discovered_selectors)} potential selectors for {url}")
                    working_selectors = discovered_selectors if discovered_selectors else content_selectors
                else:
                    working_selectors = content_selectors  # Use all as fallback

            soup = self.scrape_with_retry(url)
            if not soup:
                continue

            items_found_on_page = 0
            selector_stats = {}

            for selector in working_selectors:
                elements = soup.select(selector)
                selector_items = 0

                for element in elements:
                    item = self._extract_item_enhanced(element, IncentiveSource.MIMIT, base_url, keywords)
                    if item:
                        items.append(item)
                        items_found_on_page += 1
                        selector_items += 1

                        # Limit items per page to avoid overwhelming
                        if items_found_on_page >= 50:
                            break

                if selector_items > 0:
                    selector_stats[selector] = selector_items
                    logger.debug(f"Selector '{selector}' found {selector_items} items on {url}")

                if items_found_on_page >= 50:
                    break

            logger.info(f"Found {items_found_on_page} items on {url} using {len(selector_stats)} selectors")
            if selector_stats:
                logger.debug(f"Selector performance: {selector_stats}")

            time.sleep(self.request_delay)

        return items

    def _discover_content_selectors(self, soup: BeautifulSoup) -> List[str]:
        """Automatically discover potential content selectors from page structure"""
        discovered = []

        try:
            # Look for common content patterns
            patterns = [
                # Elements with meaningful text content
                'article', 'section', '.content', '.main', '.news', '.post',
                # Lists that might contain items
                'ul li', 'ol li', '.list-item', '.item',
                # Cards and containers
                '.card', '.box', '.container', '.wrapper',
                # Links with substantial text
                'a[href]:has(text)',
                # Headers that might indicate content sections
                'h2', 'h3', 'h4'
            ]

            for pattern in patterns:
                elements = soup.select(pattern)
                if len(elements) > 0:
                    # Check if elements have meaningful content
                    meaningful_count = 0
                    for elem in elements[:5]:  # Check first 5 elements
                        text = elem.get_text(strip=True)
                        if len(text) > 20 and not any(skip in text.lower() for skip in ['cookie', 'privacy', 'login']):
                            meaningful_count += 1

                    if meaningful_count > 0:
                        discovered.append(pattern)
                        logger.debug(f"Discovered selector '{pattern}': {len(elements)} elements, {meaningful_count} meaningful")

            # Sort by potential relevance (prefer more specific selectors)
            discovered.sort(key=lambda x: (x.count('.'), x.count('#'), -len(x)))

        except Exception as e:
            logger.error(f"Error discovering selectors: {e}")

        return discovered[:10]  # Return top 10 discovered selectors

    def _scrape_invitalia_enhanced(self, keywords: List[str]) -> List[IncentiveItem]:
        """Enhanced Invitalia scraping"""
        items = []

        invitalia_config = self._get_website_config("Invitalia")
        if not invitalia_config:
            logger.warning("Invitalia configuration not found")
            return items

        base_url = invitalia_config['url']
        search_paths = invitalia_config.get('search_paths', [])

        content_selectors = [
            # Invitalia content selectors
            '.incentive-card', '.news-card', '.content-card',
            '.measure-item', 'article', '.post',
            '.card-wrapper', '.card-body', '.news-item',
            'a[href*="/incentivi-e-strumenti/"]', 'a[href*="/per-le-imprese/"]',
            'h2', 'h3', 'h4',
            '[class*="card"]', '[class*="news"]', '[class*="item"]'
        ]

        urls_to_check = [f"{base_url}{path}" for path in search_paths] if search_paths else [
            f"{base_url}/per-le-imprese",
            f"{base_url}/news",
            f"{base_url}/per-chi-vuole-fare-impresa"
        ]

        for url in urls_to_check:
            soup = self.scrape_with_retry(url)
            if not soup:
                continue

            items_found_on_page = 0
            for selector in content_selectors:
                elements = soup.select(selector)
                for element in elements:
                    item = self._extract_item_enhanced(element, IncentiveSource.INVITALIA, base_url, keywords)
                    if item:
                        items.append(item)
                        items_found_on_page += 1

                        if items_found_on_page >= 50:
                            break

                if items_found_on_page >= 50:
                    break

            logger.info(f"Found {items_found_on_page} items on {url}")
            time.sleep(self.request_delay)

        return items

    def _scrape_simest_enhanced(self, keywords: List[str]) -> List[IncentiveItem]:
        """Enhanced SIMEST scraping"""
        items = []

        simest_config = self._get_website_config("SIMEST")
        if not simest_config:
            logger.warning("SIMEST configuration not found")
            return items

        base_url = simest_config['url']
        search_paths = simest_config.get('search_paths', [])

        content_selectors = [
            # SIMEST specific financing content
            'div:has(h4:contains("Finanziamenti"))', 'div:has(h3:contains("agevolat"))',
            'section:has(h2:contains("finanziament"))', 'article:has(h3:contains("internazionalizzazione"))',
            # Specific SIMEST service areas
            'a[href*="/finanziamenti-agevolati/"]:not([href*="cookie"]):not([href*="privacy"])',
            'a[href*="/investimenti-partecipativi"]:not([href*="cookie"]):not([href*="privacy"])',
            'a[href*="/export-credit"]:not([href*="cookie"]):not([href*="privacy"])',
            # Content with financing keywords
            'div:has(h4:contains("agevolat"))', 'section:has(h3:contains("export"))',
            'article:has(h2:contains("venture"))', 'div:has(h3:contains("equity"))',
            # News and updates about financing
            '.news-item:has(a[href*="/finanziamenti"])', '.content-item:has(h3:contains("finanziament"))',
            # Headers indicating financing content
            'h2:contains("finanziament")', 'h3:contains("agevolat")', 'h4:contains("internazionalizzazione")'
        ]

        urls_to_check = [f"{base_url}{path}" for path in search_paths] if search_paths else [
            f"{base_url}/per-le-imprese",
            f"{base_url}/per-le-imprese/finanziamenti-agevolati",
            f"{base_url}/media"
        ]

        for url in urls_to_check:
            soup = self.scrape_with_retry(url)
            if not soup:
                continue

            items_found_on_page = 0
            for selector in content_selectors:
                elements = soup.select(selector)
                for element in elements:
                    item = self._extract_item_enhanced(element, IncentiveSource.SIMEST, base_url, keywords)
                    if item:
                        items.append(item)
                        items_found_on_page += 1

                        if items_found_on_page >= 50:
                            break

                if items_found_on_page >= 50:
                    break

            logger.info(f"Found {items_found_on_page} items on {url}")
            time.sleep(self.request_delay)

        return items

    def _scrape_custom_websites_enhanced(self, keywords: List[str]) -> List[IncentiveItem]:
        """Enhanced custom website scraping"""
        items = []

        for website in self.websites_config:
            if not website.get('enabled', True):
                continue

            website_name = website.get('name', '')
            if any(name in website_name.upper() for name in ['MIMIT', 'INVITALIA', 'SIMEST']):
                continue

            try:
                logger.info(f"Scraping custom website: {website_name}")
                base_url = website['url']
                search_paths = website.get('search_paths', ['/'])

                source = IncentiveSource.OTHER
                if 'CAMPANIA' in website_name.upper() or 'regione.campania.it' in base_url:
                    source = IncentiveSource.CAMPANIA

                content_selectors = [
                    '.news-item', '.content-item', '.article-item',
                    'article', '.post', '.card', '.card-body',
                    'h2', 'h3', 'h4', 'a[href*="/"]',
                    '[class*="news"]', '[class*="article"]', '[class*="content"]'
                ]

                urls_to_check = [f"{base_url}{path}" for path in search_paths]

                for url in urls_to_check:
                    # Special handling for Campania region
                    if 'regione.campania.it' in base_url:
                        soup = self._scrape_campania_with_special_handling(url)
                    else:
                        soup = self.scrape_with_retry(url)

                    if not soup:
                        continue

                    items_found_on_page = 0
                    for selector in content_selectors:
                        elements = soup.select(selector)
                        for element in elements:
                            item = self._extract_item_enhanced(element, source, base_url, keywords)
                            if item:
                                items.append(item)
                                items_found_on_page += 1

                                if items_found_on_page >= 30:
                                    break

                        if items_found_on_page >= 30:
                            break

                    logger.info(f"Found {items_found_on_page} items on {url}")
                    time.sleep(self.request_delay)

            except Exception as e:
                logger.error(f"Error scraping custom website {website_name}: {e}")

        return items

    def _scrape_campania_with_special_handling(self, url: str) -> Optional[BeautifulSoup]:
        """Special handling for Regione Campania website with enhanced anti-detection"""
        import random
        import time

        try:
            # Use a more conservative approach for Campania
            logger.debug(f"Using special handling for Campania URL: {url}")

            # Rotate user agent before each request
            self._rotate_user_agent()

            # Add extra headers that might help with government sites
            special_headers = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'it-IT,it;q=0.9,en;q=0.8',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache',
                'Referer': 'https://www.regione.campania.it/',
                'Origin': 'https://www.regione.campania.it'
            }

            # Create a new session for this request to avoid session pollution
            temp_session = requests.Session()
            temp_session.headers.update(self.session.headers)
            temp_session.headers.update(special_headers)

            # Add longer delay for government sites
            delay = random.uniform(5, 10)
            logger.debug(f"Waiting {delay:.1f}s before Campania request")
            time.sleep(delay)

            # Try the request with timeout
            response = temp_session.get(url, timeout=self.timeout * 2)  # Double timeout for government sites

            if response.status_code == 200:
                return BeautifulSoup(response.content, 'html.parser')
            elif response.status_code == 403:
                logger.warning(f"Campania site returned 403 for {url}, trying alternative approach")

                # Try with minimal headers
                minimal_session = requests.Session()
                minimal_session.headers.update({
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
                })

                time.sleep(random.uniform(10, 15))  # Even longer delay
                response = minimal_session.get(url, timeout=self.timeout * 2)

                if response.status_code == 200:
                    return BeautifulSoup(response.content, 'html.parser')
                else:
                    logger.error(f"Campania site still returns {response.status_code} for {url}")
                    return None
            else:
                logger.warning(f"Campania site returned {response.status_code} for {url}")
                return None

        except Exception as e:
            logger.error(f"Error in special Campania handling for {url}: {e}")
            return None

    def _extract_item_enhanced(self, element, source: IncentiveSource, base_url: str, keywords: List[str]) -> Optional[IncentiveItem]:
        """Enhanced item extraction with better validation"""
        try:
            # Extract title with multiple strategies
            title = self._extract_title(element)
            if not title or len(title.strip()) < self.validation_config.min_title_length:
                return None

            # Extract description
            description = self._extract_description(element, title)

            # Extract URL
            source_url = self._extract_url(element, base_url)

            # Enhanced keyword matching
            content_text = f"{title} {description}".lower()
            matched_keywords = [kw for kw in keywords if kw.lower() in content_text]

            # Check relevance
            if keywords and not matched_keywords:
                # Try fuzzy matching for keywords
                import difflib
                for keyword in keywords:
                    matches = difflib.get_close_matches(keyword.lower(), content_text.split(), n=1, cutoff=0.8)
                    if matches:
                        matched_keywords.append(keyword)
                        break

                if not matched_keywords:
                    return None

            # Enhanced content filtering
            if not self._is_relevant_content(title, description):
                return None

            # Create content hash for duplicate detection
            content_hash = hashlib.md5(f"{title}{description}".encode()).hexdigest()

            # Extract raw content (enhanced)
            raw_content = self._extract_raw_content(element)

            # Create incentive item
            item = IncentiveItem(
                title=title.strip(),
                description=description.strip(),
                source=source,
                source_url=source_url,
                content_hash=content_hash,
                keywords_matched=matched_keywords,
                raw_content=raw_content,
                status=IncentiveStatus.NEW,
                priority=IncentivePriority.MEDIUM
            )

            return item

        except Exception as e:
            logger.error(f"Error extracting item from element: {e}")
            return None

    def _extract_title(self, element) -> str:
        """Enhanced title extraction"""
        title = ""

        # Strategy 1: If element is a link, use its text
        if element.name == 'a':
            title = element.get_text(strip=True)

        # Strategy 2: Look for title in child elements
        if not title:
            title_selectors = ['h1', 'h2', 'h3', 'h4', 'h5', '.title', '.headline', '.subject', 'strong', 'b']
            for selector in title_selectors:
                title_elem = element.select_one(selector)
                if title_elem:
                    title = title_elem.get_text(strip=True)
                    if len(title) >= 10:  # Minimum meaningful title length
                        break

        # Strategy 3: Use element text if still no title
        if not title:
            title = element.get_text(strip=True)
            # Take first meaningful sentence
            sentences = title.split('.')
            if sentences:
                title = sentences[0].strip()

        # Clean up title
        title = re.sub(r'\s+', ' ', title)
        title = title.strip()

        # Limit title length
        if len(title) > self.validation_config.max_title_length:
            title = title[:self.validation_config.max_title_length] + "..."

        return title

    def _extract_description(self, element, title: str) -> str:
        """Enhanced description extraction"""
        description = ""

        # Look for description in various elements
        desc_selectors = [
            '.description', '.summary', '.excerpt', '.abstract',
            '.content', '.text', 'p', 'div'
        ]

        for selector in desc_selectors:
            desc_elem = element.select_one(selector)
            if desc_elem and desc_elem != element:
                desc_text = desc_elem.get_text(strip=True)
                if desc_text and desc_text != title and len(desc_text) >= 20:
                    description = desc_text
                    break

        # If no specific description found, use element text excluding title
        if not description:
            full_text = element.get_text(strip=True)
            if title in full_text:
                description = full_text.replace(title, '').strip()
            else:
                description = full_text

        # Clean up description
        description = re.sub(r'\s+', ' ', description)
        description = description.strip()

        # Limit description length
        if len(description) > self.validation_config.max_description_length:
            description = description[:self.validation_config.max_description_length] + "..."

        return description

    def _extract_url(self, element, base_url: str) -> str:
        """Enhanced URL extraction with validation"""
        source_url = ""

        # Strategy 1: Element is a link
        if element.name == 'a' and element.get('href'):
            href = element.get('href')
            if self._is_valid_incentive_url(href):
                source_url = urljoin(base_url, href)

        # Strategy 2: Find link in child elements
        if not source_url:
            link_elem = element.select_one('a[href]')
            if link_elem:
                href = link_elem.get('href')
                if self._is_valid_incentive_url(href):
                    source_url = urljoin(base_url, href)

        # Strategy 3: Look for data attributes
        if not source_url:
            for attr in ['data-url', 'data-href', 'data-link']:
                if element.get(attr):
                    href = element.get(attr)
                    if self._is_valid_incentive_url(href):
                        source_url = urljoin(base_url, href)
                        break

        return source_url

    def _is_valid_incentive_url(self, href: str) -> bool:
        """Check if URL is likely to contain incentive content"""
        if not href or href in ['#', '/', 'javascript:void(0)', 'javascript:;']:
            return False

        href_lower = href.lower()

        # Skip navigation and footer URLs
        skip_patterns = [
            'cookie', 'privacy', 'contatti', 'chi-siamo', 'note-legali',
            'trasparenza', 'lavora-con-noi', 'media', 'bandi-e-gare',
            'gruppo-cdp', 'governance', 'linkedin', 'twitter', 'youtube',
            'instagram', 'facebook', 'login', 'registrati', 'portale',
            'sedi-estere', 'bilanci', 'documentazione', 'strategia',
            'sostenibilita', 'persone', 'numeri', 'mailto:', 'tel:',
            '#', 'javascript:', 'void(0)'
        ]

        if any(pattern in href_lower for pattern in skip_patterns):
            return False

        # Prefer URLs that contain incentive-related terms
        incentive_patterns = [
            'incentiv', 'finanziament', 'bando', 'agevolaz', 'contribut',
            'sostegno', 'fondo', 'credito', 'investiment', 'startup',
            'pmi', 'innovazion', 'ricerca', 'sviluppo', 'digital',
            'energia', 'sostenibil', 'internazionalizzazione', 'export',
            'misura', 'strumento', 'opportunita', 'pnrr', 'recovery'
        ]

        # If URL contains incentive terms, it's likely relevant
        if any(pattern in href_lower for pattern in incentive_patterns):
            return True

        # For general URLs, be more permissive but still filter out obvious navigation
        # Allow URLs that look like content pages (not just navigation)
        if len(href) > 5:  # More permissive length check
            return True

        return False

    def _extract_raw_content(self, element) -> str:
        """Enhanced raw content extraction"""
        # Remove script and style elements
        for script in element.find_all(["script", "style"]):
            script.decompose()

        raw_content = element.get_text(strip=True)
        raw_content = re.sub(r'\s+', ' ', raw_content)

        # Limit content length
        if len(raw_content) > 2000:
            raw_content = raw_content[:2000] + "..."

        return raw_content

    def _is_relevant_content(self, title: str, description: str) -> bool:
        """Enhanced relevance checking with stricter filtering"""
        content_text = f"{title} {description}".lower()

        # Skip generic/navigation content (expanded list)
        generic_patterns = [
            'home', 'menu', 'cerca', 'contatti', 'privacy', 'cookie',
            'seguici', 'social', 'login', 'registrati', 'newsletter',
            'footer', 'header', 'navigation', 'breadcrumb', 'chi siamo',
            'note legali', 'trasparenza', 'lavora con noi', 'media',
            'bandi e gare', 'gruppo cdp', 'governance', 'linkedin',
            'twitter', 'youtube', 'instagram', 'facebook', 'accedi',
            'portale', 'sedi estere', 'bilanci', 'documentazione',
            'strategia', 'sostenibilità', 'persone', 'numeri',
            'enti e fondazioni', 'assessorati', 'regione', 'tematiche'
        ]

        # Check if content is too generic
        if any(pattern in content_text for pattern in generic_patterns):
            return False

        # Check minimum content length
        if len(title) < 10 or len(description) < 20:
            return False

        # Check for incentive-related keywords (more specific)
        incentive_keywords = [
            'incentiv', 'finanziament', 'bando', 'agevolazion', 'contribut',
            'sostegno', 'fondo perduto', 'credito agevolato', 'investiment',
            'startup', 'pmi', 'innovazion', 'ricerca', 'sviluppo',
            'transizion', 'digital', 'energia', 'sostenibil',
            'internazionalizzazione', 'export', 'misura', 'strumento',
            'opportunità', 'pnrr', 'recovery', 'europa'
        ]

        # More balanced keyword matching
        relevance_score = sum(1 for keyword in incentive_keywords if keyword in content_text)

        # Special case: if title contains strong incentive indicators, allow with 1 keyword
        strong_indicators = ['bando', 'incentiv', 'finanziament', 'contribut', 'agevolaz']
        has_strong_indicator = any(indicator in title.lower() for indicator in strong_indicators)

        if has_strong_indicator and relevance_score >= 1:
            return True

        return relevance_score >= 1  # Require at least 1 incentive-related keyword

    def _get_website_config(self, website_name: str) -> Optional[Dict[str, Any]]:
        """Get configuration for a specific website by name"""
        for website in self.websites_config:
            if website.get('name', '').upper().startswith(website_name.upper()):
                if website.get('enabled', True):
                    return website
        return None

    def scrape_single_url_enhanced(self, url: str) -> Optional[str]:
        """Enhanced single URL scraping with retry logic"""
        soup = self.scrape_with_retry(url)
        if not soup:
            return None

        # Remove script and style elements
        for script in soup(["script", "style"]):
            script.decompose()

        # Try to find main content area with enhanced selectors
        content_selectors = [
            'main', 'article', '.content', '.main-content',
            '.post-content', '.entry-content', '.article-content',
            '#content', '#main', '.container', '.page-content',
            '[role="main"]', '.primary-content'
        ]

        content_text = ""
        for selector in content_selectors:
            content_elem = soup.select_one(selector)
            if content_elem:
                content_text = content_elem.get_text(strip=True)
                if len(content_text) > 100:  # Ensure meaningful content
                    break

        # If no specific content area found, use body
        if not content_text:
            body = soup.find('body')
            if body:
                content_text = body.get_text(strip=True)

        # Clean up the text
        content_text = re.sub(r'\s+', ' ', content_text)
        content_text = content_text.strip()

        # Limit content length
        if len(content_text) > 10000:
            content_text = content_text[:10000] + "..."

        logger.info(f"Successfully scraped {len(content_text)} characters from {url}")
        return content_text

    def get_health_report(self) -> Dict[str, Any]:
        """Get comprehensive health report"""
        healthy_count = sum(1 for health in self.website_health.values() if health.is_healthy)
        total_count = len(self.website_health)

        unhealthy_sites = [
            {
                'url': health.url,
                'error': health.error_message,
                'failures': health.consecutive_failures,
                'last_check': health.last_check.isoformat()
            }
            for health in self.website_health.values()
            if not health.is_healthy
        ]

        avg_response_time = sum(health.response_time for health in self.website_health.values()) / max(total_count, 1)

        return {
            'healthy_sites': healthy_count,
            'total_sites': total_count,
            'health_percentage': (healthy_count / max(total_count, 1)) * 100,
            'average_response_time': avg_response_time,
            'unhealthy_sites': unhealthy_sites,
            'last_updated': datetime.now().isoformat()
        }

    def get_selector_report(self) -> Dict[str, Any]:
        """Get selector validation report"""
        total_validations = 0
        working_validations = 0

        site_reports = {}
        for url, validations in self.selector_validations.items():
            working = sum(1 for v in validations if v.is_valid)
            total = len(validations)

            site_reports[url] = {
                'working_selectors': working,
                'total_selectors': total,
                'success_rate': (working / max(total, 1)) * 100,
                'last_validated': max(v.last_validated for v in validations).isoformat() if validations else None
            }

            total_validations += total
            working_validations += working

        return {
            'overall_success_rate': (working_validations / max(total_validations, 1)) * 100,
            'total_validations': total_validations,
            'working_validations': working_validations,
            'site_reports': site_reports
        }

    def cleanup(self):
        """Cleanup resources"""
        if self.selenium_driver:
            try:
                self.selenium_driver.quit()
                logger.info("Selenium driver closed")
            except Exception as e:
                logger.error(f"Error closing Selenium driver: {e}")

        if self.session:
            self.session.close()

        # Save final health data
        self._save_health_data()

    def __del__(self):
        """Destructor to ensure cleanup"""
        self.cleanup()
