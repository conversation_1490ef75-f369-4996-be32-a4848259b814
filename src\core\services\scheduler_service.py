# -*- coding: utf-8 -*-
"""
Servizio di scheduling per report automatici
"""

import threading
import time
from datetime import datetime, time as dt_time
from typing import Dict, Any, List

from ..config import AppConfig
from ..database import DatabaseManagerExtended
from .statistics_service import StatisticsService
from ..utils import get_logger

logger = get_logger(__name__)

class SchedulerService:
    """Servizio per la gestione dei report programmati"""
    
    def __init__(self, db_manager: DatabaseManagerExtended, config: AppConfig):
        self.db = db_manager
        self.config = config
        self.statistics_service = StatisticsService(db_manager, config)
        self.running = False
        self.scheduler_thread = None
        self.check_interval = 60  # Controlla ogni minuto
        self.last_morning_send = None
        self.last_evening_send = None
        
    def start(self):
        """Avvia il servizio di scheduling"""
        if self.running:
            logger.warning("Scheduler già in esecuzione")
            return
            
        self.running = True
        self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self.scheduler_thread.start()
        logger.info("Scheduler service avviato")
        
    def stop(self):
        """Ferma il servizio di scheduling"""
        self.running = False
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=5)
        logger.info("Scheduler service fermato")
        
    def _scheduler_loop(self):
        """Loop principale del scheduler"""
        while self.running:
            try:
                self._check_scheduled_reports()
                time.sleep(self.check_interval)
            except Exception as e:
                logger.error(f"Errore nel scheduler loop: {e}")
                time.sleep(self.check_interval)
                
    def _check_scheduled_reports(self):
        """Controlla se è il momento di inviare report programmati"""
        try:
            # Carica le impostazioni più recenti
            scheduled_settings = self._get_scheduled_settings()
            
            if not scheduled_settings.get('enabled', False):
                logger.debug("Report programmati disabilitati")
                return
                
            logger.debug(f"Controllo report programmati - Settings: {scheduled_settings}")
                
            now = datetime.now()
            current_time = now.time()
            current_date = now.date()
            
            # Controlla se è un giorno lavorativo (se richiesto)
            if scheduled_settings.get('workdays_only', True):
                if now.weekday() >= 5:  # Sabato = 5, Domenica = 6
                    return
                    
            # Controlla report mattutino
            if scheduled_settings.get('morning_enabled', False):
                morning_time_str = scheduled_settings.get('morning_time', '09:00')
                morning_time = self._parse_time(morning_time_str)
                
                logger.debug(f"Controllo report mattutino - Ora corrente: {current_time}, Ora target: {morning_time}")
                
                if (self._is_time_to_send(current_time, morning_time) and 
                    self.last_morning_send != current_date):
                    logger.info(f"Invio report mattutino programmato per le {morning_time}")
                    self._send_scheduled_report('morning', scheduled_settings)
                    self.last_morning_send = current_date
                elif self.last_morning_send == current_date:
                    logger.debug("Report mattutino già inviato oggi")
                else:
                    logger.debug(f"Non è ancora il momento per il report mattutino ({morning_time})")
                    
            # Controlla report serale
            if scheduled_settings.get('evening_enabled', False):
                evening_time_str = scheduled_settings.get('evening_time', '17:30')
                evening_time = self._parse_time(evening_time_str)
                
                if (self._is_time_to_send(current_time, evening_time) and 
                    self.last_evening_send != current_date):
                    self._send_scheduled_report('evening', scheduled_settings)
                    self.last_evening_send = current_date
                    
        except Exception as e:
            logger.error(f"Errore nel controllo report programmati: {e}")
            
    def _get_scheduled_settings(self) -> Dict[str, Any]:
        """Recupera le impostazioni dei report programmati"""
        try:
            # Carica dalla configurazione
            if hasattr(self.config, 'scheduled_reports_config'):
                return self.config.scheduled_reports_config
            else:
                # Impostazioni di default
                return {
                    'enabled': False,
                    'morning_enabled': True,
                    'morning_time': '09:00',
                    'evening_enabled': True,
                    'evening_time': '17:30',
                    'workdays_only': True,
                    'recipients': [],
                    'include_project_percentages': True,
                    'include_overdue_items': True,
                    'include_upcoming_deadlines': True,
                    'days_ahead_filter': 7
                }
        except Exception as e:
            logger.error(f"Errore nel caricamento impostazioni: {e}")
            return {}
            
    def _parse_time(self, time_str: str) -> dt_time:
        """Converte una stringa orario in oggetto time"""
        try:
            hour, minute = map(int, time_str.split(':'))
            return dt_time(hour, minute)
        except:
            return dt_time(9, 0)  # Default 09:00
            
    def _is_time_to_send(self, current_time: dt_time, target_time: dt_time) -> bool:
        """Verifica se è il momento di inviare il report"""
        # Considera una finestra di 1 minuto per l'invio
        current_minutes = current_time.hour * 60 + current_time.minute
        target_minutes = target_time.hour * 60 + target_time.minute
        
        return abs(current_minutes - target_minutes) <= 1
        
    def _send_scheduled_report(self, report_time: str, settings: Dict[str, Any]):
        """Invia un report programmato"""
        try:
            logger.info(f"Invio report programmato ({report_time})")
            
            # Determina i destinatari
            recipients = settings.get('recipients', [])
            if not recipients:
                # Se non ci sono destinatari specifici, usa l'email del mittente
                sender_email = self.config.email_config.get('from_email')
                if sender_email:
                    recipients = [sender_email]
                else:
                    logger.warning("Nessun destinatario configurato per i report")
                    return
                    
            # Invia il report automatico con task expiration tracking
            success = self.statistics_service.send_statistics_email(
                recipients=recipients,
                report_type='automatic'
            )
            
            if success:
                logger.info(f"Report {report_time} inviato con successo a {len(recipients)} destinatari")
            else:
                logger.error(f"Errore nell'invio del report {report_time}")
                
        except Exception as e:
            logger.error(f"Errore nell'invio report programmato: {e}")
            
    def send_manual_report(self, recipients: List[str] = None) -> bool:
        """Invia un report manualmente"""
        try:
            logger.info("📧 Inizio invio report manuale")
            
            if not recipients:
                # Usa i destinatari configurati o l'email del mittente
                settings = self._get_scheduled_settings()
                recipients = settings.get('recipients', [])
                if not recipients:
                    sender_email = self.config.email_config.get('from_email')
                    if sender_email:
                        recipients = [sender_email]
                    else:
                        logger.error("Nessun destinatario specificato")
                        return False
            
            logger.info(f"[EMAIL] Destinatari: {recipients}")

            # Try simplified report first to avoid crash
            try:
                logger.info("[REPORT] Tentativo invio report semplificato...")
                success = self._send_simplified_report(recipients)
                if success:
                    logger.info("[SUCCESS] Report semplificato inviato con successo")
                    return True
                else:
                    logger.warning("[WARNING] Report semplificato fallito, provo report completo...")
            except Exception as simple_ex:
                logger.error(f"[ERROR] Errore report semplificato: {simple_ex}")
            
            # Fallback to automatic report method
            success = self.statistics_service.send_statistics_email(
                recipients=recipients,
                report_type='automatic'
            )
            
            if success:
                logger.info(f"[SUCCESS] Report manuale inviato con successo a {len(recipients)} destinatari")
            else:
                logger.error("[ERROR] Errore nell'invio del report manuale")
                
            return success
            
        except Exception as e:
            logger.error(f"❌ Errore nell'invio report manuale: {e}")
            import traceback
            logger.error(f"❌ Traceback: {traceback.format_exc()}")
            return False
    
    def _send_simplified_report(self, recipients: List[str]) -> bool:
        """Invia un report semplificato per test"""
        try:
            from datetime import datetime
            
            # Create simple report
            simple_html = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Report Test - Agevolami PM</title>
</head>
<body style="font-family: Arial, sans-serif; padding: 20px;">
    <h2>[REPORT] Report Test Agevolami PM</h2>
    <p><strong>Data:</strong> {datetime.now().strftime('%d/%m/%Y alle %H:%M')}</p>
    <p>Questo è un report di test semplificato per verificare il funzionamento del sistema email.</p>
    <p><strong>Sistema:</strong> Operativo [OK]</p>
    <p><strong>Email:</strong> Configurazione SMTP funzionante [OK]</p>
    <hr>
    <p><em>Report generato automaticamente da Agevolami PM</em></p>
</body>
</html>
"""
            
            simple_text = f"""
REPORT TEST - AGEVOLAMI PM
Data: {datetime.now().strftime('%d/%m/%Y alle %H:%M')}

Questo è un report di test semplificato per verificare il funzionamento del sistema email.

Sistema: Operativo [OK]
Email: Configurazione SMTP funzionante [OK]

---
Report generato automaticamente da Agevolami PM
"""
            
            # Send to all recipients
            success_count = 0
            for recipient in recipients:
                try:
                    success = self.statistics_service.email_service.send_email(
                        to_email=recipient,
                        subject=f"Report Test Agevolami PM - {datetime.now().strftime('%d/%m/%Y')}",
                        body=simple_text,
                        html_body=simple_html
                    )
                    
                    if success:
                        success_count += 1
                        logger.info(f"[SUCCESS] Report test inviato a: {recipient}")
                    else:
                        logger.error(f"[ERROR] Errore invio report test a: {recipient}")

                except Exception as e:
                    logger.error(f"[ERROR] Errore invio email test a {recipient}: {e}")
            
            return success_count > 0
            
        except Exception as e:
            logger.error(f"[ERROR] Errore invio report semplificato: {e}")
            return False