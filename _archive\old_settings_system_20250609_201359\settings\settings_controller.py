#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Enhanced Settings Controller
Centralized state management for all settings categories with validation and persistence
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional, List, Callable
from core import get_logger

logger = get_logger(__name__)

class SettingsController:
    """
    Enhanced settings controller with validation, change tracking, and persistence
    Supports all settings categories: email, notifications, google_services, windows, reports
    """
    
    def __init__(self, app_instance):
        self.app = app_instance
        self.config = app_instance.config

        # Initialize real services
        self._init_services()

        # Change callback registry
        self.change_callbacks: Dict[str, List[Callable]] = {}

        # Validation error tracking
        self.validation_errors: Dict[str, Dict[str, str]] = {}

        # Flag to track if settings have been loaded
        self._settings_loaded = False
        
        # Settings data with all categories
        self.settings = {
            'email': {
                'server': '',
                'port': 587,
                'username': '',
                'password': '',
                'use_tls': True,
                'sender_name': 'Agevolami PM',
                'sender_email': '',
                'test_connection_status': 'unknown'
            },
            'notifications': {
                'enabled': True,
                'email_enabled': False,
                'check_interval': 24,
                'advance_days': [1, 3, 7, 15],
                'working_hours_only': True,
                'weekend_alerts': False,
                'reminder_recipients': [],
                'priority_filter': 'normal'
            },
            'google_services': {
                # Google Drive
                'drive_enabled': False,
                'drive_authenticated': False,
                'drive_auto_backup': False,
                'drive_backup_frequency': 'daily',
                'drive_retention_days': 30,
                'drive_include_logs': True,
                'drive_include_assets': True,
                
                # Google Calendar  
                'calendar_enabled': False,
                'calendar_authenticated': False,
                'calendar_auto_sync': True,
                'calendar_sync_completed': False,
                'calendar_delete_completed': True,
                'calendar_color_priority': True,
                'calendar_reminders': True,
                
                # Google Tasks
                'tasks_enabled': False,
                'tasks_authenticated': False,
                'tasks_auto_sync': True,
                'tasks_sync_on_deadline_change': True,
                'tasks_create_list_per_deadline': True,
                'tasks_sync_completed': True
            },
            'windows': {
                'startup_enabled': False,
                'startup_minimized': True,
                'notifications_enabled': True,
                'notification_sound': True,
                'notification_priority_filter': 'normal',
                'notification_quiet_hours_enabled': False,
                'notification_quiet_start': '22:00',
                'notification_quiet_end': '08:00',
                'notification_desktop_enabled': True,
                'notification_email_enabled': True
            },
            'reports': {
                'scheduled_enabled': False,
                'morning_enabled': True,
                'morning_time': '09:00',
                'evening_enabled': True,
                'evening_time': '17:30',
                'workdays_only': True,
                'content_types': {
                    'project_completion': True,
                    'overdue_items': True,
                    'upcoming_deadlines': True,
                    'incomplete_tasks': True,
                    'client_summary': False,
                    'statistics': True
                },
                'recipients': [],
                'days_ahead_filter': 7,
                'include_project_percentages': True,
                'include_overdue_items': True,
                'include_upcoming_deadlines': True
            },
            'incentives': {
                'enabled': False,
                'frequency': 'weekly',
                'keywords': ['incentivi', 'finanziamenti', 'bandi', 'agevolazioni'],
                'openrouter_api_key': '',
                'llm_model': 'gpt-3.5-turbo',
                'llm_enabled': True,
                'email_notifications': True,
                'desktop_notifications': False,
                'notification_email': '',
                'only_new_content': True,
                'min_relevance_score': 0.5,
                'auto_archive_old': True,
                'archive_after_days': 90,
                'max_items_per_session': 50,
                'request_delay_seconds': 2.0,
                'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'custom_urls': '',
                'custom_models': {},
                'websites': [
                    {
                        'name': 'MIMIT - Ministero Imprese',
                        'url': 'https://www.mimit.gov.it',
                        'enabled': True,
                        'search_paths': ['/it/incentivi-alle-imprese', '/it/notizie', '/it/bandi-e-avvisi']
                    },
                    {
                        'name': 'Invitalia',
                        'url': 'https://www.invitalia.it',
                        'enabled': True,
                        'search_paths': ['/cosa-facciamo/sosteniamo-le-imprese', '/news-e-media/news', '/bandi-e-gare']
                    },
                    {
                        'name': 'SIMEST',
                        'url': 'https://www.simest.it',
                        'enabled': True,
                        'search_paths': ['/servizi', '/news', '/bandi']
                    }
                ]
            },
            'application': {
                'theme': 'light',
                'language': 'it',
                'auto_backup': True,
                'backup_interval': 7,
                'max_backups': 10,
                'auto_save': True,
                'confirmation_dialogs': True
            }
        }
        
        # Load settings from file
        self._load_settings()

        # Ensure settings are properly initialized and synced
        self._ensure_settings_consistency()

        logger.info("Enhanced SettingsController initialized with all categories")

    def _init_services(self):
        """Initialize real services for settings operations"""
        try:
            # Import services
            from core.services import EmailService, GoogleDriveService, GoogleTasksService
            from core.services.google_calendar_service import GoogleCalendarService
            from core.utils.windows_utils import WindowsSystemIntegration
            import os

            # Initialize services
            self.email_service = EmailService(self.config)
            self.windows_integration = WindowsSystemIntegration()

            # Google services
            config_dir = os.path.join(self.config.data_dir, "config")
            self.google_drive_service = GoogleDriveService(self.config.data_dir)
            self.google_calendar_service = GoogleCalendarService(config_dir)
            self.google_tasks_service = GoogleTasksService(config_dir)

            logger.info("Real services initialized successfully")

        except Exception as e:
            logger.error(f"Error initializing services: {e}")
            # Set services to None if initialization fails
            self.email_service = None
            self.windows_integration = None
            self.google_drive_service = None
            self.google_calendar_service = None
            self.google_tasks_service = None
    
    def register_change_callback(self, category: str, callback: Callable[[str, Any, Any], None]):
        """Register a callback for setting changes in a specific category"""
        if category not in self.change_callbacks:
            self.change_callbacks[category] = []
        self.change_callbacks[category].append(callback)
        logger.debug(f"Registered change callback for category: {category}")
    
    def get_setting(self, category: str, key: Optional[str] = None) -> Any:
        """Get a setting value or entire category"""
        if category not in self.settings:
            logger.warning(f"Unknown settings category: {category}")
            return {} if key is None else None
        
        if key is None:
            return self.settings[category].copy()
        
        return self.settings[category].get(key)
    
    def set_setting(self, category: str, key: str, value: Any) -> bool:
        """Set a setting value with validation and change notification"""
        if category not in self.settings:
            logger.error(f"Unknown settings category: {category}")
            return False
        
        # Get old value for change callback
        old_value = self.settings[category].get(key)
        
        # Validate the setting
        if not self._validate_setting(category, key, value):
            logger.warning(f"Validation failed for {category}.{key} = {value}")
            return False
        
        # Update the setting
        self.settings[category][key] = value
        
        # Clear any validation error for this setting
        if category in self.validation_errors and key in self.validation_errors[category]:
            del self.validation_errors[category][key]
        
        # Notify change callbacks
        self._notify_change_callbacks(category, key, value, old_value)
        
        logger.debug(f"Setting updated: {category}.{key} = {value}")
        return True
    
    def _validate_setting(self, category: str, key: str, value: Any) -> bool:
        """Validate a setting value"""
        try:
            # Email validation
            if category == 'email':
                if key == 'port' and (not isinstance(value, int) or value < 1 or value > 65535):
                    self._set_validation_error(category, key, "Port must be between 1 and 65535")
                    return False
                elif key == 'sender_email' and value and len(value) > 3 and '@' not in value:
                    self._set_validation_error(category, key, "Invalid email format")
                    return False
                elif key == 'server' and not value and len(str(value)) == 0:
                    # Only validate if user has finished typing (empty string, not just started)
                    self._set_validation_error(category, key, "SMTP server is required")
                    return False
            
            # Notifications validation
            elif category == 'notifications':
                if key == 'check_interval' and (not isinstance(value, int) or value < 1 or value > 168):
                    self._set_validation_error(category, key, "Check interval must be between 1 and 168 hours")
                    return False
                elif key == 'advance_days' and not isinstance(value, list):
                    self._set_validation_error(category, key, "Advance days must be a list")
                    return False
                elif key == 'reminder_recipients' and value:
                    for email in value:
                        if '@' not in email or '.' not in email.split('@')[1]:
                            self._set_validation_error(category, key, f"Invalid email: {email}")
                            return False
            
            # Windows validation
            elif category == 'windows':
                if key in ['notification_quiet_start', 'notification_quiet_end']:
                    if not self._validate_time_format(value):
                        self._set_validation_error(category, key, "Invalid time format (use HH:MM)")
                        return False
            
            # Reports validation  
            elif category == 'reports':
                if key in ['morning_time', 'evening_time']:
                    if not self._validate_time_format(value):
                        self._set_validation_error(category, key, "Invalid time format (use HH:MM)")
                        return False
                elif key == 'days_ahead_filter' and (not isinstance(value, int) or value < 1 or value > 365):
                    self._set_validation_error(category, key, "Days ahead must be between 1 and 365")
                    return False
                elif key == 'recipients' and value:
                    for email in value:
                        if '@' not in email or '.' not in email.split('@')[1]:
                            self._set_validation_error(category, key, f"Invalid email: {email}")
                            return False
            
            # Google Services validation
            elif category == 'google_services':
                if key == 'drive_retention_days' and (not isinstance(value, int) or value < 1 or value > 365):
                    self._set_validation_error(category, key, "Retention days must be between 1 and 365")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Validation error for {category}.{key}: {e}")
            self._set_validation_error(category, key, f"Validation error: {str(e)}")
            return False
    
    def _validate_time_format(self, time_str: str) -> bool:
        """Validate time format (HH:MM)"""
        try:
            parts = time_str.split(':')
            if len(parts) != 2:
                return False
            
            hour = int(parts[0])
            minute = int(parts[1])
            
            return 0 <= hour <= 23 and 0 <= minute <= 59
        except (ValueError, IndexError):
            return False
    
    def _set_validation_error(self, category: str, key: str, message: str):
        """Set a validation error"""
        if category not in self.validation_errors:
            self.validation_errors[category] = {}
        self.validation_errors[category][key] = message
    
    def get_validation_errors(self, category: Optional[str] = None) -> Dict[str, Any]:
        """Get validation errors for a category or all categories"""
        if category:
            return self.validation_errors.get(category, {})
        return self.validation_errors
    
    def has_validation_errors(self, category: Optional[str] = None) -> bool:
        """Check if there are validation errors"""
        if category:
            return category in self.validation_errors and len(self.validation_errors[category]) > 0
        return len(self.validation_errors) > 0
    
    def _notify_change_callbacks(self, category: str, key: str, new_value: Any, old_value: Any):
        """Notify registered change callbacks"""
        if category in self.change_callbacks:
            for callback in self.change_callbacks[category]:
                try:
                    callback(key, new_value, old_value)
                except Exception as e:
                    logger.error(f"Error in change callback for {category}.{key}: {e}")
    
    def save_settings(self) -> bool:
        """Save all settings to file"""
        try:
            # Only check for critical validation errors (not typing-in-progress errors)
            critical_errors = self._get_critical_validation_errors()
            if critical_errors:
                logger.warning(f"Cannot save settings with critical validation errors: {critical_errors}")
                return False
            
            # Update main app config
            self._sync_to_app_config()
            
            # Save to JSON file
            settings_file = self.config.data_dir / 'settings.json'
            settings_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, indent=2, ensure_ascii=False, default=str)
            
            logger.info(f"Settings saved to {settings_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving settings: {e}")
            return False
    
    def _load_settings(self):
        """Load settings from file and app config"""
        try:
            # Load from JSON file if it exists
            settings_file = self.config.data_dir / 'settings.json'
            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    saved_settings = json.load(f)

                # Merge saved settings with defaults
                for category, category_settings in saved_settings.items():
                    if category in self.settings:
                        self.settings[category].update(category_settings)

                logger.info(f"Settings loaded from {settings_file}")

                # After loading from file, sync TO app config (not FROM it)
                # This ensures the app config gets the saved settings, not the other way around
                self._sync_to_app_config()
            else:
                # Only sync FROM app config if no settings file exists (first run)
                logger.info("No settings file found, using app config defaults")
                self._sync_from_app_config()
            
        except Exception as e:
            logger.error(f"Error loading settings: {e}")
            # Continue with default settings

    def _ensure_settings_consistency(self):
        """Ensure settings are consistent and properly initialized"""
        try:
            # Mark settings as loaded
            self._settings_loaded = True

            # Preserve email settings if they exist
            email_settings = self.settings.get('email', {})
            has_email_config = bool(email_settings.get('server') and email_settings.get('username'))

            if has_email_config:
                logger.info("Email settings detected, preserving configuration")

            # Update Google service authentication status based on actual token files
            self._update_google_auth_status()

            # Update Windows integration status
            self._update_windows_integration_status()

            # Sync to app config to ensure consistency (but preserve email settings)
            self._sync_to_app_config()

            logger.info("Settings consistency check completed")

        except Exception as e:
            logger.error(f"Error ensuring settings consistency: {e}")

    def _update_google_auth_status(self):
        """Update Google service authentication status based on actual token files"""
        try:
            import os
            config_dir = os.path.join(self.config.data_dir, "config")

            # Check Google Drive token
            drive_token = os.path.join(config_dir, "google_drive_token.pickle")
            drive_authenticated = os.path.exists(drive_token)
            self.settings['google_services']['drive_authenticated'] = drive_authenticated
            self.settings['google_services']['drive_enabled'] = drive_authenticated

            # Check Google Calendar token
            calendar_token = os.path.join(config_dir, "google_token.pickle")
            calendar_authenticated = os.path.exists(calendar_token)
            self.settings['google_services']['calendar_authenticated'] = calendar_authenticated
            self.settings['google_services']['calendar_enabled'] = calendar_authenticated

            # Check Google Tasks token
            tasks_token = os.path.join(config_dir, "google_tasks_token.pickle")
            tasks_authenticated = os.path.exists(tasks_token)
            self.settings['google_services']['tasks_authenticated'] = tasks_authenticated
            self.settings['google_services']['tasks_enabled'] = tasks_authenticated

            logger.info(f"Google auth status updated: Drive={drive_authenticated}, Calendar={calendar_authenticated}, Tasks={tasks_authenticated}")

        except Exception as e:
            logger.error(f"Error updating Google auth status: {e}")

    def _update_windows_integration_status(self):
        """Update Windows integration status"""
        try:
            if self.windows_integration:
                # Check startup status
                startup_enabled = self.windows_integration.is_startup_enabled()
                self.settings['windows']['startup_enabled'] = startup_enabled

                logger.info(f"Windows integration status updated: Startup={startup_enabled}")

        except Exception as e:
            logger.error(f"Error updating Windows integration status: {e}")
    
    def _sync_from_app_config(self):
        """Sync settings from app config (only for initial setup or when settings are empty)"""
        try:
            # Email settings - only sync if current settings are empty (to avoid overwriting saved settings)
            if hasattr(self.config, 'email_config'):
                email_config = self.config.email_config
                current_email = self.settings['email']

                # Only update if current settings are empty/default
                if not current_email.get('server') and email_config.get('smtp_server'):
                    self.settings['email'].update({
                        'server': email_config.get('smtp_server', ''),
                        'port': email_config.get('smtp_port', 587),
                        'username': email_config.get('smtp_username', ''),
                        'password': email_config.get('smtp_password', ''),
                        'use_tls': email_config.get('smtp_use_tls', True),
                        'sender_name': email_config.get('from_name', 'Agevolami PM'),
                        'sender_email': email_config.get('from_email', '')
                    })
                    logger.info("Email settings synced from app config")
                else:
                    logger.debug("Email settings already configured, skipping app config sync")
            
            # Notifications settings
            if hasattr(self.config, 'alert_config'):
                alert_config = self.config.alert_config
                self.settings['notifications'].update({
                    'enabled': alert_config.get('email_notifications_enabled', True),
                    'email_enabled': alert_config.get('email_notifications_enabled', False),
                    'check_interval': alert_config.get('check_interval_hours', 24),
                    'advance_days': [alert_config.get('days_before_deadline', 15)],
                    'reminder_recipients': alert_config.get('reminder_recipients', [])
                })
            
            # Google Services settings
            if hasattr(self.config, 'google_services_config'):
                google_config = self.config.google_services_config
                self.settings['google_services'].update(google_config)
            
            # Windows settings
            if hasattr(self.config, 'windows_config'):
                windows_config = self.config.windows_config
                self.settings['windows'].update(windows_config)
            
            # Reports settings
            if hasattr(self.config, 'reports_config'):
                reports_config = self.config.reports_config
                self.settings['reports'].update(reports_config)
            
        except Exception as e:
            logger.error(f"Error syncing from app config: {e}")
    
    def _sync_to_app_config(self):
        """Sync settings to app config"""
        try:
            # Email config
            self.config.email_config.update({
                'smtp_server': self.settings['email']['server'],
                'smtp_port': self.settings['email']['port'],
                'smtp_username': self.settings['email']['username'],
                'smtp_password': self.settings['email']['password'],
                'smtp_use_tls': self.settings['email']['use_tls'],
                'from_name': self.settings['email']['sender_name'],
                'from_email': self.settings['email']['sender_email']
            })
            
            # Alert config
            self.config.alert_config.update({
                'email_notifications_enabled': self.settings['notifications']['enabled'],
                'check_interval_hours': self.settings['notifications']['check_interval'],
                'days_before_deadline': self.settings['notifications']['advance_days'][0] if self.settings['notifications']['advance_days'] else 15,
                'reminder_recipients': self.settings['notifications']['reminder_recipients']
            })
            
            # Create other config sections if they don't exist
            if not hasattr(self.config, 'google_services_config'):
                self.config.google_services_config = {}
            self.config.google_services_config.update(self.settings['google_services'])
            
            if not hasattr(self.config, 'windows_config'):
                self.config.windows_config = {}
            self.config.windows_config.update(self.settings['windows'])
            
            if not hasattr(self.config, 'reports_config'):
                self.config.reports_config = {}
            self.config.reports_config.update(self.settings['reports'])
            
        except Exception as e:
            logger.error(f"Error syncing to app config: {e}")
    
    def reset_to_defaults(self, category: Optional[str] = None) -> bool:
        """Reset settings to defaults"""
        try:
            if category and category in self.settings:
                # Reset single category
                if category == 'email':
                    self.settings['email'] = {
                        'server': '',
                        'port': 587,
                        'username': '',
                        'password': '',
                        'use_tls': True,
                        'sender_name': 'Agevolami PM',
                        'sender_email': '',
                        'test_connection_status': 'unknown'
                    }
                elif category == 'notifications':
                    self.settings['notifications'] = {
                        'enabled': True,
                        'email_enabled': False,
                        'check_interval': 24,
                        'advance_days': [1, 3, 7, 15],
                        'working_hours_only': True,
                        'weekend_alerts': False,
                        'reminder_recipients': [],
                        'priority_filter': 'normal'
                    }
                # Add other categories as needed...
                
                # Clear validation errors for this category
                if category in self.validation_errors:
                    del self.validation_errors[category]
                
                logger.info(f"Reset {category} settings to defaults")
            else:
                # Reset all categories
                self.__init__(self.app)
                logger.info("Reset all settings to defaults")
            
            return True
            
        except Exception as e:
            logger.error(f"Error resetting settings: {e}")
            return False
    
    def export_settings(self, file_path: Optional[Path] = None) -> bool:
        """Export settings to a file"""
        try:
            if not file_path:
                file_path = self.config.data_dir / f'settings_export_{int(time.time())}.json'
            
            export_data = {
                'version': '2.0',
                'exported_at': str(datetime.now()),
                'settings': self.settings
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False, default=str)
            
            logger.info(f"Settings exported to {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting settings: {e}")
            return False
    
    def import_settings(self, file_path: Path) -> bool:
        """Import settings from a file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                import_data = json.load(f)
            
            if 'settings' in import_data:
                # Validate and merge imported settings
                imported_settings = import_data['settings']
                for category, category_settings in imported_settings.items():
                    if category in self.settings:
                        # Validate each setting
                        for key, value in category_settings.items():
                            if self._validate_setting(category, key, value):
                                self.settings[category][key] = value
                
                logger.info(f"Settings imported from {file_path}")
                return True
            else:
                logger.error("Invalid settings file format")
                return False
                
        except Exception as e:
            logger.error(f"Error importing settings: {e}")
            return False
    
    def get_settings_summary(self) -> Dict[str, Any]:
        """Get a summary of current settings status"""
        summary = {
            'categories': len(self.settings),
            'validation_errors': len(self.validation_errors),
            'configured_services': [],
            'email_configured': bool(self.settings['email']['server']),
            'notifications_enabled': self.settings['notifications']['enabled'],
            'google_services_connected': 0,
            'windows_integration': 0,
            'reports_enabled': self.settings['reports']['scheduled_enabled']
        }
        
        # Count Google services
        google_settings = self.settings['google_services']
        if google_settings['drive_authenticated']:
            summary['configured_services'].append('Google Drive')
            summary['google_services_connected'] += 1
        if google_settings['calendar_authenticated']:
            summary['configured_services'].append('Google Calendar')
            summary['google_services_connected'] += 1
        if google_settings['tasks_authenticated']:
            summary['configured_services'].append('Google Tasks')
            summary['google_services_connected'] += 1
        
        # Count Windows integrations
        windows_settings = self.settings['windows']
        if windows_settings['startup_enabled']:
            summary['windows_integration'] += 1
        if windows_settings['notifications_enabled']:
            summary['windows_integration'] += 1
        
        return summary

    # Service Access Methods
    def test_email_connection(self) -> dict:
        """Test email connection using real EmailService"""
        if not self.email_service:
            logger.error("EmailService not available")
            return {"success": False, "message": "EmailService not available"}

        # Sync current settings to email service before testing
        try:
            self._sync_email_settings_to_service()
            result = self.email_service.test_connection()
            if result:
                self.set_setting('email', 'test_connection_status', 'success')
                return {"success": True, "message": "Connessione SMTP riuscita"}
            else:
                self.set_setting('email', 'test_connection_status', 'failed')
                return {"success": False, "message": "Connessione SMTP fallita"}
        except Exception as e:
            logger.error(f"Email connection test failed: {e}")
            self.set_setting('email', 'test_connection_status', 'error')
            return {"success": False, "message": f"Errore connessione: {str(e)}"}

    def send_test_email(self, recipient: str) -> bool:
        """Send test email using real EmailService"""
        if not self.email_service:
            logger.error("EmailService not available")
            return False

        try:
            # Sync current settings to email service before sending
            self._sync_email_settings_to_service()
            return self.email_service.send_test_email(recipient)
        except Exception as e:
            logger.error(f"Test email send failed: {e}")
            return False

    def authenticate_google_drive(self) -> bool:
        """Authenticate with Google Drive using real service"""
        if not self.google_drive_service:
            logger.error("GoogleDriveService not available")
            return False

        try:
            result = self.google_drive_service.authenticate()
            self.set_setting('google_services', 'drive_authenticated', result)
            self.set_setting('google_services', 'drive_enabled', result)
            return result
        except Exception as e:
            logger.error(f"Google Drive authentication failed: {e}")
            return False

    def test_google_drive_connection(self) -> bool:
        """Test Google Drive connection"""
        if not self.google_drive_service:
            return False

        try:
            return self.google_drive_service.test_connection()
        except Exception as e:
            logger.error(f"Google Drive connection test failed: {e}")
            return False

    def authenticate_google_calendar(self) -> bool:
        """Authenticate with Google Calendar using real service"""
        if not self.google_calendar_service:
            logger.error("GoogleCalendarService not available")
            return False

        try:
            result = self.google_calendar_service.authenticate()
            self.set_setting('google_services', 'calendar_authenticated', result)
            self.set_setting('google_services', 'calendar_enabled', result)
            return result
        except Exception as e:
            logger.error(f"Google Calendar authentication failed: {e}")
            return False

    def test_google_calendar_connection(self) -> bool:
        """Test Google Calendar connection"""
        if not self.google_calendar_service:
            return False

        try:
            return self.google_calendar_service.test_connection()
        except Exception as e:
            logger.error(f"Google Calendar connection test failed: {e}")
            return False

    def authenticate_google_tasks(self) -> bool:
        """Authenticate with Google Tasks using real service"""
        if not self.google_tasks_service:
            logger.error("GoogleTasksService not available")
            return False

        try:
            result = self.google_tasks_service.authenticate()
            self.set_setting('google_services', 'tasks_authenticated', result)
            self.set_setting('google_services', 'tasks_enabled', result)
            return result
        except Exception as e:
            logger.error(f"Google Tasks authentication failed: {e}")
            return False

    def test_google_tasks_connection(self) -> bool:
        """Test Google Tasks connection"""
        if not self.google_tasks_service:
            return False

        try:
            return self.google_tasks_service.test_connection()
        except Exception as e:
            logger.error(f"Google Tasks connection test failed: {e}")
            return False

    def configure_windows_startup(self, enabled: bool) -> bool:
        """Configure Windows startup using real WindowsSystemIntegration"""
        if not self.windows_integration:
            logger.error("WindowsSystemIntegration not available")
            return False

        try:
            result = self.windows_integration.configure_startup(enabled)
            self.set_setting('windows', 'startup_enabled', result and enabled)
            return result
        except Exception as e:
            logger.error(f"Windows startup configuration failed: {e}")
            return False

    def is_windows_startup_enabled(self) -> bool:
        """Check if Windows startup is enabled"""
        if not self.windows_integration:
            return False

        try:
            return self.windows_integration.is_startup_enabled()
        except Exception as e:
            logger.error(f"Windows startup check failed: {e}")
            return False

    def send_windows_test_notification(self, title: str, message: str) -> bool:
        """Send test Windows notification"""
        if not self.windows_integration:
            logger.error("WindowsSystemIntegration not available")
            return False

        try:
            return self.windows_integration.send_notification(title, message, "normal")
        except Exception as e:
            logger.error(f"Windows notification test failed: {e}")
            return False

    def disconnect_google_service(self, service_name: str) -> bool:
        """Disconnect from a Google service"""
        try:
            if service_name == 'drive' and self.google_drive_service:
                result = self.google_drive_service.disconnect() if hasattr(self.google_drive_service, 'disconnect') else True
                self.set_setting('google_services', 'drive_authenticated', False)
                self.set_setting('google_services', 'drive_enabled', False)
                return result
            elif service_name == 'calendar' and self.google_calendar_service:
                result = self.google_calendar_service.disconnect() if hasattr(self.google_calendar_service, 'disconnect') else True
                self.set_setting('google_services', 'calendar_authenticated', False)
                self.set_setting('google_services', 'calendar_enabled', False)
                return result
            elif service_name == 'tasks' and self.google_tasks_service:
                result = self.google_tasks_service.disconnect()
                self.set_setting('google_services', 'tasks_authenticated', False)
                self.set_setting('google_services', 'tasks_enabled', False)
                return result
            else:
                logger.error(f"Unknown service or service not available: {service_name}")
                return False
        except Exception as e:
            logger.error(f"Error disconnecting from {service_name}: {e}")
            return False

    def _sync_email_settings_to_service(self):
        """Sync current email settings to the EmailService"""
        if not self.email_service:
            return

        try:
            email_settings = self.settings['email']

            # Update the email service configuration
            self.email_service.smtp_config.update({
                'smtp_server': email_settings.get('server', ''),
                'smtp_port': email_settings.get('port', 587),
                'smtp_username': email_settings.get('username', ''),
                'smtp_password': email_settings.get('password', ''),
                'smtp_use_tls': email_settings.get('use_tls', True),
                'from_name': email_settings.get('sender_name', 'Agevolami PM'),
                'from_email': email_settings.get('sender_email', ''),
                'enabled': bool(email_settings.get('server'))  # Enable if server is set
            })

            logger.debug("Email settings synced to EmailService")

        except Exception as e:
            logger.error(f"Error syncing email settings to service: {e}")

    def _get_critical_validation_errors(self) -> dict:
        """Get only critical validation errors that should prevent saving"""
        critical_errors = {}

        for category, errors in self.validation_errors.items():
            critical_category_errors = {}

            for key, error in errors.items():
                # Only consider errors critical if they're not just "typing in progress"
                if category == 'email':
                    # For email, only critical if server is completely empty and user tried to save
                    if key == 'server' and not self.settings[category].get(key):
                        continue  # Allow empty server during configuration
                    elif key == 'sender_email' and len(str(self.settings[category].get(key, ''))) <= 3:
                        continue  # Allow partial email during typing

                # Add other category-specific logic here if needed
                critical_category_errors[key] = error

            if critical_category_errors:
                critical_errors[category] = critical_category_errors

        return critical_errors