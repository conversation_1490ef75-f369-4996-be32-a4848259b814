#!/usr/bin/env python3
import traceback
import sys
import os

sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    print("🔍 Testing MatplotlibGanttView import...")
    from ui.views.matplotlib_gantt import MatplotlibGanttView
    print('✅ MatplotlibGanttView imported successfully')
    
    # Test attribute access with mock
    class MockDB:
        def get_all_projects(self): return []
        def get_deadlines_by_date_range(self, s, e): return []
        def get_all_clients(self): return []
    
    class MockApp:
        def __init__(self):
            self.db_manager = MockDB()
    
    app = MockApp()
    print("🔍 Creating MatplotlibGanttView instance...")
    gantt = MatplotlibGanttView(app)
    
    print('✅ MatplotlibGanttView created successfully')
    print(f'filter_client exists: {hasattr(gantt, "filter_client")}')
    print(f'filter_status exists: {hasattr(gantt, "filter_status")}')
    print(f'filter_client value: {getattr(gantt, "filter_client", "NOT_FOUND")}')
    print(f'filter_status value: {getattr(gantt, "filter_status", "NOT_FOUND")}')
    
    # Test chart creation
    print("🔍 Testing chart creation...")
    chart = gantt._create_matplotlib_gantt()
    print('✅ Chart created successfully')
    
except Exception as e:
    print(f'❌ Error: {e}')
    traceback.print_exc() 