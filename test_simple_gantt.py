#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 Simple Native Gantt Test
Quick test with mock data
"""

import flet as ft
from datetime import datetime, timedelta

print("🚀 Simple Native Gantt Test...")

def main(page: ft.Page):
    page.title = "🚀 Native Gantt Test"
    page.window.width = 1200 
    page.window.height = 800
    
    # Create simple mock chart manually
    from dataclasses import dataclass
    
    @dataclass
    class SimpleTask:
        name: str
        start: datetime
        end: datetime
        color: str
        is_milestone: bool = False
    
    # Sample data
    tasks = [
        SimpleTask("🏗️ Project Setup", datetime.now() - timedelta(days=5), datetime.now() + timedelta(days=2), "#4CAF50"),
        SimpleTask("📱 Development Phase", datetime.now(), datetime.now() + timedelta(days=15), "#2196F3"),
        SimpleTask("🧪 Testing Phase", datetime.now() + timedelta(days=12), datetime.now() + timedelta(days=20), "#FF9800"),
        SimpleTask("📅 Launch Date", datetime.now() + timedelta(days=20), datetime.now() + timedelta(days=20), "#F44336", True)
    ]
    
    # Chart dimensions
    chart_width = 800
    row_height = 50
    label_width = 200
    
    # Calculate date range
    all_dates = []
    for task in tasks:
        all_dates.extend([task.start, task.end])
    
    start_date = min(all_dates) - timedelta(days=2)
    end_date = max(all_dates) + timedelta(days=2) 
    total_days = (end_date - start_date).days
    
    def date_to_x(date):
        days_from_start = (date - start_date).days
        return (days_from_start / total_days) * chart_width
    
    # Create timeline header
    timeline_items = []
    weeks = max(1, total_days // 7)
    week_width = chart_width / weeks
    
    current_date = start_date
    for week in range(weeks):
        x_pos = week * week_width
        week_text = current_date.strftime("%d %b")
        
        timeline_items.append(
            ft.Container(
                content=ft.Text(week_text, size=11, color=ft.Colors.GREY_700),
                left=x_pos,
                top=5,
                width=week_width,
                height=30
            )
        )
        current_date += timedelta(days=7)
    
    # Today marker
    today_x = date_to_x(datetime.now())
    if 0 <= today_x <= chart_width:
        timeline_items.extend([
            ft.Container(bgcolor=ft.Colors.RED, left=today_x, top=0, width=2, height=40),
            ft.Container(
                content=ft.Text("TODAY", size=8, color=ft.Colors.RED, weight=ft.FontWeight.BOLD),
                left=today_x - 15, top=2, width=30, height=12, bgcolor=ft.Colors.WHITE70, border_radius=2
            )
        ])
    
    timeline = ft.Container(
        content=ft.Stack(timeline_items),
        width=chart_width,
        height=40,
        bgcolor=ft.Colors.GREY_100,
        border=ft.border.all(1, ft.Colors.GREY_300)
    )
    
    # Create task labels  
    labels = []
    for i, task in enumerate(tasks):
        icon = ft.Icons.SCHEDULE if task.is_milestone else ft.Icons.WORK
        
        labels.append(
            ft.Container(
                content=ft.Row([
                    ft.Icon(icon, color=task.color, size=16),
                    ft.Text(task.name, size=12, color=ft.Colors.GREY_800, expand=True)
                ], spacing=8),
                padding=ft.padding.symmetric(horizontal=10, vertical=15),
                bgcolor=ft.Colors.WHITE if i % 2 == 0 else ft.Colors.GREY_50,
                border=ft.border.all(0.5, ft.Colors.GREY_200),
                height=row_height
            )
        )
    
    labels_column = ft.Container(
        content=ft.Column(labels, spacing=0),
        width=label_width,
        bgcolor=ft.Colors.GREY_50
    )
    
    # Create task bars
    chart_elements = []
    
    for i, task in enumerate(tasks):
        y_pos = i * row_height
        
        # Row background
        chart_elements.append(
            ft.Container(
                bgcolor=ft.Colors.WHITE if i % 2 == 0 else ft.Colors.GREY_50,
                left=0, top=y_pos, width=chart_width, height=row_height,
                border=ft.border.all(0.5, ft.Colors.GREY_200)
            )
        )
        
        if task.is_milestone:
            # Milestone diamond
            x_pos = date_to_x(task.start)
            chart_elements.append(
                ft.Container(
                    content=ft.Icon(ft.Icons.DIAMOND, color=task.color, size=20),
                    left=x_pos - 10, top=y_pos + 15, width=20, height=20
                )
            )
        else:
            # Task bar
            start_x = date_to_x(task.start)
            end_x = date_to_x(task.end)
            bar_width = max(20, end_x - start_x)
            
            chart_elements.append(
                ft.Container(
                    bgcolor=task.color,
                    left=start_x, top=y_pos + 18, width=bar_width, height=14,
                    border_radius=7, opacity=0.8
                )
            )
    
    chart_area = ft.Container(
        content=ft.Stack(chart_elements),
        width=chart_width,
        height=len(tasks) * row_height,
        bgcolor=ft.Colors.WHITE
    )
    
    # Assemble the chart
    main_chart = ft.Container(
        content=ft.Row([
            labels_column,
            ft.Column([timeline, chart_area], spacing=0)
        ], spacing=0),
        border_radius=5,
        border=ft.border.all(1, ft.Colors.GREY_300),
        bgcolor=ft.Colors.WHITE
    )
    
    # Header
    header = ft.Container(
        content=ft.Row([
            ft.Icon(ft.Icons.TIMELINE, size=28, color=ft.Colors.BLUE_600),
            ft.Text("🚀 Simple Native Gantt", size=24, weight=ft.FontWeight.BOLD),
            ft.Container(expand=True),
            ft.Text(f"📊 {len(tasks)} tasks", size=14, color=ft.Colors.GREY_600)
        ]),
        padding=ft.padding.all(20),
        bgcolor=ft.Colors.WHITE,
        border_radius=8,
        border=ft.border.all(1, ft.Colors.GREY_200)
    )
    
    # Build layout
    page.add(
        ft.Container(
            content=ft.Column([
                header,
                main_chart,
                ft.Container(height=20)
            ], spacing=15),
            padding=ft.padding.all(20),
            bgcolor=ft.Colors.GREY_50,
            expand=True
        )
    )
    
    print("✅ Simple Native Gantt loaded!")
    print("🎯 Pure Flet implementation working!")

if __name__ == "__main__":
    ft.app(target=main, port=8083) 