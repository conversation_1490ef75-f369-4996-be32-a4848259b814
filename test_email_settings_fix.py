#!/usr/bin/env python3
"""
Test script to verify email settings persistence fix
"""

import os
import sys
import json
import tempfile
import shutil

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_email_settings_persistence():
    """Test that email settings are preserved across app restarts"""
    print("📧 Testing Email Settings Persistence...")
    print("=" * 50)
    
    try:
        from core.config.app_config import AppConfig
        from ui.views.settings.settings_controller import SettingsController
        from pathlib import Path
        
        # Create a temporary directory for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create mock app with temporary data directory
            class MockApp:
                def __init__(self, data_dir):
                    self.config = AppConfig()
                    # Override data directory for testing
                    self.config.data_dir = data_dir
                    self.config.database_path = data_dir / "test.db"
            
            # Test 1: Save email settings
            print("📝 Test 1: Saving email settings...")
            app1 = MockApp(temp_path)
            controller1 = SettingsController(app1)
            
            # Set test email configuration
            test_email_config = {
                'server': 'smtp.gmail.com',
                'port': 587,
                'username': '<EMAIL>',
                'password': 'test_password',
                'use_tls': True,
                'sender_name': 'Test Sender',
                'sender_email': '<EMAIL>'
            }
            
            for key, value in test_email_config.items():
                controller1.set_setting('email', key, value)
            
            # Save settings
            save_result = controller1.save_settings()
            print(f"   Settings save: {'✅ SUCCESS' if save_result else '❌ FAILED'}")
            
            # Verify settings file was created
            settings_file = temp_path / 'settings.json'
            file_exists = settings_file.exists()
            print(f"   Settings file created: {'✅ YES' if file_exists else '❌ NO'}")
            
            if file_exists:
                with open(settings_file, 'r', encoding='utf-8') as f:
                    saved_data = json.load(f)
                
                saved_email = saved_data.get('email', {})
                print(f"   Saved server: {saved_email.get('server', 'MISSING')}")
                print(f"   Saved username: {saved_email.get('username', 'MISSING')}")
            
            # Test 2: Load email settings (simulate app restart)
            print("\n🔄 Test 2: Loading email settings (simulating app restart)...")
            app2 = MockApp(temp_path)
            controller2 = SettingsController(app2)
            
            # Get loaded email settings
            loaded_email = controller2.get_setting('email')
            
            print(f"   Loaded server: {loaded_email.get('server', 'MISSING')}")
            print(f"   Loaded username: {loaded_email.get('username', 'MISSING')}")
            print(f"   Loaded password: {'***' if loaded_email.get('password') else 'MISSING'}")
            
            # Verify all settings were preserved
            settings_preserved = True
            for key, expected_value in test_email_config.items():
                actual_value = loaded_email.get(key)
                if actual_value != expected_value:
                    print(f"   ❌ {key}: expected {expected_value}, got {actual_value}")
                    settings_preserved = False
                else:
                    print(f"   ✅ {key}: preserved correctly")
            
            # Test 3: Save again to ensure no overwriting
            print("\n💾 Test 3: Save again to test overwriting protection...")
            save_again_result = controller2.save_settings()
            print(f"   Second save: {'✅ SUCCESS' if save_again_result else '❌ FAILED'}")
            
            # Load one more time to verify
            app3 = MockApp(temp_path)
            controller3 = SettingsController(app3)
            final_email = controller3.get_setting('email')
            
            final_preserved = (
                final_email.get('server') == test_email_config['server'] and
                final_email.get('username') == test_email_config['username'] and
                final_email.get('password') == test_email_config['password']
            )
            
            print(f"   Final verification: {'✅ PRESERVED' if final_preserved else '❌ LOST'}")
            
            return settings_preserved and final_preserved
            
    except Exception as e:
        print(f"❌ Error testing email settings persistence: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_current_settings_file():
    """Test the current settings file to see if email config exists"""
    print("\n📄 Testing Current Settings File...")
    print("=" * 50)
    
    try:
        from core.config.app_config import AppConfig
        config = AppConfig()
        settings_file = config.data_dir / 'settings.json'
        
        if settings_file.exists():
            with open(settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)
            
            email_settings = settings.get('email', {})
            
            print("📧 Current Email Settings:")
            print("-" * 30)
            print(f"   Server: {email_settings.get('server', 'NOT SET')}")
            print(f"   Port: {email_settings.get('port', 'NOT SET')}")
            print(f"   Username: {email_settings.get('username', 'NOT SET')}")
            print(f"   Password: {'***' if email_settings.get('password') else 'NOT SET'}")
            print(f"   Use TLS: {email_settings.get('use_tls', 'NOT SET')}")
            print(f"   Sender Name: {email_settings.get('sender_name', 'NOT SET')}")
            print(f"   Sender Email: {email_settings.get('sender_email', 'NOT SET')}")
            print(f"   Test Status: {email_settings.get('test_connection_status', 'NOT SET')}")
            
            # Check if email is configured
            is_configured = bool(
                email_settings.get('server') and 
                email_settings.get('username') and 
                email_settings.get('password')
            )
            
            print(f"\n📊 Configuration Status: {'✅ CONFIGURED' if is_configured else '❌ NOT CONFIGURED'}")
            
            return is_configured
        else:
            print("❌ Settings file does not exist")
            return False
            
    except Exception as e:
        print(f"❌ Error reading current settings: {e}")
        return False

def backup_current_settings():
    """Create a backup of current settings before testing"""
    print("\n💾 Creating Settings Backup...")
    print("=" * 50)
    
    try:
        from core.config.app_config import AppConfig
        import time
        
        config = AppConfig()
        settings_file = config.data_dir / 'settings.json'
        
        if settings_file.exists():
            backup_file = config.data_dir / f'settings_backup_{int(time.time())}.json'
            shutil.copy2(settings_file, backup_file)
            print(f"✅ Backup created: {backup_file}")
            return str(backup_file)
        else:
            print("⚠️ No settings file to backup")
            return None
            
    except Exception as e:
        print(f"❌ Error creating backup: {e}")
        return None

def main():
    """Main test function"""
    print("🚀 Email Settings Persistence Fix Test")
    print("=" * 60)
    
    # Create backup first
    backup_path = backup_current_settings()
    
    # Test current settings
    current_configured = test_current_settings_file()
    
    # Test persistence mechanism
    persistence_works = test_email_settings_persistence()
    
    print("\n" + "=" * 60)
    print("📋 TEST RESULTS")
    print("=" * 60)
    
    if current_configured:
        print("✅ Current email settings are configured")
    else:
        print("⚠️ Current email settings are not configured")
    
    if persistence_works:
        print("✅ Email settings persistence mechanism works correctly")
        print("💡 SMTP config should no longer get deleted on app restart")
    else:
        print("❌ Email settings persistence has issues")
        print("💡 SMTP config may still get deleted - check the errors above")
    
    if backup_path:
        print(f"📁 Settings backup available at: {backup_path}")
    
    print("\n🎯 RECOMMENDATION:")
    if persistence_works:
        print("   The fix should resolve the SMTP deletion issue!")
        print("   Try closing and reopening the app to verify.")
    else:
        print("   Additional debugging may be needed.")
        print("   Check the error messages above for clues.")
    
    return persistence_works

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
