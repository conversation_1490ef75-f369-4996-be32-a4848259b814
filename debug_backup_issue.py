#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug script per identificare il problema con il backup
"""

import sys
import os
from pathlib import Path

# Aggiungi il percorso src al PYTHONPATH
sys.path.insert(0, str(Path(__file__).parent / "src"))

from core.config import AppConfig

def debug_backup_paths():
    """Debug dei percorsi utilizzati per il backup"""
    print("🔍 Debug percorsi backup")
    print("=" * 50)
    
    # Inizializza configurazione
    config = AppConfig()
    
    print(f"📁 Data directory: {config.data_dir}")
    print(f"📁 Data directory esiste: {config.data_dir.exists()}")
    
    # Controlla database in diverse posizioni
    possible_db_paths = [
        config.data_dir / "agevolami.db",
        Path("D:/pro projects/flet/AGevolami_PM/agevolami_pm.db"),
        Path.cwd() / "agevolami_pm.db",
        Path.cwd() / "agevolami.db"
    ]

    print(f"🗄️ Ricerca database in {len(possible_db_paths)} posizioni:")
    database_found = None

    for db_path in possible_db_paths:
        exists = db_path.exists() if db_path else False
        print(f"   • {db_path}: {'✅' if exists else '❌'}")
        if exists and not database_found:
            database_found = db_path
            size = db_path.stat().st_size
            print(f"     → Dimensione: {size} bytes ({size / 1024:.1f} KB)")

    if database_found:
        print(f"✅ Database principale: {database_found}")
    else:
        print("❌ Nessun database trovato")
    
    # Controlla file di configurazione
    config_files = ['settings.json', 'app_config.json']
    print(f"\n📋 File di configurazione:")
    for config_file in config_files:
        config_path = config.data_dir / config_file
        exists = config_path.exists()
        size = config_path.stat().st_size if exists else 0
        print(f"   • {config_file}: {'✅' if exists else '❌'} ({size} bytes)")
    
    # Controlla logs
    logs_dir = config.data_dir.parent / 'logs'
    print(f"\n📝 Logs directory: {logs_dir}")
    print(f"📝 Logs directory esiste: {logs_dir.exists()}")
    
    if logs_dir.exists():
        log_files = list(logs_dir.glob('*.log'))
        print(f"📝 Log files trovati: {len(log_files)}")
        for log_file in log_files[:5]:  # Primi 5
            size = log_file.stat().st_size
            print(f"   • {log_file.name}: {size} bytes")
    
    # Controlla assets
    assets_dir = config.data_dir.parent / 'assets'
    print(f"\n🎨 Assets directory: {assets_dir}")
    print(f"🎨 Assets directory esiste: {assets_dir.exists()}")
    
    if assets_dir.exists():
        asset_files = list(assets_dir.rglob('*'))
        asset_files = [f for f in asset_files if f.is_file()]
        print(f"🎨 Asset files trovati: {len(asset_files)}")
    
    # Lista tutti i file nella data directory
    print(f"\n📂 Contenuto data directory ({config.data_dir}):")
    if config.data_dir.exists():
        for item in config.data_dir.iterdir():
            if item.is_file():
                size = item.stat().st_size
                print(f"   📄 {item.name}: {size} bytes")
            elif item.is_dir():
                file_count = len(list(item.rglob('*')))
                print(f"   📁 {item.name}/: {file_count} items")
    
    print(f"\n🎯 Raccomandazioni:")
    
    if not database_path.exists():
        print("❌ Database non trovato!")
        print("   • Verifica che l'app sia stata avviata almeno una volta")
        print("   • Controlla se il database ha un nome diverso")
        
        # Cerca database con nomi alternativi
        possible_db_files = list(config.data_dir.glob('*.db'))
        if possible_db_files:
            print(f"   • Database alternativi trovati: {[f.name for f in possible_db_files]}")
        else:
            print("   • Nessun file .db trovato nella directory")
    else:
        print("✅ Database trovato e accessibile")
    
    # Controlla se ci sono almeno alcuni file per il backup
    has_config = any((config.data_dir / f).exists() for f in config_files)
    has_logs = logs_dir.exists() and any(logs_dir.glob('*.log'))
    
    if has_config or has_logs or database_path.exists():
        print("✅ Ci sono file sufficienti per creare un backup")
    else:
        print("❌ Non ci sono file sufficienti per creare un backup")
        print("   • Assicurati che l'app sia stata configurata e utilizzata")

def test_backup_creation():
    """Test creazione backup locale"""
    print("\n🧪 Test creazione backup locale")
    print("=" * 50)
    
    try:
        from core.services.google_drive_service import GoogleDriveService
        
        config = AppConfig()
        database_path = config.data_dir / "agevolami.db"
        
        # Crea servizio (senza autenticazione)
        drive_service = GoogleDriveService(config.data_dir / "config")
        
        print("📦 Tentativo creazione backup locale...")
        backup_path = drive_service._create_local_backup(database_path, config.data_dir)
        
        if backup_path and backup_path.exists():
            print(f"✅ Backup locale creato: {backup_path}")
            print(f"📏 Dimensione: {backup_path.stat().st_size} bytes")
            
            # Test verifica integrità
            print("🔍 Test verifica integrità...")
            integrity_ok = drive_service._verify_backup_integrity(backup_path)
            print(f"🔍 Integrità: {'✅ OK' if integrity_ok else '❌ FALLITA'}")
            
            # Pulisci
            os.unlink(backup_path)
            print("🧹 File di test eliminato")
            
        else:
            print("❌ Impossibile creare backup locale")
            
    except Exception as e:
        print(f"❌ Errore durante il test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_backup_paths()
    test_backup_creation()
