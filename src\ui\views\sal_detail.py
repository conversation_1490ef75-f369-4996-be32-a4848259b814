#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vista dettagli SAL per Agevolami PM
"""

import flet as ft
from datetime import datetime, date
from typing import Optional, List, Dict, Any
from uuid import UUID

from core.models import (
    SAL, Project, Client, Deadline, Document, Alert,
    ProjectStatus, SALType, SALMilestoneType, DeadlineStatus, Priority
)
from core.services.sal_service import SALService
from core.utils import get_logger

logger = get_logger(__name__)

class SALDetailView:
    """Vista per visualizzare i dettagli di un SAL"""
    
    def __init__(self, app, sal_id: UUID):
        self.app = app
        self.page = app.page
        self.sal_id = sal_id
        self.sal_service = SALService(app.db)
        
        # Data
        self.sal: Optional[SAL] = None
        self.project: Optional[Project] = None
        self.client: Optional[Client] = None
        self.deadlines: List[Deadline] = []
        self.documents: List[Document] = []
        
        # UI Components
        self.content = ft.Column()
        self.header_section = ft.Container()
        self.overview_section = ft.Container()
        self.deadlines_section = ft.Container()
        self.documents_section = ft.Container()
        
    def build(self) -> ft.Control:
        """Costruisce la vista dettagli SAL"""
        try:
            self._load_data()
            self._build_header()
            self._build_overview()
            self._build_deadlines_section()
            self._build_documents_section()
            
            # Layout a tab moderno
            tabs = ft.Tabs(
                selected_index=0,
                animation_duration=200,
                indicator_color=ft.Colors.BLUE_600,
                label_color=ft.Colors.BLUE_600,
                unselected_label_color=ft.Colors.GREY_600,
                tabs=[
                    ft.Tab(
                        text="Panoramica",
                        icon=ft.Icons.DASHBOARD_OUTLINED,
                        content=ft.Container(
                            content=self.overview_section,
                            padding=16
                        )
                    ),
                    ft.Tab(
                        text="Scadenze",
                        icon=ft.Icons.SCHEDULE_OUTLINED,
                        content=ft.Container(
                            content=self.deadlines_section,
                            padding=16
                        )
                    ),
                    ft.Tab(
                        text="Documenti",
                        icon=ft.Icons.FOLDER_OUTLINED,
                        content=ft.Container(
                            content=self.documents_section,
                            padding=16
                        )
                    )
                ],
                expand=True
            )
            
            self.content = ft.Column(
                controls=[
                    self.header_section,
                    ft.Container(height=12),
                    tabs
                ],
                spacing=0,
                expand=True
            )

            return ft.Container(
                content=self.content,
                expand=True,
                bgcolor=ft.Colors.GREY_50,
                padding=12
            )
            
        except Exception as e:
            logger.error(f"Errore costruzione vista SAL: {e}")
            return ft.Container(
                content=ft.Text(f"Errore caricamento dettagli SAL: {e}", color=ft.Colors.RED),
                padding=20
            )
    
    def _load_data(self):
        """Carica i dati del SAL"""
        try:
            self.sal = self.app.db.get_sal_by_id(self.sal_id)
            if not self.sal:
                raise Exception("SAL non trovato")

            self.project = self.app.db.get_project(self.sal.project_id)
            if self.project:
                self.client = self.app.db.get_client(self.project.client_id)
            
            self.deadlines = self.sal_service.get_sal_deadlines(self.sal_id)
            self.documents = self.sal_service.get_sal_documents(self.sal_id)
            
            logger.info(f"Caricati dati per SAL: {self.sal.name}")
            
        except Exception as e:
            logger.error(f"Errore caricamento dati SAL: {e}")
            raise

    def _get_sal_type_display(self) -> str:
        """Ottiene il display text per il tipo SAL in modo sicuro"""
        if not self.sal or not self.sal.sal_type:
            return "Non specificato"

        # Se è un enum, usa il valore
        if hasattr(self.sal.sal_type, 'value'):
            return self.sal.sal_type.value

        # Se è una stringa, usala direttamente
        if isinstance(self.sal.sal_type, str):
            return self.sal.sal_type

        # Fallback
        return str(self.sal.sal_type)

    def _get_milestone_type_display(self) -> str:
        """Ottiene il display text per il tipo milestone in modo sicuro"""
        if not self.sal or not self.sal.milestone_type:
            return "Non specificato"

        # Se è un enum, usa il valore
        if hasattr(self.sal.milestone_type, 'value'):
            return self.sal.milestone_type.value

        # Se è una stringa, usala direttamente
        if isinstance(self.sal.milestone_type, str):
            return self.sal.milestone_type

        # Fallback
        return str(self.sal.milestone_type)

    def _build_header(self):
        """Costruisce la sezione header"""
        if not self.sal:
            return

        # Status badge
        status_config = {
            ProjectStatus.DRAFT: ("Bozza", ft.Colors.GREY_600, ft.Icons.EDIT),
            ProjectStatus.IN_PROGRESS: ("In Corso", ft.Colors.BLUE_600, ft.Icons.PLAY_CIRCLE),
            ProjectStatus.COMPLETED: ("Completato", ft.Colors.GREEN_600, ft.Icons.CHECK_CIRCLE),
            ProjectStatus.SUSPENDED: ("Sospeso", ft.Colors.ORANGE_600, ft.Icons.PAUSE_CIRCLE)
        }

        status_text, status_color, status_icon = status_config.get(
            self.sal.status,
            ("Sconosciuto", ft.Colors.GREY_600, ft.Icons.HELP_OUTLINE)
        )

        status_badge = ft.Container(
            content=ft.Row(
                controls=[
                    ft.Icon(status_icon, size=14, color=ft.Colors.WHITE),
                    ft.Text(status_text, size=11, color=ft.Colors.WHITE, weight=ft.FontWeight.BOLD)
                ],
                spacing=4,
                tight=True
            ),
            bgcolor=status_color,
            padding=ft.padding.symmetric(horizontal=10, vertical=5),
            border_radius=12
        )

        # Info principale
        sal_info = ft.Column(
            controls=[
                ft.Row(
                    controls=[
                        ft.Text(
                            self.sal.name,
                            size=24,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.GREY_900,
                            expand=True
                        ),
                        status_badge
                    ],
                    spacing=12
                ),
                ft.Text(
                    f"Sequenza #{self.sal.sequence_number} • {self.sal.percentage}% completamento",
                    size=14,
                    color=ft.Colors.GREY_600,
                    weight=ft.FontWeight.BOLD
                ),
                ft.Row(
                    controls=[
                        ft.Icon(ft.Icons.FOLDER, size=14, color=ft.Colors.GREY_500),
                        ft.Text(
                            self.project.name if self.project else 'Progetto non trovato',
                            size=12,
                            color=ft.Colors.GREY_600
                        )
                    ],
                    spacing=6
                ),
                ft.Row(
                    controls=[
                        ft.Icon(ft.Icons.BUSINESS, size=14, color=ft.Colors.GREY_500),
                        ft.Text(
                            self.client.name if self.client else 'Cliente non trovato',
                            size=12,
                            color=ft.Colors.GREY_600
                        )
                    ],
                    spacing=6
                )
            ],
            spacing=4
        )

        # Pulsanti azione
        action_buttons = ft.Row(
            controls=[
                ft.ElevatedButton(
                    "Modifica",
                    icon=ft.Icons.EDIT_OUTLINED,
                    on_click=lambda _: self._edit_sal(),
                    bgcolor=ft.Colors.BLUE_600,
                    color=ft.Colors.WHITE,
                    height=36
                ),
                ft.OutlinedButton(
                    "Nuova Scadenza",
                    icon=ft.Icons.SCHEDULE_OUTLINED,
                    on_click=lambda _: self._create_deadline(),
                    height=36
                ),
                ft.OutlinedButton(
                    "Carica Documento",
                    icon=ft.Icons.UPLOAD_FILE_OUTLINED,
                    on_click=lambda _: self._upload_document(),
                    height=36
                )
            ],
            spacing=6,
            wrap=True
        )

        self.header_section = ft.Container(
            content=ft.Row(
                controls=[
                    ft.Container(content=sal_info, expand=True),
                    ft.Container(content=action_buttons)
                ],
                alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                vertical_alignment=ft.CrossAxisAlignment.START
            ),
            bgcolor=ft.Colors.WHITE,
            padding=16,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )
    
    def _build_overview(self):
        """Costruisce la sezione panoramica"""
        # Progress bar
        progress_color = ft.Colors.GREEN_600 if self.sal.percentage >= 80 else ft.Colors.BLUE_600 if self.sal.percentage >= 50 else ft.Colors.ORANGE_600
        
        progress_section = ft.Container(
            content=ft.Column([
                ft.Text("Avanzamento SAL", size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_800),
                ft.Container(height=8),
                ft.Row([
                    ft.Text("Completamento", size=12, color=ft.Colors.GREY_600),
                    ft.Text(f"{self.sal.percentage}%", size=12, weight=ft.FontWeight.BOLD, color=progress_color)
                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                ft.ProgressBar(
                    value=self.sal.percentage / 100,
                    color=progress_color,
                    bgcolor=ft.Colors.GREY_200,
                    height=8
                )
            ], spacing=4),
            bgcolor=ft.Colors.WHITE,
            padding=16,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )
        
        # Informazioni dettagliate
        detail_rows = []
        
        if self.sal.description:
            detail_rows.append(self._create_detail_row("Descrizione", self.sal.description))
        
        if self.sal.sal_type:
            detail_rows.append(self._create_detail_row("Tipo SAL", self._get_sal_type_display()))

        if self.sal.milestone_type:
            detail_rows.append(self._create_detail_row("Milestone", self._get_milestone_type_display()))
        
        if self.sal.planned_start_date:
            detail_rows.append(self._create_detail_row("Data Inizio Pianificata", self.sal.planned_start_date.strftime('%d/%m/%Y')))
        
        if self.sal.planned_end_date:
            detail_rows.append(self._create_detail_row("Data Fine Pianificata", self.sal.planned_end_date.strftime('%d/%m/%Y')))
        
        if self.sal.actual_start_date:
            detail_rows.append(self._create_detail_row("Data Inizio Effettiva", self.sal.actual_start_date.strftime('%d/%m/%Y')))
        
        if self.sal.actual_end_date:
            detail_rows.append(self._create_detail_row("Data Fine Effettiva", self.sal.actual_end_date.strftime('%d/%m/%Y')))
        
        if self.sal.budget:
            detail_rows.append(self._create_detail_row("Budget", f"€{self.sal.budget:,.2f}"))
        
        if self.sal.actual_cost:
            detail_rows.append(self._create_detail_row("Costo Effettivo", f"€{self.sal.actual_cost:,.2f}"))
        
        detail_rows.append(self._create_detail_row("Creato il", self.sal.created_at.strftime('%d/%m/%Y %H:%M')))
        
        if self.sal.updated_at:
            detail_rows.append(self._create_detail_row("Aggiornato il", self.sal.updated_at.strftime('%d/%m/%Y %H:%M')))

        details_section = ft.Container(
            content=ft.Column([
                ft.Text("Dettagli SAL", size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_800),
                ft.Container(height=8),
                *detail_rows
            ], spacing=8),
            bgcolor=ft.Colors.WHITE,
            padding=16,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )
        
        self.overview_section = ft.Column([
            progress_section,
            ft.Container(height=16),
            details_section
        ], spacing=0)
    
    def _create_detail_row(self, label: str, value: str) -> ft.Container:
        """Crea una riga di dettaglio"""
        return ft.Container(
            content=ft.Row([
                ft.Text(f"{label}:", size=12, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_600, width=150),
                ft.Text(value, size=12, color=ft.Colors.GREY_800, expand=True)
            ], spacing=8),
            padding=ft.padding.symmetric(vertical=4)
        )
    
    def _build_deadlines_section(self):
        """Costruisce la sezione scadenze"""
        if not self.deadlines:
            self.deadlines_section = ft.Container(
                content=ft.Column([
                    ft.Icon(ft.Icons.SCHEDULE_OUTLINED, size=48, color=ft.Colors.GREY_400),
                    ft.Text("Nessuna scadenza associata", size=14, color=ft.Colors.GREY_600, weight=ft.FontWeight.BOLD),
                    ft.Text("Crea scadenze per tracciare le milestone del SAL", size=11, color=ft.Colors.GREY_400)
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=8),
                padding=32,
                alignment=ft.alignment.center
            )
            return
        
        deadline_cards = []
        for deadline in self.deadlines:
            deadline_cards.append(self._create_deadline_card(deadline))
        
        self.deadlines_section = ft.Column(
            controls=deadline_cards,
            spacing=10,
            scroll=ft.ScrollMode.AUTO
        )
    
    def _create_deadline_card(self, deadline: Deadline) -> ft.Card:
        """Crea una card scadenza"""
        status_config = {
            DeadlineStatus.PENDING: ("In Attesa", ft.Colors.ORANGE_600, ft.Icons.SCHEDULE),
            DeadlineStatus.COMPLETED: ("Completata", ft.Colors.GREEN_600, ft.Icons.CHECK_CIRCLE),
            DeadlineStatus.OVERDUE: ("Scaduta", ft.Colors.RED_600, ft.Icons.ERROR)
        }

        status_text, status_color, status_icon = status_config.get(
            deadline.status,
            ("Sconosciuto", ft.Colors.GREY_600, ft.Icons.HELP_OUTLINE)
        )

        return ft.Card(
            content=ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Text(deadline.title, size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_800, expand=True),
                        ft.Container(
                            content=ft.Row([
                                ft.Icon(status_icon, size=12, color=ft.Colors.WHITE),
                                ft.Text(status_text, size=10, color=ft.Colors.WHITE, weight=ft.FontWeight.BOLD)
                            ], spacing=4, tight=True),
                            bgcolor=status_color,
                            padding=ft.padding.symmetric(horizontal=8, vertical=4),
                            border_radius=10
                        )
                    ]),
                    *([ft.Text(deadline.description, size=11, color=ft.Colors.GREY_600, max_lines=2)] if deadline.description else []),
                    ft.Text(f"Scadenza: {deadline.due_date.strftime('%d/%m/%Y')}", size=11, color=ft.Colors.GREY_600)
                ], spacing=6),
                padding=12
            ),
            elevation=1
        )
    
    def _build_documents_section(self):
        """Costruisce la sezione documenti"""
        if not self.documents:
            self.documents_section = ft.Container(
                content=ft.Column([
                    ft.Icon(ft.Icons.FOLDER_OUTLINED, size=48, color=ft.Colors.GREY_400),
                    ft.Text("Nessun documento caricato", size=14, color=ft.Colors.GREY_600, weight=ft.FontWeight.BOLD),
                    ft.Text("Carica documenti specifici per questo SAL", size=11, color=ft.Colors.GREY_400)
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=8),
                padding=32,
                alignment=ft.alignment.center
            )
            return
        
        document_cards = []
        for document in self.documents:
            document_cards.append(self._create_document_card(document))
        
        self.documents_section = ft.Column(
            controls=document_cards,
            spacing=10,
            scroll=ft.ScrollMode.AUTO
        )
    
    def _create_document_card(self, document: Document) -> ft.Card:
        """Crea una card documento"""
        return ft.Card(
            content=ft.Container(
                content=ft.Row([
                    ft.Icon(ft.Icons.DESCRIPTION, size=32, color=ft.Colors.BLUE_600),
                    ft.Column([
                        ft.Text(document.name, size=14, weight=ft.FontWeight.BOLD, max_lines=1),
                        ft.Text(document.description or "Nessuna descrizione", size=12, color=ft.Colors.GREY_600, max_lines=1),
                        ft.Text(f"Caricato: {document.created_at.strftime('%d/%m/%Y')}", size=10, color=ft.Colors.GREY_500)
                    ], spacing=2, expand=True),
                    ft.IconButton(
                        icon=ft.Icons.OPEN_IN_NEW,
                        tooltip="Apri documento",
                        on_click=lambda _, doc=document: self._open_document(doc)
                    )
                ], spacing=12),
                padding=12
            ),
            elevation=1
        )
    
    def _edit_sal(self):
        """Apre il form di modifica SAL"""
        # TODO: Implementare form modifica SAL
        logger.info(f"Modifica SAL: {self.sal.name}")
    
    def _create_deadline(self):
        """Crea una nuova scadenza per il SAL"""
        # TODO: Implementare creazione scadenza SAL
        logger.info(f"Crea scadenza per SAL: {self.sal.name}")
    
    def _upload_document(self):
        """Carica un documento per il SAL"""
        # TODO: Implementare upload documento SAL
        logger.info(f"Carica documento per SAL: {self.sal.name}")
    
    def _open_document(self, document: Document):
        """Apre un documento"""
        # TODO: Implementare apertura documento
        logger.info(f"Apri documento: {document.name}")
