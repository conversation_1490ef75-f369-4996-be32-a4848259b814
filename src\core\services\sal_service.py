#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Servizio per la gestione degli Stati Avanzamento Lavori (SAL)
"""

from datetime import datetime, date, timedelta
from typing import Optional, List, Dict, Any, Tuple
from uuid import UUID, uuid4
from pathlib import Path

from ..models import (
    SAL, SALConfiguration, SALTemplate, Project, Deadline, Document,
    ProjectType, ProjectCategory, ProjectSubcategory, SALType, SALMilestoneType,
    ProjectStatus, DeadlineStatus, Priority
)
from ..utils import get_logger

logger = get_logger(__name__)

class SALService:
    """Servizio per la gestione completa dei SAL"""
    
    def __init__(self, db_manager):
        self.db = db_manager
        self._init_default_configurations()
    
    def _init_default_configurations(self):
        """Inizializza le configurazioni SAL predefinite"""
        self.sal_rules = {
            # Incentivi Pubblici + Ricerca & Innovazione - Richiedono SAL
            (ProjectCategory.INCENTIVI_PUBBLICI, ProjectSubcategory.RICERCA_INNOVAZIONE): {
                "requires_sal": True,
                "default_count": 3,
                "sal_type": SALType.MILESTONE,
                "template": "public_incentive_3_phase",
                "auto_deadlines": True,
                "milestones": ["project_start", "mid_progress", "completion"]
            },

            # Incentivi Pubblici + Investimenti Produttivi - Richiedono SAL
            (ProjectCategory.INCENTIVI_PUBBLICI, ProjectSubcategory.INVESTIMENTI_PRODUTTIVI): {
                "requires_sal": True,
                "default_count": 2,
                "sal_type": SALType.MILESTONE,
                "template": "investment_2_phase",
                "auto_deadlines": True,
                "milestones": ["mid_progress", "completion"]
            },

            # Crediti & Finanza + Finanziamenti - Richiedono SAL per erogazioni
            (ProjectCategory.CREDITI_FINANZA, ProjectSubcategory.FINANZIAMENTI): {
                "requires_sal": True,
                "default_count": 2,
                "sal_type": SALType.DISBURSEMENT,
                "template": "loan_disbursement",
                "auto_deadlines": True,
                "milestones": ["first_disbursement", "final_disbursement"]
            },

            # Crediti & Finanza + Crediti d'Imposta - Possono richiedere SAL
            (ProjectCategory.CREDITI_FINANZA, ProjectSubcategory.CREDITI_IMPOSTA): {
                "requires_sal": True,
                "default_count": 2,
                "sal_type": SALType.MILESTONE,
                "template": "credits_2_phase",
                "auto_deadlines": True,
                "milestones": ["mid_progress", "completion"]
            },

            # Fiscale & Contabile + Contabilità - NO SAL (periodici)
            (ProjectCategory.FISCALE_CONTABILE, ProjectSubcategory.CONTABILITA): {
                "requires_sal": False,
                "default_count": 0,
                "sal_type": None,
                "template": None,
                "auto_deadlines": False,
                "milestones": []
            },

            # Fiscale & Contabile + Adempimenti Fiscali - NO SAL (periodici)
            (ProjectCategory.FISCALE_CONTABILE, ProjectSubcategory.ADEMPIMENTI_FISCALI): {
                "requires_sal": False,
                "default_count": 0,
                "sal_type": None,
                "template": None,
                "auto_deadlines": False,
                "milestones": []
            }
        }
        
        # Template SAL predefiniti
        self.sal_templates = {
            "public_incentive_3_phase": [
                {
                    "name": "SAL 1 - Avvio Progetto",
                    "percentage": 30.0,
                    "milestone": SALMilestoneType.PROJECT_START,
                    "description": "Avvio attività e prime milestone",
                    "requires_approval": True
                },
                {
                    "name": "SAL 2 - Avanzamento Intermedio", 
                    "percentage": 70.0,
                    "milestone": SALMilestoneType.MID_PROGRESS,
                    "description": "Raggiungimento obiettivi intermedi",
                    "requires_approval": True
                },
                {
                    "name": "SAL 3 - Completamento",
                    "percentage": 100.0,
                    "milestone": SALMilestoneType.COMPLETION,
                    "description": "Completamento progetto e rendicontazione finale",
                    "requires_approval": True
                }
            ],
            "transition_2_phase": [
                {
                    "name": "SAL 1 - Implementazione Transizione",
                    "percentage": 60.0,
                    "milestone": SALMilestoneType.MID_PROGRESS,
                    "description": "Implementazione misure di transizione digitale/ecologica",
                    "requires_approval": True
                },
                {
                    "name": "SAL 2 - Completamento",
                    "percentage": 100.0,
                    "milestone": SALMilestoneType.COMPLETION,
                    "description": "Completamento transizione e rendicontazione",
                    "requires_approval": True
                }
            ],
            "credits_2_phase": [
                {
                    "name": "SAL 1 - Avanzamento Attività",
                    "percentage": 70.0,
                    "milestone": SALMilestoneType.MID_PROGRESS,
                    "description": "Avanzamento attività di ricerca e sviluppo",
                    "requires_approval": True
                },
                {
                    "name": "SAL 2 - Completamento",
                    "percentage": 100.0,
                    "milestone": SALMilestoneType.COMPLETION,
                    "description": "Completamento attività e rendicontazione finale",
                    "requires_approval": True
                }
            ],
            "investment_2_phase": [
                {
                    "name": "SAL 1 - Avanzamento Investimenti",
                    "percentage": 60.0,
                    "milestone": SALMilestoneType.MID_PROGRESS,
                    "description": "Avanzamento investimenti sostenibili",
                    "requires_approval": True
                },
                {
                    "name": "SAL 2 - Completamento",
                    "percentage": 100.0,
                    "milestone": SALMilestoneType.COMPLETION,
                    "description": "Completamento investimenti e messa in opera",
                    "requires_approval": True
                }
            ],
            "development_contract_3_phase": [
                {
                    "name": "SAL 1 - Avvio Contratto",
                    "percentage": 30.0,
                    "milestone": SALMilestoneType.PROJECT_START,
                    "description": "Avvio attività del contratto di sviluppo",
                    "requires_approval": True
                },
                {
                    "name": "SAL 2 - Avanzamento Intermedio",
                    "percentage": 70.0,
                    "milestone": SALMilestoneType.MID_PROGRESS,
                    "description": "Avanzamento intermedio del progetto",
                    "requires_approval": True
                },
                {
                    "name": "SAL 3 - Completamento",
                    "percentage": 100.0,
                    "milestone": SALMilestoneType.COMPLETION,
                    "description": "Completamento contratto di sviluppo",
                    "requires_approval": True
                }
            ],
            "mini_development_2_phase": [
                {
                    "name": "SAL 1 - Avanzamento",
                    "percentage": 60.0,
                    "milestone": SALMilestoneType.MID_PROGRESS,
                    "description": "Avanzamento mini contratto di sviluppo",
                    "requires_approval": True
                },
                {
                    "name": "SAL 2 - Completamento",
                    "percentage": 100.0,
                    "milestone": SALMilestoneType.COMPLETION,
                    "description": "Completamento mini contratto",
                    "requires_approval": True
                }
            ],
            "loan_disbursement": [
                {
                    "name": "Prima Erogazione",
                    "percentage": 50.0,
                    "milestone": SALMilestoneType.FIRST_DISBURSEMENT,
                    "description": "Prima tranche di finanziamento",
                    "requires_approval": False
                },
                {
                    "name": "Saldo Finale",
                    "percentage": 100.0,
                    "milestone": SALMilestoneType.FINAL_DISBURSEMENT,
                    "description": "Erogazione finale del finanziamento",
                    "requires_approval": True
                }
            ],
            "guarantee_milestone": [
                {
                    "name": "Rilascio Garanzia",
                    "percentage": 100.0,
                    "milestone": SALMilestoneType.COMPLETION,
                    "description": "Completamento istruttoria e rilascio garanzia",
                    "requires_approval": True
                }
            ]
        }
    
    def project_requires_sal(self, project: Project) -> bool:
        """Determina se un progetto richiede SAL basandosi sul tipo"""
        if hasattr(project, 'requires_sal') and project.requires_sal is not None:
            return project.requires_sal

        rule = self.sal_rules.get((project.category, project.subcategory))
        return rule.get("requires_sal", False) if rule else False

    def get_sal_suggestions(self, project: Project) -> Dict[str, Any]:
        """Ottiene suggerimenti SAL per un progetto"""
        rule = self.sal_rules.get((project.category, project.subcategory))
        if not rule or not rule.get("requires_sal", False):
            return {
                "requires_sal": False,
                "suggested_count": 0,
                "template": None,
                "sal_definitions": []
            }

        template_name = rule.get("template")
        sal_definitions = self.sal_templates.get(template_name, [])

        return {
            "requires_sal": True,
            "suggested_count": rule.get("default_count", 0),
            "sal_type": rule.get("sal_type"),
            "template": template_name,
            "auto_deadlines": rule.get("auto_deadlines", False),
            "sal_definitions": sal_definitions
        }
    
    def create_sal_from_template(self, project_id: UUID, template_name: str, 
                                custom_data: Optional[Dict] = None) -> List[SAL]:
        """Crea SAL automaticamente da template"""
        try:
            template_definitions = self.sal_templates.get(template_name, [])
            if not template_definitions:
                raise ValueError(f"Template SAL non trovato: {template_name}")
            
            created_sals = []
            
            for i, sal_def in enumerate(template_definitions, 1):
                # Merge con dati personalizzati se forniti
                sal_data = sal_def.copy()
                if custom_data and str(i) in custom_data:
                    sal_data.update(custom_data[str(i)])
                
                sal = SAL(
                    id=uuid4(),
                    project_id=project_id,
                    name=sal_data.get("name", f"SAL {i}"),
                    description=sal_data.get("description"),
                    sequence_number=i,
                    percentage=sal_data.get("percentage", 0.0),
                    sal_type=SALType(sal_data.get("sal_type", SALType.MILESTONE)),
                    milestone_type=SALMilestoneType(sal_data.get("milestone")) if sal_data.get("milestone") else None,
                    template_id=template_name,
                    auto_generated=True,
                    requires_approval=sal_data.get("requires_approval", False),
                    status=ProjectStatus.DRAFT,
                    created_at=datetime.now()
                )
                
                created_sal = self.db.create_sal(sal)
                created_sals.append(created_sal)
                
                logger.info(f"SAL creato da template: {sal.name} per progetto {project_id}")
            
            return created_sals
            
        except Exception as e:
            logger.error(f"Errore creazione SAL da template: {e}")
            raise

    def create_suggested_sals(self, project_id: UUID, suggestions: Dict[str, Any]) -> List[SAL]:
        """Crea SAL suggeriti automaticamente"""
        try:
            if not suggestions.get("requires_sal", False):
                return []

            template_name = suggestions.get("template")
            if not template_name:
                logger.warning(f"Nessun template specificato per progetto {project_id}")
                return []

            # Usa il metodo esistente per creare SAL da template
            created_sals = self.create_sal_from_template(project_id, template_name)

            logger.info(f"Creati {len(created_sals)} SAL suggeriti per progetto {project_id}")
            return created_sals

        except Exception as e:
            logger.error(f"Errore creazione SAL suggeriti: {e}")
            raise

    def create_sal(self, sal: SAL) -> SAL:
        """Crea un nuovo SAL"""
        try:
            created_sal = self.db.create_sal(sal)
            logger.info(f"SAL creato: {created_sal.name} per progetto {created_sal.project_id}")
            return created_sal
        except Exception as e:
            logger.error(f"Errore creazione SAL: {e}")
            raise

    def update_sal(self, sal: SAL) -> SAL:
        """Aggiorna un SAL esistente"""
        try:
            updated_sal = self.db.update_sal(sal)
            logger.info(f"SAL aggiornato: {updated_sal.name}")
            return updated_sal
        except Exception as e:
            logger.error(f"Errore aggiornamento SAL: {e}")
            raise

    def delete_sal(self, sal_id: UUID) -> bool:
        """Elimina un SAL"""
        try:
            success = self.db.delete_sal(sal_id)
            if success:
                logger.info(f"SAL eliminato: {sal_id}")
            return success
        except Exception as e:
            logger.error(f"Errore eliminazione SAL: {e}")
            raise

    def get_sal_by_id(self, sal_id: UUID) -> Optional[SAL]:
        """Recupera un SAL per ID"""
        try:
            return self.db.get_sal_by_id(sal_id)
        except Exception as e:
            logger.error(f"Errore recupero SAL: {e}")
            return None

    def create_sal_deadlines(self, sal: SAL, project: Project) -> List[Deadline]:
        """Crea automaticamente le scadenze per un SAL"""
        try:
            created_deadlines = []
            
            # Scadenza principale del SAL
            if sal.planned_end_date:
                main_deadline = Deadline(
                    id=uuid4(),
                    title=f"Scadenza {sal.name}",
                    description=f"Completamento {sal.name} - {sal.description or ''}",
                    due_date=sal.planned_end_date,
                    status=DeadlineStatus.PENDING,
                    priority=Priority.HIGH if sal.requires_approval else Priority.MEDIUM,
                    project_id=project.id,
                    client_id=project.client_id,
                    sal_id=sal.id,
                    alert_days_before=15,
                    email_notifications=True,
                    created_at=datetime.now()
                )
                
                created_deadline = self.db.create_deadline(main_deadline)
                created_deadlines.append(created_deadline)
                
                logger.info(f"Scadenza SAL creata: {main_deadline.title}")
            
            # Scadenze aggiuntive basate sul tipo di milestone
            if sal.milestone_type == SALMilestoneType.PROJECT_START and sal.planned_start_date:
                start_deadline = Deadline(
                    id=uuid4(),
                    title=f"Avvio {sal.name}",
                    description=f"Data di avvio prevista per {sal.name}",
                    due_date=sal.planned_start_date,
                    status=DeadlineStatus.PENDING,
                    priority=Priority.MEDIUM,
                    project_id=project.id,
                    client_id=project.client_id,
                    sal_id=sal.id,
                    alert_days_before=7,
                    email_notifications=True,
                    created_at=datetime.now()
                )
                
                created_deadline = self.db.create_deadline(start_deadline)
                created_deadlines.append(created_deadline)
            
            return created_deadlines
            
        except Exception as e:
            logger.error(f"Errore creazione scadenze SAL: {e}")
            return []
    
    def calculate_project_progress(self, project_id: UUID) -> Dict[str, Any]:
        """Calcola l'avanzamento complessivo del progetto basato sui SAL"""
        try:
            sals = self.db.get_sals_by_project(project_id)
            if not sals:
                return {
                    "total_progress": 0.0,
                    "completed_sals": 0,
                    "total_sals": 0,
                    "next_sal": None,
                    "status": "no_sals"
                }
            
            completed_sals = [sal for sal in sals if sal.status == ProjectStatus.COMPLETED]
            in_progress_sals = [sal for sal in sals if sal.status == ProjectStatus.IN_PROGRESS]
            
            # Calcola progresso totale basato sui SAL completati
            total_progress = sum(sal.percentage for sal in completed_sals)
            
            # Aggiungi progresso parziale dei SAL in corso (stimato al 50%)
            for sal in in_progress_sals:
                total_progress += sal.percentage * 0.5
            
            # Trova il prossimo SAL da completare
            next_sal = None
            pending_sals = [sal for sal in sals if sal.status in [ProjectStatus.DRAFT, ProjectStatus.IN_PROGRESS]]
            if pending_sals:
                next_sal = min(pending_sals, key=lambda s: s.sequence_number)
            
            return {
                "total_progress": min(total_progress, 100.0),
                "completed_sals": len(completed_sals),
                "total_sals": len(sals),
                "in_progress_sals": len(in_progress_sals),
                "next_sal": next_sal,
                "status": "active" if sals else "no_sals"
            }
            
        except Exception as e:
            logger.error(f"Errore calcolo progresso progetto: {e}")
            return {
                "total_progress": 0.0,
                "completed_sals": 0,
                "total_sals": 0,
                "next_sal": None,
                "status": "error"
            }
    
    def validate_sal_sequence(self, sal: SAL, project_id: UUID) -> Tuple[bool, str]:
        """Valida la sequenza e percentuale di un SAL"""
        try:
            existing_sals = self.db.get_sals_by_project(project_id)
            
            # Verifica sequenza duplicata
            for existing_sal in existing_sals:
                if existing_sal.id != sal.id and existing_sal.sequence_number == sal.sequence_number:
                    return False, f"Numero sequenza {sal.sequence_number} già utilizzato"
            
            # Verifica percentuale logica
            if sal.sequence_number > 1:
                previous_sals = [s for s in existing_sals if s.sequence_number < sal.sequence_number]
                if previous_sals:
                    max_previous_percentage = max(s.percentage for s in previous_sals)
                    if sal.percentage <= max_previous_percentage:
                        return False, f"La percentuale deve essere superiore a {max_previous_percentage}%"
            
            # Verifica percentuale finale
            if sal.percentage == 100.0:
                higher_sequence_sals = [s for s in existing_sals if s.sequence_number > sal.sequence_number]
                if higher_sequence_sals:
                    return False, "Non può esserci un SAL al 100% se ci sono SAL successivi"
            
            return True, "Validazione superata"
            
        except Exception as e:
            logger.error(f"Errore validazione SAL: {e}")
            return False, f"Errore validazione: {str(e)}"
    
    def get_sal_documents(self, sal_id: UUID) -> List[Document]:
        """Recupera tutti i documenti associati a un SAL"""
        try:
            return self.db.get_documents_by_sal(sal_id)
        except Exception as e:
            logger.error(f"Errore recupero documenti SAL: {e}")
            return []
    
    def get_sal_deadlines(self, sal_id: UUID) -> List[Deadline]:
        """Recupera tutte le scadenze associate a un SAL"""
        try:
            return self.db.get_deadlines_by_sal(sal_id)
        except Exception as e:
            logger.error(f"Errore recupero scadenze SAL: {e}")
            return []
