#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Servizio Statistiche per Agevolami PM
"""

import json
from datetime import datetime, date, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import asdict

from ..utils import get_logger
from ..database import DatabaseManagerExtended
from ..config import AppConfig
from .email_service import EmailService
from .email_templates import EmailTemplates
from ..models import DeadlineStatus, ProjectStatus, TaskStatus

logger = get_logger(__name__)

class StatisticsService:
    """Servizio per generare e inviare statistiche"""
    
    def __init__(self, db_manager: DatabaseManagerExtended, config: AppConfig):
        self.db = db_manager
        self.config = config
        self.email_service = EmailService(config)
    
    def generate_full_report(self) -> Dict[str, Any]:
        """Genera un report completo con tutte le statistiche"""
        try:
            logger.info("🔄 Inizio generazione report completo...")

            # Generate basic stats first
            summary = self._get_summary_stats()
            logger.info(f"[OK] Summary stats generato: {summary}")

            # Generate lightweight project and client stats
            try:
                projects_data = self._get_projects_stats()
                logger.info("[OK] Projects stats generato")
            except Exception as e:
                logger.error(f"[ERROR] Errore projects stats: {e}")
                projects_data = {'total': 0, 'active_projects': []}

            try:
                clients_data = self._get_clients_stats()
                logger.info("[OK] Clients stats generato")
            except Exception as e:
                logger.error(f"[ERROR] Errore clients stats: {e}")
                clients_data = {'total': 0, 'top_clients': []}

            # Generate comprehensive task statistics
            try:
                tasks_data = self._get_tasks_stats()
                logger.info("[OK] Tasks stats generato")
            except Exception as e:
                logger.error(f"[ERROR] Errore tasks stats: {e}")
                tasks_data = {'total': 0, 'expired': 0, 'expiring_soon': 0, 'completed': 0, 'pending': 0}
            
            # Generate deadlines data separately with error handling
            deadlines_data = []
            try:
                logger.info("[INFO] Inizio generazione deadlines...")
                deadlines = self.db.get_all_deadlines()
                logger.info(f"[INFO] Trovate {len(deadlines)} scadenze")
                
                today = date.today()
                for deadline in deadlines[:50]:  # Limit to 50 deadlines to prevent overload
                    try:
                        deadline_data = {
                            'id': str(deadline.id),
                            'title': deadline.title or 'N/A',
                            'description': (deadline.description or '')[:200],  # Limit description length
                            'due_date': deadline.due_date.isoformat(),
                            'status': str(deadline.status),
                            'priority': str(deadline.priority),
                            'days_remaining': (deadline.due_date - today).days,
                            'project': None,
                            'client': None
                        }
                        
                        # Add project info with error handling
                        if deadline.project_id:
                            try:
                                project = self.db.get_project(deadline.project_id)
                                if project:
                                    deadline_data['project'] = {
                                        'name': project.name or 'N/A',
                                        'reference_code': project.reference_code or 'N/A'
                                    }
                                    
                                    if project.client_id:
                                        client = self.db.get_client(project.client_id)
                                        if client:
                                            deadline_data['client'] = {
                                                'name': client.name or 'N/A',
                                                'company': client.business_name or 'N/A'
                                            }
                            except Exception as pe:
                                logger.warning(f"[WARNING] Errore caricamento progetto {deadline.project_id}: {pe}")
                        
                        deadlines_data.append(deadline_data)
                        
                    except Exception as de:
                        logger.warning(f"[WARNING] Errore elaborazione scadenza {deadline.id}: {de}")
                        continue
                
                logger.info(f"[OK] Elaborati {len(deadlines_data)} deadlines")
                
            except Exception as e:
                logger.error(f"[ERROR] Errore generazione deadlines: {e}")
                deadlines_data = []
            
            report = {
                'generated_at': datetime.now().isoformat(),
                'period': {
                    'start': date.today().replace(day=1).isoformat(),
                    'end': date.today().isoformat()
                },
                'summary': summary,
                'clients': clients_data,
                'projects': projects_data,
                'tasks': tasks_data,  # Add comprehensive task statistics
                'deadlines': deadlines_data,  # Use the limited deadlines list
                'alerts': {'total_active': 0}  # Simplified alerts to prevent issues
            }
            
            logger.info(f"[OK] Report completo generato con {len(deadlines_data)} scadenze")
            return report
            
        except Exception as e:
            logger.error(f"[ERROR] Errore critico generazione report: {e}")
            import traceback
            logger.error(f"[ERROR] Traceback: {traceback.format_exc()}")
            
            # Return minimal report in case of error
            return {
                'generated_at': datetime.now().isoformat(),
                'period': {
                    'start': date.today().replace(day=1).isoformat(),
                    'end': date.today().isoformat()
                },
                'summary': {'total_clients': 0, 'total_projects': 0, 'total_deadlines': 0, 'overdue_deadlines': 0, 'upcoming_deadlines': 0, 'active_projects': 0, 'total_tasks': 0, 'expired_tasks': 0, 'expiring_tasks': 0},
                'clients': {'total': 0, 'top_clients': []},
                'projects': {'total': 0, 'active_projects': []},
                'tasks': {'total': 0, 'expired': 0, 'expiring_soon': 0, 'completed': 0, 'pending': 0},
                'deadlines': [],
                'alerts': {'total_active': 0}
            }
    
    def generate_deadlines_report(self) -> Dict[str, Any]:
        """Genera un report specifico per le scadenze"""
        try:
            today = date.today()
            
            report = {
                'generated_at': datetime.now().isoformat(),
                'type': 'deadlines_only',
                'summary': {
                    'total_deadlines': 0,
                    'overdue': 0,
                    'due_today': 0,
                    'due_this_week': 0,
                    'due_this_month': 0,
                    'completed': 0
                },
                'deadlines': []
            }
            
            # Recupera tutte le scadenze
            deadlines = self.db.get_all_deadlines()
            report['summary']['total_deadlines'] = len(deadlines)
            
            for deadline in deadlines:
                deadline_data = {
                    'id': str(deadline.id),
                    'title': deadline.title,
                    'description': deadline.description,
                    'due_date': deadline.due_date.isoformat(),
                    'status': str(deadline.status),
                    'priority': str(deadline.priority),
                    'days_remaining': (deadline.due_date - today).days,
                    'project': None,
                    'client': None
                }
                
                # Aggiungi info progetto e cliente
                if deadline.project_id:
                    try:
                        project = self.db.get_project(deadline.project_id)
                        if project:
                            deadline_data['project'] = {
                                'name': project.name,
                                'reference_code': project.reference_code
                            }
                            
                            if project.client_id:
                                client = self.db.get_client(project.client_id)
                                if client:
                                    deadline_data['client'] = {
                                        'name': client.name,
                                        'company': client.business_name
                                    }
                    except Exception:
                        pass
                
                # Aggiorna contatori
                if deadline.status == DeadlineStatus.COMPLETED:
                    report['summary']['completed'] += 1
                else:
                    days_remaining = (deadline.due_date - today).days
                    
                    if days_remaining < 0:
                        report['summary']['overdue'] += 1
                    elif days_remaining == 0:
                        report['summary']['due_today'] += 1
                    elif days_remaining <= 7:
                        report['summary']['due_this_week'] += 1
                    elif days_remaining <= 30:
                        report['summary']['due_this_month'] += 1
                
                report['deadlines'].append(deadline_data)
            
            # Ordina per data di scadenza
            report['deadlines'].sort(key=lambda x: x['due_date'])
            
            logger.info(f"Report scadenze generato: {len(deadlines)} scadenze")
            return report
            
        except Exception as e:
            logger.error(f"Errore generazione report scadenze: {e}")
            return {}
    
    def generate_automatic_report(self) -> Dict[str, Any]:
        """Genera un report automatico completo con statistiche database e scadenze dettagliate"""
        try:
            logger.info("[INFO] Generazione report automatico completo...")

            today = date.today()
            summary = self._get_summary_stats()
            tasks_data = self._get_tasks_stats()
            clients_data = self._get_clients_stats()
            projects_data = self._get_projects_stats()
            deadlines_data = self._get_deadlines_stats()

            # Get detailed deadline information for next 15 days
            upcoming_deadlines = self._get_detailed_upcoming_deadlines(days_ahead=15)

            # Focus on comprehensive information for automatic reports
            report = {
                'generated_at': datetime.now().isoformat(),
                'type': 'automatic',
                'period': {
                    'start': today.isoformat(),
                    'end': (today + timedelta(days=15)).isoformat(),
                    'description': f"Report automatico dal {today.strftime('%d/%m/%Y')} al {(today + timedelta(days=15)).strftime('%d/%m/%Y')}"
                },
                'summary': {
                    'total_clients': summary.get('total_clients', 0),
                    'total_projects': summary.get('total_projects', 0),
                    'active_projects': summary.get('active_projects', 0),
                    'completed_projects': projects_data.get('by_status', {}).get('completed', 0),
                    'total_tasks': summary.get('total_tasks', 0),
                    'completed_tasks': tasks_data.get('completed', 0),
                    'pending_tasks': tasks_data.get('pending', 0),
                    'expired_tasks': summary.get('expired_tasks', 0),
                    'expiring_tasks': summary.get('expiring_tasks', 0),
                    'total_deadlines': summary.get('total_deadlines', 0),
                    'overdue_deadlines': summary.get('overdue_deadlines', 0),
                    'upcoming_deadlines': summary.get('upcoming_deadlines', 0)
                },
                'database_statistics': {
                    'clients': {
                        'total': clients_data.get('total', 0),
                        'top_clients': clients_data.get('top_clients', [])[:5]  # Top 5 clients
                    },
                    'projects': {
                        'total': projects_data.get('total', 0),
                        'by_status': projects_data.get('by_status', {}),
                        'by_type': projects_data.get('by_type', {}),
                        'recent_count': len(projects_data.get('recent_projects', []))
                    },
                    'tasks': {
                        'total': tasks_data.get('total', 0),
                        'completion_rate': tasks_data.get('completion_rate', 0),
                        'by_priority': tasks_data.get('by_priority', {}),
                        'status_breakdown': {
                            'completed': tasks_data.get('completed', 0),
                            'in_progress': tasks_data.get('in_progress', 0),
                            'pending': tasks_data.get('pending', 0)
                        }
                    },
                    'deadlines': {
                        'total': deadlines_data.get('total', 0),
                        'by_status': deadlines_data.get('by_status', {}),
                        'upcoming_count': len(deadlines_data.get('upcoming', [])),
                        'overdue_count': len(deadlines_data.get('overdue', []))
                    }
                },
                'upcoming_deadlines_detailed': upcoming_deadlines,
                'tasks': {
                    'expired_count': tasks_data.get('expired', 0),
                    'expiring_count': tasks_data.get('expiring_soon', 0),
                    'completion_rate': tasks_data.get('completion_rate', 0),
                    'expired_tasks': tasks_data.get('expired_tasks', [])[:15],  # Show more critical tasks
                    'expiring_tasks': tasks_data.get('expiring_tasks', [])[:15]  # Show more urgent tasks
                },
                'alerts': {
                    'critical_count': tasks_data.get('expired', 0) + summary.get('overdue_deadlines', 0),
                    'warning_count': tasks_data.get('expiring_soon', 0) + summary.get('upcoming_deadlines', 0),
                    'total_issues': tasks_data.get('expired', 0) + summary.get('overdue_deadlines', 0) + tasks_data.get('expiring_soon', 0) + summary.get('upcoming_deadlines', 0)
                },
                'performance_indicators': {
                    'project_completion_rate': self._calculate_project_completion_rate(projects_data),
                    'deadline_adherence_rate': self._calculate_deadline_adherence_rate(deadlines_data),
                    'task_efficiency_score': self._calculate_task_efficiency_score(tasks_data),
                    'client_activity_level': self._calculate_client_activity_level(clients_data, projects_data)
                }
            }

            logger.info(f"[OK] Report automatico completo generato - {report['tasks']['expired_count']} task scadute, {report['tasks']['expiring_count']} in scadenza, {len(upcoming_deadlines)} scadenze dettagliate")
            return report

        except Exception as e:
            logger.error(f"[ERROR] Errore generazione report automatico: {e}")
            return {}

    def generate_custom_report(self, filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """Genera un report personalizzato con filtri specifici"""
        try:
            logger.info("[INFO] Generazione report personalizzato...")

            if not filters:
                filters = {}

            # Get all data first
            summary = self._get_summary_stats()
            clients_data = self._get_clients_stats()
            projects_data = self._get_projects_stats()
            tasks_data = self._get_tasks_stats()
            deadlines_data = self._get_deadlines_stats()

            # Apply filters
            include_clients = filters.get('include_clients', True)
            include_projects = filters.get('include_projects', True)
            include_tasks = filters.get('include_tasks', True)
            include_deadlines = filters.get('include_deadlines', True)
            include_expired_only = filters.get('include_expired_only', False)
            days_ahead = filters.get('days_ahead', 30)

            report = {
                'generated_at': datetime.now().isoformat(),
                'type': 'custom',
                'filters_applied': filters,
                'period': {
                    'start': date.today().isoformat(),
                    'end': (date.today() + timedelta(days=days_ahead)).isoformat()
                },
                'summary': summary
            }

            if include_clients:
                report['clients'] = clients_data

            if include_projects:
                report['projects'] = projects_data

            if include_tasks:
                if include_expired_only:
                    # Only include expired and expiring tasks
                    report['tasks'] = {
                        'expired': tasks_data.get('expired', 0),
                        'expiring_soon': tasks_data.get('expiring_soon', 0),
                        'expired_tasks': tasks_data.get('expired_tasks', []),
                        'expiring_tasks': tasks_data.get('expiring_tasks', [])
                    }
                else:
                    report['tasks'] = tasks_data

                # Add detailed task information for custom reports
                report['tasks_detailed'] = {
                    'expired_count': tasks_data.get('expired', 0),
                    'expiring_count': tasks_data.get('expiring_soon', 0),
                    'completion_rate': tasks_data.get('completion_rate', 0),
                    'expired_tasks': tasks_data.get('expired_tasks', [])[:10],  # Show up to 10 expired tasks
                    'expiring_tasks': tasks_data.get('expiring_tasks', [])[:10]  # Show up to 10 expiring tasks
                }

            if include_deadlines:
                report['deadlines'] = deadlines_data

                # Add detailed upcoming deadlines for custom reports
                upcoming_deadlines = self._get_detailed_upcoming_deadlines(days_ahead)
                report['upcoming_deadlines_detailed'] = upcoming_deadlines

                logger.info(f"[INFO] Incluse {len(upcoming_deadlines)} scadenze dettagliate per i prossimi {days_ahead} giorni")

            logger.info(f"[OK] Report personalizzato generato con filtri: {filters}")
            return report

        except Exception as e:
            logger.error(f"[ERROR] Errore generazione report personalizzato: {e}")
            return {}

    def send_statistics_email(self, recipients: List[str], report_type: str = 'full') -> bool:
        """Invia le statistiche via email"""
        try:
            # Genera il report
            if report_type == 'deadlines':
                report = self.generate_deadlines_report()
                subject = f"Report Scadenze - {date.today().strftime('%d/%m/%Y')}"
            elif report_type == 'automatic':
                report = self.generate_automatic_report()
                subject = f"Report Automatico Agevolami PM - {date.today().strftime('%d/%m/%Y')}"
            elif report_type == 'custom':
                report = self.generate_custom_report()
                subject = f"Report Personalizzato Agevolami PM - {date.today().strftime('%d/%m/%Y')}"
            else:
                report = self.generate_full_report()
                subject = f"Report Statistiche Agevolami PM - {date.today().strftime('%d/%m/%Y')}"

            if not report:
                logger.error("Impossibile generare il report")
                return False
            
            # Genera email HTML
            html_body = self._generate_email_html(report, report_type)
            
            # Invia a tutti i destinatari
            success_count = 0
            for recipient in recipients:
                try:
                    success = self.email_service.send_email(
                        to_email=recipient,
                        subject=subject,
                        body=self._generate_email_text(report, report_type),
                        html_body=html_body
                    )
                    
                    if success:
                        success_count += 1
                        logger.info(f"Report inviato a: {recipient}")
                    else:
                        logger.error(f"Errore invio report a: {recipient}")
                        
                except Exception as e:
                    logger.error(f"Errore invio email a {recipient}: {e}")
            
            return success_count > 0
            
        except Exception as e:
            logger.error(f"Errore invio statistiche email: {e}")
            return False

    def send_custom_report_email(self, recipients: List[str], filters: Dict[str, Any] = None) -> bool:
        """Invia un report personalizzato via email con filtri specifici"""
        try:
            logger.info(f"🔄 Invio report personalizzato con filtri: {filters}")

            # Genera il report personalizzato
            report = self.generate_custom_report(filters)

            if not report:
                logger.error("Impossibile generare il report personalizzato")
                return False

            # Crea subject personalizzato basato sui filtri
            subject_parts = ["Report Personalizzato Agevolami PM"]
            if filters:
                if filters.get('include_expired_only'):
                    subject_parts.append("- Task Scadute")
                if filters.get('days_ahead'):
                    subject_parts.append(f"- {filters['days_ahead']} giorni")

            subject = f"{' '.join(subject_parts)} - {date.today().strftime('%d/%m/%Y')}"

            # Genera email HTML
            html_body = self._generate_email_html(report, 'custom')

            # Invia a tutti i destinatari
            success_count = 0
            for recipient in recipients:
                try:
                    success = self.email_service.send_email(
                        to_email=recipient,
                        subject=subject,
                        body=self._generate_email_text(report, 'custom'),
                        html_body=html_body
                    )

                    if success:
                        success_count += 1
                        logger.info(f"Report personalizzato inviato a: {recipient}")
                    else:
                        logger.error(f"Errore invio report personalizzato a: {recipient}")

                except Exception as e:
                    logger.error(f"Errore invio email personalizzata a {recipient}: {e}")

            return success_count > 0

        except Exception as e:
            logger.error(f"Errore invio report personalizzato: {e}")
            return False

    def _generate_fallback_html(self, report: Dict[str, Any], report_type: str) -> str:
        """Genera un HTML di fallback semplice in caso di errore"""
        summary = report.get('summary', {})
        generated_date = datetime.fromisoformat(report.get('generated_at', datetime.now().isoformat()))
        date_str = generated_date.strftime('%d/%m/%Y alle %H:%M')

        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Report Agevolami PM</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background: #667eea; color: white; padding: 20px; text-align: center; }}
                .content {{ padding: 20px; }}
                .stat {{ margin: 10px 0; padding: 10px; background: #f8f9fa; border-left: 4px solid #667eea; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>📊 Report Agevolami PM</h1>
                <p>{date_str}</p>
            </div>
            <div class="content">
                <h2>📈 Statistiche Generali</h2>
                <div class="stat">👥 Clienti: {summary.get('total_clients', 0)}</div>
                <div class="stat">📁 Progetti: {summary.get('total_projects', 0)}</div>
                <div class="stat">📋 Task: {summary.get('total_tasks', 0)}</div>
                <div class="stat">🚨 Task Scadute: {summary.get('expired_tasks', 0)}</div>
                <div class="stat">⏰ Task in Scadenza: {summary.get('expiring_tasks', 0)}</div>
                <div class="stat">📅 Scadenze: {summary.get('total_deadlines', 0)}</div>
                <div class="stat">🚨 Scadenze Superate: {summary.get('overdue_deadlines', 0)}</div>
            </div>
        </body>
        </html>
        """
    
    def _get_summary_stats(self) -> Dict[str, int]:
        """Ottiene statistiche di riepilogo"""
        try:
            today = date.today()
            
            # Clienti
            clients = self.db.get_all_clients()
            total_clients = len(clients)
            
            # Progetti
            projects = self.db.get_all_projects()
            total_projects = len(projects)
            active_projects = len([p for p in projects if p.status == ProjectStatus.IN_PROGRESS])
            
            # Scadenze
            deadlines = self.db.get_all_deadlines()
            total_deadlines = len(deadlines)
            overdue_deadlines = len([d for d in deadlines if d.due_date < today and d.status != DeadlineStatus.COMPLETED])
            upcoming_deadlines = len([d for d in deadlines if today <= d.due_date <= today + timedelta(days=7) and d.status != DeadlineStatus.COMPLETED])

            # Tasks - handle datetime/date conversion
            tasks = self.db.get_all_tasks()
            total_tasks = len(tasks)

            expired_tasks = 0
            expiring_tasks = 0

            for task in tasks:
                if task.due_date and task.status != TaskStatus.COMPLETED:
                    # Convert datetime to date for comparison if needed
                    task_due_date = task.due_date.date() if isinstance(task.due_date, datetime) else task.due_date

                    if task_due_date < today:
                        expired_tasks += 1
                    elif today <= task_due_date <= today + timedelta(days=15):
                        expiring_tasks += 1

            return {
                'total_clients': total_clients,
                'total_projects': total_projects,
                'active_projects': active_projects,
                'total_deadlines': total_deadlines,
                'overdue_deadlines': overdue_deadlines,
                'upcoming_deadlines': upcoming_deadlines,
                'total_tasks': total_tasks,
                'expired_tasks': expired_tasks,
                'expiring_tasks': expiring_tasks
            }
            
        except Exception as e:
            logger.error(f"Errore calcolo statistiche riepilogo: {e}")
            return {}
    
    def _get_clients_stats(self) -> Dict[str, Any]:
        """Ottiene statistiche sui clienti"""
        try:
            clients = self.db.get_all_clients()
            
            stats = {
                'total': len(clients),
                'by_type': {},
                'top_clients': []
            }
            
            # Statistiche per cliente
            for client in clients:
                projects = [p for p in self.db.get_all_projects() if p.client_id == client.id]
                
                client_data = {
                    'name': client.name,
                    'company': client.business_name,
                    'projects_count': len(projects),
                    'active_projects': len([p for p in projects if p.status == ProjectStatus.IN_PROGRESS])
                }
                
                stats['top_clients'].append(client_data)
            
            # Ordina per numero di progetti
            stats['top_clients'].sort(key=lambda x: x['projects_count'], reverse=True)
            stats['top_clients'] = stats['top_clients'][:10]  # Top 10
            
            return stats
            
        except Exception as e:
            logger.error(f"Errore calcolo statistiche clienti: {e}")
            return {}
    
    def _get_projects_stats(self) -> Dict[str, Any]:
        """Ottiene statistiche sui progetti"""
        try:
            projects = self.db.get_all_projects()
            
            stats = {
                'total': len(projects),
                'by_status': {},
                'by_type': {},
                'recent_projects': []
            }
            
            # Raggruppa per status
            for status in ProjectStatus:
                count = len([p for p in projects if p.status == status])
                stats['by_status'][status.value] = count
            
            # Raggruppa per tipo
            type_counts = {}
            for project in projects:
                # Safe enum handling
                if hasattr(project.project_type, 'value'):
                    project_type = project.project_type.value
                else:
                    project_type = str(project.project_type) if project.project_type else 'unknown'
                type_counts[project_type] = type_counts.get(project_type, 0) + 1
            
            stats['by_type'] = type_counts
            
            # Progetti recenti (ultimi 30 giorni)
            recent_date = date.today() - timedelta(days=30)
            recent_projects = [p for p in projects if p.created_at.date() >= recent_date]
            
            for project in recent_projects:
                # Safe enum handling for status
                status_value = project.status.value if hasattr(project.status, 'value') else str(project.status)
                stats['recent_projects'].append({
                    'name': project.name,
                    'reference_code': project.reference_code,
                    'status': status_value,
                    'created_at': project.created_at.isoformat()
                })
            
            return stats

        except Exception as e:
            logger.error(f"Errore calcolo statistiche progetti: {e}")
            return {}

    def _get_tasks_stats(self) -> Dict[str, Any]:
        """Ottiene statistiche complete sui task"""
        try:
            today = date.today()
            tasks = self.db.get_all_tasks()

            stats = {
                'total': len(tasks),
                'completed': 0,
                'pending': 0,
                'in_progress': 0,
                'expired': 0,
                'expiring_soon': 0,
                'by_priority': {
                    'critica': 0,
                    'alta': 0,
                    'media': 0,
                    'bassa': 0
                },
                'expired_tasks': [],
                'expiring_tasks': [],
                'completion_rate': 0.0
            }

            for task in tasks:
                # Count by status
                if task.status == TaskStatus.COMPLETED:
                    stats['completed'] += 1
                elif task.status == TaskStatus.IN_PROGRESS:
                    stats['in_progress'] += 1
                else:
                    stats['pending'] += 1

                # Count by priority
                priority_value = task.priority.value if hasattr(task.priority, 'value') else str(task.priority)
                if priority_value in stats['by_priority']:
                    stats['by_priority'][priority_value] += 1

                # Check for expired/expiring tasks - handle datetime/date conversion
                if task.due_date and task.status != TaskStatus.COMPLETED:
                    # Convert datetime to date for comparison if needed
                    task_due_date = task.due_date.date() if isinstance(task.due_date, datetime) else task.due_date

                    if task_due_date < today:
                        stats['expired'] += 1
                        # Get project and deadline info for context
                        project = self.db.get_project(task.project_id) if task.project_id else None
                        deadline = self.db.get_deadline(task.deadline_id) if task.deadline_id else None

                        stats['expired_tasks'].append({
                            'title': task.title,
                            'due_date': task.due_date.isoformat() if isinstance(task.due_date, datetime) else task.due_date.isoformat(),
                            'days_overdue': (today - task_due_date).days,
                            'priority': priority_value,
                            'project_name': project.name if project else 'N/A',
                            'deadline_title': deadline.title if deadline else 'N/A'
                        })
                    elif task_due_date <= today + timedelta(days=15):
                        stats['expiring_soon'] += 1
                        project = self.db.get_project(task.project_id) if task.project_id else None
                        deadline = self.db.get_deadline(task.deadline_id) if task.deadline_id else None

                        stats['expiring_tasks'].append({
                            'title': task.title,
                            'due_date': task.due_date.isoformat() if isinstance(task.due_date, datetime) else task.due_date.isoformat(),
                            'days_remaining': (task_due_date - today).days,
                            'priority': priority_value,
                            'project_name': project.name if project else 'N/A',
                            'deadline_title': deadline.title if deadline else 'N/A'
                        })

            # Calculate completion rate
            if stats['total'] > 0:
                stats['completion_rate'] = round((stats['completed'] / stats['total']) * 100, 1)

            # Sort expired and expiring tasks by date
            stats['expired_tasks'].sort(key=lambda x: x['due_date'])
            stats['expiring_tasks'].sort(key=lambda x: x['due_date'])

            return stats

        except Exception as e:
            logger.error(f"Errore calcolo statistiche task: {e}")
            return {
                'total': 0,
                'completed': 0,
                'pending': 0,
                'in_progress': 0,
                'expired': 0,
                'expiring_soon': 0,
                'by_priority': {'critica': 0, 'alta': 0, 'media': 0, 'bassa': 0},
                'expired_tasks': [],
                'expiring_tasks': [],
                'completion_rate': 0.0
            }
    
    def _get_deadlines_stats(self) -> Dict[str, Any]:
        """Ottiene statistiche sulle scadenze"""
        try:
            deadlines = self.db.get_all_deadlines()
            today = date.today()
            
            stats = {
                'total': len(deadlines),
                'by_status': {},
                'by_priority': {},
                'upcoming': [],
                'overdue': []
            }
            
            # Raggruppa per status
            for status in DeadlineStatus:
                count = len([d for d in deadlines if d.status == status])
                stats['by_status'][status.value] = count
            
            # Scadenze imminenti (prossimi 7 giorni)
            upcoming_date = today + timedelta(days=7)
            upcoming = [d for d in deadlines if today <= d.due_date <= upcoming_date and d.status != DeadlineStatus.COMPLETED]
            
            for deadline in upcoming:
                # Safe enum handling for priority
                priority_value = deadline.priority.value if hasattr(deadline.priority, 'value') else str(deadline.priority)
                stats['upcoming'].append({
                    'title': deadline.title,
                    'due_date': deadline.due_date.isoformat(),
                    'days_remaining': (deadline.due_date - today).days,
                    'priority': priority_value
                })
            
            # Scadenze scadute
            overdue = [d for d in deadlines if d.due_date < today and d.status != DeadlineStatus.COMPLETED]
            
            for deadline in overdue:
                # Safe enum handling for priority
                priority_value = deadline.priority.value if hasattr(deadline.priority, 'value') else str(deadline.priority)
                stats['overdue'].append({
                    'title': deadline.title,
                    'due_date': deadline.due_date.isoformat(),
                    'days_overdue': (today - deadline.due_date).days,
                    'priority': priority_value
                })
            
            return stats

        except Exception as e:
            logger.error(f"Errore calcolo statistiche scadenze: {e}")
            return {}

    def _get_detailed_upcoming_deadlines(self, days_ahead: int = 15) -> List[Dict[str, Any]]:
        """Ottiene scadenze dettagliate per i prossimi giorni specificati"""
        try:
            today = date.today()
            end_date = today + timedelta(days=days_ahead)
            deadlines = self.db.get_all_deadlines()

            upcoming = []
            for deadline in deadlines:
                if (deadline.due_date >= today and
                    deadline.due_date <= end_date and
                    deadline.status != DeadlineStatus.COMPLETED):

                    # Get related project and client info
                    project = self.db.get_project(deadline.project_id) if deadline.project_id else None
                    client = self.db.get_client(project.client_id) if project and project.client_id else None

                    # Get related tasks
                    related_tasks = [t for t in self.db.get_all_tasks() if t.deadline_id == deadline.id]

                    deadline_data = {
                        'id': deadline.id,
                        'title': deadline.title,
                        'description': deadline.description or '',
                        'due_date': deadline.due_date.isoformat(),
                        'days_remaining': (deadline.due_date - today).days,
                        'priority': deadline.priority.value if hasattr(deadline.priority, 'value') else str(deadline.priority),
                        'status': deadline.status.value if hasattr(deadline.status, 'value') else str(deadline.status),
                        'project_info': {
                            'name': project.name if project else 'N/A',
                            'reference_code': project.reference_code if project else 'N/A',
                            'status': project.status.value if project and hasattr(project.status, 'value') else 'N/A'
                        },
                        'client_info': {
                            'name': client.name if client else 'N/A',
                            'business_name': client.business_name if client else 'N/A'
                        },
                        'related_tasks': [
                            {
                                'title': task.title,
                                'status': task.status.value if hasattr(task.status, 'value') else str(task.status),
                                'due_date': task.due_date.isoformat() if task.due_date else None
                            }
                            for task in related_tasks[:5]  # Limit to 5 most relevant tasks
                        ],
                        'urgency_level': self._calculate_deadline_urgency(deadline, today)
                    }

                    upcoming.append(deadline_data)

            # Sort by urgency and due date
            upcoming.sort(key=lambda x: (x['urgency_level'], x['due_date']))
            return upcoming

        except Exception as e:
            logger.error(f"Errore recupero scadenze dettagliate: {e}")
            return []

    def _calculate_deadline_urgency(self, deadline, today: date) -> int:
        """Calcola il livello di urgenza di una scadenza (0=massima urgenza, 3=minima)"""
        if not deadline.due_date:
            return 3

        days_remaining = (deadline.due_date - today).days
        priority = deadline.priority.value if hasattr(deadline.priority, 'value') else str(deadline.priority)

        # Base urgency on days remaining
        if days_remaining <= 1:
            urgency = 0  # Critical
        elif days_remaining <= 3:
            urgency = 1  # High
        elif days_remaining <= 7:
            urgency = 2  # Medium
        else:
            urgency = 3  # Low

        # Adjust based on priority
        if priority in ['critica', 'critical', 'alta', 'high']:
            urgency = max(0, urgency - 1)
        elif priority in ['bassa', 'low']:
            urgency = min(3, urgency + 1)

        return urgency

    def _calculate_project_completion_rate(self, projects_data: Dict[str, Any]) -> float:
        """Calcola il tasso di completamento progetti"""
        try:
            total = projects_data.get('total', 0)
            if total == 0:
                return 0.0

            completed = projects_data.get('by_status', {}).get('completed', 0)
            return round((completed / total) * 100, 1)
        except:
            return 0.0

    def _calculate_deadline_adherence_rate(self, deadlines_data: Dict[str, Any]) -> float:
        """Calcola il tasso di rispetto delle scadenze"""
        try:
            total = deadlines_data.get('total', 0)
            if total == 0:
                return 100.0

            overdue = len(deadlines_data.get('overdue', []))
            adherence = ((total - overdue) / total) * 100
            return round(adherence, 1)
        except:
            return 100.0

    def _calculate_task_efficiency_score(self, tasks_data: Dict[str, Any]) -> float:
        """Calcola un punteggio di efficienza dei task"""
        try:
            total = tasks_data.get('total', 0)
            if total == 0:
                return 100.0

            completed = tasks_data.get('completed', 0)
            expired = tasks_data.get('expired', 0)

            # Efficiency = (completed - expired) / total * 100
            efficiency = ((completed - expired) / total) * 100
            return round(max(0, efficiency), 1)
        except:
            return 100.0

    def _calculate_client_activity_level(self, clients_data: Dict[str, Any], projects_data: Dict[str, Any]) -> str:
        """Calcola il livello di attività dei clienti"""
        try:
            total_clients = clients_data.get('total', 0)
            active_projects = projects_data.get('by_status', {}).get('in_progress', 0)

            if total_clients == 0:
                return "Nessun cliente"

            activity_ratio = active_projects / total_clients

            if activity_ratio >= 2:
                return "Molto Alta"
            elif activity_ratio >= 1:
                return "Alta"
            elif activity_ratio >= 0.5:
                return "Media"
            else:
                return "Bassa"
        except:
            return "Non disponibile"
    
    def _get_alerts_stats(self) -> Dict[str, Any]:
        """Ottiene statistiche sugli alert"""
        try:
            # Usa il servizio alert esistente
            from core.services.alert_service import AlertService
            alert_service = AlertService(self.db, self.config)
            
            dashboard_alerts = alert_service.get_dashboard_alerts()
            
            stats = {
                'total_active': len(dashboard_alerts),
                'by_urgency': {
                    'critical': 0,
                    'high': 0,
                    'medium': 0,
                    'low': 0
                }
            }
            
            # Classifica per urgenza basata sui giorni rimanenti
            for alert_data in dashboard_alerts:
                days_remaining = alert_data.get('days_remaining', 0)
                
                if days_remaining < 0:
                    stats['by_urgency']['critical'] += 1
                elif days_remaining <= 1:
                    stats['by_urgency']['high'] += 1
                elif days_remaining <= 7:
                    stats['by_urgency']['medium'] += 1
                else:
                    stats['by_urgency']['low'] += 1
            
            return stats
            
        except Exception as e:
            logger.error(f"Errore calcolo statistiche alert: {e}")
            return {}
    
    def _generate_email_html(self, report: Dict[str, Any], report_type: str) -> str:
        """Genera il corpo HTML dell'email usando i nuovi template"""
        try:
            if report_type == 'automatic':
                return EmailTemplates.generate_automatic_report_html(report)
            elif report_type == 'custom':
                return EmailTemplates.generate_custom_report_html(report)
            elif report_type == 'deadlines':
                return self._generate_deadlines_html(report)  # Keep existing for deadlines
            else:
                return EmailTemplates.generate_full_report_html(report)
        except Exception as e:
            logger.error(f"Errore generazione HTML template: {e}")
            return self._generate_fallback_html(report, report_type)
    
    def _generate_email_text(self, report: Dict[str, Any], report_type: str) -> str:
        """Genera il corpo testuale dell'email"""
        if report_type == 'deadlines':
            return self._generate_deadlines_text(report)
        elif report_type == 'automatic':
            return self._generate_automatic_report_text(report)
        elif report_type == 'custom':
            return self._generate_custom_report_text(report)
        else:
            return self._generate_full_report_text(report)
    
    def _generate_deadlines_html(self, report: Dict[str, Any]) -> str:
        """Genera HTML per il report scadenze"""
        summary = report.get('summary', {})
        deadlines = report.get('deadlines', [])
        
        # Filtra scadenze per urgenza
        overdue = [d for d in deadlines if d['days_remaining'] < 0 and d['status'] != 'completed']
        due_today = [d for d in deadlines if d['days_remaining'] == 0 and d['status'] != 'completed']
        due_soon = [d for d in deadlines if 0 < d['days_remaining'] <= 7 and d['status'] != 'completed']
        
        html = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Report Scadenze - Agevolami PM</title>
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 800px; margin: 0 auto; padding: 20px; }}
        .header {{ background: #1565C0; color: white; padding: 20px; border-radius: 10px; text-align: center; margin-bottom: 20px; }}
        .summary {{ display: flex; flex-wrap: wrap; gap: 15px; margin-bottom: 30px; }}
        .stat-card {{ background: #f8f9fa; padding: 15px; border-radius: 8px; flex: 1; min-width: 150px; text-align: center; }}
        .stat-number {{ font-size: 24px; font-weight: bold; color: #1565C0; }}
        .stat-label {{ font-size: 12px; color: #666; }}
        .section {{ margin-bottom: 30px; }}
        .section-title {{ font-size: 18px; font-weight: bold; color: #1565C0; margin-bottom: 15px; border-bottom: 2px solid #1565C0; padding-bottom: 5px; }}
        .deadline-item {{ background: white; border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin-bottom: 10px; }}
        .deadline-title {{ font-weight: bold; color: #333; }}
        .deadline-meta {{ font-size: 12px; color: #666; margin-top: 5px; }}
        .priority-high {{ border-left: 4px solid #f44336; }}
        .priority-medium {{ border-left: 4px solid #ff9800; }}
        .priority-low {{ border-left: 4px solid #4caf50; }}
        .overdue {{ background-color: #ffebee; border-color: #f44336; }}
        .due-today {{ background-color: #fff3e0; border-color: #ff9800; }}
        .footer {{ text-align: center; margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px; font-size: 12px; color: #666; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>📅 Report Scadenze</h1>
        <p>Agevolami PM - {datetime.now().strftime('%d/%m/%Y alle %H:%M')}</p>
    </div>
    
    <div class="summary">
        <div class="stat-card">
            <div class="stat-number">{summary.get('total_deadlines', 0)}</div>
            <div class="stat-label">Totali</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" style="color: #f44336;">{summary.get('overdue', 0)}</div>
            <div class="stat-label">Scadute</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" style="color: #ff9800;">{summary.get('due_today', 0)}</div>
            <div class="stat-label">Oggi</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" style="color: #2196f3;">{summary.get('due_this_week', 0)}</div>
            <div class="stat-label">Questa Settimana</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" style="color: #4caf50;">{summary.get('completed', 0)}</div>
            <div class="stat-label">Completate</div>
        </div>
    </div>
"""
        
        # Sezione scadenze scadute
        if overdue:
            html += f"""
    <div class="section">
        <div class="section-title">🚨 Scadenze Scadute ({len(overdue)})</div>
"""
            for deadline in overdue[:10]:  # Limita a 10
                project_info = ""
                if deadline['project']:
                    project_info = f" - {deadline['project']['name']}"
                if deadline['client']:
                    project_info += f" ({deadline['client']['company']})"
                
                html += f"""
        <div class="deadline-item overdue priority-{deadline['priority']}">
            <div class="deadline-title">{deadline['title']}{project_info}</div>
            <div class="deadline-meta">
                📅 Scadenza: {datetime.fromisoformat(deadline['due_date']).strftime('%d/%m/%Y')} 
                ⏰ Scaduta da {abs(deadline['days_remaining'])} giorni
                🔥 Priorità: {deadline['priority'].title()}
            </div>
        </div>
"""
            html += "    </div>\n"
        
        # Sezione scadenze di oggi
        if due_today:
            html += f"""
    <div class="section">
        <div class="section-title">⚠️ Scadenze di Oggi ({len(due_today)})</div>
"""
            for deadline in due_today:
                project_info = ""
                if deadline['project']:
                    project_info = f" - {deadline['project']['name']}"
                if deadline['client']:
                    project_info += f" ({deadline['client']['company']})"
                
                html += f"""
        <div class="deadline-item due-today priority-{deadline['priority']}">
            <div class="deadline-title">{deadline['title']}{project_info}</div>
            <div class="deadline-meta">
                📅 Scadenza: OGGI
                🔥 Priorità: {deadline['priority'].title()}
            </div>
        </div>
"""
            html += "    </div>\n"
        
        # Sezione scadenze prossime
        if due_soon:
            html += f"""
    <div class="section">
        <div class="section-title">📋 Scadenze Prossime (7 giorni) ({len(due_soon)})</div>
"""
            for deadline in due_soon[:15]:  # Limita a 15
                project_info = ""
                if deadline['project']:
                    project_info = f" - {deadline['project']['name']}"
                if deadline['client']:
                    project_info += f" ({deadline['client']['company']})"
                
                html += f"""
        <div class="deadline-item priority-{deadline['priority']}">
            <div class="deadline-title">{deadline['title']}{project_info}</div>
            <div class="deadline-meta">
                📅 Scadenza: {datetime.fromisoformat(deadline['due_date']).strftime('%d/%m/%Y')} 
                ⏰ Tra {deadline['days_remaining']} giorni
                🔥 Priorità: {deadline['priority'].title()}
            </div>
        </div>
"""
            html += "    </div>\n"
        
        html += f"""
    <div class="footer">
        <p>Report generato automaticamente da <strong>Agevolami PM</strong></p>
        <p>Per maggiori dettagli, accedi all'applicazione</p>
    </div>
</body>
</html>
"""
        
        return html
    
    def _generate_deadlines_text(self, report: Dict[str, Any]) -> str:
        """Genera testo per il report scadenze"""
        summary = report.get('summary', {})
        deadlines = report.get('deadlines', [])
        
        text = f"""
REPORT SCADENZE - AGEVOLAMI PM
Generato il: {datetime.now().strftime('%d/%m/%Y alle %H:%M')}

=== RIEPILOGO ===
Totale scadenze: {summary.get('total_deadlines', 0)}
Scadenze scadute: {summary.get('overdue', 0)}
Scadenze di oggi: {summary.get('due_today', 0)}
Scadenze questa settimana: {summary.get('due_this_week', 0)}
Scadenze completate: {summary.get('completed', 0)}

"""
        
        # Scadenze scadute
        overdue = [d for d in deadlines if d['days_remaining'] < 0 and d['status'] != 'completed']
        if overdue:
            text += f"\n=== SCADENZE SCADUTE ({len(overdue)}) ===\n"
            for deadline in overdue[:10]:
                project_info = ""
                if deadline['project']:
                    project_info = f" - {deadline['project']['name']}"
                if deadline['client']:
                    project_info += f" ({deadline['client']['company']})"
                
                text += f"• {deadline['title']}{project_info}\n"
                text += f"  Scadenza: {datetime.fromisoformat(deadline['due_date']).strftime('%d/%m/%Y')} (scaduta da {abs(deadline['days_remaining'])} giorni)\n"
                text += f"  Priorità: {deadline['priority'].title()}\n\n"
        
        # Scadenze di oggi
        due_today = [d for d in deadlines if d['days_remaining'] == 0 and d['status'] != 'completed']
        if due_today:
            text += f"\n=== SCADENZE DI OGGI ({len(due_today)}) ===\n"
            for deadline in due_today:
                project_info = ""
                if deadline['project']:
                    project_info = f" - {deadline['project']['name']}"
                if deadline['client']:
                    project_info += f" ({deadline['client']['company']})"
                
                text += f"• {deadline['title']}{project_info}\n"
                text += f"  Priorità: {deadline['priority'].title()}\n\n"
        
        # Scadenze prossime
        due_soon = [d for d in deadlines if 0 < d['days_remaining'] <= 7 and d['status'] != 'completed']
        if due_soon:
            text += f"\n=== SCADENZE PROSSIME (7 GIORNI) ({len(due_soon)}) ===\n"
            for deadline in due_soon[:15]:
                project_info = ""
                if deadline['project']:
                    project_info = f" - {deadline['project']['name']}"
                if deadline['client']:
                    project_info += f" ({deadline['client']['company']})"
                
                text += f"• {deadline['title']}{project_info}\n"
                text += f"  Scadenza: {datetime.fromisoformat(deadline['due_date']).strftime('%d/%m/%Y')} (tra {deadline['days_remaining']} giorni)\n"
                text += f"  Priorità: {deadline['priority'].title()}\n\n"
        
        text += "\n---\nReport generato automaticamente da Agevolami PM\n"
        
        return text
    
    def _generate_full_report_html(self, report: Dict[str, Any]) -> str:
        """Genera HTML per il report completo"""
        summary = report.get('summary', {})
        deadlines_data = report.get('deadlines', {})
        projects_data = report.get('projects', {})
        clients_data = report.get('clients', {})
        
        # Get actual deadline list if available
        deadlines_list = []
        if isinstance(deadlines_data, dict) and 'list' in deadlines_data:
            deadlines_list = deadlines_data['list']
        elif isinstance(deadlines_data, list):
            deadlines_list = deadlines_data
        
        # Filter deadlines by priority
        overdue = [d for d in deadlines_list if d.get('days_remaining', 0) < 0 and d.get('status') != 'completed']
        due_today = [d for d in deadlines_list if d.get('days_remaining', 0) == 0 and d.get('status') != 'completed']
        due_soon = [d for d in deadlines_list if 0 < d.get('days_remaining', 0) <= 7 and d.get('status') != 'completed']
        
        html = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Report Statistiche - Agevolami PM</title>
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 800px; margin: 0 auto; padding: 20px; }}
        .header {{ background: #1565C0; color: white; padding: 20px; border-radius: 10px; text-align: center; margin-bottom: 20px; }}
        .summary {{ display: flex; flex-wrap: wrap; gap: 15px; margin-bottom: 30px; }}
        .stat-card {{ background: #f8f9fa; padding: 15px; border-radius: 8px; flex: 1; min-width: 150px; text-align: center; }}
        .stat-number {{ font-size: 24px; font-weight: bold; color: #1565C0; }}
        .stat-label {{ font-size: 12px; color: #666; }}
        .section {{ margin-bottom: 30px; }}
        .section-title {{ font-size: 18px; font-weight: bold; color: #1565C0; margin-bottom: 15px; border-bottom: 2px solid #1565C0; padding-bottom: 5px; }}
        .deadline-item {{ background: white; border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin-bottom: 10px; }}
        .deadline-title {{ font-weight: bold; color: #333; }}
        .deadline-meta {{ font-size: 12px; color: #666; margin-top: 5px; }}
        .deadline-description {{ font-size: 13px; color: #555; margin-top: 8px; font-style: italic; }}
        .priority-high {{ border-left: 4px solid #f44336; }}
        .priority-medium {{ border-left: 4px solid #ff9800; }}
        .priority-low {{ border-left: 4px solid #4caf50; }}
        .overdue {{ background-color: #ffebee; border-color: #f44336; }}
        .due-today {{ background-color: #fff3e0; border-color: #ff9800; }}
        .project-section {{ background: #f0f8ff; padding: 15px; border-radius: 8px; margin-bottom: 20px; }}
        .project-item {{ background: white; padding: 12px; border-radius: 6px; margin-bottom: 8px; border-left: 3px solid #2196f3; }}
        .client-section {{ background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px; }}
        .footer {{ text-align: center; margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px; font-size: 12px; color: #666; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 Report Statistiche Completo</h1>
        <p>Agevolami PM - {datetime.now().strftime('%d/%m/%Y alle %H:%M')}</p>
    </div>
    
    <div class="summary">
        <div class="stat-card">
            <div class="stat-number">{summary.get('total_clients', 0)}</div>
            <div class="stat-label">Clienti</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{summary.get('total_projects', 0)}</div>
            <div class="stat-label">Progetti</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{summary.get('active_projects', 0)}</div>
            <div class="stat-label">Progetti Attivi</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{summary.get('total_deadlines', 0)}</div>
            <div class="stat-label">Scadenze</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" style="color: #f44336;">{summary.get('overdue_deadlines', 0)}</div>
            <div class="stat-label">Scadute</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" style="color: #ff9800;">{summary.get('upcoming_deadlines', 0)}</div>
            <div class="stat-label">Imminenti</div>
        </div>
    </div>
"""
        
        # Sezione progetti attivi
        if isinstance(projects_data, dict) and projects_data.get('active_projects'):
            html += f"""
    <div class="section">
        <div class="section-title">🚀 Progetti Attivi ({len(projects_data['active_projects'])})</div>
        <div class="project-section">
"""
            for project in projects_data['active_projects'][:10]:  # Limit to 10
                completion = project.get('completion_percentage', 0)
                html += f"""
            <div class="project-item">
                <div style="font-weight: bold; color: #1565C0;">{project.get('name', 'N/A')}</div>
                <div style="font-size: 12px; color: #666; margin-top: 4px;">
                    📈 Completamento: {completion}% | 
                    📅 Scadenza: {project.get('end_date', 'N/A')} |
                    👥 Cliente: {project.get('client_name', 'N/A')}
                </div>
            </div>
"""
            html += """
        </div>
    </div>
"""
        
        # Sezione scadenze urgenti (scadute)
        if overdue:
            html += f"""
    <div class="section">
        <div class="section-title">🚨 Scadenze Scadute ({len(overdue)})</div>
"""
            for deadline in overdue[:10]:  # Limit to 10
                project_info = ""
                if deadline.get('project'):
                    project_info = f" - {deadline['project']['name']}"
                if deadline.get('client'):
                    project_info += f" ({deadline['client']['company']})"
                
                description = deadline.get('description', '')
                description_html = f'<div class="deadline-description">{description[:100]}{"..." if len(description) > 100 else ""}</div>' if description else ""
                
                html += f"""
        <div class="deadline-item overdue priority-{deadline.get('priority', 'medium')}">
            <div class="deadline-title">{deadline.get('title', 'N/A')}{project_info}</div>
            <div class="deadline-meta">
                📅 Scadenza: {datetime.fromisoformat(deadline['due_date']).strftime('%d/%m/%Y') if deadline.get('due_date') else 'N/A'} 
                ⏰ Scaduta da {abs(deadline.get('days_remaining', 0))} giorni
                🔥 Priorità: {deadline.get('priority', 'medium').title()}
            </div>
            {description_html}
        </div>
"""
            html += "    </div>\n"
        
        # Sezione scadenze di oggi
        if due_today:
            html += f"""
    <div class="section">
        <div class="section-title">⚠️ Scadenze di Oggi ({len(due_today)})</div>
"""
            for deadline in due_today:
                project_info = ""
                if deadline.get('project'):
                    project_info = f" - {deadline['project']['name']}"
                if deadline.get('client'):
                    project_info += f" ({deadline['client']['company']})"
                
                description = deadline.get('description', '')
                description_html = f'<div class="deadline-description">{description[:100]}{"..." if len(description) > 100 else ""}</div>' if description else ""
                
                html += f"""
        <div class="deadline-item due-today priority-{deadline.get('priority', 'medium')}">
            <div class="deadline-title">{deadline.get('title', 'N/A')}{project_info}</div>
            <div class="deadline-meta">
                📅 Scadenza: OGGI
                🔥 Priorità: {deadline.get('priority', 'medium').title()}
            </div>
            {description_html}
        </div>
"""
            html += "    </div>\n"
        
        # Sezione scadenze prossime
        if due_soon:
            html += f"""
    <div class="section">
        <div class="section-title">📋 Scadenze Prossime (7 giorni) ({len(due_soon)})</div>
"""
            for deadline in due_soon[:15]:  # Limit to 15
                project_info = ""
                if deadline.get('project'):
                    project_info = f" - {deadline['project']['name']}"
                if deadline.get('client'):
                    project_info += f" ({deadline['client']['company']})"
                
                description = deadline.get('description', '')
                description_html = f'<div class="deadline-description">{description[:100]}{"..." if len(description) > 100 else ""}</div>' if description else ""
                
                html += f"""
        <div class="deadline-item priority-{deadline.get('priority', 'medium')}">
            <div class="deadline-title">{deadline.get('title', 'N/A')}{project_info}</div>
            <div class="deadline-meta">
                📅 Scadenza: {datetime.fromisoformat(deadline['due_date']).strftime('%d/%m/%Y') if deadline.get('due_date') else 'N/A'} 
                ⏰ Tra {deadline.get('days_remaining', 0)} giorni
                🔥 Priorità: {deadline.get('priority', 'medium').title()}
            </div>
            {description_html}
        </div>
"""
            html += "    </div>\n"
        
        # Sezione clienti top (se disponibile)
        if isinstance(clients_data, dict) and clients_data.get('top_clients'):
            html += f"""
    <div class="section">
        <div class="section-title">🏆 Top Clienti per Progetti</div>
        <div class="client-section">
"""
            for client in clients_data['top_clients'][:5]:  # Top 5
                html += f"""
            <div style="display: flex; justify-content: space-between; padding: 8px; border-bottom: 1px solid #eee;">
                <span style="font-weight: bold;">{client.get('name', 'N/A')}</span>
                <span style="color: #666;">{client.get('project_count', 0)} progetti</span>
            </div>
"""
            html += """
        </div>
    </div>
"""
        
        html += f"""
    <div class="footer">
        <p>Report generato automaticamente da <strong>Agevolami PM</strong></p>
        <p>Questo report include statistiche generali e dettagli delle scadenze più importanti</p>
        <p>Per visualizzare tutti i dettagli, accedi all'applicazione</p>
    </div>
</body>
</html>
"""
        
        return html
    
    def _generate_full_report_text(self, report: Dict[str, Any]) -> str:
        """Genera testo per il report completo"""
        summary = report.get('summary', {})
        deadlines_list = report.get('deadlines', [])
        projects_data = report.get('projects', {})
        clients_data = report.get('clients', {})
        
        text = f"""
REPORT STATISTICHE COMPLETO - AGEVOLAMI PM
Generato il: {datetime.now().strftime('%d/%m/%Y alle %H:%M')}

=== RIEPILOGO GENERALE ===
Clienti totali: {summary.get('total_clients', 0)}
Progetti totali: {summary.get('total_projects', 0)}
Progetti attivi: {summary.get('active_projects', 0)}
Scadenze totali: {summary.get('total_deadlines', 0)}
Scadenze scadute: {summary.get('overdue_deadlines', 0)}
Scadenze imminenti: {summary.get('upcoming_deadlines', 0)}

"""
        
        # Progetti attivi
        if isinstance(projects_data, dict) and projects_data.get('active_projects'):
            text += f"\n=== PROGETTI ATTIVI ({len(projects_data['active_projects'])}) ===\n"
            for project in projects_data['active_projects'][:10]:
                text += f"• {project.get('name', 'N/A')}\n"
                text += f"  Cliente: {project.get('client_name', 'N/A')}\n"
                text += f"  Completamento: {project.get('completion_percentage', 0)}%\n"
                text += f"  Scadenza: {project.get('end_date', 'N/A')}\n\n"
        
        # Scadenze scadute
        overdue = [d for d in deadlines_list if d.get('days_remaining', 0) < 0 and d.get('status') != 'completed']
        if overdue:
            text += f"\n=== SCADENZE SCADUTE ({len(overdue)}) ===\n"
            for deadline in overdue[:10]:
                project_info = ""
                if deadline.get('project'):
                    project_info = f" - {deadline['project']['name']}"
                if deadline.get('client'):
                    project_info += f" ({deadline['client']['company']})"
                
                text += f"• {deadline.get('title', 'N/A')}{project_info}\n"
                text += f"  Scadenza: {datetime.fromisoformat(deadline['due_date']).strftime('%d/%m/%Y') if deadline.get('due_date') else 'N/A'} (scaduta da {abs(deadline.get('days_remaining', 0))} giorni)\n"
                text += f"  Priorità: {deadline.get('priority', 'medium').title()}\n"
                if deadline.get('description'):
                    desc = deadline['description'][:100] + ("..." if len(deadline['description']) > 100 else "")
                    text += f"  Descrizione: {desc}\n"
                text += "\n"
        
        # Scadenze di oggi
        due_today = [d for d in deadlines_list if d.get('days_remaining', 0) == 0 and d.get('status') != 'completed']
        if due_today:
            text += f"\n=== SCADENZE DI OGGI ({len(due_today)}) ===\n"
            for deadline in due_today:
                project_info = ""
                if deadline.get('project'):
                    project_info = f" - {deadline['project']['name']}"
                if deadline.get('client'):
                    project_info += f" ({deadline['client']['company']})"
                
                text += f"• {deadline.get('title', 'N/A')}{project_info}\n"
                text += f"  Priorità: {deadline.get('priority', 'medium').title()}\n"
                if deadline.get('description'):
                    desc = deadline['description'][:100] + ("..." if len(deadline['description']) > 100 else "")
                    text += f"  Descrizione: {desc}\n"
                text += "\n"
        
        # Scadenze prossime
        due_soon = [d for d in deadlines_list if 0 < d.get('days_remaining', 0) <= 7 and d.get('status') != 'completed']
        if due_soon:
            text += f"\n=== SCADENZE PROSSIME (7 GIORNI) ({len(due_soon)}) ===\n"
            for deadline in due_soon[:15]:
                project_info = ""
                if deadline.get('project'):
                    project_info = f" - {deadline['project']['name']}"
                if deadline.get('client'):
                    project_info += f" ({deadline['client']['company']})"
                
                text += f"• {deadline.get('title', 'N/A')}{project_info}\n"
                text += f"  Scadenza: {datetime.fromisoformat(deadline['due_date']).strftime('%d/%m/%Y') if deadline.get('due_date') else 'N/A'} (tra {deadline.get('days_remaining', 0)} giorni)\n"
                text += f"  Priorità: {deadline.get('priority', 'medium').title()}\n"
                if deadline.get('description'):
                    desc = deadline['description'][:100] + ("..." if len(deadline['description']) > 100 else "")
                    text += f"  Descrizione: {desc}\n"
                text += "\n"
        
        # Top clienti
        if isinstance(clients_data, dict) and clients_data.get('top_clients'):
            text += f"\n=== TOP CLIENTI PER PROGETTI ===\n"
            for i, client in enumerate(clients_data['top_clients'][:5], 1):
                text += f"{i}. {client.get('name', 'N/A')} - {client.get('project_count', 0)} progetti\n"
        
        text += "\n---\nReport generato automaticamente da Agevolami PM\n"
        text += "Questo report include sia statistiche generali che dettagli delle scadenze più importanti.\n"

        return text

    def _generate_automatic_report_text(self, report: Dict[str, Any]) -> str:
        """Genera testo per report automatico"""
        try:
            summary = report.get('summary', {})
            tasks = report.get('tasks', {})
            alerts = report.get('alerts', {})

            generated_date = datetime.fromisoformat(report.get('generated_at', datetime.now().isoformat()))
            date_str = generated_date.strftime('%d/%m/%Y alle %H:%M')

            text = f"""
REPORT AUTOMATICO AGEVOLAMI PM
{date_str}

=== RIEPILOGO ALERT ===
🚨 Elementi Critici: {alerts.get('critical_count', 0)}
⚠️ Attenzione Richiesta: {alerts.get('warning_count', 0)}
📋 Task Totali: {summary.get('total_tasks', 0)}
[OK] Completamento: {tasks.get('completion_rate', 0)}%

=== STATISTICHE GENERALI ===
👥 Clienti: {summary.get('total_clients', 0)}
📁 Progetti: {summary.get('total_projects', 0)}
🔄 Progetti Attivi: {summary.get('active_projects', 0)}
📅 Scadenze Imminenti: {summary.get('upcoming_deadlines', 0)}
"""

            # Add expired tasks if any
            expired_tasks = tasks.get('expired_tasks', [])
            if expired_tasks:
                text += f"\n=== TASK SCADUTE ({len(expired_tasks)}) ===\n"
                for task in expired_tasks[:5]:  # Show max 5
                    text += f"🚨 {task.get('title', 'N/A')} - {task.get('days_overdue', 0)} giorni fa\n"
                    text += f"   📁 {task.get('project_name', 'N/A')} • 🎯 {task.get('deadline_title', 'N/A')}\n"

            # Add expiring tasks if any
            expiring_tasks = tasks.get('expiring_tasks', [])
            if expiring_tasks:
                text += f"\n=== TASK IN SCADENZA ({len(expiring_tasks)}) ===\n"
                for task in expiring_tasks[:5]:  # Show max 5
                    days_remaining = task.get('days_remaining', 0)
                    urgency_text = 'oggi' if days_remaining == 0 else f'{days_remaining} giorni'
                    text += f"⏰ {task.get('title', 'N/A')} - {urgency_text}\n"
                    text += f"   📁 {task.get('project_name', 'N/A')} • 🎯 {task.get('deadline_title', 'N/A')}\n"

            text += "\n---\nReport generato automaticamente da Agevolami PM"
            return text

        except Exception as e:
            logger.error(f"Errore generazione testo automatico: {e}")
            return f"Errore nella generazione del report automatico: {str(e)}"

    def _generate_custom_report_text(self, report: Dict[str, Any]) -> str:
        """Genera testo per report personalizzato"""
        try:
            summary = report.get('summary', {})
            tasks = report.get('tasks', {})
            filters = report.get('filters_applied', {})

            generated_date = datetime.fromisoformat(report.get('generated_at', datetime.now().isoformat()))
            date_str = generated_date.strftime('%d/%m/%Y alle %H:%M')

            text = f"""
REPORT PERSONALIZZATO AGEVOLAMI PM
{date_str}

=== STATISTICHE GENERALI ===
👥 Clienti: {summary.get('total_clients', 0)}
📁 Progetti: {summary.get('total_projects', 0)}
📋 Task: {summary.get('total_tasks', 0)}
📅 Scadenze: {summary.get('total_deadlines', 0)}
"""

            # Add task details if included
            if tasks and isinstance(tasks, dict):
                text += f"""
=== DETTAGLI TASK ===
[OK] Completate: {tasks.get('completed', 0)}
🔄 In Corso: {tasks.get('in_progress', 0)}
🚨 Scadute: {tasks.get('expired', 0)}
⏰ In Scadenza: {tasks.get('expiring_soon', 0)}
📊 Tasso Completamento: {tasks.get('completion_rate', 0)}%
"""

                # Add expired tasks if any
                expired_tasks = tasks.get('expired_tasks', [])
                if expired_tasks:
                    text += f"\n=== TASK SCADUTE ({len(expired_tasks)}) ===\n"
                    for task in expired_tasks:
                        text += f"🚨 {task.get('title', 'N/A')} - {task.get('days_overdue', 0)} giorni fa\n"
                        text += f"   📁 {task.get('project_name', 'N/A')} • 🎯 {task.get('deadline_title', 'N/A')}\n"

            # Add filters info
            if filters:
                text += f"\n=== FILTRI APPLICATI ===\n"
                text += f"• Giorni anticipo: {filters.get('days_ahead', 30)}\n"
                text += f"• Solo scadute: {'Sì' if filters.get('include_expired_only') else 'No'}\n"

            text += "\n---\nReport personalizzato generato da Agevolami PM"
            return text

        except Exception as e:
            logger.error(f"Errore generazione testo personalizzato: {e}")
            return f"Errore nella generazione del report personalizzato: {str(e)}"