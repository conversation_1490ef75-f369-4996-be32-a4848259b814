# 🔧 GitHub Actions Troubleshooting Guide

## Common Build Issues and Solutions

### ❌ Issue: "flet: command not found"

**Problem**: Flet is not installed in the GitHub Actions environment.

**Solution**: ✅ **FIXED** - Updated workflow to install Flet explicitly:
```yaml
- name: Install dependencies
  run: |
    python -m pip install --upgrade pip
    pip install -r requirements-build.txt
```

### ❌ Issue: Requirements.txt contains built-in modules

**Problem**: Original requirements.txt included built-in Python modules like `os`, `json`, `datetime`.

**Solution**: ✅ **FIXED** - Created clean `requirements-build.txt` with only external packages:
- `flet>=0.27.0`
- `packaging>=21.0`
- `pydantic>=2.0.0`
- etc.

### ❌ Issue: Build fails due to missing dependencies

**Problem**: Some dependencies might not be available on Windows build environment.

**Solutions**:
1. ✅ Added caching for pip dependencies
2. ✅ Explicit version pinning
3. ✅ Verification step to check imports

### ❌ Issue: Unicode encoding error (cp1252)

**Problem**: `UnicodeEncodeError: 'charmap' codec can't encode character '\u25cf'`

**Root cause**: Windows console in GitHub Actions uses cp1252 encoding, but Flet CLI tries to display Unicode characters.

**Solution**: ✅ **FIXED** - Multiple approaches implemented:

1. **Python build script** (`scripts/build_windows.py`):
   - Handles encoding properly
   - Streams output safely
   - Better error handling

2. **Environment variables**:
   ```yaml
   env:
     PYTHONIOENCODING: utf-8
     PYTHONUTF8: 1
     PYTHONLEGACYWINDOWSSTDIO: 1
   ```

3. **Alternative workflow** (`.github/workflows/build-alternative.yml`):
   - Fallback option if main workflow fails
   - Simplified build process

### ❌ Issue: Flet build command fails

**Potential causes and solutions**:

1. **Missing pyproject.toml configuration**:
   ```toml
   [tool.flet.app]
   path = "src"
   name = "Agevolami PM"
   icon = "assets/icon.ico"
   ```

2. **Missing main.py in src directory**:
   - Ensure `src/main.py` exists
   - Check that it has proper Flet app structure

3. **Import errors in code**:
   - Test locally first with `python scripts/test_build.py`
   - Fix any import issues before pushing

## 🧪 Testing Locally Before Push

### Run the test script:
```bash
python scripts/test_build.py
```

This will:
- ✅ Check project structure
- ✅ Install dependencies
- ✅ Verify Flet installation
- ✅ Test build process

### Manual testing:
```bash
# Install dependencies
pip install -r requirements-build.txt

# Test Flet
flet --version
python -c "import flet; print('OK')"

# Test build
flet build windows --verbose
```

## 📋 Workflow Improvements Made

### 1. Dependency Management
- ✅ Created `requirements-build.txt` with clean dependencies
- ✅ Added pip caching for faster builds
- ✅ Explicit version pinning

### 2. Error Prevention
- ✅ Added verification steps
- ✅ Better error messages
- ✅ Verbose output for debugging

### 3. Build Process
- ✅ Proper Python version (3.11)
- ✅ Windows-specific optimizations
- ✅ Artifact packaging

## 🔍 Debugging Failed Builds

### Check GitHub Actions Logs:
1. Go to your repository on GitHub
2. Click **Actions** tab
3. Click on the failed workflow run
4. Expand each step to see detailed logs

### Common log patterns to look for:

**Dependency issues**:
```
ERROR: Could not find a version that satisfies the requirement
```

**Import errors**:
```
ModuleNotFoundError: No module named 'xyz'
```

**Flet build errors**:
```
flet: error: the following arguments are required
```

## 🚀 Next Steps After Fix

### 1. Test the fix:
```bash
# Run local test
python scripts/test_build.py

# If successful, create a test release
python scripts/create_release.py patch "Fix GitHub Actions build"
```

### 2. Monitor the build:
- Watch the GitHub Actions workflow
- Check for any remaining issues
- Verify the release is created successfully

### 3. Test the auto-update:
- Download the built executable
- Test the update checking functionality
- Verify the complete update process

## 📁 File Structure for Reference

```
agevolami_pm_v2/
├── .github/
│   └── workflows/
│       └── build-and-release.yml    # ✅ Fixed workflow
├── src/
│   ├── main.py                      # ✅ Entry point
│   ├── core/
│   │   ├── updater.py              # ✅ Update logic
│   │   └── update_manager.py       # ✅ Update UI
│   └── ui/
│       └── update_dialog.py        # ✅ Update dialogs
├── scripts/
│   ├── create_release.py           # ✅ Release automation
│   └── test_build.py               # ✅ Local testing
├── pyproject.toml                  # ✅ Flet configuration
├── requirements.txt                # ✅ Runtime dependencies
├── requirements-build.txt          # ✅ Build dependencies
└── requirements-google.txt         # ✅ Google API dependencies
```

## 🎯 Expected Build Process

1. **Trigger**: Push tag `v1.0.x`
2. **Setup**: Install Python 3.11
3. **Dependencies**: Install from `requirements-build.txt`
4. **Verify**: Check Flet installation
5. **Build**: Run `flet build windows --verbose`
6. **Package**: Create ZIP with executable
7. **Release**: Create GitHub release with assets

## 💡 Tips for Future Releases

### Before creating a release:
1. ✅ Test locally with `python scripts/test_build.py`
2. ✅ Ensure all imports work
3. ✅ Check pyproject.toml configuration
4. ✅ Verify version numbers are updated

### After successful build:
1. ✅ Test the downloaded executable
2. ✅ Verify auto-update functionality
3. ✅ Check release notes and assets
4. ✅ Monitor user feedback

## 🆘 Emergency Fixes

If builds continue to fail:

### Option 1: Simplified workflow
Create a minimal workflow that just builds without all features.

### Option 2: Local build + manual upload
Build locally and upload manually to GitHub releases.

### Option 3: Alternative CI/CD
Consider other platforms like AppVeyor or Azure DevOps.

---

**Status**: ✅ **FIXED** - The workflow should now build successfully!
