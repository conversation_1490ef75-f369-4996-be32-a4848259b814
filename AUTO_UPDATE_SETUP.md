# 🚀 Automatic Updates Setup for Agevolami PM

## Overview
This document explains the automatic update system implemented for Agevolami PM, including GitHub Actions for CI/CD and in-app update functionality.

## 📋 Features Implemented

### ✅ GitHub Actions CI/CD Pipeline
- **Automatic building** on version tags
- **Windows executable generation** using Flet
- **Automatic GitHub releases** with downloadable assets
- **Version management** and artifact packaging

### ✅ In-App Update System
- **Automatic update checking** on startup (daily)
- **Manual update checking** via menu/button
- **Progress dialogs** with download status
- **Automatic installation** and app restart
- **Error handling** and user notifications

### ✅ Version Management
- **Semantic versioning** (major.minor.patch)
- **Automated version bumping** script
- **Git tagging** and release automation

## 🔧 Setup Instructions

### 1. GitHub Repository Setup

#### Enable GitHub Actions (FREE for private repos!)
- **2,000 minutes/month** free for private repositories
- **500 MB** artifact storage
- **Unlimited** for public repositories

#### Required Repository Settings:
1. Go to **Settings > Actions > General**
2. Enable **"Allow all actions and reusable workflows"**
3. Set **Workflow permissions** to **"Read and write permissions"**

### 2. Repository Configuration

#### Update Repository Information:
Edit these files with your actual repository details:

**`src/core/updater.py`** (lines 15-16):
```python
def __init__(self, 
             repo_owner: str = "YOUR_GITHUB_USERNAME", 
             repo_name: str = "agevolami_pm_v2",
```

**`scripts/create_release.py`** (line 150):
```python
print(f"📦 Check: https://github.com/YOUR_USERNAME/agevolami_pm_v2/actions")
```

### 3. Creating Your First Release

#### Method 1: Using the Release Script (Recommended)
```bash
# Navigate to project directory
cd agevolami_pm_v2

# Create a patch release (1.0.1 -> 1.0.2)
python scripts/create_release.py patch "Bug fixes and improvements"

# Create a minor release (1.0.1 -> 1.1.0)
python scripts/create_release.py minor "New features added"

# Create a major release (1.0.1 -> 2.0.0)
python scripts/create_release.py major "Breaking changes and major updates"
```

#### Method 2: Manual Git Tagging
```bash
# Update version in pyproject.toml manually
# Then commit and tag:
git add .
git commit -m "Bump version to 1.0.2"
git tag -a v1.0.2 -m "Release v1.0.2"
git push
git push origin v1.0.2
```

### 4. Integration with Your App

#### Add Update Manager to Main App:
```python
# In your main.py or app initialization
from src.core.update_manager import UpdateManager

def main(page: ft.Page):
    # Initialize update manager
    update_manager = UpdateManager(page, current_version="1.0.1")
    
    # Check for updates on startup (silent)
    update_manager.check_for_updates_on_startup(silent=True)
    
    # Add update menu item to your app menu
    update_menu_item = update_manager.create_update_menu_item()
    
    # Or add update button to settings/about page
    update_button = update_manager.create_update_button()
```

## 🔄 How It Works

### Automatic Release Process:
1. **Developer** runs `create_release.py` script
2. **Script** updates version numbers in all files
3. **Git** commits changes and creates version tag
4. **GitHub Actions** detects new tag and triggers build
5. **Flet** builds Windows executable
6. **GitHub** creates release with downloadable ZIP
7. **Users** get notified of update on next app startup

### User Update Experience:
1. **App startup** checks for updates (once daily)
2. **Update dialog** shows if new version available
3. **User clicks** "Update Now"
4. **App downloads** and installs update automatically
5. **App restarts** with new version

## 📁 Files Created/Modified

### New Files:
- `.github/workflows/build-and-release.yml` - GitHub Actions workflow
- `src/core/updater.py` - Core update functionality
- `src/ui/update_dialog.py` - Update UI dialogs
- `src/core/update_manager.py` - Update coordination
- `scripts/create_release.py` - Release automation script
- `AUTO_UPDATE_SETUP.md` - This documentation

### Modified Files:
- `pyproject.toml` - Version bump to 1.0.1
- `requirements.txt` - Added `packaging>=21.0` dependency

## 🎯 Usage Examples

### For Developers:

#### Create a Bug Fix Release:
```bash
python scripts/create_release.py patch "Fixed database connection issue"
```

#### Create a Feature Release:
```bash
python scripts/create_release.py minor "Added Google Calendar integration"
```

#### Manual Update Check (for testing):
```python
# In your app, add a debug menu item
update_manager.check_for_updates_manual()
```

### For Users:
- Updates are **automatic** - no action needed
- **Manual check** available in app menu
- **Progress indicators** show download status
- **Automatic restart** after successful update

## 🔒 Security & Privacy

### GitHub Actions Security:
- Uses **official GitHub Actions** only
- **No external secrets** required for basic functionality
- **Read-only** access to repository for building

### Update Security:
- Downloads **only from GitHub releases**
- **Verifies** download URLs from official API
- **Backup** created before installing updates
- **Error recovery** if update fails

## 💰 Cost Analysis

### GitHub Actions (Private Repository):
- **FREE tier**: 2,000 minutes/month
- **Typical build time**: 5-10 minutes per release
- **Monthly capacity**: ~200-400 releases (more than sufficient)
- **Storage**: 500MB (plenty for executable files)

### Alternative Solutions:
- **Self-hosted runner**: Free but requires server
- **Public repository**: Unlimited GitHub Actions
- **External CI/CD**: Usually more expensive

## 🚨 Troubleshooting

### Common Issues:

#### Build Fails in GitHub Actions:
1. Check **requirements.txt** for missing dependencies
2. Verify **Python version** compatibility
3. Check **Flet build** command syntax

#### Update Check Fails:
1. Verify **repository owner/name** in updater.py
2. Check **internet connection**
3. Ensure **GitHub API** is accessible

#### Update Installation Fails:
1. Check **file permissions** on Windows
2. Verify **antivirus** not blocking downloads
3. Ensure **sufficient disk space**

### Debug Mode:
Enable verbose logging in updater.py for troubleshooting:
```python
# Add to updater.py for debugging
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 🎉 Benefits

### For Developers:
- **Automated** release process
- **Professional** update system
- **Version control** integration
- **Free** hosting and distribution

### For Users:
- **Seamless** updates
- **No manual downloads**
- **Always latest version**
- **Professional** user experience

### For Business:
- **Reduced support** requests
- **Faster bug fixes** deployment
- **Feature rollout** control
- **Professional image**

## 📞 Support

For issues with the auto-update system:
1. Check this documentation
2. Review GitHub Actions logs
3. Test with manual update check
4. Contact development team

---

**🎯 Ready to use!** Your automatic update system is now configured and ready for production use.
