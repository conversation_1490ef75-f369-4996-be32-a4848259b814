#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Modern Settings System
Clean interface for the new modular settings architecture
"""

# Import modern components directly
try:
    import flet as ft
    from core import get_logger

    # Core system imports
    from .settings_controller import SettingsController
    from .modern_settings_view import ModernSettingsView

    # Component imports for advanced usage
    from .components.form_components import FormField, SwitchField, DropdownField, CheckboxGroup, ButtonGroup, InfoBox
    from .components.settings_card import SettingsCard, SettingsCardSection, QuickActionsCard

    # Section imports
    from .sections import (
        EmailSection,
        NotificationsSection,
        GoogleServicesSection,
        WindowsSection,
        ReportsSection
    )

    MODERN_SYSTEM_AVAILABLE = True
    IMPORT_ERROR = None

    # Create the main SettingsView class that uses the modern system
    class SettingsView:
        """
        Modern Settings View - uses the new modular architecture
        """
        def __init__(self, app_instance):
            self.app = app_instance
            self.controller = SettingsController(app_instance)

            # Initialize sections directly
            self.sections = {
                'email': EmailSection(self.controller),
                'notifications': NotificationsSection(self.controller),
                'google_services': GoogleServicesSection(self.controller),
                'windows': WindowsSection(self.controller),
                'reports': ReportsSection(self.controller)
            }

        def build(self):
            """Build the settings view using the modern system"""
            try:
                # Create a simple layout with sections
                content = []

                # Header
                content.append(
                    ft.Container(
                        content=ft.Text(
                            "⚙️ Impostazioni Agevolami PM",
                            size=24,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.GREY_800
                        ),
                        padding=ft.padding.only(bottom=20)
                    )
                )

                # Add sections
                for section_name, section in self.sections.items():
                    try:
                        section_card = section.build()
                        # If section.build() returns a custom class, get the actual control
                        if hasattr(section_card, 'build'):
                            section_card = section_card.build()
                        content.append(section_card)
                    except Exception as e:
                        # Fallback for sections that fail
                        content.append(
                            ft.Container(
                                content=ft.Text(f"⚠️ Error loading {section_name} section: {str(e)}"),
                                padding=ft.padding.all(10),
                                bgcolor=ft.Colors.RED_50,
                                border_radius=8
                            )
                        )

                return ft.Container(
                    content=ft.Column(content, spacing=16, scroll=ft.ScrollMode.AUTO),
                    padding=ft.padding.all(20),
                    expand=True,
                    bgcolor=ft.Colors.GREY_50
                )

            except Exception as e:
                # Ultimate fallback
                return ft.Container(
                    content=ft.Column([
                        ft.Text("⚠️ Settings System Error", size=20, weight=ft.FontWeight.BOLD),
                        ft.Text(f"Error: {str(e)}", size=14),
                        ft.Text("Using basic settings interface.", size=12, color=ft.Colors.GREY_600)
                    ], spacing=10),
                    padding=ft.padding.all(20),
                    bgcolor=ft.Colors.WHITE,
                    border_radius=8
                )

        def refresh_data(self):
            """Refresh settings data"""
            try:
                self.controller._load_settings()
                for section in self.sections.values():
                    if hasattr(section, 'refresh_data'):
                        section.refresh_data()
            except Exception as e:
                logger.error(f"Error refreshing settings data: {e}")

        def handle_setting_change(self, category: str, key: str, value):
            """Handle setting changes"""
            return self.controller.set_setting(category, key, value)
    
except ImportError as e:
    # Fall back to minimal system if components aren't ready
    MODERN_SYSTEM_AVAILABLE = False
    IMPORT_ERROR = str(e)

    # Create minimal fallback SettingsView
    class SettingsView:
        def __init__(self, app_instance):
            self.app = app_instance

        def build(self):
            import flet as ft
            return ft.Container(
                content=ft.Column([
                    ft.Text("⚠️ Settings System Error", size=20, weight=ft.FontWeight.BOLD),
                    ft.Text(f"Error loading modern settings: {IMPORT_ERROR}", size=14),
                    ft.Text("Please check the settings components.", size=12, color=ft.Colors.GREY_600)
                ], spacing=10),
                padding=ft.padding.all(20),
                bgcolor=ft.Colors.WHITE,
                border_radius=8
            )

        def refresh_data(self):
            pass

        def handle_setting_change(self, category: str, key: str, value):
            pass

# Version and metadata
__version__ = "2.0.0"
__author__ = "Agevolami Team"
__description__ = "Modern Settings System with modular architecture"

# Export main components
if MODERN_SYSTEM_AVAILABLE:
    __all__ = [
        'SettingsView',  # Main settings view
        'SettingsController',
        'ModernSettingsView',
        'FormField',
        'SwitchField',
        'DropdownField',
        'CheckboxGroup',
        'ButtonGroup',
        'InfoBox',
        'SettingsCard',
        'SettingsCardSection',
        'QuickActionsCard',
        'EmailSection',
        'NotificationsSection',
        'GoogleServicesSection',
        'WindowsSection',
        'ReportsSection',
    ]
else:
    __all__ = [
        'SettingsView',  # Fallback
    ]

def get_system_info():
    """Get information about the settings system"""
    return {
        'version': __version__,
        'modern_system_available': MODERN_SYSTEM_AVAILABLE,
        'components_loaded': len(__all__),
        'description': __description__,
        'import_error': IMPORT_ERROR
    }