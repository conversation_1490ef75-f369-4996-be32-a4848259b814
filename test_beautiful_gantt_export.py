#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Script for Beautiful Enhanced Gantt Chart with Export Functionality
Demonstrates the new visual improvements and PDF/PNG/SVG export capabilities
"""

import flet as ft
import sys
import os
from datetime import datetime, date, timedelta
from dataclasses import dataclass
from enum import Enum
from typing import List

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Mock the models that might be imported
class ProjectStatus:
    DRAFT = "bozza"
    SUBMITTED = "presentato"
    APPROVED = "approvato"
    IN_PROGRESS = "in_corso"
    COMPLETED = "completato"
    SUSPENDED = "sospeso"
    CANCELLED = "cancellato"

class Priority:
    LOW = "bassa"
    MEDIUM = "media"
    HIGH = "alta"
    CRITICAL = "critica"

class DeadlineStatus:
    PENDING = "in_attesa"
    COMPLETED = "completato"
    OVERDUE = "scaduto"
    CANCELLED = "annullato"

@dataclass
class Project:
    id: str
    name: str
    description: str
    client_id: str
    status: str
    start_date: date
    end_date: date = None
    budget: float = 0.0
    
@dataclass
class Deadline:
    id: str
    title: str
    description: str
    client_id: str
    due_date: date
    priority: str
    status: str

@dataclass
class Client:
    id: str
    name: str
    email: str

# Mock database manager
class MockDatabaseManager:
    def __init__(self):
        self.projects = []
        self.deadlines = []
        self.clients = []
        self._create_beautiful_test_data()
    
    def _create_beautiful_test_data(self):
        """Create comprehensive test data showcasing all features"""
        today = date.today()
        
        # Create clients
        self.clients = [
            Client("1", "🏢 TechCorp Italia", "<EMAIL>"),
            Client("2", "🏗️ Costruzioni Moderne", "<EMAIL>"),
            Client("3", "🍕 Ristorante Bella Vista", "<EMAIL>"),
            Client("4", "💼 Studio Legale Rossi", "<EMAIL>"),
            Client("5", "🏥 Clinica San Marco", "<EMAIL>"),
            Client("6", "🎓 Università Nova", "<EMAIL>")
        ]
        
        # Create diverse projects with different statuses and timelines
        self.projects = [
            Project("p1", "🚀 Sistema CRM Avanzato", "Sviluppo CRM personalizzato", "1", 
                   ProjectStatus.IN_PROGRESS, today - timedelta(days=20), today + timedelta(days=40)),
            
            Project("p2", "🏗️ Gestionale Cantieri", "Software gestione cantieri", "2", 
                   ProjectStatus.APPROVED, today + timedelta(days=5), today + timedelta(days=60)),
            
            Project("p3", "📱 App Mobile Prenotazioni", "App per prenotazioni tavoli", "3", 
                   ProjectStatus.SUBMITTED, today - timedelta(days=10), today + timedelta(days=30)),
            
            Project("p4", "⚖️ Archivio Digitale", "Digitalizzazione archivio legale", "4", 
                   ProjectStatus.DRAFT, today + timedelta(days=15), today + timedelta(days=75)),
            
            Project("p5", "🩺 Sistema Pazienti", "Gestione cartelle cliniche", "5", 
                   ProjectStatus.COMPLETED, today - timedelta(days=60), today - timedelta(days=10)),
            
            Project("p6", "📚 Portale Studenti", "Piattaforma e-learning", "6", 
                   ProjectStatus.IN_PROGRESS, today - timedelta(days=30), today + timedelta(days=50)),
            
            Project("p7", "🔧 Manutenzione Sito", "Aggiornamento sito web", "1", 
                   ProjectStatus.SUSPENDED, today - timedelta(days=5), today + timedelta(days=20)),
            
            Project("p8", "📊 Dashboard Analytics", "Cruscotto business intelligence", "2", 
                   ProjectStatus.CANCELLED, today - timedelta(days=15), today + timedelta(days=10))
        ]
        
        # Create deadlines with various priorities and statuses
        self.deadlines = [
            Deadline("d1", "📄 Consegna Documentazione", "Documentazione tecnica finale", "1", 
                    today + timedelta(days=7), Priority.HIGH, DeadlineStatus.PENDING),
            
            Deadline("d2", "🏗️ Approvazione Progetto", "Approvazione finale del progetto", "2", 
                    today + timedelta(days=12), Priority.CRITICAL, DeadlineStatus.PENDING),
            
            Deadline("d3", "🍕 Lancio App Beta", "Release versione beta", "3", 
                    today + timedelta(days=25), Priority.MEDIUM, DeadlineStatus.PENDING),
            
            Deadline("d4", "⚖️ Revisione Legale", "Controllo compliance normativa", "4", 
                    today + timedelta(days=45), Priority.LOW, DeadlineStatus.PENDING),
            
            Deadline("d5", "🩺 Test Sistema", "Testing funzionalità complete", "5", 
                    today + timedelta(days=18), Priority.HIGH, DeadlineStatus.PENDING),
            
            Deadline("d6", "📚 Formazione Utenti", "Training per amministratori", "6", 
                    today + timedelta(days=35), Priority.MEDIUM, DeadlineStatus.PENDING),
            
            Deadline("d7", "🔐 Audit Sicurezza", "Verifica sicurezza sistema", "1", 
                    today + timedelta(days=3), Priority.CRITICAL, DeadlineStatus.PENDING),
            
            Deadline("d8", "📈 Report Performance", "Analisi performance mensile", "2", 
                    today + timedelta(days=28), Priority.LOW, DeadlineStatus.PENDING),
            
            # Add some completed deadlines (past dates)
            Deadline("d9", "✅ Setup Iniziale", "Configurazione ambiente sviluppo", "1", 
                    today - timedelta(days=30), Priority.MEDIUM, DeadlineStatus.COMPLETED),
            
            Deadline("d10", "✅ Prima Review", "Revisione documenti base", "2", 
                    today - timedelta(days=15), Priority.LOW, DeadlineStatus.COMPLETED),
            
            Deadline("d11", "✅ Test Alpha", "Testing versione alpha", "3", 
                    today - timedelta(days=5), Priority.HIGH, DeadlineStatus.COMPLETED),
            
            # Add an overdue deadline
            Deadline("d12", "⚠️ Backup Sistema", "Backup di sicurezza scaduto", "1", 
                    today - timedelta(days=2), Priority.CRITICAL, DeadlineStatus.OVERDUE)
        ]
    
    def get_all_projects(self):
        return self.projects
    
    def get_deadlines_by_date_range(self, start_date, end_date):
        return [d for d in self.deadlines if start_date <= d.due_date <= end_date]
    
    def get_all_clients(self):
        return self.clients

# Mock app instance
class MockApp:
    def __init__(self):
        self.db_manager = MockDatabaseManager()
        self.page = None
        self.notifications = []
    
    def show_success(self, message: str):
        """Show success notification"""
        print(f"✅ SUCCESS: {message}")
        self.notifications.append(("success", message))
    
    def show_error(self, message: str):
        """Show error notification"""
        print(f"❌ ERROR: {message}")
        self.notifications.append(("error", message))

def main(page: ft.Page):
    """Main application function"""
    page.title = "🎨 Beautiful Enhanced Gantt Chart with Export"
    page.theme_mode = ft.ThemeMode.LIGHT
    page.padding = 0
    page.spacing = 0
    page.bgcolor = ft.Colors.GREY_50
    
    print("🎨 Initializing Beautiful Enhanced Gantt Chart...")
    
    try:
        # Import here to avoid import issues
        from ui.views.matplotlib_gantt import MatplotlibGanttView
        
        # Create mock app instance
        app = MockApp()
        app.page = page
        
        # Create enhanced Gantt view
        gantt_view = MatplotlibGanttView(app)
        
        # Add welcome message
        welcome_card = ft.Container(
            content=ft.Column([
                ft.Text(
                    "🎨 Beautiful Enhanced Gantt Chart",
                    size=28,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.WHITE
                ),
                ft.Text(
                    "✨ Con miglioramenti grafici professionali e funzionalità di esportazione",
                    size=16,
                    color=ft.Colors.WHITE70
                ),
                ft.Divider(color=ft.Colors.WHITE30),
                ft.Row([
                    ft.Container(
                        content=ft.Column([
                            ft.Icon(ft.Icons.PALETTE, size=32, color=ft.Colors.WHITE),
                            ft.Text("Design Moderno", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.WHITE),
                            ft.Text("Colori gradient, ombre, effetti glow", size=12, color=ft.Colors.WHITE70)
                        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                        width=200
                    ),
                    ft.Container(
                        content=ft.Column([
                            ft.Icon(ft.Icons.PICTURE_AS_PDF, size=32, color=ft.Colors.WHITE),
                            ft.Text("Export PDF", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.WHITE),
                            ft.Text("Alta qualità 300 DPI", size=12, color=ft.Colors.WHITE70)
                        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                        width=200
                    ),
                    ft.Container(
                        content=ft.Column([
                            ft.Icon(ft.Icons.IMAGE, size=32, color=ft.Colors.WHITE),
                            ft.Text("Export PNG/SVG", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.WHITE),
                            ft.Text("Immagini e vettoriali", size=12, color=ft.Colors.WHITE70)
                        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                        width=200
                    )
                ], alignment=ft.MainAxisAlignment.SPACE_AROUND)
            ], spacing=16),
            padding=ft.padding.all(24),
            margin=ft.margin.all(20),
            bgcolor=ft.Colors.INDIGO_600,
            border_radius=16,
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=12,
                color=ft.Colors.with_opacity(0.2, ft.Colors.BLACK),
                offset=ft.Offset(0, 4)
            )
        )
        
        # Main layout
        page.add(
            ft.Column([
                welcome_card,
                gantt_view.build()
            ], spacing=0, scroll=ft.ScrollMode.AUTO)
        )
        
        print("✅ Beautiful Enhanced Gantt Chart loaded successfully!")
        
    except Exception as e:
        print(f"❌ Error loading Gantt Chart: {e}")
        page.add(
            ft.Container(
                content=ft.Column([
                    ft.Icon(ft.Icons.ERROR, size=64, color=ft.Colors.RED),
                    ft.Text(f"Error: {e}", size=16, color=ft.Colors.RED),
                    ft.Text("Please check the console for details", size=12, color=ft.Colors.GREY)
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=ft.padding.all(40),
                alignment=ft.alignment.center
            )
        )

if __name__ == "__main__":
    print("🚀 Avvio Beautiful Enhanced Gantt Chart Test...")
    print("📊 Caricamento dati di test con tutti i miglioramenti grafici...")
    print("💾 Funzionalità di esportazione PDF/PNG/SVG attive!")
    print("✨ Ready per il test!")
    ft.app(target=main, view=ft.AppView.WEB_BROWSER, port=8081) 